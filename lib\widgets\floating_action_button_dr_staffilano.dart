import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../theme/dr_staffilano_theme.dart';

/// Floating Action Button personalizzato Dr. Staffilano con animazioni premium
class FloatingActionButtonDrStaffilano extends StatefulWidget {
  final VoidCallback? onPressed;
  final IconData icon;
  final String? tooltip;
  final bool isExtended;
  final String? label;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? elevation;
  final bool showPulse;

  const FloatingActionButtonDrStaffilano({
    Key? key,
    this.onPressed,
    required this.icon,
    this.tooltip,
    this.isExtended = false,
    this.label,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.showPulse = true,
  }) : super(key: key);

  @override
  State<FloatingActionButtonDrStaffilano> createState() => _FloatingActionButtonDrStaffilanoState();
}

class _FloatingActionButtonDrStaffilanoState extends State<FloatingActionButtonDrStaffilano>
    with TickerProviderStateMixin {
  
  late AnimationController _pulseController;
  late AnimationController _scaleController;
  late AnimationController _rotationController;
  
  late Animation<double> _pulseAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    
    // Controller per l'effetto pulse
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    // Controller per l'effetto scale al tap
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    // Controller per la rotazione dell'icona
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.25, // 90 gradi
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.easeInOut,
    ));

    // Avvia l'animazione pulse se abilitata
    if (widget.showPulse) {
      _startPulseAnimation();
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _scaleController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  void _startPulseAnimation() {
    _pulseController.repeat(reverse: true);
  }

  void _stopPulseAnimation() {
    _pulseController.stop();
    _pulseController.reset();
  }

  void _onTapDown(TapDownDetails details) {
    setState(() {
      _isPressed = true;
    });
    _scaleController.forward();
    _rotationController.forward();
    HapticFeedback.mediumImpact();
  }

  void _onTapUp(TapUpDetails details) {
    setState(() {
      _isPressed = false;
    });
    _scaleController.reverse();
    _rotationController.reverse();
  }

  void _onTapCancel() {
    setState(() {
      _isPressed = false;
    });
    _scaleController.reverse();
    _rotationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final backgroundColor = widget.backgroundColor ?? DrStaffilanoTheme.primaryGreen;
    final foregroundColor = widget.foregroundColor ?? Colors.white;
    
    return AnimatedBuilder(
      animation: Listenable.merge([_pulseAnimation, _scaleAnimation, _rotationAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value * _scaleAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(widget.isExtended ? 28 : 28),
              boxShadow: [
                BoxShadow(
                  color: backgroundColor.withOpacity(0.3),
                  blurRadius: _isPressed ? 8 : 12,
                  offset: Offset(0, _isPressed ? 2 : 4),
                  spreadRadius: _isPressed ? 0 : 2,
                ),
                // Effetto glow aggiuntivo
                if (widget.showPulse)
                  BoxShadow(
                    color: backgroundColor.withOpacity(0.2),
                    blurRadius: 20,
                    offset: const Offset(0, 0),
                    spreadRadius: 4,
                  ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: GestureDetector(
                onTapDown: _onTapDown,
                onTapUp: _onTapUp,
                onTapCancel: _onTapCancel,
                onTap: widget.onPressed,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        backgroundColor,
                        backgroundColor.withOpacity(0.8),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(widget.isExtended ? 28 : 28),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: widget.isExtended
                      ? _buildExtendedContent(foregroundColor)
                      : _buildRegularContent(foregroundColor),
                ),
              ),
            ),
          ),
        );
      },
    ).animate()
        .slideY(begin: 1, end: 0, duration: 600.ms, curve: Curves.elasticOut)
        .fadeIn(duration: 400.ms);
  }

  /// Contenuto per FAB normale
  Widget _buildRegularContent(Color foregroundColor) {
    return Container(
      width: 56,
      height: 56,
      child: Center(
        child: AnimatedBuilder(
          animation: _rotationAnimation,
          builder: (context, child) {
            return Transform.rotate(
              angle: _rotationAnimation.value * 2 * 3.14159,
              child: Icon(
                widget.icon,
                color: foregroundColor,
                size: 24,
              ),
            );
          },
        ),
      ),
    );
  }

  /// Contenuto per FAB esteso
  Widget _buildExtendedContent(Color foregroundColor) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          AnimatedBuilder(
            animation: _rotationAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotationAnimation.value * 2 * 3.14159,
                child: Icon(
                  widget.icon,
                  color: foregroundColor,
                  size: 20,
                ),
              );
            },
          ),
          if (widget.label != null) ...[
            const SizedBox(width: 12),
            Text(
              widget.label!,
              style: TextStyle(
                color: foregroundColor,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Variante mini del FAB Dr. Staffilano
class MiniFabDrStaffilano extends StatelessWidget {
  final VoidCallback? onPressed;
  final IconData icon;
  final String? tooltip;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const MiniFabDrStaffilano({
    Key? key,
    this.onPressed,
    required this.icon,
    this.tooltip,
    this.backgroundColor,
    this.foregroundColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final backgroundColor = this.backgroundColor ?? DrStaffilanoTheme.secondaryBlue;
    final foregroundColor = this.foregroundColor ?? Colors.white;
    
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            backgroundColor,
            backgroundColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: backgroundColor.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () {
            HapticFeedback.lightImpact();
            onPressed?.call();
          },
          child: Center(
            child: Icon(
              icon,
              color: foregroundColor,
              size: 18,
            ),
          ),
        ),
      ),
    ).animate()
        .scale(delay: 200.ms, duration: 300.ms, curve: Curves.elasticOut)
        .fadeIn();
  }
}

/// FAB con menu a comparsa
class ExpandableFabDrStaffilano extends StatefulWidget {
  final List<FabMenuItem> menuItems;
  final IconData mainIcon;
  final String? tooltip;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const ExpandableFabDrStaffilano({
    Key? key,
    required this.menuItems,
    this.mainIcon = FontAwesomeIcons.plus,
    this.tooltip,
    this.backgroundColor,
    this.foregroundColor,
  }) : super(key: key);

  @override
  State<ExpandableFabDrStaffilano> createState() => _ExpandableFabDrStaffilanoState();
}

class _ExpandableFabDrStaffilanoState extends State<ExpandableFabDrStaffilano>
    with TickerProviderStateMixin {
  
  late AnimationController _controller;
  late Animation<double> _expandAnimation;
  late Animation<double> _rotateAnimation;
  
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _expandAnimation = CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    );
    
    _rotateAnimation = Tween<double>(
      begin: 0.0,
      end: 0.125, // 45 gradi
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggle() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
    
    if (_isExpanded) {
      _controller.forward();
    } else {
      _controller.reverse();
    }
    
    HapticFeedback.mediumImpact();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ...widget.menuItems.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          
          return AnimatedBuilder(
            animation: _expandAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _expandAnimation.value,
                child: Container(
                  margin: EdgeInsets.only(
                    bottom: 12,
                    top: index == 0 ? 12 : 0,
                  ),
                  child: MiniFabDrStaffilano(
                    onPressed: () {
                      item.onPressed();
                      _toggle();
                    },
                    icon: item.icon,
                    tooltip: item.tooltip,
                    backgroundColor: item.backgroundColor,
                    foregroundColor: item.foregroundColor,
                  ),
                ),
              );
            },
          );
        }).toList().reversed.toList(),
        FloatingActionButtonDrStaffilano(
          onPressed: _toggle,
          icon: widget.mainIcon,
          tooltip: widget.tooltip,
          backgroundColor: widget.backgroundColor,
          foregroundColor: widget.foregroundColor,
          showPulse: false,
        ),
      ],
    );
  }
}

/// Elemento del menu FAB
class FabMenuItem {
  final IconData icon;
  final VoidCallback onPressed;
  final String? tooltip;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const FabMenuItem({
    required this.icon,
    required this.onPressed,
    this.tooltip,
    this.backgroundColor,
    this.foregroundColor,
  });
}
