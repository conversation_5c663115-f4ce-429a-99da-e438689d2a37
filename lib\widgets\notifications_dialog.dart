import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/community_notification.dart';
import '../services/notification_service.dart';
import '../theme/dr_staffilano_theme.dart';

/// Dialog centrato per visualizzare le notifiche della community
class NotificationsDialog extends StatefulWidget {
  const NotificationsDialog({super.key});

  @override
  State<NotificationsDialog> createState() => _NotificationsDialogState();
}

class _NotificationsDialogState extends State<NotificationsDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Opacity(
          opacity: _opacityAnimation.value,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Dialog(
              backgroundColor: Colors.transparent,
              insetPadding: const EdgeInsets.all(20),
              child: Container(
                constraints: const BoxConstraints(
                  maxWidth: 500,
                  maxHeight: 600,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildHeader(context),
                    const Divider(height: 1),
                    Flexible(
                      child: _buildNotificationsList(context),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Header del dialog con titolo e azioni
  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Icon(
            Icons.notifications,
            color: DrStaffilanoTheme.primaryGreen,
            size: 28,
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              'Notifiche',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
          Consumer<NotificationService>(
            builder: (context, notificationService, child) {
              return notificationService.unreadCount > 0
                  ? TextButton(
                      onPressed: () async {
                        await notificationService.markAllAsRead();
                      },
                      child: Text(
                        'Segna tutte come lette',
                        style: TextStyle(
                          color: DrStaffilanoTheme.secondaryBlue,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    )
                  : const SizedBox.shrink();
            },
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close),
            color: Colors.grey[600],
          ),
        ],
      ),
    );
  }

  /// Lista delle notifiche
  Widget _buildNotificationsList(BuildContext context) {
    return Consumer<NotificationService>(
      builder: (context, notificationService, child) {
        final notifications = notificationService.notifications;

        if (notifications.isEmpty) {
          return _buildEmptyState();
        }

        return ListView.separated(
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: notifications.length,
          separatorBuilder: (context, index) => const Divider(
            height: 1,
            indent: 20,
            endIndent: 20,
          ),
          itemBuilder: (context, index) {
            final notification = notifications[index];
            return _buildNotificationItem(context, notification, notificationService);
          },
        );
      },
    );
  }

  /// Stato vuoto quando non ci sono notifiche
  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 80,
            color: Colors.grey[300],
          ),
          const SizedBox(height: 16),
          Text(
            'Nessuna notifica',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Le tue notifiche appariranno qui',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Singolo item di notifica
  Widget _buildNotificationItem(
    BuildContext context,
    CommunityNotification notification,
    NotificationService notificationService,
  ) {
    final typeColor = Color(int.parse(notification.type.colorHex.substring(1), radix: 16) + 0xFF000000);

    return InkWell(
      onTap: () async {
        if (!notification.isRead) {
          await notificationService.markAsRead(notification.id);
        }
        // TODO: Navigare al post se postId è presente
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        color: notification.isRead ? Colors.transparent : Colors.blue.withOpacity(0.05),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Avatar dell'utente che ha generato la notifica
            CircleAvatar(
              radius: 24,
              backgroundColor: typeColor.withOpacity(0.1),
              backgroundImage: notification.actorUserAvatar != null
                  ? NetworkImage(notification.actorUserAvatar!)
                  : null,
              child: notification.actorUserAvatar == null
                  ? Text(
                      notification.actorUserName.isNotEmpty
                          ? notification.actorUserName[0].toUpperCase()
                          : '?',
                      style: TextStyle(
                        color: typeColor,
                        fontWeight: FontWeight.bold,
                      ),
                    )
                  : null,
            ),
            const SizedBox(width: 12),
            // Contenuto della notifica
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      // Icona del tipo di notifica
                      Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: typeColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(
                          notification.type.icon,
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Indicatore non letta
                      if (!notification.isRead)
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: DrStaffilanoTheme.secondaryBlue,
                            shape: BoxShape.circle,
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // Messaggio della notifica
                  Text(
                    notification.message,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: notification.isRead ? FontWeight.normal : FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  // Timestamp
                  Text(
                    notification.relativeTime,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            // Menu azioni
            PopupMenuButton<String>(
              icon: Icon(
                Icons.more_vert,
                color: Colors.grey[600],
                size: 20,
              ),
              onSelected: (value) async {
                switch (value) {
                  case 'mark_read':
                    if (!notification.isRead) {
                      await notificationService.markAsRead(notification.id);
                    }
                    break;
                  case 'remove':
                    await notificationService.removeNotification(notification.id);
                    break;
                }
              },
              itemBuilder: (context) => [
                if (!notification.isRead)
                  const PopupMenuItem(
                    value: 'mark_read',
                    child: Row(
                      children: [
                        Icon(Icons.mark_email_read, size: 18),
                        SizedBox(width: 8),
                        Text('Segna come letta'),
                      ],
                    ),
                  ),
                const PopupMenuItem(
                  value: 'remove',
                  child: Row(
                    children: [
                      Icon(Icons.delete_outline, size: 18),
                      SizedBox(width: 8),
                      Text('Rimuovi'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// Funzione helper per mostrare il dialog delle notifiche
Future<void> showNotificationsDialog(BuildContext context) {
  return showDialog(
    context: context,
    barrierDismissible: true,
    builder: (context) => const NotificationsDialog(),
  );
}
