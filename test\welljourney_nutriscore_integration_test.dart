import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../lib/controllers/welljourney_controller.dart';
import '../lib/services/nutriscore_service.dart';

/// Test di integrazione per verificare che i punti WellJourney
/// si integrino correttamente con il sistema NutriScore
void main() {
  group('WellJourney-NutriScore Integration Tests', () {
    late WellJourneyController controller;
    late NutriScoreService nutriScoreService;

    setUp(() async {
      // Mock SharedPreferences
      SharedPreferences.setMockInitialValues({});

      controller = WellJourneyController();
      nutriScoreService = NutriScoreService.instance;

      await controller.initialize();
    });

    test('Quiz completion should add points to WellJourney', () async {
      // Arrange
      const quizId = 'heart_quiz_1';
      const moduleId = 'heart_module_1';
      const score = 85; // Punteggio eccellente
      const maxScore = 100;
      const bonusPoints = 25;

      final initialPoints = controller.userProgress?.totalPoints ?? 0;

      // Act
      await controller.completeQuiz(quizId, moduleId, score, maxScore, bonusPoints: bonusPoints);

      // Assert
      final finalPoints = controller.userProgress?.totalPoints ?? 0;
      final expectedBasePoints = 125; // Per punteggio 85%
      final expectedTotalPoints = expectedBasePoints + bonusPoints;

      expect(finalPoints, equals(initialPoints + expectedTotalPoints));
      print('✅ Punti aggiunti correttamente: +$expectedTotalPoints');
    });

    test('Perfect quiz score should give maximum points', () async {
      // Arrange
      const quizId = 'prevention_quiz_5';
      const moduleId = 'prevention_module_5';
      const score = 100; // Punteggio perfetto
      const maxScore = 100;
      const bonusPoints = 50; // Bonus per punteggio perfetto

      final initialPoints = controller.userProgress?.totalPoints ?? 0;

      // Act
      await controller.completeQuiz(quizId, moduleId, score, maxScore, bonusPoints: bonusPoints);

      // Assert
      final finalPoints = controller.userProgress?.totalPoints ?? 0;
      final expectedBasePoints = 150; // Massimo per punteggio perfetto
      final expectedTotalPoints = expectedBasePoints + bonusPoints;

      expect(finalPoints, equals(initialPoints + expectedTotalPoints));
      print('✅ Punteggio perfetto ricompensato correttamente: +$expectedTotalPoints');
    });

    test('Quiz completion should update NutriScore educational component', () async {
      // Arrange
      const quizId = 'mediterranean_quiz_3';
      const moduleId = 'mediterranean_module_3';
      const score = 90; // Punteggio molto alto
      const maxScore = 100;

      // Act
      await controller.completeQuiz(quizId, moduleId, score, maxScore);

      // Assert
      final nutriScoreHistory = controller.nutriScoreHistory;
      final today = DateTime.now();
      final dateKey = '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';

      expect(nutriScoreHistory.containsKey(dateKey), isTrue);

      final todayScore = nutriScoreHistory[dateKey]!;
      expect(todayScore.categoryScores['education'], greaterThan(0));
      print('✅ NutriScore educativo aggiornato: ${todayScore.categoryScores['education']} punti');
    });

    test('Multiple quiz completions should accumulate points correctly', () async {
      // Arrange
      final quizzes = [
        {'id': 'heart_quiz_1', 'module': 'heart_module_1', 'score': 75},
        {'id': 'weight_quiz_2', 'module': 'weight_module_2', 'score': 85},
        {'id': 'sports_quiz_3', 'module': 'sports_module_3', 'score': 95},
      ];

      final initialPoints = controller.userProgress?.totalPoints ?? 0;
      int expectedTotalPoints = 0;

      // Act & Assert
      for (final quiz in quizzes) {
        await controller.completeQuiz(
          quiz['id'] as String,
          quiz['module'] as String,
          quiz['score'] as int,
          100,
        );

        // Calcola punti attesi per questo quiz
        final score = quiz['score'] as int;
        int expectedQuizPoints = 0;
        if (score >= 95) {
          expectedQuizPoints = 150;
        } else if (score >= 85) {
          expectedQuizPoints = 125;
        } else if (score >= 75) {
          expectedQuizPoints = 100;
        }

        expectedTotalPoints += expectedQuizPoints;
      }

      final finalPoints = controller.userProgress?.totalPoints ?? 0;
      expect(finalPoints, equals(initialPoints + expectedTotalPoints));
      print('✅ Accumulo punti multipli corretto: +$expectedTotalPoints');
    });

    test('Quiz points should persist between app sessions', () async {
      // Arrange
      const quizId = 'prevention_quiz_1';
      const moduleId = 'prevention_module_1';
      const score = 80;
      const maxScore = 100;

      // Act - Prima sessione
      await controller.completeQuiz(quizId, moduleId, score, maxScore);
      final pointsAfterQuiz = controller.userProgress?.totalPoints ?? 0;

      // Simula riavvio app
      final newController = WellJourneyController();
      await newController.initialize();

      // Assert
      final pointsAfterRestart = newController.userProgress?.totalPoints ?? 0;
      expect(pointsAfterRestart, equals(pointsAfterQuiz));
      print('✅ Persistenza punti verificata: $pointsAfterRestart punti');
    });

    test('WellJourney points should accumulate correctly', () async {
      // Arrange - Completa alcuni quiz per accumulare punti
      await controller.completeQuiz('heart_quiz_1', 'heart_module_1', 90, 100);
      await controller.completeQuiz('weight_quiz_1', 'weight_module_1', 85, 100);
      await controller.completeQuiz('sports_quiz_1', 'sports_module_1', 95, 100);

      // Assert
      final userPoints = controller.userProgress?.totalPoints ?? 0;
      expect(userPoints, greaterThan(0));
      print('✅ Punti WellJourney accumulati: $userPoints');

      // Verifica che il NutriScore sia stato aggiornato
      final nutriScoreHistory = controller.nutriScoreHistory;
      expect(nutriScoreHistory.isNotEmpty, isTrue);
      print('✅ NutriScore history aggiornato: ${nutriScoreHistory.length} giorni');
    });

    test('Bonus points should be awarded for exceptional performance', () async {
      // Arrange
      const quizId = 'mediterranean_quiz_5';
      const moduleId = 'mediterranean_module_5';
      const score = 100; // Punteggio perfetto
      const maxScore = 100;
      const bonusPoints = 75; // Bonus elevato per quiz difficile

      final initialPoints = controller.userProgress?.totalPoints ?? 0;

      // Act
      await controller.completeQuiz(quizId, moduleId, score, maxScore, bonusPoints: bonusPoints);

      // Assert
      final finalPoints = controller.userProgress?.totalPoints ?? 0;
      final pointsAdded = finalPoints - initialPoints;

      expect(pointsAdded, greaterThanOrEqualTo(150 + bonusPoints)); // Base + bonus
      print('✅ Bonus per performance eccezionale: +$pointsAdded punti totali');
    });

    test('Failed quiz should still award minimum points', () async {
      // Arrange
      const quizId = 'heart_quiz_2';
      const moduleId = 'heart_module_2';
      const score = 40; // Punteggio insufficiente
      const maxScore = 100;

      final initialPoints = controller.userProgress?.totalPoints ?? 0;

      // Act
      await controller.completeQuiz(quizId, moduleId, score, maxScore);

      // Assert
      final finalPoints = controller.userProgress?.totalPoints ?? 0;
      final pointsAdded = finalPoints - initialPoints;

      expect(pointsAdded, equals(25)); // Punti minimi per il tentativo
      print('✅ Punti minimi assegnati per tentativo: +$pointsAdded');
    });

    tearDown(() async {
      // Cleanup
      await controller.resetAllProgress();
    });
  });
}
