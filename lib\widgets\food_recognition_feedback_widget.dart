import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/food_recognition_result.dart';
import '../models/food.dart';
import '../theme/dr_staffilano_theme.dart';
import '../data/food_database.dart';

/// Widget per il feedback sui risultati del riconoscimento alimentare
/// Permette all'utente di correggere e migliorare l'accuratezza
class FoodRecognitionFeedbackWidget extends StatefulWidget {
  final FoodRecognitionResult result;
  final Function(FoodRecognitionResult) onResultCorrected;
  final VoidCallback? onClose;

  const FoodRecognitionFeedbackWidget({
    Key? key,
    required this.result,
    required this.onResultCorrected,
    this.onClose,
  }) : super(key: key);

  @override
  State<FoodRecognitionFeedbackWidget> createState() => _FoodRecognitionFeedbackWidgetState();
}

class _FoodRecognitionFeedbackWidgetState extends State<FoodRecognitionFeedbackWidget> {
  late List<RecognizedFood> _correctedFoods;
  final FoodDatabase _foodDatabase = FoodDatabase();
  List<Food> _availableFoods = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _correctedFoods = List.from(widget.result.recognizedFoods);
    _loadAvailableFoods();
  }

  Future<void> _loadAvailableFoods() async {
    try {
      _availableFoods = await _foodDatabase.getAllFoods();
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      print('Errore nel caricamento degli alimenti: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: DrStaffilanoTheme.backgroundWhite,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Header
          _buildHeader(),
          
          // Content
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildContent(),
          ),
          
          // Actions
          _buildActions(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Row(
        children: [
          Icon(
            FontAwesomeIcons.userPen,
            color: DrStaffilanoTheme.primaryGreen,
            size: 24,
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              'Correggi Riconoscimento',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: DrStaffilanoTheme.textPrimary,
              ),
            ),
          ),
          IconButton(
            onPressed: widget.onClose,
            icon: const Icon(Icons.close),
            color: DrStaffilanoTheme.textSecondary,
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Istruzioni
          _buildInstructions(),
          
          const SizedBox(height: 20),
          
          // Alimenti riconosciuti
          _buildRecognizedFoodsList(),
          
          const SizedBox(height: 20),
          
          // Aggiungi nuovo alimento
          _buildAddFoodSection(),
        ],
      ),
    );
  }

  Widget _buildInstructions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.accentGold.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: DrStaffilanoTheme.accentGold.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.lightbulb,
                color: DrStaffilanoTheme.accentGold,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'Come correggere',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: DrStaffilanoTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            '• Tocca un alimento per modificarlo o rimuoverlo\n'
            '• Regola le porzioni usando i pulsanti +/-\n'
            '• Aggiungi alimenti mancanti dalla lista\n'
            '• Le tue correzioni aiutano a migliorare l\'AI',
            style: TextStyle(
              fontSize: 14,
              color: DrStaffilanoTheme.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecognizedFoodsList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Alimenti Riconosciuti',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: DrStaffilanoTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        if (_correctedFoods.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: DrStaffilanoTheme.backgroundLight,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Text(
              'Nessun alimento riconosciuto. Aggiungi manualmente gli alimenti presenti nell\'immagine.',
              style: TextStyle(
                fontSize: 14,
                color: DrStaffilanoTheme.textSecondary,
                fontStyle: FontStyle.italic,
              ),
            ),
          )
        else
          ...List.generate(_correctedFoods.length, (index) => 
            _buildEditableFoodItem(_correctedFoods[index], index)
          ),
      ],
    );
  }

  Widget _buildEditableFoodItem(RecognizedFood recognizedFood, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.backgroundWhite,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: DrStaffilanoTheme.borderLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  recognizedFood.food.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: DrStaffilanoTheme.textPrimary,
                  ),
                ),
              ),
              IconButton(
                onPressed: () => _removeFoodItem(index),
                icon: const Icon(FontAwesomeIcons.trash),
                color: Colors.red,
                iconSize: 16,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              const Text(
                'Porzione: ',
                style: TextStyle(
                  fontSize: 14,
                  color: DrStaffilanoTheme.textSecondary,
                ),
              ),
              IconButton(
                onPressed: () => _adjustPortion(index, -10),
                icon: const Icon(FontAwesomeIcons.minus),
                iconSize: 16,
                color: DrStaffilanoTheme.primaryGreen,
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${recognizedFood.estimatedGrams}g',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: DrStaffilanoTheme.primaryGreen,
                  ),
                ),
              ),
              IconButton(
                onPressed: () => _adjustPortion(index, 10),
                icon: const Icon(FontAwesomeIcons.plus),
                iconSize: 16,
                color: DrStaffilanoTheme.primaryGreen,
              ),
              const Spacer(),
              Text(
                '${recognizedFood.estimatedCalories} kcal',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: DrStaffilanoTheme.accentGold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAddFoodSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Aggiungi Alimento',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: DrStaffilanoTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        ElevatedButton.icon(
          onPressed: _showAddFoodDialog,
          icon: const Icon(FontAwesomeIcons.plus),
          label: const Text('Aggiungi alimento mancante'),
          style: ElevatedButton.styleFrom(
            backgroundColor: DrStaffilanoTheme.primaryGreen,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
      ],
    );
  }

  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.backgroundLight,
        border: Border(
          top: BorderSide(color: DrStaffilanoTheme.borderLight),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: widget.onClose,
              child: const Text('Annulla'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                side: const BorderSide(color: DrStaffilanoTheme.textSecondary),
                foregroundColor: DrStaffilanoTheme.textSecondary,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _saveCorrections,
              child: const Text('Salva Correzioni'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                backgroundColor: DrStaffilanoTheme.primaryGreen,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _removeFoodItem(int index) {
    setState(() {
      _correctedFoods.removeAt(index);
    });
  }

  void _adjustPortion(int index, int adjustment) {
    setState(() {
      final currentGrams = _correctedFoods[index].estimatedGrams;
      final newGrams = (currentGrams + adjustment).clamp(10, 500);
      
      _correctedFoods[index] = RecognizedFood(
        food: _correctedFoods[index].food,
        confidence: _correctedFoods[index].confidence,
        estimatedGrams: newGrams,
        boundingBox: _correctedFoods[index].boundingBox,
      );
    });
  }

  void _showAddFoodDialog() {
    showDialog(
      context: context,
      builder: (context) => _AddFoodDialog(
        availableFoods: _availableFoods,
        onFoodSelected: (food, grams) {
          setState(() {
            _correctedFoods.add(RecognizedFood(
              food: food,
              confidence: 1.0, // Confidenza massima per alimenti aggiunti manualmente
              estimatedGrams: grams,
              boundingBox: const BoundingBox(x: 0.1, y: 0.1, width: 0.8, height: 0.8),
            ));
          });
        },
      ),
    );
  }

  void _saveCorrections() {
    // Ricalcola il riepilogo nutrizionale
    final nutritionalSummary = _calculateNutritionalSummary(_correctedFoods);
    
    // Crea il risultato corretto
    final correctedResult = widget.result.copyWith(
      recognizedFoods: _correctedFoods,
      nutritionalSummary: nutritionalSummary,
      confidenceScore: _calculateOverallConfidence(_correctedFoods),
    );
    
    // Notifica il callback
    widget.onResultCorrected(correctedResult);
    
    // Chiudi il widget
    widget.onClose?.call();
  }

  NutritionalSummary _calculateNutritionalSummary(List<RecognizedFood> foods) {
    double totalCalories = 0;
    double totalProteins = 0;
    double totalCarbs = 0;
    double totalFats = 0;
    double totalFiber = 0;
    
    for (final recognizedFood in foods) {
      final factor = recognizedFood.estimatedGrams / 100.0;
      totalCalories += recognizedFood.food.calories * factor;
      totalProteins += recognizedFood.food.proteins * factor;
      totalCarbs += recognizedFood.food.carbs * factor;
      totalFats += recognizedFood.food.fats * factor;
      totalFiber += recognizedFood.food.fiber * factor;
    }
    
    return NutritionalSummary(
      calories: totalCalories.round(),
      proteins: totalProteins,
      carbs: totalCarbs,
      fats: totalFats,
      fiber: totalFiber,
    );
  }

  double _calculateOverallConfidence(List<RecognizedFood> foods) {
    if (foods.isEmpty) return 0.0;
    
    final totalConfidence = foods.fold(0.0, (sum, food) => sum + food.confidence);
    return totalConfidence / foods.length;
  }
}

/// Dialog per aggiungere un nuovo alimento
class _AddFoodDialog extends StatefulWidget {
  final List<Food> availableFoods;
  final Function(Food, int) onFoodSelected;

  const _AddFoodDialog({
    required this.availableFoods,
    required this.onFoodSelected,
  });

  @override
  State<_AddFoodDialog> createState() => _AddFoodDialogState();
}

class _AddFoodDialogState extends State<_AddFoodDialog> {
  String _searchQuery = '';
  Food? _selectedFood;
  int _selectedGrams = 100;

  List<Food> get _filteredFoods {
    if (_searchQuery.isEmpty) return widget.availableFoods.take(20).toList();
    
    return widget.availableFoods
        .where((food) => food.name.toLowerCase().contains(_searchQuery.toLowerCase()))
        .take(20)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Aggiungi Alimento'),
      content: SizedBox(
        width: double.maxFinite,
        height: 400,
        child: Column(
          children: [
            // Campo di ricerca
            TextField(
              decoration: const InputDecoration(
                hintText: 'Cerca alimento...',
                prefixIcon: Icon(FontAwesomeIcons.magnifyingGlass),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            
            const SizedBox(height: 16),
            
            // Lista alimenti
            Expanded(
              child: ListView.builder(
                itemCount: _filteredFoods.length,
                itemBuilder: (context, index) {
                  final food = _filteredFoods[index];
                  final isSelected = _selectedFood?.id == food.id;
                  
                  return ListTile(
                    title: Text(food.name),
                    subtitle: Text(food.description),
                    selected: isSelected,
                    onTap: () {
                      setState(() {
                        _selectedFood = food;
                      });
                    },
                  );
                },
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Selezione porzione
            if (_selectedFood != null) ...[
              const Text('Porzione (grammi):'),
              const SizedBox(height: 8),
              Row(
                children: [
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _selectedGrams = (_selectedGrams - 10).clamp(10, 500);
                      });
                    },
                    icon: const Icon(FontAwesomeIcons.minus),
                  ),
                  Expanded(
                    child: Text(
                      '$_selectedGrams g',
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _selectedGrams = (_selectedGrams + 10).clamp(10, 500);
                      });
                    },
                    icon: const Icon(FontAwesomeIcons.plus),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Annulla'),
        ),
        ElevatedButton(
          onPressed: _selectedFood != null
              ? () {
                  widget.onFoodSelected(_selectedFood!, _selectedGrams);
                  Navigator.of(context).pop();
                }
              : null,
          child: const Text('Aggiungi'),
        ),
      ],
    );
  }
}
