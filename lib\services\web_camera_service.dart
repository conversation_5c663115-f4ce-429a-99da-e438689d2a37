import 'dart:html' as html;
import 'dart:typed_data';
import 'dart:convert';
import 'dart:ui' as ui;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'media_service.dart';

/// Servizio per gestire la fotocamera su piattaforma web (solo scatto foto)
class WebCameraService {
  static final WebCameraService _instance = WebCameraService._internal();
  factory WebCameraService() => _instance;
  WebCameraService._internal();

  html.MediaStream? _stream;
  html.VideoElement? _videoElement;
  bool _isInitialized = false;

  /// Verifica se la fotocamera è supportata su web
  bool get isSupported => kIsWeb && html.window.navigator.mediaDevices != null;

  /// Inizializza la fotocamera web
  Future<bool> initialize() async {
    if (!isSupported) return false;

    try {
      // Richiedi accesso alla fotocamera
      final constraints = {
        'video': {
          'width': {'ideal': 1920},
          'height': {'ideal': 1080},
          'facingMode': 'environment', // Fotocamera posteriore se disponibile
        },
        'audio': false,
      };

      _stream = await html.window.navigator.mediaDevices!.getUserMedia(constraints);

      // Crea elemento video per preview
      _videoElement = html.VideoElement()
        ..srcObject = _stream
        ..autoplay = true
        ..muted = true
        ..style.width = '100%'
        ..style.height = '100%'
        ..style.objectFit = 'cover';

      _isInitialized = true;
      return true;
    } catch (e) {
      debugPrint('Errore nell\'inizializzazione fotocamera web: $e');
      return false;
    }
  }

  /// Mostra il modal della fotocamera e cattura una foto
  Future<MediaFile?> capturePhoto(BuildContext context) async {
    if (!_isInitialized) {
      final initialized = await initialize();
      if (!initialized) return null;
    }

    return await showDialog<MediaFile?>(
      context: context,
      barrierDismissible: false,
      builder: (context) => _WebCameraModal(
        videoElement: _videoElement!,
        onCapture: _capturePhotoFromVideo,
        onClose: dispose,
      ),
    );
  }

  /// Cattura una foto dal video stream
  Future<MediaFile?> _capturePhotoFromVideo() async {
    if (_videoElement == null) return null;

    try {
      // Crea un canvas per catturare il frame
      final canvas = html.CanvasElement(
        width: _videoElement!.videoWidth,
        height: _videoElement!.videoHeight,
      );

      final context2d = canvas.context2D;
      context2d.drawImageScaled(_videoElement!, 0, 0, canvas.width!, canvas.height!);

      // Converti in blob
      final blob = await canvas.toBlob('image/jpeg', 0.8);

      // Leggi i bytes
      final reader = html.FileReader();
      reader.readAsArrayBuffer(blob);
      await reader.onLoad.first;

      final bytes = Uint8List.fromList((reader.result as List<int>));

      // Crea MediaFile
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'camera_photo_$timestamp.jpg';

      // Genera thumbnail
      final thumbnailData = await _generateThumbnail(bytes);

      return MediaFile(
        id: timestamp.toString(),
        name: fileName,
        path: 'data:image/jpeg;base64,${base64Encode(bytes)}', // Data URL per web
        type: MediaType.image,
        size: bytes.length,
        createdAt: DateTime.now(),
        thumbnailData: thumbnailData,
      );
    } catch (e) {
      debugPrint('Errore nella cattura foto: $e');
      return null;
    }
  }

  /// Genera thumbnail per l'immagine
  Future<Uint8List?> _generateThumbnail(Uint8List imageBytes) async {
    try {
      // Crea un'immagine temporanea per generare il thumbnail
      final canvas = html.CanvasElement(width: 200, height: 200);
      final context2d = canvas.context2D;

      // Crea un elemento immagine
      final img = html.ImageElement();
      img.src = 'data:image/jpeg;base64,${base64Encode(imageBytes)}';

      // Aspetta che l'immagine si carichi
      await img.onLoad.first;

      // Disegna l'immagine ridimensionata
      context2d.drawImageScaled(img, 0, 0, 200, 200);

      // Converti in bytes
      final blob = await canvas.toBlob('image/jpeg', 0.8);
      final reader = html.FileReader();
      reader.readAsArrayBuffer(blob);
      await reader.onLoad.first;

      return Uint8List.fromList((reader.result as List<int>));
    } catch (e) {
      debugPrint('Errore nella generazione thumbnail: $e');
      return null;
    }
  }

  /// Rilascia le risorse
  void dispose() {
    if (_stream != null) {
      _stream!.getTracks().forEach((track) => track.stop());
      _stream = null;
    }
    _videoElement = null;
    _isInitialized = false;
  }
}

/// Modal per la fotocamera web
class _WebCameraModal extends StatefulWidget {
  final html.VideoElement videoElement;
  final Future<MediaFile?> Function() onCapture;
  final VoidCallback onClose;

  const _WebCameraModal({
    required this.videoElement,
    required this.onCapture,
    required this.onClose,
  });

  @override
  State<_WebCameraModal> createState() => _WebCameraModalState();
}

class _WebCameraModalState extends State<_WebCameraModal> {
  bool _isCapturing = false;

  @override
  Widget build(BuildContext context) {
    return Dialog.fullscreen(
      backgroundColor: Colors.black,
      child: Scaffold(
        backgroundColor: Colors.black,
        appBar: AppBar(
          backgroundColor: Colors.black,
          foregroundColor: Colors.white,
          title: const Text('Fotocamera'),
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              widget.onClose();
              Navigator.of(context).pop();
            },
          ),
        ),
        body: Stack(
          children: [
            // Video preview
            Center(
              child: AspectRatio(
                aspectRatio: 16 / 9,
                child: HtmlElementView(
                  viewType: 'camera-video-${widget.videoElement.hashCode}',
                ),
              ),
            ),

            // Controls
            Positioned(
              bottom: 50,
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Capture button
                  GestureDetector(
                    onTap: _isCapturing ? null : _capturePhoto,
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: _isCapturing ? Colors.grey : Colors.white,
                        border: Border.all(color: Colors.white, width: 4),
                      ),
                      child: _isCapturing
                          ? const Center(
                              child: CircularProgressIndicator(
                                color: Colors.black,
                                strokeWidth: 3,
                              ),
                            )
                          : const Icon(
                              Icons.camera_alt,
                              size: 40,
                              color: Colors.black,
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _capturePhoto() async {
    if (_isCapturing) return;

    setState(() {
      _isCapturing = true;
    });

    try {
      final mediaFile = await widget.onCapture();

      if (mediaFile != null && mounted) {
        widget.onClose();
        Navigator.of(context).pop(mediaFile);
      } else if (mounted) {
        setState(() {
          _isCapturing = false;
        });
      }
    } catch (e) {
      debugPrint('Errore nella cattura: $e');
      if (mounted) {
        setState(() {
          _isCapturing = false;
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();
    // Registra l'elemento video per HtmlElementView
    // ignore: undefined_prefixed_name
    ui.platformViewRegistry.registerViewFactory(
      'camera-video-${widget.videoElement.hashCode}',
      (int viewId) => widget.videoElement,
    );
  }
}
