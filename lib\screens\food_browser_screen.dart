import 'package:flutter/material.dart';
import '../data/food_database.dart';
import '../models/food.dart';
import '../widgets/food_grid_item.dart';

class FoodBrowserScreen extends StatefulWidget {
  const FoodBrowserScreen({Key? key}) : super(key: key);

  @override
  _FoodBrowserScreenState createState() => _FoodBrowserScreenState();
}

class _FoodBrowserScreenState extends State<FoodBrowserScreen> {
  final FoodDatabase _foodDatabase = FoodDatabase();
  List<Food> _foods = [];
  List<Food> _filteredFoods = [];
  bool _isLoading = true;
  bool _isGridView = true;
  String _searchQuery = '';
  FoodCategory? _selectedCategory;
  
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadFoods();
  }
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadFoods() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final foods = await _foodDatabase.getAllFoods();
      setState(() {
        _foods = foods;
        _filteredFoods = foods;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Errore nel caricamento degli alimenti: $e')),
      );
    }
  }
  
  void _filterFoods() {
    setState(() {
      _filteredFoods = _foods.where((food) {
        // Filtra per query di ricerca
        final matchesQuery = _searchQuery.isEmpty ||
            food.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            food.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            food.tags.any((tag) => tag.toLowerCase().contains(_searchQuery.toLowerCase()));
        
        // Filtra per categoria
        final matchesCategory = _selectedCategory == null ||
            food.categories.contains(_selectedCategory);
        
        return matchesQuery && matchesCategory;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Esplora alimenti'),
        actions: [
          // Pulsante per cambiare la visualizzazione (griglia/lista)
          IconButton(
            icon: Icon(_isGridView ? Icons.view_list : Icons.grid_view),
            onPressed: () {
              setState(() {
                _isGridView = !_isGridView;
              });
            },
          ),
          // Pulsante per filtrare gli alimenti
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // Barra di ricerca
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Cerca alimenti...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                          _filterFoods();
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
                _filterFoods();
              },
            ),
          ),
          
          // Chip per la categoria selezionata
          if (_selectedCategory != null)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Row(
                children: [
                  Chip(
                    label: Text(_selectedCategory.toString().split('.').last),
                    onDeleted: () {
                      setState(() {
                        _selectedCategory = null;
                      });
                      _filterFoods();
                    },
                  ),
                ],
              ),
            ),
          
          // Conteggio dei risultati
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Row(
              children: [
                Text(
                  'Trovati ${_filteredFoods.length} alimenti',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          
          // Lista o griglia degli alimenti
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredFoods.isEmpty
                    ? const Center(child: Text('Nessun alimento trovato'))
                    : _isGridView
                        ? _buildGridView()
                        : _buildListView(),
          ),
        ],
      ),
    );
  }
  
  Widget _buildGridView() {
    return GridView.builder(
      padding: const EdgeInsets.all(8.0),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 8.0,
        mainAxisSpacing: 8.0,
      ),
      itemCount: _filteredFoods.length,
      itemBuilder: (context, index) {
        return FoodGridItem(food: _filteredFoods[index]);
      },
    );
  }
  
  Widget _buildListView() {
    return ListView.builder(
      padding: const EdgeInsets.all(8.0),
      itemCount: _filteredFoods.length,
      itemBuilder: (context, index) {
        return FoodListItem(food: _filteredFoods[index]);
      },
    );
  }
  
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Filtra per categoria'),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView(
              shrinkWrap: true,
              children: [
                ListTile(
                  title: const Text('Tutte le categorie'),
                  selected: _selectedCategory == null,
                  onTap: () {
                    setState(() {
                      _selectedCategory = null;
                    });
                    _filterFoods();
                    Navigator.pop(context);
                  },
                ),
                ...FoodCategory.values.map((category) {
                  return ListTile(
                    title: Text(category.toString().split('.').last),
                    selected: _selectedCategory == category,
                    onTap: () {
                      setState(() {
                        _selectedCategory = category;
                      });
                      _filterFoods();
                      Navigator.pop(context);
                    },
                  );
                }).toList(),
              ],
            ),
          ),
        );
      },
    );
  }
}
