import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../models/diet_plan.dart';
import '../models/food.dart';
import '../theme/app_theme.dart';
import 'food_alternatives_widget.dart';

/// Widget per visualizzare un pasto pianificato con alternative
class PlannedMealCard extends StatefulWidget {
  final PlannedMeal meal;
  final Function(bool) onCompletedChanged;
  final Function(String, Food, Food)? onFoodReplaced; // mealId, oldFood, newFood

  const PlannedMealCard({
    Key? key,
    required this.meal,
    required this.onCompletedChanged,
    this.onFoodReplaced,
  }) : super(key: key);

  @override
  State<PlannedMealCard> createState() => _PlannedMealCardState();
}

class _PlannedMealCardState extends State<PlannedMealCard> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final totalCalories = widget.meal.foods.fold(0, (sum, portion) => sum + portion.calories);
    final totalProteins = widget.meal.foods.fold(0.0, (sum, portion) => sum + portion.proteins);
    final totalCarbs = widget.meal.foods.fold(0.0, (sum, portion) => sum + portion.carbs);
    final totalFats = widget.meal.foods.fold(0.0, (sum, portion) => sum + portion.fats);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: widget.meal.isCompleted ? 2 : 6,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: widget.meal.isCompleted
            ? BorderSide(color: AppTheme.primaryColor, width: 2)
            : BorderSide.none,
      ),
      child: Column(
        children: [
          // Header del pasto
          InkWell(
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppTheme.primaryColor,
                    AppTheme.primaryColor.withOpacity(0.8),
                  ],
                ),
                borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
              ),
              child: Row(
                children: [
                  // Icona del pasto
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      _getMealIcon(widget.meal.type),
                      color: Colors.white,
                      size: 24,
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Nome e orario
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.meal.name,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (widget.meal.time != null)
                          Text(
                            widget.meal.time!,
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.9),
                              fontSize: 14,
                            ),
                          ),
                      ],
                    ),
                  ),

                  // Calorie totali
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppTheme.accentColor,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${totalCalories} kcal',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),

                  const SizedBox(width: 8),

                  // Checkbox completamento
                  Transform.scale(
                    scale: 1.2,
                    child: Checkbox(
                      value: widget.meal.isCompleted,
                      onChanged: (value) => widget.onCompletedChanged(value ?? false),
                      activeColor: Colors.white,
                      checkColor: AppTheme.primaryColor,
                      side: const BorderSide(color: Colors.white, width: 2),
                    ),
                  ),

                  // Icona espansione
                  Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: Colors.white,
                  ),
                ],
              ),
            ),
          ),

          // Macronutrienti summary
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            color: AppTheme.primaryColor.withOpacity(0.1),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildMacroChip('P', totalProteins, AppTheme.primaryColor),
                _buildMacroChip('C', totalCarbs, AppTheme.secondaryColor),
                _buildMacroChip('G', totalFats, AppTheme.accentColor),
              ],
            ),
          ),

          // Lista alimenti espandibile
          if (_isExpanded) ...[
            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    child: Row(
                      children: [
                        Icon(
                          Icons.restaurant_menu,
                          color: AppTheme.primaryColor,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Alimenti del pasto:',
                          style: TextStyle(
                            color: AppTheme.primaryColor,
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Lista alimenti con alternative
                  ...widget.meal.foods.map((portion) => FoodAlternativesWidget(
                    originalPortion: portion,
                    onAlternativeSelected: (alternative) {
                      if (widget.onFoodReplaced != null) {
                        widget.onFoodReplaced!(
                          widget.meal.id,
                          portion.food,
                          alternative,
                        );
                      }
                    },
                  )),
                ],
              ),
            ),
          ],
        ],
      ),
    ).animate().fadeIn(duration: 300.ms).slideY(begin: 0.1, end: 0);
  }

  Widget _buildMacroChip(String label, double value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            '${value.round()}g',
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getMealIcon(MealType type) {
    switch (type) {
      case MealType.breakfast:
        return Icons.wb_sunny;
      case MealType.lunch:
        return Icons.restaurant;
      case MealType.dinner:
        return Icons.dinner_dining;
      case MealType.snack:
        return Icons.local_cafe;
      default:
        return Icons.fastfood;
    }
  }
}

/// Widget semplificato per la vista compatta
class CompactPlannedMealCard extends StatelessWidget {
  final PlannedMeal meal;
  final VoidCallback onTap;
  final Function(bool) onCompletedChanged;

  const CompactPlannedMealCard({
    Key? key,
    required this.meal,
    required this.onTap,
    required this.onCompletedChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final totalCalories = meal.foods.fold(0, (sum, portion) => sum + portion.calories);
    final alternativesCount = meal.foods.length * 2; // Simulato per ora

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppTheme.primaryColor,
          child: Icon(
            _getMealIcon(meal.type),
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Text(
          meal.name,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            decoration: meal.isCompleted ? TextDecoration.lineThrough : null,
          ),
        ),
        subtitle: Row(
          children: [
            Text('${totalCalories} kcal'),
            const SizedBox(width: 8),
            FoodAlternativesIndicator(
              alternativesCount: alternativesCount,
              onTap: onTap,
            ),
          ],
        ),
        trailing: Checkbox(
          value: meal.isCompleted,
          onChanged: (value) => onCompletedChanged(value ?? false),
          activeColor: AppTheme.primaryColor,
        ),
        onTap: onTap,
      ),
    );
  }

  IconData _getMealIcon(MealType type) {
    switch (type) {
      case MealType.breakfast:
        return Icons.wb_sunny;
      case MealType.lunch:
        return Icons.restaurant;
      case MealType.dinner:
        return Icons.dinner_dining;
      case MealType.snack:
        return Icons.local_cafe;
      default:
        return Icons.fastfood;
    }
  }
}
