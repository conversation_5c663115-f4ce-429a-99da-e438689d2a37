import 'package:json_annotation/json_annotation.dart';

part 'community_notification.g.dart';

/// Tipi di notifica supportati nella community
enum NotificationType {
  @JsonValue('LIKE_POST')
  likePost,
  
  @JsonValue('COMMENT_POST')
  commentPost,
  
  @JsonValue('SHARE_POST')
  sharePost,
  
  @JsonValue('TAG_USER')
  tagUser,
  
  @JsonValue('FRIEND_REQUEST')
  friendRequest,
}

/// Estensioni per NotificationType
extension NotificationTypeExtension on NotificationType {
  /// Icona per il tipo di notifica
  String get icon {
    switch (this) {
      case NotificationType.likePost:
        return '❤️';
      case NotificationType.commentPost:
        return '💬';
      case NotificationType.sharePost:
        return '🔄';
      case NotificationType.tagUser:
        return '🏷️';
      case NotificationType.friendRequest:
        return '👥';
    }
  }

  /// Colore per il tipo di notifica (usando i colori Dr. Staffilano)
  String get colorHex {
    switch (this) {
      case NotificationType.likePost:
        return '#F5533D'; // Rosso per i like
      case NotificationType.commentPost:
        return '#1877F2'; // Blu professionale per i commenti
      case NotificationType.sharePost:
        return '#45BD62'; // Verde per le condivisioni
      case NotificationType.tagUser:
        return '#F7B928'; // Oro per i tag
      case NotificationType.friendRequest:
        return '#1877F2'; // Blu per le richieste di amicizia
    }
  }

  /// Descrizione del tipo di notifica
  String get description {
    switch (this) {
      case NotificationType.likePost:
        return 'Mi piace al post';
      case NotificationType.commentPost:
        return 'Commento al post';
      case NotificationType.sharePost:
        return 'Condivisione del post';
      case NotificationType.tagUser:
        return 'Tag in un post';
      case NotificationType.friendRequest:
        return 'Richiesta di amicizia';
    }
  }
}

/// Modello per le notifiche della community
@JsonSerializable()
class CommunityNotification {
  /// ID univoco della notifica
  final String id;

  /// ID dell'utente che riceve la notifica
  final String userId;

  /// Tipo di notifica
  final NotificationType type;

  /// ID del post correlato (opzionale)
  final String? postId;

  /// ID dell'utente che ha generato la notifica
  final String actorUserId;

  /// Nome dell'utente che ha generato la notifica
  final String actorUserName;

  /// Avatar dell'utente che ha generato la notifica (opzionale)
  final String? actorUserAvatar;

  /// Messaggio della notifica
  final String message;

  /// Timestamp di creazione
  final DateTime timestamp;

  /// Se la notifica è stata letta
  final bool isRead;

  /// Dati aggiuntivi (opzionali)
  final Map<String, dynamic>? metadata;

  const CommunityNotification({
    required this.id,
    required this.userId,
    required this.type,
    this.postId,
    required this.actorUserId,
    required this.actorUserName,
    this.actorUserAvatar,
    required this.message,
    required this.timestamp,
    this.isRead = false,
    this.metadata,
  });

  /// Factory constructor per creare una notifica
  factory CommunityNotification.create({
    required String userId,
    required NotificationType type,
    String? postId,
    required String actorUserId,
    required String actorUserName,
    String? actorUserAvatar,
    required String message,
    Map<String, dynamic>? metadata,
  }) {
    return CommunityNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      type: type,
      postId: postId,
      actorUserId: actorUserId,
      actorUserName: actorUserName,
      actorUserAvatar: actorUserAvatar,
      message: message,
      timestamp: DateTime.now(),
      isRead: false,
      metadata: metadata,
    );
  }

  /// Factory constructor da JSON
  factory CommunityNotification.fromJson(Map<String, dynamic> json) =>
      _$CommunityNotificationFromJson(json);

  /// Converte in JSON
  Map<String, dynamic> toJson() => _$CommunityNotificationToJson(this);

  /// Crea una copia con modifiche
  CommunityNotification copyWith({
    String? id,
    String? userId,
    NotificationType? type,
    String? postId,
    String? actorUserId,
    String? actorUserName,
    String? actorUserAvatar,
    String? message,
    DateTime? timestamp,
    bool? isRead,
    Map<String, dynamic>? metadata,
  }) {
    return CommunityNotification(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      postId: postId ?? this.postId,
      actorUserId: actorUserId ?? this.actorUserId,
      actorUserName: actorUserName ?? this.actorUserName,
      actorUserAvatar: actorUserAvatar ?? this.actorUserAvatar,
      message: message ?? this.message,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Marca la notifica come letta
  CommunityNotification markAsRead() {
    return copyWith(isRead: true);
  }

  /// Ottieni il tempo relativo (es. "2 ore fa")
  String get relativeTime {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Ora';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} min fa';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} ore fa';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} giorni fa';
    } else {
      return '${(difference.inDays / 7).floor()} settimane fa';
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CommunityNotification &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CommunityNotification{id: $id, type: $type, actorUserName: $actorUserName, message: $message, isRead: $isRead}';
  }
}
