import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../theme/dr_staffilano_theme.dart';

/// Widget per la barra di navigazione inferiore moderna
class ModernBottomNavigation extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const ModernBottomNavigation({
    Key? key,
    required this.currentIndex,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.backgroundWhite,
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.shadowLight,
            blurRadius: 12,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: SafeArea(
        child: Container(
          height: 80,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNavItem(0, Icons.home_rounded, 'Home'),
              _buildNavItem(1, Icons.restaurant_menu_rounded, 'Dieta'),
              _buildFoodOracleButton(), // Food Oracle al centro (indice 2)
              _buildNavItem(3, FontAwesomeIcons.users, 'Community'),
              _buildNavItem(4, Icons.auto_awesome_rounded, 'AI'),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(int index, IconData icon, String label) {
    final isSelected = currentIndex == index;

    return Expanded(
      child: InkWell(
        onTap: () => onTap(index),
        borderRadius: BorderRadius.circular(12),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(
            horizontal: 4,
            vertical: 6,
          ),
          decoration: BoxDecoration(
            color: isSelected ? DrStaffilanoTheme.primaryGreen.withOpacity(0.1) : Colors.transparent,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: isSelected ? DrStaffilanoTheme.primaryGreen : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: isSelected ? DrStaffilanoTheme.textOnPrimary : DrStaffilanoTheme.textSecondary,
                  size: 20,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                label,
                style: TextStyle(
                  color: isSelected ? DrStaffilanoTheme.primaryGreen : DrStaffilanoTheme.textSecondary,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                  fontSize: 10,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Costruisce il pulsante speciale per Food Oracle al centro
  Widget _buildFoodOracleButton() {
    final isSelected = currentIndex == 2; // Food Oracle è l'indice 2 (al centro)

    return Expanded(
      child: InkWell(
        onTap: () => onTap(2),
        borderRadius: BorderRadius.circular(20),
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Pulsante circolare più grande per Food Oracle
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                width: isSelected ? 56 : 48,
                height: isSelected ? 56 : 48,
                decoration: BoxDecoration(
                  gradient: isSelected
                    ? DrStaffilanoTheme.goldGradient
                    : LinearGradient(
                        colors: [
                          DrStaffilanoTheme.primaryGreen,
                          DrStaffilanoTheme.primaryGreenLight,
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                  borderRadius: BorderRadius.circular(28),
                  boxShadow: [
                    BoxShadow(
                      color: isSelected
                        ? DrStaffilanoTheme.accentGold.withOpacity(0.4)
                        : DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
                      blurRadius: isSelected ? 12 : 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Icon(
                  FontAwesomeIcons.camera,
                  color: Colors.white,
                  size: isSelected ? 24 : 20,
                ),
              ),
              const SizedBox(height: 4),
              // Label
              AnimatedDefaultTextStyle(
                duration: const Duration(milliseconds: 200),
                style: TextStyle(
                  color: isSelected ? DrStaffilanoTheme.accentGold : DrStaffilanoTheme.textSecondary,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                  fontSize: 10,
                ),
                child: const Text(
                  'Oracle',
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
