import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../services/community_profile_service.dart';
import '../../theme/dr_staffilano_theme.dart';

/// Widget per visualizzare le statistiche dettagliate dell'utente
class UserStatsWidget extends StatelessWidget {
  final String userId;

  const UserStatsWidget({
    super.key,
    required this.userId,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<CommunityProfileService>(
      builder: (context, profileService, child) {
        final stats = profileService.getUserStats(userId);
        
        return SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Statistiche principali
              _buildMainStats(stats),
              
              const SizedBox(height: 24),
              
              // Grafico attività
              _buildActivityChart(stats),
              
              const SizedBox(height: 24),
              
              // Statistiche dettagliate
              _buildDetailedStats(stats),
              
              const SizedBox(height: 24),
              
              // Progressi e obiettivi
              _buildProgressSection(stats),
            ],
          ),
        );
      },
    );
  }

  /// Costruisce le statistiche principali
  Widget _buildMainStats(Map<String, dynamic> stats) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Statistiche Principali',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Post Totali',
                stats['totalPosts']?.toString() ?? '0',
                Icons.article,
                DrStaffilanoTheme.primaryGreen,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'Like Ricevuti',
                stats['totalLikes']?.toString() ?? '0',
                Icons.favorite,
                Colors.red,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Commenti',
                stats['totalComments']?.toString() ?? '0',
                Icons.chat_bubble,
                DrStaffilanoTheme.secondaryBlue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'Streak Giorni',
                '${stats['streak'] ?? 0}',
                Icons.local_fire_department,
                Colors.orange,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Costruisce una card delle statistiche
  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Costruisce il grafico dell'attività
  Widget _buildActivityChart(Map<String, dynamic> stats) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Attività Settimanale',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          height: 200,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: _buildBarChart(),
        ),
      ],
    );
  }

  /// Costruisce il grafico a barre
  Widget _buildBarChart() {
    // Dati simulati per l'attività settimanale
    final List<double> weeklyData = [3, 5, 2, 8, 4, 6, 7];
    final List<String> weekDays = ['Lun', 'Mar', 'Mer', 'Gio', 'Ven', 'Sab', 'Dom'];

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: 10,
        barTouchData: BarTouchData(enabled: false),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < weekDays.length) {
                  return Text(
                    weekDays[index],
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  );
                }
                return const Text('');
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                );
              },
            ),
          ),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false),
        barGroups: weeklyData.asMap().entries.map((entry) {
          return BarChartGroupData(
            x: entry.key,
            barRods: [
              BarChartRodData(
                toY: entry.value,
                color: DrStaffilanoTheme.primaryGreen,
                width: 20,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  /// Costruisce le statistiche dettagliate
  Widget _buildDetailedStats(Map<String, dynamic> stats) {
    final totalPosts = stats['totalPosts'] ?? 0;
    final totalLikes = stats['totalLikes'] ?? 0;
    final engagementRate = totalPosts > 0 ? (totalLikes / totalPosts).toStringAsFixed(1) : '0.0';
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Statistiche Dettagliate',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        _buildDetailCard('Tasso di Engagement', '$engagementRate like/post', Icons.trending_up),
        const SizedBox(height: 12),
        _buildDetailCard('Media Like per Post', engagementRate, Icons.thumb_up),
        const SizedBox(height: 12),
        _buildDetailCard('Ultimo Post', _formatLastPost(stats['lastPostDate']), Icons.schedule),
      ],
    );
  }

  /// Costruisce una card di dettaglio
  Widget _buildDetailCard(String title, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: DrStaffilanoTheme.primaryGreen,
            size: 24,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Costruisce la sezione dei progressi
  Widget _buildProgressSection(Map<String, dynamic> stats) {
    final streak = stats['streak'] ?? 0;
    final totalPosts = stats['totalPosts'] ?? 0;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Progressi e Obiettivi',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        
        // Progresso streak
        _buildProgressCard(
          'Streak Giornaliero',
          'Mantieni l\'attività quotidiana',
          streak / 30, // Obiettivo 30 giorni
          '$streak/30 giorni',
          Icons.local_fire_department,
          Colors.orange,
        ),
        
        const SizedBox(height: 12),
        
        // Progresso post
        _buildProgressCard(
          'Post Pubblicati',
          'Condividi i tuoi contenuti',
          totalPosts / 50, // Obiettivo 50 post
          '$totalPosts/50 post',
          Icons.article,
          DrStaffilanoTheme.primaryGreen,
        ),
      ],
    );
  }

  /// Costruisce una card di progresso
  Widget _buildProgressCard(
    String title,
    String description,
    double progress,
    String progressText,
    IconData icon,
    Color color,
  ) {
    final clampedProgress = progress.clamp(0.0, 1.0);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                progressText,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          LinearProgressIndicator(
            value: clampedProgress,
            backgroundColor: Colors.grey.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: 6,
          ),
        ],
      ),
    );
  }

  /// Formatta la data dell'ultimo post
  String _formatLastPost(String? lastPostDate) {
    if (lastPostDate == null) return 'Nessun post';
    
    try {
      final date = DateTime.parse(lastPostDate);
      final now = DateTime.now();
      final difference = now.difference(date);
      
      if (difference.inDays == 0) {
        return 'Oggi';
      } else if (difference.inDays == 1) {
        return 'Ieri';
      } else if (difference.inDays < 7) {
        return '${difference.inDays} giorni fa';
      } else {
        return '${(difference.inDays / 7).floor()} settimane fa';
      }
    } catch (e) {
      return 'Data non valida';
    }
  }
}
