import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // Colori principali - Palette premium Dr. Staffilano
  static const Color primaryColor = Color(0xFF1E5631);      // Verde profondo - Autorevolezza e benessere
  static const Color secondaryColor = Color(0xFF1A365D);    // Blu navy - Professionalità e fiducia
  static const Color accentColor = Color(0xFFD4AF37);       // Oro - Premium e celebrazione
  static const Color tertiaryColor = Color(0xFFC27C54);     // Terracotta - Calore e naturalezza

  // Colori per macronutrienti
  static const Color proteinColor = Color(0xFFE63946);      // Rosso - Proteine
  static const Color carbColor = Color(0xFF457B9D);         // Blu - Carboidrati
  static const Color fatColor = Color(0xFFD4AF37);          // Oro - Grassi

  // Colori di stato
  static const Color successColor = Color(0xFF4CAF50);      // Verde per successo
  static const Color warningColor = Color(0xFFFFC107);      // Giallo per avvisi
  static const Color errorColor = Color(0xFFF44336);        // Rosso per errori

  // Colori di sfondo
  static const Color backgroundColor = Color(0xFFF8F9FA);   // Bianco caldo per sfondo
  static const Color cardColor = Colors.white;              // Bianco per le card
  static const Color surfaceColor = Colors.white;           // Bianco per superfici
  static const Color dividerColor = Color(0xFFE0E0E0);      // Grigio chiaro per divisori

  // Colori di testo
  static const Color textPrimaryColor = Color(0xFF1A1A1A);  // Quasi nero
  static const Color textSecondaryColor = Color(0xFF4A4A4A); // Grigio scuro
  static const Color textLightColor = Color(0xFF8A8A8A);    // Grigio medio
  static const Color textOnPrimaryColor = Colors.white;     // Bianco su sfondo primario

  // Tema chiaro
  static ThemeData lightTheme() {
    final baseTheme = ThemeData.light();

    return baseTheme.copyWith(
      colorScheme: ColorScheme.light(
        primary: primaryColor,
        secondary: secondaryColor,
        surface: surfaceColor,
        background: backgroundColor,
        error: errorColor,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: textPrimaryColor,
        onBackground: textPrimaryColor,
        onError: Colors.white,
        brightness: Brightness.light,
      ),

      // Tipografia - Utilizzo dei font premium
      textTheme: TextTheme(
        // Titoli principali con font serif elegante
        displayLarge: const TextStyle(
          fontFamily: 'serif',
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: textPrimaryColor,
          letterSpacing: -0.5,
        ),
        displayMedium: const TextStyle(
          fontFamily: 'serif',
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: textPrimaryColor,
          letterSpacing: -0.5,
        ),
        displaySmall: const TextStyle(
          fontFamily: 'serif',
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: textPrimaryColor,
          letterSpacing: -0.25,
        ),
        // Titoli secondari con font sans-serif moderno
        headlineLarge: const TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          color: textPrimaryColor,
        ),
        headlineMedium: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: textPrimaryColor,
        ),
        titleLarge: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: textPrimaryColor,
        ),
        // Corpo del testo per leggibilità
        bodyLarge: const TextStyle(
          fontSize: 16,
          color: textPrimaryColor,
          height: 1.5,
        ),
        bodyMedium: const TextStyle(
          fontSize: 14,
          color: textPrimaryColor,
          height: 1.5,
        ),
        // Etichette e pulsanti
        labelLarge: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: textOnPrimaryColor,
          letterSpacing: 0.5,
        ),
      ),

      // AppBar - Stile premium
      appBarTheme: const AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: textOnPrimaryColor,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontFamily: 'serif',
          fontSize: 22,
          fontWeight: FontWeight.bold,
          color: textOnPrimaryColor,
          letterSpacing: 0.5,
        ),
        iconTheme: IconThemeData(
          color: textOnPrimaryColor,
          size: 24,
        ),
      ),

      // Card - Design elegante con bordi più morbidi
      cardTheme: const CardTheme(
        color: cardColor,
        elevation: 1,
        shadowColor: Color(0x1A000000), // Ombra più sottile
        margin: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
      ),

      // Floating Action Button - Stile premium
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: accentColor,
        foregroundColor: textPrimaryColor,
        elevation: 3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(16)),
        ),
      ),

      // Elevated Button - Design elegante
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: textOnPrimaryColor,
          elevation: 1,
          shadowColor: Color(0x40000000),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          textStyle: const TextStyle(
            fontFamily: 'Montserrat',
            fontSize: 16,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
          ),
        ),
      ),

      // Text Button - Stile raffinato
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryColor,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          textStyle: const TextStyle(
            fontFamily: 'Montserrat',
            fontSize: 16,
            fontWeight: FontWeight.w500,
            letterSpacing: 0.25,
          ),
        ),
      ),

      // Outlined Button - Per opzioni secondarie
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryColor,
          side: const BorderSide(color: primaryColor, width: 1.5),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          textStyle: const TextStyle(
            fontFamily: 'Montserrat',
            fontSize: 16,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.25,
          ),
        ),
      ),

      // Input Decoration - Design raffinato
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: Colors.white,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: dividerColor, width: 1),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: dividerColor, width: 1),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: primaryColor, width: 1.5),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(color: errorColor, width: 1.5),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 18, vertical: 16),
        labelStyle: const TextStyle(
          fontFamily: 'Montserrat',
          fontSize: 16,
          color: textSecondaryColor,
        ),
        hintStyle: const TextStyle(
          fontFamily: 'Montserrat',
          fontSize: 16,
          color: textLightColor,
        ),
        floatingLabelStyle: const TextStyle(
          fontFamily: 'Montserrat',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: primaryColor,
        ),
      ),

      // Checkbox - Stile premium
      checkboxTheme: CheckboxThemeData(
        fillColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return primaryColor;
          }
          return Colors.transparent;
        }),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
        side: const BorderSide(color: textLightColor, width: 1.5),
      ),

      // Switch - Stile premium
      switchTheme: SwitchThemeData(
        thumbColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return primaryColor;
          }
          return Colors.white;
        }),
        trackColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return primaryColor.withOpacity(0.3);
          }
          return textLightColor.withOpacity(0.3);
        }),
      ),

      // Divider
      dividerTheme: const DividerThemeData(
        color: dividerColor,
        thickness: 1,
        space: 24,
      ),

      // Scaffold background color
      scaffoldBackgroundColor: backgroundColor,
    );
  }
}
