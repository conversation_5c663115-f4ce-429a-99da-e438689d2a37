import 'dart:convert';
import 'package:flutter/material.dart' hide Badge;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/badge_models.dart';
import '../theme/dr_staffilano_theme.dart';

/// Servizio per la gestione dei badge
class BadgeService extends ChangeNotifier {
  static BadgeService? _instance;
  static BadgeService get instance => _instance ??= BadgeService._();

  BadgeService._();

  // Chiavi per SharedPreferences
  static const String _badgeCollectionKey = 'user_badge_collection';
  static const String _badgeProgressKey = 'badge_progress';

  // Stato interno
  UserBadgeCollection? _userCollection;
  List<AppBadge> _allBadges = [];
  bool _isInitialized = false;

  // Getters
  UserBadgeCollection? get userCollection => _userCollection;
  List<AppBadge> get allBadges => _allBadges;
  List<AppBadge> get unlockedBadges => _allBadges.where((badge) =>
      _userCollection?.unlockedBadges.contains(badge.id) ?? false).toList();
  List<AppBadge> get lockedBadges => _allBadges.where((badge) =>
      !(_userCollection?.unlockedBadges.contains(badge.id) ?? false)).toList();
  bool get isInitialized => _isInitialized;

  /// Inizializza il servizio badge
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      print('🏆 Inizializzazione BadgeService...');

      // Carica i badge predefiniti
      _allBadges = _createAllBadges();

      // Carica la collezione dell'utente
      await _loadUserCollection();

      // Inizializza il progresso per i nuovi badge
      await _initializeBadgeProgress();

      _isInitialized = true;
      print('✅ BadgeService inizializzato con ${_allBadges.length} badge');

      notifyListeners();
    } catch (e) {
      print('❌ Errore nell\'inizializzazione BadgeService: $e');
      _isInitialized = false;
    }
  }

  /// Carica la collezione badge dell'utente
  Future<void> _loadUserCollection() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final collectionJson = prefs.getString(_badgeCollectionKey);

      if (collectionJson != null) {
        final data = jsonDecode(collectionJson);
        _userCollection = UserBadgeCollection.fromJson(data);
      } else {
        // Crea una nuova collezione
        _userCollection = UserBadgeCollection(
          userId: 'user_001', // TODO: Ottieni ID utente reale
          badgeProgress: [],
          unlockedBadges: [],
          lastUpdated: DateTime.now(),
          totalBadgesUnlocked: 0,
          totalPoints: 0,
        );
        await _saveUserCollection();
      }
    } catch (e) {
      print('❌ Errore nel caricamento collezione badge: $e');
      // Crea collezione di default in caso di errore
      _userCollection = UserBadgeCollection(
        userId: 'user_001',
        badgeProgress: [],
        unlockedBadges: [],
        lastUpdated: DateTime.now(),
        totalBadgesUnlocked: 0,
        totalPoints: 0,
      );
    }
  }

  /// Salva la collezione badge dell'utente
  Future<void> _saveUserCollection() async {
    if (_userCollection == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      final collectionJson = jsonEncode(_userCollection!.toJson());
      await prefs.setString(_badgeCollectionKey, collectionJson);
    } catch (e) {
      print('❌ Errore nel salvataggio collezione badge: $e');
    }
  }

  /// Inizializza il progresso per i badge che non ce l'hanno
  Future<void> _initializeBadgeProgress() async {
    if (_userCollection == null) return;

    final existingProgressIds = _userCollection!.badgeProgress
        .map((bp) => bp.badgeId)
        .toSet();

    final newProgress = <BadgeProgress>[];

    for (final badge in _allBadges) {
      if (!existingProgressIds.contains(badge.id)) {
        newProgress.add(BadgeProgress(
          badgeId: badge.id,
          currentValue: 0,
          requiredValue: badge.requiredValue,
          lastUpdated: DateTime.now(),
        ));
      }
    }

    if (newProgress.isNotEmpty) {
      final updatedProgress = [
        ..._userCollection!.badgeProgress,
        ...newProgress,
      ];

      _userCollection = UserBadgeCollection(
        userId: _userCollection!.userId,
        badgeProgress: updatedProgress,
        unlockedBadges: _userCollection!.unlockedBadges,
        lastUpdated: DateTime.now(),
        totalBadgesUnlocked: _userCollection!.totalBadgesUnlocked,
        totalPoints: _userCollection!.totalPoints,
      );

      await _saveUserCollection();
    }
  }

  /// Aggiorna il progresso di un badge
  Future<bool> updateBadgeProgress(String badgeId, int newValue) async {
    if (_userCollection == null || !_isInitialized) return false;

    try {
      final progressIndex = _userCollection!.badgeProgress
          .indexWhere((bp) => bp.badgeId == badgeId);

      if (progressIndex == -1) return false;

      final currentProgress = _userCollection!.badgeProgress[progressIndex];

      // Non aggiornare se il badge è già sbloccato
      if (currentProgress.isUnlocked) return false;

      // Aggiorna solo se il nuovo valore è maggiore
      if (newValue <= currentProgress.currentValue) return false;

      final updatedProgress = currentProgress.copyWith(
        currentValue: newValue,
        lastUpdated: DateTime.now(),
      );

      // Sostituisci il progresso aggiornato
      final newProgressList = List<BadgeProgress>.from(_userCollection!.badgeProgress);
      newProgressList[progressIndex] = updatedProgress;

      _userCollection = UserBadgeCollection(
        userId: _userCollection!.userId,
        badgeProgress: newProgressList,
        unlockedBadges: _userCollection!.unlockedBadges,
        lastUpdated: DateTime.now(),
        totalBadgesUnlocked: _userCollection!.totalBadgesUnlocked,
        totalPoints: _userCollection!.totalPoints,
      );

      // Controlla se il badge è stato completato
      if (updatedProgress.isCompleted) {
        await _unlockAppBadge(badgeId);
        return true; // Badge sbloccato
      }

      await _saveUserCollection();
      notifyListeners();
      return false; // Progresso aggiornato ma badge non ancora sbloccato
    } catch (e) {
      print('❌ Errore nell\'aggiornamento progresso badge $badgeId: $e');
      return false;
    }
  }

  /// Sblocca un badge
  Future<void> _unlockAppBadge(String badgeId) async {
    if (_userCollection == null) return;

    try {
      // Controlla se il badge è già sbloccato
      if (_userCollection!.unlockedBadges.contains(badgeId)) return;

      // Aggiorna il progresso come sbloccato
      final progressIndex = _userCollection!.badgeProgress
          .indexWhere((bp) => bp.badgeId == badgeId);

      if (progressIndex != -1) {
        final updatedProgress = _userCollection!.badgeProgress[progressIndex].copyWith(
          isUnlocked: true,
          unlockedAt: DateTime.now(),
        );

        final newProgressList = List<BadgeProgress>.from(_userCollection!.badgeProgress);
        newProgressList[progressIndex] = updatedProgress;

        // Aggiorna la collezione
        _userCollection = UserBadgeCollection(
          userId: _userCollection!.userId,
          badgeProgress: newProgressList,
          unlockedBadges: [..._userCollection!.unlockedBadges, badgeId],
          lastUpdated: DateTime.now(),
          totalBadgesUnlocked: _userCollection!.totalBadgesUnlocked + 1,
          totalPoints: _userCollection!.totalPoints,
        );

        await _saveUserCollection();
        notifyListeners();

        print('🏆 Badge sbloccato: $badgeId');
      }
    } catch (e) {
      print('❌ Errore nello sblocco badge $badgeId: $e');
    }
  }

  /// Ottiene il progresso di un badge specifico
  BadgeProgress? getBadgeProgress(String badgeId) {
    return _userCollection?.badgeProgress
        .firstWhere((bp) => bp.badgeId == badgeId, orElse: () =>
            BadgeProgress(
              badgeId: badgeId,
              currentValue: 0,
              requiredValue: 1,
              lastUpdated: DateTime.now(),
            ));
  }

  /// Controlla se un badge è sbloccato
  bool isBadgeUnlocked(String badgeId) {
    return _userCollection?.unlockedBadges.contains(badgeId) ?? false;
  }

  /// Ottiene un badge per ID
  AppBadge? getBadgeById(String badgeId) {
    try {
      return _allBadges.firstWhere((badge) => badge.id == badgeId);
    } catch (e) {
      return null;
    }
  }

  /// Crea tutti i badge predefiniti
  List<AppBadge> _createAllBadges() {
    return [
      // Badge Punti WellJourney
      ..._createPointsBadges(),
      // Badge Percorsi
      ..._createPathwayBadges(),
      // Badge Streak
      ..._createStreakBadges(),
      // Badge Food Oracle
      ..._createFoodOracleBadges(),
      // Badge NutriScore
      ..._createNutriScoreBadges(),
      // Badge Sfide
      ..._createChallengeBadges(),
      // Badge Speciali Dr. Staffilano
      ..._createSpecialBadges(),
    ];
  }

  /// Badge per punti WellJourney
  List<AppBadge> _createPointsBadges() {
    return [
      AppBadge(
        id: 'points_first_step',
        name: 'Primo Passo',
        description: 'Hai guadagnato i tuoi primi 100 punti WellJourney',
        type: BadgeType.points,
        rarity: BadgeRarity.common,
        icon: Icons.directions_walk,
        primaryColor: DrStaffilanoTheme.primaryGreen,
        secondaryColor: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
        requiredValue: 100,
        requiredUnit: 'punti',
        drStaffilanoQuote: 'Ogni grande viaggio inizia con un singolo passo. Complimenti per aver iniziato!',
      ),
      AppBadge(
        id: 'points_on_the_way',
        name: 'In Cammino',
        description: 'Hai raggiunto 500 punti WellJourney',
        type: BadgeType.points,
        rarity: BadgeRarity.uncommon,
        icon: Icons.trending_up,
        primaryColor: DrStaffilanoTheme.professionalBlue,
        secondaryColor: DrStaffilanoTheme.professionalBlue.withOpacity(0.3),
        requiredValue: 500,
        requiredUnit: 'punti',
        drStaffilanoQuote: 'La costanza è la chiave del successo. Stai facendo progressi eccellenti!',
      ),
      AppBadge(
        id: 'points_determined',
        name: 'Determinato',
        description: 'Hai raggiunto 1000 punti WellJourney',
        type: BadgeType.points,
        rarity: BadgeRarity.rare,
        icon: Icons.psychology,
        primaryColor: DrStaffilanoTheme.accentGold,
        secondaryColor: DrStaffilanoTheme.accentGold.withOpacity(0.3),
        requiredValue: 1000,
        requiredUnit: 'punti',
        drStaffilanoQuote: 'La determinazione è il muscolo più forte del corpo umano. Continuate così!',
      ),
      AppBadge(
        id: 'points_expert',
        name: 'Esperto',
        description: 'Hai raggiunto 2500 punti WellJourney',
        type: BadgeType.points,
        rarity: BadgeRarity.epic,
        icon: Icons.school,
        primaryColor: Colors.purple,
        secondaryColor: Colors.purple.withOpacity(0.3),
        requiredValue: 2500,
        requiredUnit: 'punti',
        drStaffilanoQuote: 'La conoscenza senza azione è sterile. Voi state trasformando il sapere in salute!',
      ),
      AppBadge(
        id: 'points_master',
        name: 'Maestro',
        description: 'Hai raggiunto 5000 punti WellJourney',
        type: BadgeType.points,
        rarity: BadgeRarity.legendary,
        icon: Icons.emoji_events,
        primaryColor: Colors.orange,
        secondaryColor: Colors.orange.withOpacity(0.3),
        requiredValue: 5000,
        requiredUnit: 'punti',
        drStaffilanoQuote: 'Siete diventati maestri del vostro benessere. Il vostro cuore vi ringrazia!',
      ),
    ];
  }

  /// Badge per percorsi WellJourney
  List<AppBadge> _createPathwayBadges() {
    return [
      AppBadge(
        id: 'pathway_heart_health',
        name: 'Guardiano del Cuore',
        description: 'Hai completato il percorso Heart Health',
        type: BadgeType.pathway,
        rarity: BadgeRarity.rare,
        icon: FontAwesomeIcons.heartPulse,
        primaryColor: Colors.red,
        secondaryColor: Colors.red.withOpacity(0.3),
        requiredValue: 1,
        requiredUnit: 'percorso',
        drStaffilanoQuote: 'Il cuore è il motore della vita. Ora sapete come mantenerlo in perfetta forma!',
      ),
      AppBadge(
        id: 'pathway_weight_management',
        name: 'Equilibrio Perfetto',
        description: 'Hai completato il percorso Weight Management',
        type: BadgeType.pathway,
        rarity: BadgeRarity.rare,
        icon: FontAwesomeIcons.scaleBalanced,
        primaryColor: DrStaffilanoTheme.professionalBlue,
        secondaryColor: DrStaffilanoTheme.professionalBlue.withOpacity(0.3),
        requiredValue: 1,
        requiredUnit: 'percorso',
        drStaffilanoQuote: 'L\'equilibrio è la chiave di tutto. Avete trovato il vostro peso ideale!',
      ),
      AppBadge(
        id: 'pathway_sports_nutrition',
        name: 'Atleta Nutrizionale',
        description: 'Hai completato il percorso Sports Nutrition',
        type: BadgeType.pathway,
        rarity: BadgeRarity.rare,
        icon: FontAwesomeIcons.dumbbell,
        primaryColor: Colors.orange,
        secondaryColor: Colors.orange.withOpacity(0.3),
        requiredValue: 1,
        requiredUnit: 'percorso',
        drStaffilanoQuote: 'Lo sport senza nutrizione è come un\'auto senza carburante. Ora avete entrambi!',
      ),
      AppBadge(
        id: 'pathway_mediterranean_diet',
        name: 'Maestro Mediterraneo',
        description: 'Hai completato il percorso Mediterranean Diet',
        type: BadgeType.pathway,
        rarity: BadgeRarity.epic,
        icon: FontAwesomeIcons.leaf,
        primaryColor: DrStaffilanoTheme.primaryGreen,
        secondaryColor: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
        requiredValue: 1,
        requiredUnit: 'percorso',
        drStaffilanoQuote: 'La dieta mediterranea è patrimonio dell\'umanità. Ora ne siete ambasciatori!',
      ),
      AppBadge(
        id: 'pathway_prevention_care',
        name: 'Custode della Prevenzione',
        description: 'Hai completato il percorso Prevention & Care',
        type: BadgeType.pathway,
        rarity: BadgeRarity.epic,
        icon: FontAwesomeIcons.shield,
        primaryColor: DrStaffilanoTheme.accentGold,
        secondaryColor: DrStaffilanoTheme.accentGold.withOpacity(0.3),
        requiredValue: 1,
        requiredUnit: 'percorso',
        drStaffilanoQuote: 'Prevenire è meglio che curare. Avete scelto la strada della saggezza!',
      ),
    ];
  }

  /// Badge per streak consecutivi
  List<AppBadge> _createStreakBadges() {
    return [
      AppBadge(
        id: 'streak_consistency',
        name: 'Costanza',
        description: 'Hai mantenuto uno streak di 7 giorni consecutivi',
        type: BadgeType.streak,
        rarity: BadgeRarity.uncommon,
        icon: Icons.local_fire_department,
        primaryColor: Colors.orange,
        secondaryColor: Colors.orange.withOpacity(0.3),
        requiredValue: 7,
        requiredUnit: 'giorni',
        drStaffilanoQuote: 'La costanza batte il talento quando il talento non è costante!',
      ),
      AppBadge(
        id: 'streak_dedication',
        name: 'Dedizione',
        description: 'Hai mantenuto uno streak di 30 giorni consecutivi',
        type: BadgeType.streak,
        rarity: BadgeRarity.rare,
        icon: Icons.whatshot,
        primaryColor: Colors.red,
        secondaryColor: Colors.red.withOpacity(0.3),
        requiredValue: 30,
        requiredUnit: 'giorni',
        drStaffilanoQuote: 'Un mese di dedizione vale più di un anno di buone intenzioni!',
      ),
      AppBadge(
        id: 'streak_legend',
        name: 'Leggenda',
        description: 'Hai mantenuto uno streak di 100 giorni consecutivi',
        type: BadgeType.streak,
        rarity: BadgeRarity.legendary,
        icon: FontAwesomeIcons.fire,
        primaryColor: Colors.deepOrange,
        secondaryColor: Colors.deepOrange.withOpacity(0.3),
        requiredValue: 100,
        requiredUnit: 'giorni',
        drStaffilanoQuote: 'Siete diventati una leggenda vivente del benessere. Incredibile!',
      ),
    ];
  }

  /// Badge per Food Oracle
  List<AppBadge> _createFoodOracleBadges() {
    return [
      AppBadge(
        id: 'food_oracle_explorer',
        name: 'Esploratore',
        description: 'Hai effettuato le tue prime 10 scansioni con Food Oracle',
        type: BadgeType.foodOracle,
        rarity: BadgeRarity.common,
        icon: Icons.camera_alt,
        primaryColor: DrStaffilanoTheme.professionalBlue,
        secondaryColor: DrStaffilanoTheme.professionalBlue.withOpacity(0.3),
        requiredValue: 10,
        requiredUnit: 'scansioni',
        drStaffilanoQuote: 'La curiosità è il primo passo verso la conoscenza. Continuate ad esplorare!',
      ),
      AppBadge(
        id: 'food_oracle_analyst',
        name: 'Analista',
        description: 'Hai effettuato 50 scansioni con Food Oracle',
        type: BadgeType.foodOracle,
        rarity: BadgeRarity.uncommon,
        icon: FontAwesomeIcons.magnifyingGlass,
        primaryColor: DrStaffilanoTheme.primaryGreen,
        secondaryColor: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
        requiredValue: 50,
        requiredUnit: 'scansioni',
        drStaffilanoQuote: 'Analizzare è il primo passo per migliorare. Ottimo lavoro!',
      ),
      AppBadge(
        id: 'food_oracle_guru',
        name: 'Guru del Cibo',
        description: 'Hai effettuato 100 scansioni con Food Oracle',
        type: BadgeType.foodOracle,
        rarity: BadgeRarity.epic,
        icon: FontAwesomeIcons.brain,
        primaryColor: Colors.purple,
        secondaryColor: Colors.purple.withOpacity(0.3),
        requiredValue: 100,
        requiredUnit: 'scansioni',
        drStaffilanoQuote: 'Siete diventati dei veri guru dell\'alimentazione consapevole!',
      ),
    ];
  }

  /// Badge per NutriScore
  List<AppBadge> _createNutriScoreBadges() {
    return [
      AppBadge(
        id: 'nutriscore_beginner',
        name: 'Principiante',
        description: 'Hai raggiunto 25 punti NutriScore',
        type: BadgeType.nutriScore,
        rarity: BadgeRarity.common,
        icon: Icons.analytics,
        primaryColor: DrStaffilanoTheme.primaryGreen,
        secondaryColor: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
        requiredValue: 25,
        requiredUnit: 'punti NutriScore',
        drStaffilanoQuote: 'Ogni esperto è stato una volta un principiante. Ottimo inizio!',
      ),
      AppBadge(
        id: 'nutriscore_competent',
        name: 'Competente',
        description: 'Hai raggiunto 50 punti NutriScore',
        type: BadgeType.nutriScore,
        rarity: BadgeRarity.uncommon,
        icon: Icons.trending_up,
        primaryColor: DrStaffilanoTheme.professionalBlue,
        secondaryColor: DrStaffilanoTheme.professionalBlue.withOpacity(0.3),
        requiredValue: 50,
        requiredUnit: 'punti NutriScore',
        drStaffilanoQuote: 'La competenza si costruisce giorno dopo giorno. Continuate così!',
      ),
      AppBadge(
        id: 'nutriscore_advanced',
        name: 'Avanzato',
        description: 'Hai raggiunto 75 punti NutriScore',
        type: BadgeType.nutriScore,
        rarity: BadgeRarity.rare,
        icon: Icons.school,
        primaryColor: DrStaffilanoTheme.accentGold,
        secondaryColor: DrStaffilanoTheme.accentGold.withOpacity(0.3),
        requiredValue: 75,
        requiredUnit: 'punti NutriScore',
        drStaffilanoQuote: 'Livello avanzato raggiunto! La vostra conoscenza nutrizionale è impressionante!',
      ),
      AppBadge(
        id: 'nutriscore_nutritionist',
        name: 'Nutrizionista',
        description: 'Hai raggiunto 100 punti NutriScore',
        type: BadgeType.nutriScore,
        rarity: BadgeRarity.legendary,
        icon: FontAwesomeIcons.userDoctor,
        primaryColor: Colors.purple,
        secondaryColor: Colors.purple.withOpacity(0.3),
        requiredValue: 100,
        requiredUnit: 'punti NutriScore',
        drStaffilanoQuote: 'Potreste insegnare nutrizione! Il vostro livello è da vero professionista!',
      ),
    ];
  }

  /// Badge per sfide completate
  List<AppBadge> _createChallengeBadges() {
    return [
      AppBadge(
        id: 'challenge_first_win',
        name: 'Prima Vittoria',
        description: 'Hai completato la tua prima sfida',
        type: BadgeType.challenge,
        rarity: BadgeRarity.common,
        icon: Icons.emoji_events,
        primaryColor: DrStaffilanoTheme.accentGold,
        secondaryColor: DrStaffilanoTheme.accentGold.withOpacity(0.3),
        requiredValue: 1,
        requiredUnit: 'sfida',
        drStaffilanoQuote: 'La prima vittoria è sempre la più dolce. Complimenti!',
      ),
      AppBadge(
        id: 'challenge_champion',
        name: 'Campione',
        description: 'Hai completato 10 sfide',
        type: BadgeType.challenge,
        rarity: BadgeRarity.rare,
        icon: FontAwesomeIcons.trophy,
        primaryColor: Colors.orange,
        secondaryColor: Colors.orange.withOpacity(0.3),
        requiredValue: 10,
        requiredUnit: 'sfide',
        drStaffilanoQuote: 'Un vero campione non si arrende mai. Siete sulla strada giusta!',
      ),
      AppBadge(
        id: 'challenge_legend',
        name: 'Leggenda delle Sfide',
        description: 'Hai completato 25 sfide',
        type: BadgeType.challenge,
        rarity: BadgeRarity.legendary,
        icon: FontAwesomeIcons.crown,
        primaryColor: Colors.deepPurple,
        secondaryColor: Colors.deepPurple.withOpacity(0.3),
        requiredValue: 25,
        requiredUnit: 'sfide',
        drStaffilanoQuote: 'Siete diventati una leggenda! Il vostro impegno è straordinario!',
      ),
    ];
  }

  /// Badge speciali Dr. Staffilano
  List<AppBadge> _createSpecialBadges() {
    return [
      AppBadge(
        id: 'special_heart_guardian',
        name: 'Guardiano del Cuore',
        description: 'Hai dimostrato eccellenza nella cura cardiovascolare',
        type: BadgeType.special,
        rarity: BadgeRarity.legendary,
        icon: FontAwesomeIcons.heartPulse,
        primaryColor: Colors.red,
        secondaryColor: Colors.red.withOpacity(0.3),
        requiredValue: 1,
        requiredUnit: 'traguardo speciale',
        drStaffilanoQuote: 'Come cardiologo, sono orgoglioso di vedervi prendervi cura del vostro cuore con tale dedizione!',
        isSecret: true,
      ),
      AppBadge(
        id: 'special_wellness_ambassador',
        name: 'Ambasciatore del Benessere',
        description: 'Hai raggiunto l\'eccellenza in tutti gli aspetti del benessere',
        type: BadgeType.special,
        rarity: BadgeRarity.legendary,
        icon: FontAwesomeIcons.medal,
        primaryColor: DrStaffilanoTheme.accentGold,
        secondaryColor: DrStaffilanoTheme.accentGold.withOpacity(0.3),
        requiredValue: 1,
        requiredUnit: 'traguardo speciale',
        drStaffilanoQuote: 'Siete diventati un esempio per tutti. Il benessere è il vostro stile di vita!',
        isSecret: true,
        prerequisites: ['points_master', 'pathway_heart_health', 'streak_legend'],
      ),
    ];
  }
}
