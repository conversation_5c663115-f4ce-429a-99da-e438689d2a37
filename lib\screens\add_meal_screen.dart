import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/meal.dart';
import '../theme/app_theme.dart';

class AddMealScreen extends StatefulWidget {
  final Function(Meal) onAdd;
  final String? initialDate;

  const AddMealScreen({
    super.key,
    required this.onAdd,
    this.initialDate,
  });

  @override
  State<AddMealScreen> createState() => _AddMealScreenState();
}

class _AddMealScreenState extends State<AddMealScreen> {
  final _nomeController = TextEditingController();
  final _orarioController = TextEditingController();
  final _calorieController = TextEditingController();
  final _proteineController = TextEditingController();
  final _carboidratiController = TextEditingController();
  final _grassiController = TextEditingController();
  bool _completato = false;
  bool _calcolaAutomaticamente = false;

  @override
  void dispose() {
    _nomeController.dispose();
    _orarioController.dispose();
    _calorieController.dispose();
    _proteineController.dispose();
    _carboidratiController.dispose();
    _grassiController.dispose();
    super.dispose();
  }

  // Calcola le calorie dai macronutrienti
  void _calcolaCaloireDaMacro() {
    if (!_calcolaAutomaticamente) return;

    try {
      final proteine = double.parse(_proteineController.text.isEmpty ? "0" : _proteineController.text);
      final carboidrati = double.parse(_carboidratiController.text.isEmpty ? "0" : _carboidratiController.text);
      final grassi = double.parse(_grassiController.text.isEmpty ? "0" : _grassiController.text);

      // 1g proteine = 4 kcal, 1g carboidrati = 4 kcal, 1g grassi = 9 kcal
      final calorie = (proteine * 4 + carboidrati * 4 + grassi * 9).round();

      setState(() {
        _calorieController.text = calorie.toString();
      });
    } catch (e) {
      // Ignora errori di parsing
    }
  }

  @override
  void initState() {
    super.initState();

    // Imposta l'orario predefinito
    if (_orarioController.text.isEmpty) {
      final now = DateTime.now();
      _orarioController.text = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
    }
  }

  @override
  Widget build(BuildContext context) {
    // Determina se stiamo pianificando per una data futura
    bool isPianificazione = widget.initialDate != null;
    String? dataFormattata;

    if (isPianificazione) {
      final date = DateTime.parse(widget.initialDate!);
      dataFormattata = DateFormat('EEEE, d MMMM').format(date);
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(isPianificazione
            ? 'Pianifica Pasto'
            : 'Aggiungi Pasto'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Mostra la data se stiamo pianificando
            if (isPianificazione)
              Container(
                padding: const EdgeInsets.all(12),
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppTheme.primaryColor.withOpacity(0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      FontAwesomeIcons.calendarDay,
                      color: AppTheme.primaryColor,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Pianificazione per $dataFormattata',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ],
                ),
              ),

            // Nome pasto
            TextField(
              controller: _nomeController,
              decoration: InputDecoration(
                labelText: 'Nome pasto',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                hintText: 'es. Colazione, Pranzo, Cena',
                prefixIcon: Icon(
                  FontAwesomeIcons.utensils,
                  color: AppTheme.primaryColor,
                  size: 16,
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Orario
            TextField(
              controller: _orarioController,
              decoration: InputDecoration(
                labelText: 'Orario',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                hintText: 'es. 8:00',
                prefixIcon: Icon(
                  FontAwesomeIcons.clock,
                  color: AppTheme.primaryColor,
                  size: 16,
                ),
              ),
              onTap: () async {
                // Mostra time picker
                final TimeOfDay? pickedTime = await showTimePicker(
                  context: context,
                  initialTime: TimeOfDay.now(),
                );

                if (pickedTime != null) {
                  setState(() {
                    _orarioController.text = '${pickedTime.hour.toString().padLeft(2, '0')}:${pickedTime.minute.toString().padLeft(2, '0')}';
                  });
                }
              },
            ),
            const SizedBox(height: 24),

            // Sezione calorie e macronutrienti
            Row(
              children: [
                Icon(
                  FontAwesomeIcons.chartPie,
                  color: AppTheme.primaryColor,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  'Informazioni nutrizionali',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Switch per calcolo automatico calorie
            Container(
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: SwitchListTile(
                title: const Text('Calcola calorie automaticamente'),
                subtitle: const Text('In base ai macronutrienti inseriti'),
                value: _calcolaAutomaticamente,
                onChanged: (value) {
                  setState(() {
                    _calcolaAutomaticamente = value;
                    if (value) {
                      _calcolaCaloireDaMacro();
                    }
                  });
                },
                activeColor: AppTheme.primaryColor,
                secondary: Icon(
                  FontAwesomeIcons.calculator,
                  color: AppTheme.primaryColor,
                  size: 16,
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Calorie
            TextField(
              controller: _calorieController,
              decoration: InputDecoration(
                labelText: 'Calorie (kcal)',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                hintText: 'es. 350',
                prefixIcon: Icon(
                  FontAwesomeIcons.fire,
                  color: AppTheme.accentColor,
                  size: 16,
                ),
              ),
              keyboardType: TextInputType.number,
              enabled: !_calcolaAutomaticamente,
            ),
            const SizedBox(height: 16),

            // Macronutrienti
            Text(
              'Macronutrienti',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _proteineController,
                    decoration: InputDecoration(
                      labelText: 'Proteine (g)',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      hintText: 'es. 20',
                      prefixIcon: Icon(
                        FontAwesomeIcons.drumstickBite,
                        color: AppTheme.proteinColor,
                        size: 16,
                      ),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (_) => _calcolaCaloireDaMacro(),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextField(
                    controller: _carboidratiController,
                    decoration: InputDecoration(
                      labelText: 'Carboidrati (g)',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      hintText: 'es. 40',
                      prefixIcon: Icon(
                        FontAwesomeIcons.breadSlice,
                        color: AppTheme.carbColor,
                        size: 16,
                      ),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (_) => _calcolaCaloireDaMacro(),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextField(
                    controller: _grassiController,
                    decoration: InputDecoration(
                      labelText: 'Grassi (g)',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      hintText: 'es. 10',
                      prefixIcon: Icon(
                        FontAwesomeIcons.cheese,
                        color: AppTheme.fatColor,
                        size: 16,
                      ),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (_) => _calcolaCaloireDaMacro(),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Checkbox completato
            Container(
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: CheckboxListTile(
                title: const Text('Pasto già completato'),
                subtitle: const Text('Segna come già consumato'),
                value: _completato,
                onChanged: (value) {
                  setState(() {
                    _completato = value ?? false;
                  });
                },
                activeColor: AppTheme.successColor,
                secondary: Icon(
                  _completato ? FontAwesomeIcons.circleCheck : FontAwesomeIcons.circle,
                  color: _completato ? AppTheme.successColor : Colors.grey,
                  size: 16,
                ),
              ),
            ),

            const SizedBox(height: 32),

            // Pulsante aggiungi
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _aggiungiPasto,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 18,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
                icon: Icon(
                  isPianificazione
                      ? FontAwesomeIcons.calendarPlus
                      : FontAwesomeIcons.plus,
                  size: 18,
                ),
                label: Text(
                  isPianificazione
                      ? 'Pianifica Pasto'
                      : 'Aggiungi Pasto',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.5,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _aggiungiPasto() {
    // Validazione input
    if (_nomeController.text.isEmpty ||
        _orarioController.text.isEmpty ||
        _calorieController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Compila tutti i campi obbligatori'),
          backgroundColor: AppTheme.errorColor,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
      return;
    }

    // Conversione calorie a intero
    int calorie;
    try {
      calorie = int.parse(_calorieController.text);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Inserisci un valore numerico valido per le calorie'),
          backgroundColor: AppTheme.errorColor,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
      return;
    }

    // Conversione macronutrienti a double
    double proteine = 0.0;
    double carboidrati = 0.0;
    double grassi = 0.0;

    try {
      proteine = _proteineController.text.isEmpty
          ? 0.0
          : double.parse(_proteineController.text);
      carboidrati = _carboidratiController.text.isEmpty
          ? 0.0
          : double.parse(_carboidratiController.text);
      grassi = _grassiController.text.isEmpty
          ? 0.0
          : double.parse(_grassiController.text);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Inserisci valori numerici validi per i macronutrienti'),
          backgroundColor: AppTheme.errorColor,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
      return;
    }

    // Creazione nuovo pasto
    final nuovoPasto = Meal(
      nome: _nomeController.text,
      orario: _orarioController.text,
      calorie: calorie,
      proteine: proteine,
      carboidrati: carboidrati,
      grassi: grassi,
      completato: _completato,
    );

    // Chiamata callback per aggiungere
    widget.onAdd(nuovoPasto);

    // Mostra messaggio di conferma
    bool isPianificazione = widget.initialDate != null;
    String message = isPianificazione
        ? 'Pasto pianificato con successo'
        : 'Pasto aggiunto con successo';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );

    // Torna indietro
    Navigator.of(context).pop();
  }
}
