import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'dart:math' as math;
import '../theme/dr_staffilano_theme.dart';

/// Tipi di celebrazioni supportate
enum CelebrationType {
  achievement,
  levelUp,
  badge,
  milestone,
  streak,
}

/// Tipi di notifiche supportate
enum NotificationType {
  success,
  info,
  warning,
  achievement,
}

/// Sistema di celebrazioni e notifiche premium
class CelebrationSystem extends StatefulWidget {
  final String title;
  final String message;
  final CelebrationType type;
  final VoidCallback? onDismiss;

  const CelebrationSystem({
    Key? key,
    required this.title,
    required this.message,
    required this.type,
    this.onDismiss,
  }) : super(key: key);

  @override
  State<CelebrationSystem> createState() => _CelebrationSystemState();
}

class _CelebrationSystemState extends State<CelebrationSystem>
    with TickerProviderStateMixin {
  late AnimationController _mainController;
  late AnimationController _particleController;
  late AnimationController _pulseController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _rotationAnimation;

  final List<Particle> _particles = [];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _generateParticles();
    _startAnimations();
  }

  @override
  void dispose() {
    _mainController.dispose();
    _particleController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _mainController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _particleController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: Curves.easeOutBack,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(_pulseController);
  }

  void _generateParticles() {
    final random = math.Random();
    for (int i = 0; i < 20; i++) {
      _particles.add(Particle(
        x: random.nextDouble(),
        y: random.nextDouble(),
        size: 2 + random.nextDouble() * 4,
        color: _getParticleColor(),
        velocity: Offset(
          (random.nextDouble() - 0.5) * 2,
          -random.nextDouble() * 2 - 1,
        ),
      ));
    }
  }

  void _startAnimations() {
    _mainController.forward();
    _particleController.repeat();
    _pulseController.repeat();

    // Auto dismiss dopo 4 secondi
    Future.delayed(const Duration(seconds: 4), () {
      if (mounted) {
        _dismiss();
      }
    });
  }

  void _dismiss() {
    _mainController.reverse().then((_) {
      if (widget.onDismiss != null) {
        widget.onDismiss!();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        child: Stack(
          children: [
            // Overlay scuro
            AnimatedBuilder(
              animation: _fadeAnimation,
              builder: (context, child) {
                return Container(
                  color: Colors.black.withOpacity(0.7 * _fadeAnimation.value),
                );
              },
            ),
            // Particelle animate
            if (widget.type == CelebrationType.achievement)
              ..._buildParticles(),
            // Contenuto principale
            Center(
              child: AnimatedBuilder(
                animation: _mainController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _scaleAnimation.value,
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: _buildCelebrationCard(),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCelebrationCard() {
    return Container(
      margin: const EdgeInsets.all(32),
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: _getGradientColors(),
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: _getPrimaryColor().withOpacity(0.3),
            blurRadius: 30,
            offset: const Offset(0, 15),
            spreadRadius: 5,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildIcon(),
          const SizedBox(height: 24),
          _buildTitle(),
          const SizedBox(height: 16),
          _buildMessage(),
          const SizedBox(height: 32),
          _buildActionButton(),
        ],
      ),
    );
  }

  Widget _buildIcon() {
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return Transform.rotate(
          angle: widget.type == CelebrationType.achievement
              ? _rotationAnimation.value
              : 0,
          child: Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: _getPrimaryColor().withOpacity(0.3),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: Icon(
              _getIcon(),
              size: 40,
              color: _getPrimaryColor(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTitle() {
    return Text(
      widget.title,
      textAlign: TextAlign.center,
      style: const TextStyle(
        fontSize: 28,
        fontWeight: FontWeight.bold,
        color: Colors.white,
        shadows: [
          Shadow(
            offset: Offset(0, 2),
            blurRadius: 4,
            color: Colors.black26,
          ),
        ],
      ),
    );
  }

  Widget _buildMessage() {
    return Text(
      widget.message,
      textAlign: TextAlign.center,
      style: const TextStyle(
        fontSize: 16,
        color: Colors.white,
        height: 1.5,
      ),
    );
  }

  Widget _buildActionButton() {
    return GestureDetector(
      onTap: _dismiss,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Text(
          'Fantastico!',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _getPrimaryColor(),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildParticles() {
    return _particles.map((particle) {
      return AnimatedBuilder(
        animation: _particleController,
        builder: (context, child) {
          final progress = _particleController.value;
          final x = particle.x + particle.velocity.dx * progress;
          final y = particle.y + particle.velocity.dy * progress;

          return Positioned(
            left: x * MediaQuery.of(context).size.width,
            top: y * MediaQuery.of(context).size.height,
            child: Opacity(
              opacity: (1 - progress).clamp(0.0, 1.0),
              child: Container(
                width: particle.size,
                height: particle.size,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: particle.color,
                  boxShadow: [
                    BoxShadow(
                      color: particle.color.withOpacity(0.5),
                      blurRadius: 4,
                      spreadRadius: 1,
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      );
    }).toList();
  }

  IconData _getIcon() {
    switch (widget.type) {
      case CelebrationType.achievement:
        return FontAwesomeIcons.trophy;
      case CelebrationType.levelUp:
        return FontAwesomeIcons.arrowUp;
      case CelebrationType.badge:
        return FontAwesomeIcons.medal;
      case CelebrationType.milestone:
        return FontAwesomeIcons.flag;
      case CelebrationType.streak:
        return FontAwesomeIcons.fire;
    }
  }

  Color _getPrimaryColor() {
    switch (widget.type) {
      case CelebrationType.achievement:
        return DrStaffilanoTheme.accentGold;
      case CelebrationType.levelUp:
        return DrStaffilanoTheme.primaryGreen;
      case CelebrationType.badge:
        return Colors.purple;
      case CelebrationType.milestone:
        return DrStaffilanoTheme.secondaryBlue;
      case CelebrationType.streak:
        return Colors.orange;
    }
  }

  List<Color> _getGradientColors() {
    final primary = _getPrimaryColor();
    return [
      primary,
      primary.withOpacity(0.8),
    ];
  }

  Color _getParticleColor() {
    final colors = [
      DrStaffilanoTheme.accentGold,
      DrStaffilanoTheme.primaryGreen,
      DrStaffilanoTheme.secondaryBlue,
      Colors.purple,
      Colors.orange,
    ];
    return colors[math.Random().nextInt(colors.length)];
  }
}

/// Sistema di notifiche in-app eleganti
class ElegantNotification extends StatefulWidget {
  final String title;
  final String message;
  final NotificationType type;
  final Duration duration;
  final VoidCallback? onTap;

  const ElegantNotification({
    Key? key,
    required this.title,
    required this.message,
    required this.type,
    this.duration = const Duration(seconds: 4),
    this.onTap,
  }) : super(key: key);

  @override
  State<ElegantNotification> createState() => _ElegantNotificationState();
}

class _ElegantNotificationState extends State<ElegantNotification>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnimation();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _controller = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutBack,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));
  }

  void _startAnimation() {
    _controller.forward();

    Future.delayed(widget.duration, () {
      if (mounted) {
        _controller.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: GestureDetector(
          onTap: widget.onTap,
          child: Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: _getNotificationColors(),
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: _getNotificationColor().withOpacity(0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getNotificationIcon(),
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.message,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getNotificationIcon() {
    switch (widget.type) {
      case NotificationType.success:
        return FontAwesomeIcons.check;
      case NotificationType.info:
        return FontAwesomeIcons.info;
      case NotificationType.warning:
        return FontAwesomeIcons.exclamationTriangle;
      case NotificationType.achievement:
        return FontAwesomeIcons.star;
    }
  }

  Color _getNotificationColor() {
    switch (widget.type) {
      case NotificationType.success:
        return DrStaffilanoTheme.primaryGreen;
      case NotificationType.info:
        return DrStaffilanoTheme.secondaryBlue;
      case NotificationType.warning:
        return Colors.orange;
      case NotificationType.achievement:
        return DrStaffilanoTheme.accentGold;
    }
  }

  List<Color> _getNotificationColors() {
    final primary = _getNotificationColor();
    return [primary, primary.withOpacity(0.8)];
  }
}

/// Classe per rappresentare una particella animata
class Particle {
  final double x;
  final double y;
  final double size;
  final Color color;
  final Offset velocity;

  Particle({
    required this.x,
    required this.y,
    required this.size,
    required this.color,
    required this.velocity,
  });
}
