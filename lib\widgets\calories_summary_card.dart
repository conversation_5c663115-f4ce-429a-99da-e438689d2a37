import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../theme/app_theme.dart';

class CaloriesSummaryCard extends StatelessWidget {
  final int calorieConsumate;
  final int calorieGiornaliere;
  final Map<String, double> macronutrientiConsumati;
  final Map<String, double> macronutrientiTarget;
  final String data;

  const CaloriesSummaryCard({
    super.key,
    required this.calorieConsumate,
    required this.calorieGiornaliere,
    required this.macronutrientiConsumati,
    required this.macronutrientiTarget,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final percentuale = calorieGiornaliere > 0 
        ? (calorieConsumate / calorieGiornaliere).clamp(0.0, 2.0) 
        : 0.0;
    
    // Calcola le percentuali target in grammi
    final proteineTarget = (calorieGiornaliere * macronutrientiTarget['proteine']! / 100 / 4).round();
    final carboidratiTarget = (calorieGiornaliere * macronutrientiTarget['carboidrati']! / 100 / 4).round();
    final grassiTarget = (calorieGiornaliere * macronutrientiTarget['grassi']! / 100 / 9).round();
    
    // Ottieni i valori consumati
    final proteineConsumate = macronutrientiConsumati['proteine'] ?? 0.0;
    final carboidratiConsumati = macronutrientiConsumati['carboidrati'] ?? 0.0;
    final grassiConsumati = macronutrientiConsumati['grassi'] ?? 0.0;
    
    // Calcola le percentuali di completamento
    final proteinePercentuale = proteineTarget > 0 
        ? (proteineConsumate / proteineTarget).clamp(0.0, 1.0) 
        : 0.0;
    final carboidratiPercentuale = carboidratiTarget > 0 
        ? (carboidratiConsumati / carboidratiTarget).clamp(0.0, 1.0) 
        : 0.0;
    final grassiPercentuale = grassiTarget > 0 
        ? (grassiConsumati / grassiTarget).clamp(0.0, 1.0) 
        : 0.0;
    
    // Determina il colore in base alla percentuale
    Color getProgressColor(double percentage) {
      if (percentage <= 0.6) return AppTheme.successColor;
      if (percentage <= 0.9) return AppTheme.warningColor;
      return AppTheme.errorColor;
    }
    
    final progressColor = getProgressColor(percentuale);
    
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Intestazione con data
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Riepilogo Calorie',
                  style: textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  data,
                  style: textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryColor,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Cerchio di progresso calorie
            Center(
              child: SizedBox(
                width: 180,
                height: 180,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // Cerchio di sfondo
                    SizedBox(
                      width: 180,
                      height: 180,
                      child: CircularProgressIndicator(
                        value: 1,
                        strokeWidth: 12,
                        backgroundColor: Colors.grey.shade200,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Colors.grey.shade300,
                        ),
                      ),
                    ),
                    
                    // Cerchio di progresso
                    SizedBox(
                      width: 180,
                      height: 180,
                      child: CircularProgressIndicator(
                        value: percentuale,
                        strokeWidth: 12,
                        backgroundColor: Colors.transparent,
                        valueColor: AlwaysStoppedAnimation<Color>(progressColor),
                      ),
                    ).animate().fadeIn(duration: 500.ms).scale(
                      begin: const Offset(0.8, 0.8),
                      end: const Offset(1.0, 1.0),
                      duration: 500.ms,
                    ),
                    
                    // Testo centrale
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          '$calorieConsumate',
                          style: textTheme.headlineMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ).animate().fadeIn(duration: 300.ms).moveY(
                          begin: 10,
                          end: 0,
                          duration: 300.ms,
                        ),
                        Text(
                          'di $calorieGiornaliere kcal',
                          style: textTheme.bodyMedium?.copyWith(
                            color: AppTheme.textSecondaryColor,
                          ),
                        ).animate().fadeIn(duration: 300.ms, delay: 100.ms).moveY(
                          begin: 10,
                          end: 0,
                          duration: 300.ms,
                          delay: 100.ms,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Macronutrienti
            Text(
              'Macronutrienti',
              style: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 12),
            
            // Barre di progresso macronutrienti
            _buildMacronutrientProgress(
              context,
              'Proteine',
              proteineConsumate.round(),
              proteineTarget,
              proteinePercentuale,
              AppTheme.proteinColor,
              FontAwesomeIcons.drumstickBite,
              delay: 0.ms,
            ),
            
            const SizedBox(height: 8),
            
            _buildMacronutrientProgress(
              context,
              'Carboidrati',
              carboidratiConsumati.round(),
              carboidratiTarget,
              carboidratiPercentuale,
              AppTheme.carbColor,
              FontAwesomeIcons.breadSlice,
              delay: 100.ms,
            ),
            
            const SizedBox(height: 8),
            
            _buildMacronutrientProgress(
              context,
              'Grassi',
              grassiConsumati.round(),
              grassiTarget,
              grassiPercentuale,
              AppTheme.fatColor,
              FontAwesomeIcons.cheese,
              delay: 200.ms,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildMacronutrientProgress(
    BuildContext context,
    String label,
    int consumed,
    int target,
    double percentage,
    Color color,
    IconData icon, {
    Duration delay = Duration.zero,
  }) {
    final textTheme = Theme.of(context).textTheme;
    
    return Row(
      children: [
        // Icona
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: color.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 16,
          ),
        ),
        
        const SizedBox(width: 12),
        
        // Progresso
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Etichetta e valori
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    label,
                    style: textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '$consumed / $target g',
                    style: textTheme.bodySmall?.copyWith(
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 4),
              
              // Barra di progresso
              ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: LinearProgressIndicator(
                  value: percentage,
                  backgroundColor: color.withOpacity(0.2),
                  valueColor: AlwaysStoppedAnimation<Color>(color),
                  minHeight: 8,
                ),
              ).animate(delay: delay).fadeIn(duration: 300.ms).slideX(
                begin: -1,
                end: 0,
                duration: 500.ms,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
