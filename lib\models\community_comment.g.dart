// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'community_comment.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CommunityComment _$CommunityCommentFromJson(Map<String, dynamic> json) =>
    CommunityComment(
      id: json['id'] as String,
      postId: json['postId'] as String,
      author: CommunityUser.fromJson(json['author'] as Map<String, dynamic>),
      content: json['content'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      likesCount: json['likesCount'] as int? ?? 0,
      isLikedByCurrentUser: json['isLikedByCurrentUser'] as bool? ?? false,
      isEdited: json['isEdited'] as bool? ?? false,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$CommunityCommentToJson(CommunityComment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'postId': instance.postId,
      'author': instance.author.toJson(),
      'content': instance.content,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'likesCount': instance.likesCount,
      'isLikedByCurrentUser': instance.isLikedByCurrentUser,
      'isEdited': instance.isEdited,
      'metadata': instance.metadata,
    };
