// Modelli Supabase essenziali

/// Modello per la tabella 'profiles' (dati pubblici utente)
class SupabaseProfile {
  final String id;
  final String? username;
  final String? nome;
  final String? fotoProfiloUrl;
  final DateTime? createdAt;

  const SupabaseProfile({
    required this.id,
    this.username,
    this.nome,
    this.fotoProfiloUrl,
    this.createdAt,
  });

  factory SupabaseProfile.fromJson(Map<String, dynamic> json) {
    return SupabaseProfile(
      id: json['id'] as String,
      username: json['username'] as String?,
      nome: json['nome'] as String?,
      fotoProfiloUrl: json['foto_profilo_url'] as String?,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'nome': nome,
      'foto_profilo_url': fotoProfiloUrl,
      'created_at': createdAt?.toIso8601String(),
    };
  }
}

/// Modello per la tabella 'dati_utente' (dati privati nutrizione)
class SupabaseDatiUtente {
  final String id;
  final String? obiettivo;
  final String? sesso;
  final int? eta;
  final int? altezzaCm;
  final double? pesoKg;
  final String? livelloAttivita;
  final List<String>? allergieAlimentari;
  final List<String>? preferenzeDietetiche;
  final Map<String, dynamic>? profiloUltraDettagliato;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const SupabaseDatiUtente({
    required this.id,
    this.obiettivo,
    this.sesso,
    this.eta,
    this.altezzaCm,
    this.pesoKg,
    this.livelloAttivita,
    this.allergieAlimentari,
    this.preferenzeDietetiche,
    this.profiloUltraDettagliato,
    this.createdAt,
    this.updatedAt,
  });

  factory SupabaseDatiUtente.fromJson(Map<String, dynamic> json) {
    return SupabaseDatiUtente(
      id: json['id'] as String,
      obiettivo: json['obiettivo'] as String?,
      sesso: json['sesso'] as String?,
      eta: json['eta'] as int?,
      altezzaCm: json['altezza_cm'] as int?,
      pesoKg: (json['peso_kg'] as num?)?.toDouble(),
      livelloAttivita: json['livello_attivita'] as String?,
      allergieAlimentari: (json['allergie_alimentari'] as List?)?.cast<String>(),
      preferenzeDietetiche: (json['preferenze_dietetiche'] as List?)?.cast<String>(),
      profiloUltraDettagliato: json['profilo_ultra_dettagliato'] as Map<String, dynamic>?,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'obiettivo': obiettivo,
      'sesso': sesso,
      'eta': eta,
      'altezza_cm': altezzaCm,
      'peso_kg': pesoKg,
      'livello_attivita': livelloAttivita,
      'allergie_alimentari': allergieAlimentari,
      'preferenze_dietetiche': preferenzeDietetiche,
      'profilo_ultra_dettagliato': profiloUltraDettagliato,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}


  final String id;
  final String? obiettivo;
  final String? sesso;
  final int? eta;
  @JsonKey(name: 'altezza_cm')
  final int? altezzaCm;
  @JsonKey(name: 'peso_kg')
  final double? pesoKg;
  @JsonKey(name: 'livello_attivita')
  final String? livelloAttivita;
  @JsonKey(name: 'allergie_alimentari')
  final List<String>? allergieAlimentari;
  @JsonKey(name: 'preferenze_dietetiche')
  final List<String>? preferenzeDietetiche;
  @JsonKey(name: 'profilo_ultra_dettagliato')
  final Map<String, dynamic>? profiloUltraDettagliato;
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;

  const SupabaseDatiUtente({
    required this.id,
    this.obiettivo,
    this.sesso,
    this.eta,
    this.altezzaCm,
    this.pesoKg,
    this.livelloAttivita,
    this.allergieAlimentari,
    this.preferenzeDietetiche,
    this.profiloUltraDettagliato,
    this.createdAt,
    this.updatedAt,
  });

  factory SupabaseDatiUtente.fromJson(Map<String, dynamic> json) =>
      _$SupabaseDatiUtenteFromJson(json);

  Map<String, dynamic> toJson() => _$SupabaseDatiUtenteToJson(this);
}

/// Modello per la tabella 'diari_alimentari' (pasti giornalieri)
@JsonSerializable()
class SupabaseDiarioAlimentare {
  final int? id;
  @JsonKey(name: 'user_id')
  final String userId;
  final DateTime data;
  @JsonKey(name: 'nome_pasto')
  final String nomePasto;
  final Map<String, dynamic> alimenti;
  @JsonKey(name: 'calorie_totali')
  final int? calorieTotali;
  @JsonKey(name: 'proteine_g')
  final double? proteineG;
  @JsonKey(name: 'carboidrati_g')
  final double? carboidratiG;
  @JsonKey(name: 'grassi_g')
  final double? grassiG;
  final bool? completato;
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;

  const SupabaseDiarioAlimentare({
    this.id,
    required this.userId,
    required this.data,
    required this.nomePasto,
    required this.alimenti,
    this.calorieTotali,
    this.proteineG,
    this.carboidratiG,
    this.grassiG,
    this.completato,
    this.createdAt,
  });

  factory SupabaseDiarioAlimentare.fromJson(Map<String, dynamic> json) =>
      _$SupabaseDiarioAlimentareFromJson(json);

  Map<String, dynamic> toJson() => _$SupabaseDiarioAlimentareToJson(this);
}

/// Modello per la tabella 'posts' (community)
@JsonSerializable()
class SupabasePost {
  final int? id;
  @JsonKey(name: 'user_id')
  final String userId;
  final String contenuto;
  @JsonKey(name: 'immagine_url')
  final String? immagineUrl;
  @JsonKey(name: 'tipo_post')
  final String? tipoPost;
  final Map<String, dynamic>? metadata;
  @JsonKey(name: 'likes_count')
  final int? likesCount;
  @JsonKey(name: 'comments_count')
  final int? commentsCount;
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;
  
  // Dati del profilo dell'autore (join)
  final SupabaseProfile? profile;

  const SupabasePost({
    this.id,
    required this.userId,
    required this.contenuto,
    this.immagineUrl,
    this.tipoPost,
    this.metadata,
    this.likesCount,
    this.commentsCount,
    this.createdAt,
    this.profile,
  });

  factory SupabasePost.fromJson(Map<String, dynamic> json) =>
      _$SupabasePostFromJson(json);

  Map<String, dynamic> toJson() => _$SupabasePostToJson(this);
}

/// Modello per la tabella 'piani_dietetici' (piani generati)
@JsonSerializable()
class SupabasePianoDietetico {
  final int? id;
  @JsonKey(name: 'user_id')
  final String userId;
  final String nome;
  final String? descrizione;
  @JsonKey(name: 'piano_completo')
  final Map<String, dynamic> pianoCompleto;
  @JsonKey(name: 'calorie_target')
  final int calorieTarget;
  @JsonKey(name: 'durata_giorni')
  final int durataGiorni;
  @JsonKey(name: 'tipo_piano')
  final String tipoPiano;
  @JsonKey(name: 'is_attivo')
  final bool? isAttivo;
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;

  const SupabasePianoDietetico({
    this.id,
    required this.userId,
    required this.nome,
    this.descrizione,
    required this.pianoCompleto,
    required this.calorieTarget,
    required this.durataGiorni,
    required this.tipoPiano,
    this.isAttivo,
    this.createdAt,
    this.updatedAt,
  });

  factory SupabasePianoDietetico.fromJson(Map<String, dynamic> json) =>
      _$SupabasePianoDieteticoFromJson(json);

  Map<String, dynamic> toJson() => _$SupabasePianoDieteticoToJson(this);
}

/// Modello per la tabella 'progressi_utente' (tracking progressi)
@JsonSerializable()
class SupabaseProgressoUtente {
  final int? id;
  @JsonKey(name: 'user_id')
  final String userId;
  final DateTime data;
  @JsonKey(name: 'peso_kg')
  final double? pesoKg;
  @JsonKey(name: 'grasso_corporeo_percentuale')
  final double? grassoCorporeoPercentuale;
  @JsonKey(name: 'massa_magra_kg')
  final double? massaMagraKg;
  final Map<String, dynamic>? circonferenze;
  @JsonKey(name: 'foto_progresso_url')
  final String? fotoProgressoUrl;
  final String? note;
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;

  const SupabaseProgressoUtente({
    this.id,
    required this.userId,
    required this.data,
    this.pesoKg,
    this.grassoCorporeoPercentuale,
    this.massaMagraKg,
    this.circonferenze,
    this.fotoProgressoUrl,
    this.note,
    this.createdAt,
  });

  factory SupabaseProgressoUtente.fromJson(Map<String, dynamic> json) =>
      _$SupabaseProgressoUtenteFromJson(json);

  Map<String, dynamic> toJson() => _$SupabaseProgressoUtenteToJson(this);
}

/// Modello per la tabella 'welljourney_progress' (progressi WellJourney)
@JsonSerializable()
class SupabaseWellJourneyProgress {
  final int? id;
  @JsonKey(name: 'user_id')
  final String userId;
  @JsonKey(name: 'pathway_id')
  final String pathwayId;
  @JsonKey(name: 'module_id')
  final String moduleId;
  @JsonKey(name: 'quiz_score')
  final int? quizScore;
  @JsonKey(name: 'quiz_max_score')
  final int? quizMaxScore;
  final bool completato;
  @JsonKey(name: 'completed_at')
  final DateTime? completedAt;
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;

  const SupabaseWellJourneyProgress({
    this.id,
    required this.userId,
    required this.pathwayId,
    required this.moduleId,
    this.quizScore,
    this.quizMaxScore,
    required this.completato,
    this.completedAt,
    this.createdAt,
  });

  factory SupabaseWellJourneyProgress.fromJson(Map<String, dynamic> json) =>
      _$SupabaseWellJourneyProgressFromJson(json);

  Map<String, dynamic> toJson() => _$SupabaseWellJourneyProgressToJson(this);
}
