// Modelli Supabase essenziali

/// Modello per la tabella 'profiles' (dati pubblici utente)
class SupabaseProfile {
  final String id;
  final String? username;
  final String? nome;
  final String? fotoProfiloUrl;
  final DateTime? createdAt;

  const SupabaseProfile({
    required this.id,
    this.username,
    this.nome,
    this.fotoProfiloUrl,
    this.createdAt,
  });

  factory SupabaseProfile.fromJson(Map<String, dynamic> json) {
    return SupabaseProfile(
      id: json['id'] as String,
      username: json['username'] as String?,
      nome: json['nome'] as String?,
      fotoProfiloUrl: json['foto_profilo_url'] as String?,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'nome': nome,
      'foto_profilo_url': fotoProfiloUrl,
      'created_at': createdAt?.toIso8601String(),
    };
  }
}

/// Modello per la tabella 'dati_utente' (dati privati nutrizione)
class SupabaseDatiUtente {
  final String id;
  final String? obiettivo;
  final String? sesso;
  final int? eta;
  final int? altezzaCm;
  final double? pesoKg;
  final String? livelloAttivita;
  final List<String>? allergieAlimentari;
  final List<String>? preferenzeDietetiche;
  final Map<String, dynamic>? profiloUltraDettagliato;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const SupabaseDatiUtente({
    required this.id,
    this.obiettivo,
    this.sesso,
    this.eta,
    this.altezzaCm,
    this.pesoKg,
    this.livelloAttivita,
    this.allergieAlimentari,
    this.preferenzeDietetiche,
    this.profiloUltraDettagliato,
    this.createdAt,
    this.updatedAt,
  });

  factory SupabaseDatiUtente.fromJson(Map<String, dynamic> json) {
    return SupabaseDatiUtente(
      id: json['id'] as String,
      obiettivo: json['obiettivo'] as String?,
      sesso: json['sesso'] as String?,
      eta: json['eta'] as int?,
      altezzaCm: json['altezza_cm'] as int?,
      pesoKg: (json['peso_kg'] as num?)?.toDouble(),
      livelloAttivita: json['livello_attivita'] as String?,
      allergieAlimentari: (json['allergie_alimentari'] as List?)?.cast<String>(),
      preferenzeDietetiche: (json['preferenze_dietetiche'] as List?)?.cast<String>(),
      profiloUltraDettagliato: json['profilo_ultra_dettagliato'] as Map<String, dynamic>?,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'obiettivo': obiettivo,
      'sesso': sesso,
      'eta': eta,
      'altezza_cm': altezzaCm,
      'peso_kg': pesoKg,
      'livello_attivita': livelloAttivita,
      'allergie_alimentari': allergieAlimentari,
      'preferenze_dietetiche': preferenzeDietetiche,
      'profilo_ultra_dettagliato': profiloUltraDettagliato,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}