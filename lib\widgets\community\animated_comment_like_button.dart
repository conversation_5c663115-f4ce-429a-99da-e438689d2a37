import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/community_comment.dart';
import '../../services/comment_service.dart';
import '../../theme/dr_staffilano_theme.dart';

/// Widget per il pulsante Like animato per i commenti
class AnimatedCommentLikeButton extends StatefulWidget {
  final CommunityComment comment;
  final VoidCallback? onLikeChanged;

  const AnimatedCommentLikeButton({
    super.key,
    required this.comment,
    this.onLikeChanged,
  });

  @override
  State<AnimatedCommentLikeButton> createState() => _AnimatedCommentLikeButtonState();
}

class _AnimatedCommentLikeButtonState extends State<AnimatedCommentLikeButton>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _bounceController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _bounceAnimation;
  
  bool _isLiked = false;
  int _likesCount = 0;
  bool _isAnimating = false;

  @override
  void initState() {
    super.initState();
    
    // Inizializza lo stato dal servizio
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final commentService = context.read<CommentService>();
      setState(() {
        _isLiked = commentService.isCommentLiked(widget.comment.id);
        _likesCount = commentService.getCommentLikesCount(widget.comment.id);
        
        // Se il conteggio è 0, usa quello del commento
        if (_likesCount == 0) {
          _likesCount = widget.comment.likesCount;
          commentService.initializeCommentLikesCount(widget.comment.id, widget.comment.likesCount);
        }
      });
    });

    // Animazione di scale per il feedback immediato
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));

    // Animazione di bounce per il like
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _bounceAnimation = Tween<double>(
      begin: 1.0,
      end: 1.4,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _bounceController.dispose();
    super.dispose();
  }

  /// Gestisce il tap sul pulsante like
  Future<void> _handleLikeTap() async {
    if (_isAnimating) return;

    setState(() {
      _isAnimating = true;
    });

    try {
      final commentService = context.read<CommentService>();
      
      // Toggle dello stato like usando il servizio
      final newLikedState = await commentService.toggleCommentLike(widget.comment.id, _likesCount);
      final newLikesCount = commentService.getCommentLikesCount(widget.comment.id);

      // Aggiorna immediatamente l'UI per feedback rapido
      setState(() {
        _isLiked = newLikedState;
        _likesCount = newLikesCount;
      });

      // Avvia le animazioni
      if (newLikedState) {
        // Like aggiunto - animazione bounce + scale
        _scaleController.forward().then((_) {
          _scaleController.reverse();
        });
        _bounceController.forward().then((_) {
          _bounceController.reverse();
        });
      } else {
        // Like rimosso - solo scale
        _scaleController.forward().then((_) {
          _scaleController.reverse();
        });
      }

      // Notifica il parent widget del cambiamento
      widget.onLikeChanged?.call();

    } catch (e) {
      // In caso di errore, ricarica lo stato dal servizio
      final commentService = context.read<CommentService>();
      setState(() {
        _isLiked = commentService.isCommentLiked(widget.comment.id);
        _likesCount = commentService.getCommentLikesCount(widget.comment.id);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Errore: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isAnimating = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: _handleLikeTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icona del cuore animata
            AnimatedBuilder(
              animation: Listenable.merge([_scaleAnimation, _bounceAnimation]),
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimation.value * (_isLiked ? _bounceAnimation.value : 1.0),
                  child: Icon(
                    _isLiked ? Icons.favorite : Icons.favorite_border,
                    size: 16,
                    color: _isLiked ? Colors.red : Colors.grey[600],
                  ),
                );
              },
            ),
            
            const SizedBox(width: 4),
            
            // Conteggio like
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: Text(
                _likesCount > 0 ? _likesCount.toString() : 'Mi piace',
                key: ValueKey(_likesCount),
                style: TextStyle(
                  fontSize: 12,
                  color: _isLiked ? Colors.red : Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Widget semplificato per il like dei commenti (senza animazioni complesse)
class SimpleCommentLikeButton extends StatelessWidget {
  final CommunityComment comment;
  final VoidCallback? onTap;

  const SimpleCommentLikeButton({
    super.key,
    required this.comment,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<CommentService>(
      builder: (context, commentService, child) {
        final isLiked = commentService.isCommentLiked(comment.id);
        final likesCount = commentService.getCommentLikesCount(comment.id);
        final displayCount = likesCount > 0 ? likesCount : comment.likesCount;
        
        return InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  isLiked ? Icons.favorite : Icons.favorite_border,
                  size: 16,
                  color: isLiked ? Colors.red : Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  displayCount > 0 ? displayCount.toString() : 'Mi piace',
                  style: TextStyle(
                    fontSize: 12,
                    color: isLiked ? Colors.red : Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
