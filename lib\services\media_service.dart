import 'dart:io';
import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image/image.dart' as img;
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'web_camera_service.dart' if (dart.library.io) 'web_camera_service_stub.dart';

/// Modello per rappresentare un file media selezionato
class MediaFile {
  final String id;
  final String name;
  final String path;
  final MediaType type;
  final int size;
  final DateTime createdAt;
  final Uint8List? thumbnailData;

  MediaFile({
    required this.id,
    required this.name,
    required this.path,
    required this.type,
    required this.size,
    required this.createdAt,
    this.thumbnailData,
  });

  bool get isImage => type == MediaType.image;
  bool get isVideo => type == MediaType.video;

  String get sizeFormatted {
    if (size < 1024) return '${size}B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)}KB';
    return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
  }
}

enum MediaType { image, video }

/// Servizio per gestire la selezione e elaborazione di file media (immagini e video dalla galleria, foto dalla fotocamera, registrazione video su mobile)
class MediaService {
  static final MediaService _instance = MediaService._internal();
  factory MediaService() => _instance;
  MediaService._internal();

  final ImagePicker _picker = ImagePicker();

  // Limiti di dimensione file (in bytes)
  static const int maxImageSize = 10 * 1024 * 1024; // 10MB
  static const int maxVideoSize = 100 * 1024 * 1024; // 100MB

  // Dimensioni thumbnail
  static const int thumbnailSize = 200;

  /// Seleziona una singola immagine dalla galleria
  Future<MediaFile?> pickSingleImage() async {
    try {
      debugPrint('🔵 pickSingleImage iniziato');
      final XFile? file = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (file == null) {
        debugPrint('🔵 pickSingleImage: selezione annullata');
        return null;
      }

      debugPrint('🔵 pickSingleImage: file selezionato ${file.path}');
      final result = await _processImageFile(file);

      if (result != null) {
        debugPrint('🔵 pickSingleImage: SUCCESS - MediaFile creato');
        debugPrint('🔵   - Nome: ${result.name}');
        debugPrint('🔵   - Path: ${result.path.substring(0, 50)}...');
        debugPrint('🔵   - Ha thumbnail: ${result.thumbnailData != null}');
      } else {
        debugPrint('🔵 pickSingleImage: FAILED - MediaFile null');
      }

      return result;
    } catch (e) {
      debugPrint('🔵 Errore nella selezione immagine: $e');
      return null;
    }
  }

  /// Seleziona multiple immagini dalla galleria con dialog di conferma
  Future<List<MediaFile>> pickMultipleImages({
    int maxImages = 10,
    Function(String)? onStatusUpdate,
  }) async {
    try {
      debugPrint('📷 pickMultipleImages iniziato (max: $maxImages)');
      final List<MediaFile> mediaFiles = [];

      onStatusUpdate?.call('Seleziona la prima immagine...');

      // Prima selezione
      debugPrint('📷 Aprendo picker per prima immagine...');
      final XFile? firstFile = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (firstFile == null) {
        debugPrint('📷 Prima selezione annullata dall\'utente - ritornando lista vuota');
        onStatusUpdate?.call('Selezione annullata');
        return [];
      }

      debugPrint('📷 Prima immagine selezionata: ${firstFile.path}');
      final firstMediaFile = await _processImageFile(firstFile);
      if (firstMediaFile != null) {
        mediaFiles.add(firstMediaFile);
        debugPrint('📷 Prima immagine elaborata e aggiunta: ${firstMediaFile.name}');
        debugPrint('📷 - ID: ${firstMediaFile.id}');
        debugPrint('📷 - Tipo: ${firstMediaFile.type}');
        debugPrint('📷 - Dimensione: ${firstMediaFile.sizeFormatted}');
        debugPrint('📷 - Ha thumbnail: ${firstMediaFile.thumbnailData != null}');
        onStatusUpdate?.call('Prima immagine aggiunta! (${mediaFiles.length}/$maxImages)');
      } else {
        debugPrint('📷 Errore nell\'elaborazione della prima immagine');
      }

      // Continua a selezionare fino al limite o fino a quando l'utente annulla
      for (int i = 1; i < maxImages; i++) {
        debugPrint('📷 Ciclo $i: aprendo picker per immagine aggiuntiva...');
        onStatusUpdate?.call('Seleziona un\'altra immagine o annulla per terminare... (${mediaFiles.length}/$maxImages)');

        final XFile? file = await _picker.pickImage(
          source: ImageSource.gallery,
          maxWidth: 1920,
          maxHeight: 1920,
          imageQuality: 85,
        );

        if (file == null) {
          debugPrint('📷 Selezione $i annullata dall\'utente, terminando...');
          break; // L'utente ha annullato
        }

        debugPrint('📷 Immagine $i selezionata: ${file.path}');
        final mediaFile = await _processImageFile(file);
        if (mediaFile != null) {
          mediaFiles.add(mediaFile);
          debugPrint('📷 Immagine $i elaborata e aggiunta: ${mediaFile.name}');
          onStatusUpdate?.call('Immagine aggiunta! (${mediaFiles.length}/$maxImages)');
        } else {
          debugPrint('📷 Errore nell\'elaborazione dell\'immagine $i');
        }
      }

      debugPrint('📷 Selezione multipla completata: ${mediaFiles.length} immagini totali');
      for (int i = 0; i < mediaFiles.length; i++) {
        debugPrint('📷 Risultato $i: ${mediaFiles[i].name} (${mediaFiles[i].type})');
      }

      // Non chiamare onStatusUpdate qui per evitare interferenze con la chiusura del dialog
      debugPrint('📷 Processo di selezione multipla terminato con successo');
      return mediaFiles;
    } catch (e) {
      debugPrint('Errore nella selezione multiple immagini: $e');
      onStatusUpdate?.call('Errore nella selezione: $e');
      return [];
    }
  }

  /// Seleziona un video dalla galleria
  Future<MediaFile?> pickVideo() async {
    try {
      final XFile? file = await _picker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: const Duration(minutes: 5), // Limite 5 minuti
      );

      if (file == null) return null;

      return await _processVideoFile(file);
    } catch (e) {
      debugPrint('Errore nella selezione video: $e');
      return null;
    }
  }

  /// Scatta una foto con la fotocamera
  Future<MediaFile?> takePhoto({BuildContext? context}) async {
    try {
      // Su web, usa il servizio fotocamera web nativo
      if (kIsWeb && context != null) {
        final webCameraService = WebCameraService();
        if (webCameraService.isSupported) {
          return await webCameraService.capturePhoto(context);
        } else {
          throw Exception('Fotocamera non supportata su questo browser');
        }
      }

      // Su mobile, usa image_picker
      final XFile? file = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (file == null) return null;

      return await _processImageFile(file);
    } catch (e) {
      debugPrint('Errore nello scatto foto: $e');
      rethrow; // Rilancia l'errore per gestirlo nel chiamante
    }
  }

  /// Registra un video con la fotocamera (solo mobile)
  Future<MediaFile?> recordVideo({BuildContext? context}) async {
    try {
      // Su web, la registrazione video non è supportata
      if (kIsWeb) {
        throw Exception('Registrazione video non supportata su web. Usa la galleria per selezionare un video.');
      }

      // Su mobile, usa image_picker
      final XFile? file = await _picker.pickVideo(
        source: ImageSource.camera,
        maxDuration: const Duration(minutes: 5),
      );

      if (file == null) return null;

      return await _processVideoFile(file);
    } catch (e) {
      debugPrint('Errore nella registrazione video: $e');
      rethrow; // Rilancia l'errore per gestirlo nel chiamante
    }
  }

  /// Elabora un file immagine
  Future<MediaFile?> _processImageFile(XFile file) async {
    try {
      debugPrint('🔧 _processImageFile per ${file.path}');
      final bytes = await file.readAsBytes();
      debugPrint('🔧 Bytes letti: ${bytes.length}');

      // Verifica dimensione file
      if (bytes.length > maxImageSize) {
        throw Exception('Immagine troppo grande. Massimo ${maxImageSize ~/ (1024 * 1024)}MB');
      }

      // Genera thumbnail
      final thumbnailData = await _generateImageThumbnail(bytes);
      debugPrint('🔧 Thumbnail generato: ${thumbnailData != null ? thumbnailData!.length : 0} bytes');

      // Su web, converti blob URL in data URL per compatibilità
      String finalPath = file.path;
      if (kIsWeb && file.path.startsWith('blob:')) {
        debugPrint('🔧 Convertendo blob URL in data URL...');
        final base64String = base64Encode(bytes);
        finalPath = 'data:image/jpeg;base64,$base64String';
        debugPrint('🔧 Data URL creato: ${finalPath.substring(0, 50)}...');
      }

      final mediaFile = MediaFile(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: path.basename(file.path),
        path: finalPath,
        type: MediaType.image,
        size: bytes.length,
        createdAt: DateTime.now(),
        thumbnailData: thumbnailData,
      );

      debugPrint('🔧 MediaFile creato:');
      debugPrint('🔧   - ID: ${mediaFile.id}');
      debugPrint('🔧   - Nome: ${mediaFile.name}');
      debugPrint('🔧   - Path: ${mediaFile.path.substring(0, 50)}...');
      debugPrint('🔧   - Ha thumbnail: ${mediaFile.thumbnailData != null}');

      return mediaFile;
    } catch (e) {
      debugPrint('Errore nell\'elaborazione immagine: $e');
      return null;
    }
  }

  /// Elabora un file video
  Future<MediaFile?> _processVideoFile(XFile file) async {
    try {
      final bytes = await file.readAsBytes();

      // Verifica dimensione file
      if (bytes.length > maxVideoSize) {
        throw Exception('Video troppo grande. Massimo ${maxVideoSize ~/ (1024 * 1024)}MB');
      }

      // Per i video, generiamo un thumbnail placeholder
      // In una implementazione completa, si potrebbe usare video_thumbnail package
      final thumbnailData = await _generateVideoThumbnail();

      return MediaFile(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: path.basename(file.path),
        path: file.path,
        type: MediaType.video,
        size: bytes.length,
        createdAt: DateTime.now(),
        thumbnailData: thumbnailData,
      );
    } catch (e) {
      debugPrint('Errore nell\'elaborazione video: $e');
      return null;
    }
  }

  /// Genera thumbnail per immagine
  Future<Uint8List?> _generateImageThumbnail(Uint8List imageBytes) async {
    try {
      final image = img.decodeImage(imageBytes);
      if (image == null) return null;

      final thumbnail = img.copyResize(
        image,
        width: thumbnailSize,
        height: thumbnailSize,
        interpolation: img.Interpolation.average,
      );

      return Uint8List.fromList(img.encodeJpg(thumbnail, quality: 80));
    } catch (e) {
      debugPrint('Errore nella generazione thumbnail immagine: $e');
      return null;
    }
  }

  /// Genera thumbnail placeholder per video
  Future<Uint8List?> _generateVideoThumbnail() async {
    try {
      // Crea un'immagine placeholder per i video
      final image = img.Image(width: thumbnailSize, height: thumbnailSize);
      img.fill(image, color: img.ColorRgb8(100, 100, 100));

      // Aggiungi icona play al centro
      final centerX = thumbnailSize ~/ 2;
      final centerY = thumbnailSize ~/ 2;
      final playSize = thumbnailSize ~/ 4;

      img.fillCircle(
        image,
        x: centerX,
        y: centerY,
        radius: playSize,
        color: img.ColorRgb8(255, 255, 255),
      );

      return Uint8List.fromList(img.encodeJpg(image, quality: 80));
    } catch (e) {
      debugPrint('Errore nella generazione thumbnail video: $e');
      return null;
    }
  }

  /// Elimina un file media temporaneo
  Future<bool> deleteMediaFile(MediaFile mediaFile) async {
    try {
      final file = File(mediaFile.path);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Errore nell\'eliminazione file: $e');
      return false;
    }
  }

  /// Comprimi un'immagine se necessario
  Future<Uint8List?> compressImage(Uint8List imageBytes, {int quality = 85}) async {
    try {
      final image = img.decodeImage(imageBytes);
      if (image == null) return null;

      return Uint8List.fromList(img.encodeJpg(image, quality: quality));
    } catch (e) {
      debugPrint('Errore nella compressione immagine: $e');
      return null;
    }
  }
}
