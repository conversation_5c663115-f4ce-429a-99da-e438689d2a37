import 'dart:async';
import 'dart:math';
import '../models/community_user.dart';
import '../models/community_post.dart';
import '../models/community_group.dart';
import '../models/community_challenge.dart';

/// Service per gestire la Staffilano InnerCircle™
class CommunityService {
  static CommunityService? _instance;
  static CommunityService get instance => _instance ??= CommunityService._();
  CommunityService._();

  // Simulazione database locale
  final Map<String, CommunityUser> _users = {};
  final Map<String, CommunityPost> _posts = {};
  final Map<String, CommunityGroup> _groups = {};
  final Map<String, CommunityChallenge> _challenges = {};
  final Map<String, List<String>> _userGroups = {}; // userId -> groupIds
  final Map<String, List<String>> _userChallenges = {}; // userId -> challengeIds
  final Map<String, List<String>> _userFollowing = {}; // userId -> followingIds
  final Map<String, List<String>> _postLikes = {}; // postId -> userIds
  final Map<String, List<PostComment>> _postComments = {}; // postId -> comments

  bool _isInitialized = false;

  /// Inizializza il service con dati di esempio
  Future<void> initialize() async {
    if (_isInitialized) return;

    await _createSampleData();
    _isInitialized = true;
  }

  /// Crea dati di esempio per la community
  Future<void> _createSampleData() async {
    // Crea utenti di esempio
    final users = [
      CommunityUser.create(
        username: 'dr_staffilano',
        displayName: 'Dr. Giovanni Staffilano',
        bio: 'Cardiologo con 25+ anni di esperienza. Direttore UTIC ASL Teramo. La vera prevenzione è la ricerca della felicità.',
        avatarUrl: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?q=80&w=200',
        interests: ['cardiologia', 'prevenzione', 'nutrizione', 'benessere'],
      ).copyWith(
        membershipLevel: MembershipLevel.ambassador,
        communityPoints: 15000,
        isVerified: true,
        isMentor: true,
        totalPosts: 45,
        totalLikes: 1250,
        followersCount: 2847,
      ),
      CommunityUser.create(
        username: 'maria_nutrizionista',
        displayName: 'Maria Rossi',
        bio: 'Nutrizionista specializzata in dieta mediterranea. Aiuto le persone a trovare il loro equilibrio alimentare.',
        avatarUrl: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?q=80&w=200',
        interests: ['nutrizione', 'dieta mediterranea', 'ricette'],
      ).copyWith(
        membershipLevel: MembershipLevel.vip,
        communityPoints: 7500,
        isMentor: true,
        totalPosts: 32,
        totalLikes: 890,
        followersCount: 1456,
      ),
      CommunityUser.create(
        username: 'marco_fitness',
        displayName: 'Marco Bianchi',
        bio: 'Personal trainer e appassionato di sport nutrition. Credo nel potere dell\'allenamento per la salute del cuore.',
        avatarUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=200',
        interests: ['fitness', 'sport nutrition', 'allenamento'],
      ).copyWith(
        membershipLevel: MembershipLevel.premium,
        communityPoints: 3200,
        totalPosts: 28,
        totalLikes: 654,
        followersCount: 987,
      ),
      CommunityUser.create(
        username: 'anna_chef',
        displayName: 'Anna Verdi',
        bio: 'Chef specializzata in cucina salutare. Creo ricette gustose che fanno bene al cuore e all\'anima.',
        avatarUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=200',
        interests: ['cucina', 'ricette salutari', 'ingredienti naturali'],
      ).copyWith(
        membershipLevel: MembershipLevel.premium,
        communityPoints: 2800,
        totalPosts: 24,
        totalLikes: 567,
        followersCount: 743,
      ),
      CommunityUser.create(
        username: 'luca_principiante',
        displayName: 'Luca Neri',
        bio: 'Nuovo nel mondo della nutrizione. Sto imparando a prendermi cura della mia salute cardiovascolare.',
        avatarUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=200',
        interests: ['principiante', 'salute', 'apprendimento'],
      ).copyWith(
        membershipLevel: MembershipLevel.basic,
        communityPoints: 450,
        totalPosts: 8,
        totalLikes: 89,
        followersCount: 156,
      ),
    ];

    for (final user in users) {
      _users[user.id] = user;
    }

    // Crea relazioni di amicizia
    _createFriendships(users);

    // Crea gruppi di esempio
    await _createSampleGroups();

    // Crea sfide di esempio
    await _createSampleChallenges();

    // Crea post di esempio
    await _createSamplePosts();
  }

  /// Crea relazioni di amicizia tra gli utenti
  void _createFriendships(List<CommunityUser> users) {
    final drStaffilano = users.firstWhere((u) => u.username == 'dr_staffilano');
    final maria = users.firstWhere((u) => u.username == 'maria_nutrizionista');
    final marco = users.firstWhere((u) => u.username == 'marco_fitness');
    final anna = users.firstWhere((u) => u.username == 'anna_chef');
    final luca = users.firstWhere((u) => u.username == 'luca_principiante');

    // Dr. Staffilano è amico di tutti (è il mentore della community)
    _users[drStaffilano.id] = drStaffilano.copyWith(
      friendIds: [maria.id, marco.id, anna.id, luca.id],
    );

    // Maria è amica di Dr. Staffilano, Anna e Luca
    _users[maria.id] = maria.copyWith(
      friendIds: [drStaffilano.id, anna.id, luca.id],
    );

    // Marco è amico di Dr. Staffilano e Luca
    _users[marco.id] = marco.copyWith(
      friendIds: [drStaffilano.id, luca.id],
    );

    // Anna è amica di Dr. Staffilano e Maria
    _users[anna.id] = anna.copyWith(
      friendIds: [drStaffilano.id, maria.id],
    );

    // Luca è amico di tutti (nuovo utente che sta facendo amicizie)
    _users[luca.id] = luca.copyWith(
      friendIds: [drStaffilano.id, maria.id, marco.id],
    );
  }

  /// Crea gruppi di esempio
  Future<void> _createSampleGroups() async {
    final drStaffilano = _users.values.firstWhere((u) => u.username == 'dr_staffilano');
    final maria = _users.values.firstWhere((u) => u.username == 'maria_nutrizionista');
    final marco = _users.values.firstWhere((u) => u.username == 'marco_fitness');

    final groups = [
      CommunityGroup.create(
        name: 'Cuore Sano, Vita Felice',
        description: 'Il gruppo ufficiale del Dr. Staffilano per la prevenzione cardiovascolare e il benessere del cuore.',
        imageUrl: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?q=80&w=500',
        type: GroupType.official,
        category: GroupCategory.heartHealth,
        creatorId: drStaffilano.id,
        moderatorIds: [maria.id],
        tags: ['ufficiale', 'cardiologia', 'prevenzione'],
      ).copyWith(
        membersCount: 1247,
        postsCount: 89,
        isFeatured: true,
      ),
      CommunityGroup.create(
        name: 'Dieta Mediterranea Autentica',
        description: 'Scopriamo insieme i segreti della vera dieta mediterranea per la salute e il piacere.',
        imageUrl: 'https://images.unsplash.com/photo-1498837167922-ddd27525d352?q=80&w=500',
        type: GroupType.public,
        category: GroupCategory.dietaMediterranea,
        creatorId: maria.id,
        tags: ['dieta mediterranea', 'tradizione', 'salute'],
      ).copyWith(
        membersCount: 856,
        postsCount: 134,
        isFeatured: true,
      ),
      CommunityGroup.create(
        name: 'Sport Nutrition Pro',
        description: 'Nutrizione sportiva per atleti e appassionati di fitness. Massimizza le tue performance!',
        imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=500',
        type: GroupType.public,
        category: GroupCategory.sportNutrition,
        creatorId: marco.id,
        tags: ['sport', 'performance', 'allenamento'],
      ).copyWith(
        membersCount: 634,
        postsCount: 78,
      ),
      CommunityGroup.create(
        name: 'Ricette del Cuore',
        description: 'Condividiamo ricette gustose e salutari per prenderci cura del nostro cuore.',
        imageUrl: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?q=80&w=500',
        type: GroupType.public,
        category: GroupCategory.recipes,
        creatorId: _users.values.firstWhere((u) => u.username == 'anna_chef').id,
        tags: ['ricette', 'cucina', 'salutare'],
      ).copyWith(
        membersCount: 423,
        postsCount: 156,
      ),
      CommunityGroup.create(
        name: 'Principianti Benvenuti',
        description: 'Un gruppo di supporto per chi inizia il proprio percorso verso una vita più sana.',
        imageUrl: 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?q=80&w=500',
        type: GroupType.public,
        category: GroupCategory.beginners,
        creatorId: maria.id,
        tags: ['principianti', 'supporto', 'apprendimento'],
      ).copyWith(
        membersCount: 789,
        postsCount: 67,
      ),
    ];

    for (final group in groups) {
      _groups[group.id] = group;
    }
  }

  /// Crea sfide di esempio
  Future<void> _createSampleChallenges() async {
    final drStaffilano = _users.values.firstWhere((u) => u.username == 'dr_staffilano');
    final maria = _users.values.firstWhere((u) => u.username == 'maria_nutrizionista');

    final now = DateTime.now();
    final challenges = [
      CommunityChallenge.create(
        title: '30 Giorni di Dieta Mediterranea',
        description: 'Segui i principi della dieta mediterranea per 30 giorni e scopri i benefici per il tuo cuore.',
        imageUrl: 'https://images.unsplash.com/photo-1498837167922-ddd27525d352?q=80&w=500',
        type: ChallengeType.monthly,
        category: ChallengeCategory.nutrition,
        difficulty: ChallengeDifficulty.medium,
        startDate: now.subtract(const Duration(days: 10)),
        endDate: now.add(const Duration(days: 20)),
        creatorId: drStaffilano.id,
        requirements: {
          'daily_vegetables': 5,
          'weekly_fish': 2,
          'olive_oil_daily': true,
        },
        rewards: {
          'points': 500,
          'badge': 'mediterranean_master',
          'title': 'Maestro Mediterraneo',
        },
        tags: ['dieta mediterranea', 'nutrizione', 'cuore'],
      ).copyWith(
        currentParticipants: 234,
        completedCount: 67,
        isFeatured: true,
      ),
      CommunityChallenge.create(
        title: 'Idratazione Perfetta',
        description: 'Bevi almeno 8 bicchieri d\'acqua al giorno per una settimana.',
        imageUrl: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?q=80&w=500',
        type: ChallengeType.weekly,
        category: ChallengeCategory.hydration,
        difficulty: ChallengeDifficulty.easy,
        startDate: now.subtract(const Duration(days: 2)),
        endDate: now.add(const Duration(days: 5)),
        creatorId: maria.id,
        requirements: {
          'daily_water_glasses': 8,
        },
        rewards: {
          'points': 100,
          'badge': 'hydration_hero',
        },
        tags: ['idratazione', 'salute', 'benessere'],
      ).copyWith(
        currentParticipants: 456,
        completedCount: 123,
      ),
      CommunityChallenge.create(
        title: 'Passi del Cuore',
        description: 'Cammina almeno 10.000 passi al giorno per migliorare la salute cardiovascolare.',
        imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=500',
        type: ChallengeType.daily,
        category: ChallengeCategory.exercise,
        difficulty: ChallengeDifficulty.medium,
        startDate: now,
        endDate: now.add(const Duration(days: 1)),
        creatorId: _users.values.firstWhere((u) => u.username == 'marco_fitness').id,
        requirements: {
          'daily_steps': 10000,
        },
        rewards: {
          'points': 50,
          'badge': 'daily_walker',
        },
        tags: ['esercizio', 'camminata', 'cuore'],
      ).copyWith(
        currentParticipants: 789,
        completedCount: 234,
      ),
    ];

    for (final challenge in challenges) {
      _challenges[challenge.id] = challenge;
    }
  }

  /// Crea post di esempio
  Future<void> _createSamplePosts() async {
    final drStaffilano = _users.values.firstWhere((u) => u.username == 'dr_staffilano');
    final maria = _users.values.firstWhere((u) => u.username == 'maria_nutrizionista');
    final marco = _users.values.firstWhere((u) => u.username == 'marco_fitness');
    final anna = _users.values.firstWhere((u) => u.username == 'anna_chef');
    final luca = _users.values.firstWhere((u) => u.username == 'luca_principiante');

    final posts = [
      CommunityPost.create(
        authorId: drStaffilano.id,
        type: PostType.tip,
        content: '💡 La vera prevenzione cardiovascolare inizia dalla tavola! Ricordate: non esistono cibi "cattivi", ma solo porzioni e frequenze sbagliate. La dieta mediterranea ci insegna l\'equilibrio perfetto tra gusto e salute. #PrevenzioneCardiovascolare #DietaMediterranea',
        tags: ['prevenzione', 'cardiologia', 'dieta mediterranea'],
      ).copyWith(
        author: drStaffilano,
        likesCount: 127,
        commentsCount: 23,
        sharesCount: 45,
        isVerified: true,
      ),
      CommunityPost.create(
        authorId: maria.id,
        type: PostType.recipe,
        content: '🍅 Ricetta del giorno: Pasta integrale con pomodorini, basilico e olio EVO. Semplice, gustosa e ricca di antiossidanti! Gli omega-3 dell\'olio extravergine proteggono il nostro cuore. Chi la prova stasera? 👨‍🍳',
        imageUrls: ['https://images.unsplash.com/photo-1551183053-bf91a1d81141?q=80&w=500'],
        tags: ['ricetta', 'pasta', 'mediterranea', 'antiossidanti'],
      ).copyWith(
        author: maria,
        likesCount: 89,
        commentsCount: 15,
        sharesCount: 28,
      ),
      CommunityPost.create(
        authorId: marco.id,
        type: PostType.progress,
        content: '💪 Settimana 4 del mio programma cardio! I risultati parlano chiaro: pressione stabile, frequenza cardiaca a riposo migliorata del 15%. L\'esercizio è davvero la medicina migliore! Chi si allena con me domani? 🏃‍♂️',
        attachedData: {
          'workout_type': 'cardio',
          'duration_minutes': 45,
          'heart_rate_avg': 145,
          'calories_burned': 420,
        },
        tags: ['fitness', 'cardio', 'progresso', 'salute'],
      ).copyWith(
        author: marco,
        likesCount: 76,
        commentsCount: 12,
        sharesCount: 18,
      ),
      CommunityPost.create(
        authorId: anna.id,
        type: PostType.image,
        content: '🌈 Colori in tavola = salute nel piatto! Ogni colore apporta nutrienti diversi: rosso (licopene), verde (clorofilla), arancione (beta-carotene). La natura ci offre tutto quello di cui abbiamo bisogno! 🥗',
        imageUrls: [
          'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?q=80&w=500',
          'https://images.unsplash.com/photo-1540420773420-3366772f4999?q=80&w=500',
        ],
        tags: ['nutrizione', 'colori', 'verdure', 'antiossidanti'],
      ).copyWith(
        author: anna,
        likesCount: 94,
        commentsCount: 18,
        sharesCount: 31,
      ),
      CommunityPost.create(
        authorId: luca.id,
        type: PostType.question,
        content: '❓ Ciao a tutti! Sono nuovo qui e sto iniziando il mio percorso verso una vita più sana. Qualcuno può consigliarmi come iniziare con la dieta mediterranea? Quali sono i primi passi? Grazie! 🙏',
        tags: ['principiante', 'consigli', 'dieta mediterranea', 'aiuto'],
      ).copyWith(
        author: luca,
        likesCount: 34,
        commentsCount: 28,
        sharesCount: 5,
      ),
      CommunityPost.create(
        authorId: drStaffilano.id,
        type: PostType.achievement,
        content: '🏆 Oggi celebriamo un traguardo importante: la nostra community ha raggiunto 10.000 membri! Insieme stiamo costruendo una rete di supporto per la salute cardiovascolare. Grazie a tutti per essere parte di questa famiglia! ❤️',
        tags: ['traguardo', 'community', 'ringraziamenti', 'famiglia'],
      ).copyWith(
        author: drStaffilano,
        likesCount: 234,
        commentsCount: 67,
        sharesCount: 89,
        isPinned: true,
        isVerified: true,
      ),
      // Post con utenti taggati per testare la funzionalità
      CommunityPost.create(
        authorId: maria.id,
        type: PostType.text,
        content: '🍽️ Che bella serata di cucina insieme! Abbiamo preparato un menu completamente mediterraneo e il risultato è stato fantastico. Grazie per la compagnia e per aver condiviso le vostre ricette! 👨‍🍳✨',
        taggedUserIds: [anna.id, marco.id],
        taggedUsers: [anna, marco],
        tags: ['cucina', 'amici', 'dieta mediterranea'],
      ).copyWith(
        author: maria,
        likesCount: 45,
        commentsCount: 12,
        sharesCount: 8,
      ),
      CommunityPost.create(
        authorId: anna.id,
        type: PostType.image,
        content: '📸 Ecco il risultato della nostra sessione di meal prep! Preparare i pasti in anticipo è la chiave per mantenere una dieta sana anche quando si ha poco tempo. Grazie per i consigli preziosi! 🥗',
        imageUrls: ['https://images.unsplash.com/photo-1546069901-ba9599a7e63c?q=80&w=500'],
        taggedUserIds: [maria.id, luca.id],
        taggedUsers: [maria, luca],
        tags: ['meal prep', 'organizzazione', 'dieta sana'],
      ).copyWith(
        author: anna,
        likesCount: 67,
        commentsCount: 18,
        sharesCount: 15,
      ),
      // Post con posizione per testare la funzionalità
      CommunityPost.create(
        authorId: marco.id,
        type: PostType.progress,
        content: '🏃‍♂️ Fantastica corsa mattutina! Niente di meglio che iniziare la giornata con una bella dose di endorfine. Il percorso lungo il mare è sempre il mio preferito per l\'allenamento cardio! 💪',
        location: {
          'name': 'Lungomare di Pescara',
          'address': 'Riviera Nord, Pescara, PE',
          'latitude': 42.4584,
          'longitude': 14.2103,
        },
        tags: ['running', 'cardio', 'mare', 'allenamento'],
      ).copyWith(
        author: marco,
        likesCount: 52,
        commentsCount: 14,
        sharesCount: 9,
      ),
      CommunityPost.create(
        authorId: maria.id,
        type: PostType.recipe,
        content: '🍝 Appena finito di cucinare questa deliziosa pasta con le vongole! La ricetta tradizionale abruzzese che mia nonna mi ha insegnato. Il segreto è nel soffritto di aglio e prezzemolo! Chi vuole la ricetta? 👩‍🍳',
        imageUrls: ['https://images.unsplash.com/photo-1621996346565-e3dbc353d2e5?q=80&w=500'],
        location: {
          'name': 'Ristorante Da Nonna Rosa',
          'address': 'Via del Mare 15, Giulianova, TE',
          'latitude': 42.7539,
          'longitude': 13.9596,
        },
        tags: ['pasta', 'vongole', 'tradizione', 'abruzzo'],
      ).copyWith(
        author: maria,
        likesCount: 89,
        commentsCount: 23,
        sharesCount: 17,
      ),
      CommunityPost.create(
        authorId: drStaffilano.id,
        type: PostType.tip,
        content: '🏥 Oggi ho tenuto una conferenza sulla prevenzione cardiovascolare presso l\'ospedale. È sempre emozionante condividere le mie conoscenze con i colleghi e vedere l\'interesse per la cardiologia preventiva! La salute del cuore è nelle nostre mani. ❤️',
        location: {
          'name': 'Ospedale Giuseppe Mazzini',
          'address': 'Piazza Italia 1, Teramo, TE',
          'latitude': 42.6589,
          'longitude': 13.7037,
        },
        tags: ['cardiologia', 'prevenzione', 'conferenza', 'ospedale'],
      ).copyWith(
        author: drStaffilano,
        likesCount: 156,
        commentsCount: 34,
        sharesCount: 28,
        isVerified: true,
      ),
      // Post con diverse impostazioni di privacy per testare il sistema
      CommunityPost.create(
        authorId: maria.id,
        type: PostType.text,
        content: '👥 Questo è un post solo per i miei amici! Sto condividendo alcuni consigli nutrizionali personali che ho imparato durante la mia formazione. Spero possano essere utili! 💚',
        privacy: PostPrivacy.friends,
        tags: ['amici', 'consigli', 'nutrizione'],
      ).copyWith(
        author: maria,
        likesCount: 23,
        commentsCount: 8,
        sharesCount: 3,
      ),
      CommunityPost.create(
        authorId: luca.id,
        type: PostType.question,
        content: '🔒 Nota personale: devo ricordare di chiedere al Dr. Staffilano informazioni sui valori del colesterolo durante la prossima visita. I miei ultimi esami mostrano alcuni valori che vorrei approfondire.',
        privacy: PostPrivacy.private,
        tags: ['personale', 'salute', 'colesterolo'],
      ).copyWith(
        author: luca,
        likesCount: 0,
        commentsCount: 0,
        sharesCount: 0,
      ),
      CommunityPost.create(
        authorId: anna.id,
        type: PostType.recipe,
        content: '👥 Ricetta segreta della famiglia per gli amici: Pasta e fagioli della nonna! Questa ricetta è stata tramandata per generazioni e la condivido solo con voi. Gli ingredienti sono semplici ma il risultato è straordinario! 🍝',
        imageUrls: ['https://images.unsplash.com/photo-1551183053-bf91a1d81141?q=80&w=500'],
        privacy: PostPrivacy.friends,
        tags: ['ricetta', 'famiglia', 'tradizione'],
      ).copyWith(
        author: anna,
        likesCount: 34,
        commentsCount: 12,
        sharesCount: 6,
      ),
      CommunityPost.create(
        authorId: marco.id,
        type: PostType.progress,
        content: '🔒 Promemoria personale: oggi ho completato 45 minuti di cardio. Frequenza cardiaca media: 152 bpm. Devo aumentare gradualmente l\'intensità la prossima settimana. Obiettivo: raggiungere 160 bpm in sicurezza.',
        privacy: PostPrivacy.private,
        attachedData: {
          'workout_duration': 45,
          'avg_heart_rate': 152,
          'calories_burned': 380,
        },
        tags: ['allenamento', 'personale', 'cardio'],
      ).copyWith(
        author: marco,
        likesCount: 0,
        commentsCount: 0,
        sharesCount: 0,
      ),
    ];

    for (final post in posts) {
      _posts[post.id] = post;
    }

    // Crea alcuni commenti di esempio
    _createSampleComments();
  }

  /// Crea commenti di esempio
  void _createSampleComments() {
    final posts = _posts.values.toList();
    final users = _users.values.toList();

    for (final post in posts.take(3)) {
      final comments = <PostComment>[
        PostComment.create(
          postId: post.id,
          authorId: users[1].id,
          content: 'Ottimo consiglio! Lo metterò in pratica subito 👍',
        ).copyWith(author: users[1], likesCount: 12),
        PostComment.create(
          postId: post.id,
          authorId: users[2].id,
          content: 'Grazie per la condivisione, molto utile!',
        ).copyWith(author: users[2], likesCount: 8),
        PostComment.create(
          postId: post.id,
          authorId: users[3].id,
          content: 'Concordo pienamente, la prevenzione è fondamentale 💚',
        ).copyWith(author: users[3], likesCount: 15),
      ];

      _postComments[post.id] = comments;
    }
  }

  // Metodi pubblici del service
  Future<List<CommunityUser>> getUsers({int limit = 20}) async {
    await initialize();
    return _users.values.take(limit).toList();
  }

  Future<List<CommunityPost>> getFeedPosts({int limit = 20, String? currentUserId}) async {
    await initialize();

    // Se non è specificato un utente corrente, mostra solo post pubblici
    if (currentUserId == null) {
      return _posts.values
          .where((post) => post.privacy == PostPrivacy.public)
          .take(limit)
          .toList();
    }

    final currentUser = _users[currentUserId];
    if (currentUser == null) {
      return _posts.values
          .where((post) => post.privacy == PostPrivacy.public)
          .take(limit)
          .toList();
    }

    // Filtra i post in base alla privacy
    final filteredPosts = _posts.values.where((post) {
      switch (post.privacy) {
        case PostPrivacy.public:
          return true; // Tutti possono vedere i post pubblici
        case PostPrivacy.friends:
          // Solo gli amici dell'autore possono vedere
          return post.authorId == currentUserId ||
                 currentUser.isFriendWith(post.authorId);
        case PostPrivacy.private:
          // Solo l'autore può vedere i propri post privati
          return post.authorId == currentUserId;
      }
    }).take(limit).toList();

    return filteredPosts;
  }

  Future<List<CommunityGroup>> getGroups({int limit = 20}) async {
    await initialize();
    return _groups.values.take(limit).toList();
  }

  Future<List<CommunityChallenge>> getChallenges({int limit = 20}) async {
    await initialize();
    return _challenges.values.take(limit).toList();
  }

  Future<CommunityUser?> getUser(String userId) async {
    await initialize();
    return _users[userId];
  }

  Future<CommunityPost?> getPost(String postId) async {
    await initialize();
    return _posts[postId];
  }

  Future<CommunityGroup?> getGroup(String groupId) async {
    await initialize();
    return _groups[groupId];
  }

  Future<CommunityChallenge?> getChallenge(String challengeId) async {
    await initialize();
    return _challenges[challengeId];
  }
}
