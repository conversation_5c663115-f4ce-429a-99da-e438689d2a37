import '../models/community_notification.dart';
import '../services/notification_service.dart';

/// Helper per creare notifiche di test
class NotificationTestHelper {
  /// Crea notifiche di test per dimostrare il sistema
  static Future<void> createTestNotifications(NotificationService notificationService) async {
    // Notifica di like
    await notificationService.addNotification(
      CommunityNotification.create(
        userId: 'current_user_id',
        type: NotificationType.likePost,
        postId: 'post_123',
        actorUserId: 'user_456',
        actorUserName: '<PERSON>',
        actorUserAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        message: '<PERSON> ha messo mi piace al tuo post',
      ),
    );

    // Notifica di commento
    await notificationService.addNotification(
      CommunityNotification.create(
        userId: 'current_user_id',
        type: NotificationType.commentPost,
        postId: 'post_456',
        actorUserId: 'user_789',
        actorUserName: '<PERSON><PERSON><PERSON>',
        actorUserAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
        message: 'Giulia Bianchi ha commentato: "Ottimo consiglio per la dieta mediterranea!"',
        metadata: {'commentPreview': 'Ottimo consiglio per la dieta mediterranea!'},
      ),
    );

    // Notifica di condivisione
    await notificationService.addNotification(
      CommunityNotification.create(
        userId: 'current_user_id',
        type: NotificationType.sharePost,
        postId: 'post_789',
        actorUserId: 'user_101',
        actorUserName: 'Andrea Verdi',
        actorUserAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        message: 'Andrea Verdi ha condiviso il tuo post',
      ),
    );

    // Notifica di tag
    await notificationService.addNotification(
      CommunityNotification.create(
        userId: 'current_user_id',
        type: NotificationType.tagUser,
        postId: 'post_101',
        actorUserId: 'user_202',
        actorUserName: 'Francesca Neri',
        actorUserAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        message: 'Francesca Neri ti ha taggato in un post',
      ),
    );

    // Notifica di richiesta di amicizia
    await notificationService.addNotification(
      CommunityNotification.create(
        userId: 'current_user_id',
        type: NotificationType.friendRequest,
        actorUserId: 'user_303',
        actorUserName: 'Luca Ferrari',
        actorUserAvatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
        message: 'Luca Ferrari ti ha inviato una richiesta di amicizia',
      ),
    );

    // Notifica più vecchia (già letta)
    final oldNotification = CommunityNotification.create(
      userId: 'current_user_id',
      type: NotificationType.likePost,
      postId: 'post_old',
      actorUserId: 'user_old',
      actorUserName: 'Utente Precedente',
      message: 'Utente Precedente ha messo mi piace al tuo post',
    );

    // Modifica il timestamp per renderla più vecchia
    final oldNotificationWithOldTimestamp = oldNotification.copyWith(
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      isRead: true,
    );

    await notificationService.addNotification(oldNotificationWithOldTimestamp);

    print('✅ Notifiche di test create con successo!');
    print('📊 Totale notifiche: ${notificationService.notifications.length}');
    print('🔔 Notifiche non lette: ${notificationService.unreadCount}');
  }

  /// Pulisce tutte le notifiche di test
  static Future<void> clearTestNotifications(NotificationService notificationService) async {
    await notificationService.clearAllNotifications();
    print('🧹 Notifiche di test pulite');
  }

  /// Crea una singola notifica di test
  static Future<void> createSingleTestNotification(
    NotificationService notificationService,
    NotificationType type,
  ) async {
    String message;
    String actorName;
    String? postId;

    switch (type) {
      case NotificationType.likePost:
        message = 'Test User ha messo mi piace al tuo post';
        actorName = 'Test User';
        postId = 'test_post_${DateTime.now().millisecondsSinceEpoch}';
        break;
      case NotificationType.commentPost:
        message = 'Test User ha commentato: "Questo è un commento di test!"';
        actorName = 'Test User';
        postId = 'test_post_${DateTime.now().millisecondsSinceEpoch}';
        break;
      case NotificationType.sharePost:
        message = 'Test User ha condiviso il tuo post';
        actorName = 'Test User';
        postId = 'test_post_${DateTime.now().millisecondsSinceEpoch}';
        break;
      case NotificationType.tagUser:
        message = 'Test User ti ha taggato in un post';
        actorName = 'Test User';
        postId = 'test_post_${DateTime.now().millisecondsSinceEpoch}';
        break;
      case NotificationType.friendRequest:
        message = 'Test User ti ha inviato una richiesta di amicizia';
        actorName = 'Test User';
        postId = null;
        break;
    }

    await notificationService.addNotification(
      CommunityNotification.create(
        userId: 'current_user_id',
        type: type,
        postId: postId,
        actorUserId: 'test_user_${DateTime.now().millisecondsSinceEpoch}',
        actorUserName: actorName,
        actorUserAvatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face',
        message: message,
      ),
    );

    print('✅ Notifica di test ${type.description} creata!');
  }
}
