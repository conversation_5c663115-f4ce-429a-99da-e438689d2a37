import 'package:intl/intl.dart';
import '../models/meal.dart';
import '../models/meal_plan.dart';
import '../services/storage_service.dart';

/// Servizio per la gestione dei piani pasti nella home screen
class MealPlanService {
  StorageService? _storageService;

  /// Inizializza il servizio
  Future<void> _ensureInitialized() async {
    _storageService ??= await StorageService.getInstance();
  }

  /// Ottieni i pasti di oggi
  Future<List<Meal>> getTodayMeals() async {
    try {
      await _ensureInitialized();
      final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
      final weeklyPlan = await _storageService!.getPianoSettimanaleCorrente();

      final todayPlan = weeklyPlan.dailyPlans.firstWhere(
        (plan) => plan.date == today,
        orElse: () => DailyPlan(date: today, meals: []),
      );

      return todayPlan.meals;
    } catch (e) {
      print('❌ Errore caricamento pasti di oggi: $e');
      return [];
    }
  }

  /// Sostituisci tutti i pasti di oggi con una nuova lista
  Future<bool> replaceDailyMeals(List<Meal> newMeals) async {
    try {
      await _ensureInitialized();
      final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
      final weeklyPlan = await _storageService!.getPianoSettimanaleCorrente();

      // Crea un nuovo piano giornaliero con i nuovi pasti
      final newDailyPlan = DailyPlan(
        date: today,
        meals: newMeals,
      );

      // Aggiorna il piano settimanale
      final updatedWeeklyPlan = weeklyPlan.updateDailyPlan(newDailyPlan);

      // Salva il piano aggiornato
      await _storageService!.salvaPianoPasti(updatedWeeklyPlan);

      print('✅ ${newMeals.length} pasti sostituiti per oggi ($today)');
      return true;
    } catch (e) {
      print('❌ Errore sostituzione pasti giornalieri: $e');
      return false;
    }
  }

  /// Aggiungi un pasto alla giornata corrente
  Future<bool> addMealToToday(Meal meal) async {
    try {
      final currentMeals = await getTodayMeals();
      final updatedMeals = [...currentMeals, meal];
      return await replaceDailyMeals(updatedMeals);
    } catch (e) {
      print('❌ Errore aggiunta pasto: $e');
      return false;
    }
  }

  /// Aggiorna un pasto specifico
  Future<bool> updateMeal(int mealIndex, Meal updatedMeal) async {
    try {
      final currentMeals = await getTodayMeals();

      if (mealIndex < 0 || mealIndex >= currentMeals.length) {
        print('❌ Indice pasto non valido: $mealIndex');
        return false;
      }

      final updatedMeals = List<Meal>.from(currentMeals);
      updatedMeals[mealIndex] = updatedMeal;

      return await replaceDailyMeals(updatedMeals);
    } catch (e) {
      print('❌ Errore aggiornamento pasto: $e');
      return false;
    }
  }

  /// Rimuovi un pasto specifico
  Future<bool> removeMeal(int mealIndex) async {
    try {
      final currentMeals = await getTodayMeals();

      if (mealIndex < 0 || mealIndex >= currentMeals.length) {
        print('❌ Indice pasto non valido: $mealIndex');
        return false;
      }

      final updatedMeals = List<Meal>.from(currentMeals);
      updatedMeals.removeAt(mealIndex);

      return await replaceDailyMeals(updatedMeals);
    } catch (e) {
      print('❌ Errore rimozione pasto: $e');
      return false;
    }
  }

  /// Ottieni i pasti per una data specifica
  Future<List<Meal>> getMealsForDate(DateTime date) async {
    try {
      await _ensureInitialized();
      final dateString = DateFormat('yyyy-MM-dd').format(date);
      final weeklyPlan = await _storageService!.getPianoSettimanaleCorrente();

      final dayPlan = weeklyPlan.dailyPlans.firstWhere(
        (plan) => plan.date == dateString,
        orElse: () => DailyPlan(date: dateString, meals: []),
      );

      return dayPlan.meals;
    } catch (e) {
      print('❌ Errore caricamento pasti per data $date: $e');
      return [];
    }
  }

  /// Sostituisci i pasti per una data specifica
  Future<bool> replaceMealsForDate(DateTime date, List<Meal> newMeals) async {
    try {
      await _ensureInitialized();
      final dateString = DateFormat('yyyy-MM-dd').format(date);
      final weeklyPlan = await _storageService!.getPianoSettimanaleCorrente();

      // Crea un nuovo piano giornaliero con i nuovi pasti
      final newDailyPlan = DailyPlan(
        date: dateString,
        meals: newMeals,
      );

      // Aggiorna il piano settimanale
      final updatedWeeklyPlan = weeklyPlan.updateDailyPlan(newDailyPlan);

      // Salva il piano aggiornato
      await _storageService!.salvaPianoPasti(updatedWeeklyPlan);

      print('✅ ${newMeals.length} pasti sostituiti per $dateString');
      return true;
    } catch (e) {
      print('❌ Errore sostituzione pasti per data $date: $e');
      return false;
    }
  }

  /// Ottieni statistiche sui pasti di oggi
  Future<Map<String, dynamic>> getTodayMealStats() async {
    try {
      final todayMeals = await getTodayMeals();

      final totalCalories = todayMeals.fold(0, (sum, meal) => sum + meal.calorie);
      final completedMeals = todayMeals.where((meal) => meal.completato).length;
      final totalProteins = todayMeals.fold(0.0, (sum, meal) => sum + meal.proteine);
      final totalCarbs = todayMeals.fold(0.0, (sum, meal) => sum + meal.carboidrati);
      final totalFats = todayMeals.fold(0.0, (sum, meal) => sum + meal.grassi);

      return {
        'totalMeals': todayMeals.length,
        'completedMeals': completedMeals,
        'completionPercentage': todayMeals.isNotEmpty ? (completedMeals / todayMeals.length * 100).round() : 0,
        'totalCalories': totalCalories,
        'totalProteins': totalProteins,
        'totalCarbs': totalCarbs,
        'totalFats': totalFats,
      };
    } catch (e) {
      print('❌ Errore calcolo statistiche pasti: $e');
      return {};
    }
  }

  /// Marca un pasto come completato/non completato
  Future<bool> toggleMealCompletion(int mealIndex) async {
    try {
      final currentMeals = await getTodayMeals();

      if (mealIndex < 0 || mealIndex >= currentMeals.length) {
        print('❌ Indice pasto non valido: $mealIndex');
        return false;
      }

      final meal = currentMeals[mealIndex];
      final updatedMeal = meal.copyWith(completato: !meal.completato);

      return await updateMeal(mealIndex, updatedMeal);
    } catch (e) {
      print('❌ Errore toggle completamento pasto: $e');
      return false;
    }
  }

  /// Pulisci tutti i pasti di oggi
  Future<bool> clearTodayMeals() async {
    try {
      return await replaceDailyMeals([]);
    } catch (e) {
      print('❌ Errore pulizia pasti di oggi: $e');
      return false;
    }
  }

  /// Ottieni il piano settimanale completo
  Future<WeeklyMealPlan> getWeeklyPlan() async {
    await _ensureInitialized();
    return await _storageService!.getPianoSettimanaleCorrente();
  }

  /// Salva il piano settimanale
  Future<bool> saveWeeklyPlan(WeeklyMealPlan weeklyPlan) async {
    try {
      await _ensureInitialized();
      await _storageService!.salvaPianoPasti(weeklyPlan);
      return true;
    } catch (e) {
      print('❌ Errore salvataggio piano settimanale: $e');
      return false;
    }
  }

  /// Verifica se ci sono pasti per oggi
  Future<bool> hasMealsForToday() async {
    final todayMeals = await getTodayMeals();
    return todayMeals.isNotEmpty;
  }

  /// Ottieni il numero di pasti completati oggi
  Future<int> getCompletedMealsCount() async {
    final todayMeals = await getTodayMeals();
    return todayMeals.where((meal) => meal.completato).length;
  }

  /// Ottieni le calorie totali consumate oggi (solo pasti completati)
  Future<int> getTodayConsumedCalories() async {
    final todayMeals = await getTodayMeals();
    return todayMeals
        .where((meal) => meal.completato)
        .fold<int>(0, (int sum, Meal meal) => sum + meal.calorie);
  }

  /// Ottieni le calorie totali pianificate per oggi
  Future<int> getTodayPlannedCalories() async {
    final todayMeals = await getTodayMeals();
    return todayMeals.fold<int>(0, (int sum, Meal meal) => sum + meal.calorie);
  }
}
