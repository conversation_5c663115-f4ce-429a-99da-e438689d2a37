import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/diet_plan.dart';
import '../models/meal.dart';
import '../models/ultra_detailed_profile.dart';
import '../services/meal_plan_service.dart';
import 'ultra_diet_plan_storage.dart';

/// Servizio per applicare i piani dietetici ultra-personalizzati alla home screen
class DietPlanApplicationService {
  static const String _keyAppliedPlan = 'applied_ultra_diet_plan';
  static const String _keyAppliedProfile = 'applied_ultra_profile';
  static const String _keyApplicationDate = 'plan_application_date';
  static const String _keyPreviousMeals = 'previous_meals_backup';

  /// Applica il piano ultra-personalizzato sostituendo i pasti esistenti
  static Future<bool> applyUltraPlan({
    required DailyDietPlan dailyPlan,
    required UltraDetailedProfile profile,
  }) async {
    try {
      // 1. Backup dei pasti esistenti
      await _backupCurrentMeals();

      // 2. Converti PlannedMeal in Meal per compatibilità
      final convertedMeals = dailyPlan.meals.map((plannedMeal) => plannedMeal.toMeal()).toList();

      // 3. Sostituisci i pasti nella home screen
      final mealPlanService = MealPlanService();
      await mealPlanService.replaceDailyMeals(convertedMeals);

      // 4. Salva informazioni del piano applicato
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_keyAppliedPlan, jsonEncode(dailyPlan.toMap()));
      await prefs.setString(_keyAppliedProfile, jsonEncode(profile.toMap()));
      await prefs.setString(_keyApplicationDate, DateTime.now().toIso8601String());

      // 5. Marca il piano come applicato nel storage ultra
      await UltraDietPlanStorage.markPlanAsApplied();

      print('✅ Piano ultra-personalizzato applicato con successo');
      print('🍽️ ${convertedMeals.length} pasti sostituiti nella home screen');
      
      return true;
    } catch (e) {
      print('❌ Errore applicazione piano: $e');
      return false;
    }
  }

  /// Backup dei pasti esistenti prima della sostituzione
  static Future<bool> _backupCurrentMeals() async {
    try {
      final mealPlanService = MealPlanService();
      final currentMeals = await mealPlanService.getTodayMeals();
      
      if (currentMeals.isNotEmpty) {
        final prefs = await SharedPreferences.getInstance();
        final backup = {
          'meals': currentMeals.map((meal) => meal.toJson()).toList(),
          'backupDate': DateTime.now().toIso8601String(),
        };
        
        await prefs.setString(_keyPreviousMeals, jsonEncode(backup));
        print('✅ Backup di ${currentMeals.length} pasti esistenti completato');
      }
      
      return true;
    } catch (e) {
      print('❌ Errore backup pasti esistenti: $e');
      return false;
    }
  }

  /// Ripristina i pasti precedenti dal backup
  static Future<bool> restorePreviousMeals() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final backupJson = prefs.getString(_keyPreviousMeals);
      
      if (backupJson == null) {
        print('❌ Nessun backup disponibile');
        return false;
      }
      
      final backup = jsonDecode(backupJson) as Map<String, dynamic>;
      final mealsList = backup['meals'] as List;
      final previousMeals = mealsList.map((mealJson) => Meal.fromJson(mealJson)).toList();
      
      // Ripristina i pasti
      final mealPlanService = MealPlanService();
      await mealPlanService.replaceDailyMeals(previousMeals);
      
      // Pulisci le informazioni del piano applicato
      await _clearAppliedPlanInfo();
      
      print('✅ ${previousMeals.length} pasti precedenti ripristinati');
      return true;
    } catch (e) {
      print('❌ Errore ripristino pasti precedenti: $e');
      return false;
    }
  }

  /// Verifica se c'è un piano ultra applicato attualmente
  static Future<bool> hasAppliedUltraPlan() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_keyAppliedPlan) != null;
    } catch (e) {
      print('❌ Errore verifica piano applicato: $e');
      return false;
    }
  }

  /// Ottieni informazioni sul piano attualmente applicato
  static Future<Map<String, dynamic>?> getAppliedPlanInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final planJson = prefs.getString(_keyAppliedPlan);
      final profileJson = prefs.getString(_keyAppliedProfile);
      final applicationDate = prefs.getString(_keyApplicationDate);
      
      if (planJson == null || profileJson == null) return null;
      
      return {
        'plan': DailyDietPlan.fromMap(jsonDecode(planJson)),
        'profile': UltraDetailedProfile.fromMap(jsonDecode(profileJson)),
        'applicationDate': DateTime.parse(applicationDate!),
      };
    } catch (e) {
      print('❌ Errore caricamento info piano applicato: $e');
      return null;
    }
  }

  /// Pulisci le informazioni del piano applicato
  static Future<bool> _clearAppliedPlanInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_keyAppliedPlan);
      await prefs.remove(_keyAppliedProfile);
      await prefs.remove(_keyApplicationDate);
      return true;
    } catch (e) {
      print('❌ Errore pulizia info piano applicato: $e');
      return false;
    }
  }

  /// Ottieni statistiche sull'applicazione dei piani
  static Future<Map<String, dynamic>> getApplicationStatistics() async {
    try {
      final hasApplied = await hasAppliedUltraPlan();
      final appliedInfo = await getAppliedPlanInfo();
      final hasBackup = await _hasBackupAvailable();
      
      return {
        'hasAppliedPlan': hasApplied,
        'applicationDate': appliedInfo?['applicationDate'],
        'appliedPlanMeals': appliedInfo?['plan']?.meals?.length ?? 0,
        'hasBackupAvailable': hasBackup,
        'canRestore': hasBackup,
      };
    } catch (e) {
      print('❌ Errore calcolo statistiche applicazione: $e');
      return {};
    }
  }

  /// Verifica se c'è un backup disponibile
  static Future<bool> _hasBackupAvailable() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_keyPreviousMeals) != null;
    } catch (e) {
      return false;
    }
  }

  /// Aggiorna un pasto specifico nel piano applicato
  static Future<bool> updateAppliedMeal({
    required String mealId,
    required Meal updatedMeal,
  }) async {
    try {
      final appliedInfo = await getAppliedPlanInfo();
      if (appliedInfo == null) return false;
      
      final plan = appliedInfo['plan'] as DailyDietPlan;
      final profile = appliedInfo['profile'] as UltraDetailedProfile;
      
      // Trova e aggiorna il pasto nel piano
      final updatedMeals = plan.meals.map((plannedMeal) {
        if (plannedMeal.id == mealId) {
          // Converti il Meal aggiornato in PlannedMeal
          return _convertMealToPlannedMeal(updatedMeal, plannedMeal);
        }
        return plannedMeal;
      }).toList();
      
      final updatedPlan = plan.copyWith(meals: updatedMeals);
      
      // Salva il piano aggiornato
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_keyAppliedPlan, jsonEncode(updatedPlan.toMap()));
      
      // Aggiorna anche nel storage ultra
      await UltraDietPlanStorage.saveCurrentPlan(
        plan: updatedPlan,
        profile: profile,
      );
      
      print('✅ Pasto aggiornato nel piano applicato');
      return true;
    } catch (e) {
      print('❌ Errore aggiornamento pasto nel piano applicato: $e');
      return false;
    }
  }

  /// Converti Meal in PlannedMeal (helper method)
  static PlannedMeal _convertMealToPlannedMeal(Meal meal, PlannedMeal originalPlannedMeal) {
    // Mantieni le proprietà originali del PlannedMeal e aggiorna solo i dati necessari
    return originalPlannedMeal.copyWith(
      isCompleted: meal.completato,
      // Altri aggiornamenti se necessari
    );
  }

  /// Sincronizza lo stato di completamento dei pasti
  static Future<bool> syncMealCompletionStatus() async {
    try {
      final mealPlanService = MealPlanService();
      final currentMeals = await mealPlanService.getTodayMeals();
      
      final appliedInfo = await getAppliedPlanInfo();
      if (appliedInfo == null) return false;
      
      final plan = appliedInfo['plan'] as DailyDietPlan;
      final profile = appliedInfo['profile'] as UltraDetailedProfile;
      
      // Aggiorna lo stato di completamento nel piano
      final updatedMeals = plan.meals.map((plannedMeal) {
        final correspondingMeal = currentMeals.firstWhere(
          (meal) => meal.nome == plannedMeal.name,
          orElse: () => currentMeals.first, // Fallback
        );
        
        return plannedMeal.copyWith(isCompleted: correspondingMeal.completato);
      }).toList();
      
      final updatedPlan = plan.copyWith(meals: updatedMeals);
      
      // Salva il piano aggiornato
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_keyAppliedPlan, jsonEncode(updatedPlan.toMap()));
      
      return true;
    } catch (e) {
      print('❌ Errore sincronizzazione stato completamento: $e');
      return false;
    }
  }

  /// Pulisci tutti i dati di applicazione (per reset completo)
  static Future<bool> clearAllApplicationData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_keyAppliedPlan);
      await prefs.remove(_keyAppliedProfile);
      await prefs.remove(_keyApplicationDate);
      await prefs.remove(_keyPreviousMeals);
      
      print('✅ Tutti i dati di applicazione piano eliminati');
      return true;
    } catch (e) {
      print('❌ Errore pulizia dati applicazione: $e');
      return false;
    }
  }
}
