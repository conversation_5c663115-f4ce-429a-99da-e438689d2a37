import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:uuid/uuid.dart';
import '../models/food.dart';
import 'food_database_service.dart';
import 'food_validation_service.dart';
import 'translation_service.dart';

/// Service per l'importazione di dati alimentari da fonti esterne
class FoodImportService {
  final FoodDatabaseService _databaseService;
  final FoodValidationService _validationService;
  final TranslationService? _translationService;

  // Flag per indicare se tradurre automaticamente gli alimenti importati
  bool autoTranslate = false;

  FoodImportService({
    required FoodDatabaseService databaseService,
    required FoodValidationService validationService,
    this.autoTranslate = false,
    TranslationService? translationService,
  }) :
    _databaseService = databaseService,
    _validationService = validationService,
    _translationService = translationService;

  /// Importa alimenti da un file CSV
  /// [csvContent] - Contenuto del file CSV
  /// [hasHeader] - Indica se il file ha una riga di intestazione
  /// [delimiter] - Carattere delimitatore (default: ',')
  /// [columnMap] - Mappa che associa i nomi delle colonne ai campi del modello Food
  Future<List<Food>> importFromCsv({
    required String csvContent,
    bool hasHeader = true,
    String delimiter = ',',
    required Map<String, int> columnMap,
  }) async {
    try {
      final lines = csvContent.split('\n');

      // Rimuovi l'intestazione se presente
      if (hasHeader && lines.isNotEmpty) {
        lines.removeAt(0);
      }

      final importedFoods = <Food>[];

      for (final line in lines) {
        if (line.trim().isEmpty) continue;

        final columns = line.split(delimiter);

        // Verifica che ci siano abbastanza colonne
        if (columns.length < columnMap.values.reduce((a, b) => a > b ? a : b) + 1) {
          continue;
        }

        try {
          final food = _createFoodFromColumns(columns, columnMap);

          // Verifica se l'alimento ha dati plausibili
          if (_validationService.hasPlausibleNutrients(food)) {
            importedFoods.add(food);
          }
        } catch (e) {
          print('Errore nell\'importazione di una riga CSV: $e');
        }
      }

      return importedFoods;
    } catch (e) {
      throw Exception('Errore nell\'importazione da CSV: $e');
    }
  }

  /// Crea un oggetto Food a partire dalle colonne di un file CSV
  Food _createFoodFromColumns(List<String> columns, Map<String, int> columnMap) {
    final name = columns[columnMap['name']!].trim();

    // Estrai i valori nutrizionali
    final calories = int.tryParse(columns[columnMap['calories']!]) ?? 0;
    final proteins = double.tryParse(columns[columnMap['proteins']!]) ?? 0.0;
    final carbs = double.tryParse(columns[columnMap['carbs']!]) ?? 0.0;
    final fats = double.tryParse(columns[columnMap['fats']!]) ?? 0.0;

    // Estrai valori opzionali se presenti
    double fiber = 0.0;
    double sugar = 0.0;
    String description = '';
    String source = '';

    if (columnMap.containsKey('fiber') && columnMap['fiber']! < columns.length) {
      fiber = double.tryParse(columns[columnMap['fiber']!]) ?? 0.0;
    }

    if (columnMap.containsKey('sugar') && columnMap['sugar']! < columns.length) {
      sugar = double.tryParse(columns[columnMap['sugar']!]) ?? 0.0;
    }

    if (columnMap.containsKey('description') && columnMap['description']! < columns.length) {
      description = columns[columnMap['description']!].trim();
    }

    if (columnMap.containsKey('source') && columnMap['source']! < columns.length) {
      source = columns[columnMap['source']!].trim();
    }

    // Determina la fonte dei dati
    DataSource dataSource = DataSource.custom;
    if (source.toLowerCase().contains('usda')) {
      dataSource = DataSource.usda;
    } else if (source.toLowerCase().contains('crea')) {
      dataSource = DataSource.crea;
    } else if (source.toLowerCase().contains('open food facts')) {
      dataSource = DataSource.open_food_facts;
    }

    // Crea l'oggetto Food
    return Food(
      id: const Uuid().v4(),
      name: name,
      description: description,
      calories: calories,
      proteins: proteins,
      carbs: carbs,
      fats: fats,
      fiber: fiber,
      sugar: sugar,
      suitableForMeals: _determineSuitableMeals(name, description),
      categories: _determineCategories(name, description),
      dataSource: dataSource,
      sourceDescription: source,
      validationStatus: ValidationStatus.pending,
      lastValidatedAt: DateTime.now(),
      validatedBy: 'Import Service',
    );
  }

  /// Determina i tipi di pasto adatti in base al nome e alla descrizione
  List<MealType> _determineSuitableMeals(String name, String description) {
    final text = '${name.toLowerCase()} ${description.toLowerCase()}';
    final mealTypes = <MealType>{};

    // Colazione
    if (_containsAny(text, ['breakfast', 'colazione', 'cereali', 'latte', 'yogurt', 'muesli'])) {
      mealTypes.add(MealType.breakfast);
    }

    // Pranzo
    if (_containsAny(text, ['lunch', 'pranzo', 'pasta', 'riso', 'insalata'])) {
      mealTypes.add(MealType.lunch);
    }

    // Cena
    if (_containsAny(text, ['dinner', 'cena', 'zuppa', 'minestra'])) {
      mealTypes.add(MealType.dinner);
    }

    // Spuntino
    if (_containsAny(text, ['snack', 'spuntino', 'merenda', 'frutta', 'barretta'])) {
      mealTypes.add(MealType.snack);
    }

    // Se non è stato trovato alcun tipo di pasto, aggiungi tutti
    if (mealTypes.isEmpty) {
      mealTypes.addAll([MealType.breakfast, MealType.lunch, MealType.dinner, MealType.snack]);
    }

    return mealTypes.toList();
  }

  /// Determina le categorie in base al nome e alla descrizione
  List<FoodCategory> _determineCategories(String name, String description) {
    final text = '${name.toLowerCase()} ${description.toLowerCase()}';
    final categories = <FoodCategory>{};

    // Frutta
    if (_containsAny(text, ['fruit', 'frutta', 'apple', 'banana', 'orange', 'berry', 'mela', 'banana', 'arancia'])) {
      categories.add(FoodCategory.fruit);
    }

    // Verdura
    if (_containsAny(text, ['vegetable', 'verdura', 'carrot', 'broccoli', 'spinach', 'carota', 'broccoli', 'spinaci'])) {
      categories.add(FoodCategory.vegetable);
    }

    // Cereali
    if (_containsAny(text, ['grain', 'cereal', 'bread', 'pasta', 'rice', 'cereale', 'pane', 'pasta', 'riso'])) {
      categories.add(FoodCategory.grain);
    }

    // Proteine
    if (_containsAny(text, ['meat', 'chicken', 'beef', 'pork', 'fish', 'carne', 'pollo', 'manzo', 'maiale', 'pesce'])) {
      categories.add(FoodCategory.protein);
    }

    // Latticini
    if (_containsAny(text, ['milk', 'cheese', 'yogurt', 'dairy', 'latte', 'formaggio', 'yogurt', 'latticini'])) {
      categories.add(FoodCategory.dairy);
    }

    // Grassi
    if (_containsAny(text, ['oil', 'butter', 'margarine', 'olio', 'burro', 'margarina'])) {
      categories.add(FoodCategory.fat);
    }

    // Dolci
    if (_containsAny(text, ['sweet', 'candy', 'chocolate', 'dessert', 'dolce', 'caramella', 'cioccolato'])) {
      categories.add(FoodCategory.sweet);
    }

    // Bevande
    if (_containsAny(text, ['beverage', 'drink', 'juice', 'water', 'bevanda', 'succo', 'acqua'])) {
      categories.add(FoodCategory.beverage);
    }

    // Se non è stata trovata alcuna categoria, aggiungi "mixed"
    if (categories.isEmpty) {
      categories.add(FoodCategory.mixed);
    }

    return categories.toList();
  }

  /// Verifica se una stringa contiene una delle parole chiave
  bool _containsAny(String text, List<String> keywords) {
    for (final keyword in keywords) {
      if (text.contains(keyword)) {
        return true;
      }
    }
    return false;
  }

  /// Importa alimenti da un file JSON
  /// [jsonContent] - Contenuto del file JSON
  Future<List<Food>> importFromJson(String jsonContent) async {
    try {
      final jsonData = json.decode(jsonContent);

      if (jsonData is! List) {
        throw Exception('Il formato JSON non è valido. Deve essere un array di oggetti.');
      }

      final importedFoods = <Food>[];

      for (final item in jsonData) {
        try {
          final food = Food.fromMap(item);

          // Genera un nuovo ID per evitare conflitti
          final newFood = food.copyWith(
            id: const Uuid().v4(),
            validationStatus: ValidationStatus.pending,
            lastValidatedAt: DateTime.now(),
            validatedBy: 'Import Service',
          );

          // Verifica se l'alimento ha dati plausibili
          if (_validationService.hasPlausibleNutrients(newFood)) {
            importedFoods.add(newFood);
          }
        } catch (e) {
          print('Errore nell\'importazione di un elemento JSON: $e');
        }
      }

      return importedFoods;
    } catch (e) {
      throw Exception('Errore nell\'importazione da JSON: $e');
    }
  }

  /// Importa alimenti da un URL
  /// [url] - URL del file da importare (CSV o JSON)
  /// [fileType] - Tipo di file ('csv' o 'json')
  /// [columnMap] - Mappa che associa i nomi delle colonne ai campi del modello Food (solo per CSV)
  Future<List<Food>> importFromUrl({
    required String url,
    required String fileType,
    Map<String, int>? columnMap,
  }) async {
    try {
      final response = await http.get(Uri.parse(url));

      if (response.statusCode != 200) {
        throw Exception('Errore nel download del file: ${response.statusCode}');
      }

      final content = response.body;

      if (fileType.toLowerCase() == 'csv') {
        if (columnMap == null) {
          throw Exception('La mappa delle colonne è obbligatoria per i file CSV');
        }
        return importFromCsv(
          csvContent: content,
          columnMap: columnMap,
        );
      } else if (fileType.toLowerCase() == 'json') {
        return importFromJson(content);
      } else {
        throw Exception('Tipo di file non supportato: $fileType');
      }
    } catch (e) {
      throw Exception('Errore nell\'importazione da URL: $e');
    }
  }

  /// Traduce gli alimenti importati se la traduzione automatica è abilitata
  Future<List<Food>> _translateFoodsIfNeeded(List<Food> foods) async {
    if (!autoTranslate || _translationService == null) {
      return foods;
    }

    try {
      return await _translationService!.translateFoods(foods);
    } catch (e) {
      print('Errore nella traduzione degli alimenti: $e');
      return foods;
    }
  }

  /// Salva gli alimenti importati nel database
  Future<int> saveImportedFoods(List<Food> foods) async {
    int savedCount = 0;

    // Traduci gli alimenti se necessario
    final translatedFoods = await _translateFoodsIfNeeded(foods);

    for (final food in translatedFoods) {
      try {
        await _databaseService.saveFood(food);
        savedCount++;
      } catch (e) {
        print('Errore nel salvataggio dell\'alimento ${food.name}: $e');
      }
    }

    return savedCount;
  }
}
