import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../ai/models/ai_models.dart';
import '../ai/services/ai_service.dart';
import '../models/food.dart';
import '../models/user_profile.dart';

/// Widget che permette all'utente di fornire feedback su un alimento
class FoodFeedbackWidget extends StatefulWidget {
  final Food food;
  final UserProfile userProfile;
  final Function()? onFeedbackSubmitted;

  const FoodFeedbackWidget({
    Key? key,
    required this.food,
    required this.userProfile,
    this.onFeedbackSubmitted,
  }) : super(key: key);

  @override
  _FoodFeedbackWidgetState createState() => _FoodFeedbackWidgetState();
}

class _FoodFeedbackWidgetState extends State<FoodFeedbackWidget> {
  int _rating = 3;
  final TextEditingController _commentController = TextEditingController();
  bool _isSubmitting = false;

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  Future<void> _submitFeedback() async {
    if (_isSubmitting) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      final aiService = await AIService.getInstance();
      
      final feedback = UserFeedback(
        id: const Uuid().v4(),
        userId: widget.userProfile.id,
        foodId: widget.food.id,
        rating: _rating,
        comment: _commentController.text.isNotEmpty ? _commentController.text : null,
        createdAt: DateTime.now(),
      );
      
      await aiService.recordFeedback(feedback);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Feedback inviato con successo!'),
            backgroundColor: Colors.green,
          ),
        );
        
        if (widget.onFeedbackSubmitted != null) {
          widget.onFeedbackSubmitted!();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Errore nell\'invio del feedback: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Cosa ne pensi di ${widget.food.name}?',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            const Text(
              'Valuta questo alimento:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            _buildRatingSelector(),
            const SizedBox(height: 16),
            TextField(
              controller: _commentController,
              decoration: const InputDecoration(
                labelText: 'Commento (opzionale)',
                border: OutlineInputBorder(),
                hintText: 'Scrivi qui il tuo commento...',
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isSubmitting ? null : _submitFeedback,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: _isSubmitting
                    ? const CircularProgressIndicator()
                    : const Text('Invia feedback'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRatingSelector() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(5, (index) {
        final rating = index + 1;
        return GestureDetector(
          onTap: () {
            setState(() {
              _rating = rating;
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Column(
              children: [
                Icon(
                  rating <= _rating ? Icons.star : Icons.star_border,
                  color: rating <= _rating ? Colors.amber : Colors.grey,
                  size: 36,
                ),
                const SizedBox(height: 4),
                Text(
                  _getRatingLabel(rating),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: rating == _rating ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }

  String _getRatingLabel(int rating) {
    switch (rating) {
      case 1:
        return 'Non mi piace';
      case 2:
        return 'Così così';
      case 3:
        return 'Neutro';
      case 4:
        return 'Mi piace';
      case 5:
        return 'Adoro!';
      default:
        return '';
    }
  }
}
