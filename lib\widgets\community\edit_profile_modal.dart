import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/community_user.dart';
import '../../services/community_profile_service.dart';
import '../../theme/dr_staffilano_theme.dart';

/// Modal per modificare il profilo utente
class EditProfileModal extends StatefulWidget {
  final CommunityUser user;

  const EditProfileModal({
    super.key,
    required this.user,
  });

  @override
  State<EditProfileModal> createState() => _EditProfileModalState();
}

class _EditProfileModalState extends State<EditProfileModal> {
  late TextEditingController _displayNameController;
  late TextEditingController _bioController;
  late TextEditingController _avatarUrlController;
  
  List<String> _selectedInterests = [];
  final List<String> _availableInterests = [
    'Cardiologia',
    'Prevenzione',
    'Benessere',
    'Nutrizione',
    'Fitness',
    'Medicina',
    'Salute Mentale',
    'Rice<PERSON>',
    'Educazione',
    'Tecnologia Medica',
  ];

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _displayNameController = TextEditingController(text: widget.user.displayName);
    _bioController = TextEditingController(text: widget.user.bio ?? '');
    _avatarUrlController = TextEditingController(text: widget.user.avatarUrl ?? '');
    _selectedInterests = List.from(widget.user.interests);
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    _bioController.dispose();
    _avatarUrlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            Expanded(
              child: _buildContent(),
            ),
            _buildFooter(),
          ],
        ),
      ),
    );
  }

  /// Costruisce l'header del modal
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.primaryGreen,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.edit,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              'Modifica Profilo',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }

  /// Costruisce il contenuto del modal
  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Avatar preview
          _buildAvatarSection(),
          
          const SizedBox(height: 24),
          
          // Nome visualizzato
          _buildTextField(
            controller: _displayNameController,
            label: 'Nome visualizzato',
            icon: Icons.person,
            maxLength: 50,
          ),
          
          const SizedBox(height: 16),
          
          // Bio
          _buildTextField(
            controller: _bioController,
            label: 'Bio',
            icon: Icons.description,
            maxLines: 3,
            maxLength: 150,
            hint: 'Racconta qualcosa di te...',
          ),
          
          const SizedBox(height: 16),
          
          // URL Avatar
          _buildTextField(
            controller: _avatarUrlController,
            label: 'URL Avatar (opzionale)',
            icon: Icons.image,
            hint: 'https://esempio.com/avatar.jpg',
          ),
          
          const SizedBox(height: 24),
          
          // Interessi
          _buildInterestsSection(),
        ],
      ),
    );
  }

  /// Costruisce la sezione avatar
  Widget _buildAvatarSection() {
    return Center(
      child: Column(
        children: [
          CircleAvatar(
            radius: 50,
            backgroundColor: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
            backgroundImage: _avatarUrlController.text.isNotEmpty
                ? NetworkImage(_avatarUrlController.text)
                : null,
            child: _avatarUrlController.text.isEmpty
                ? Text(
                    _displayNameController.text.isNotEmpty
                        ? _displayNameController.text[0].toUpperCase()
                        : '?',
                    style: const TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: DrStaffilanoTheme.primaryGreen,
                    ),
                  )
                : null,
          ),
          const SizedBox(height: 8),
          Text(
            'Anteprima Avatar',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// Costruisce un campo di testo
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    int maxLines = 1,
    int? maxLength,
    String? hint,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          maxLines: maxLines,
          maxLength: maxLength,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: Icon(icon, color: DrStaffilanoTheme.primaryGreen),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.withOpacity(0.3)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: DrStaffilanoTheme.primaryGreen),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          onChanged: (value) {
            if (controller == _avatarUrlController || controller == _displayNameController) {
              setState(() {}); // Aggiorna l'anteprima avatar
            }
          },
        ),
      ],
    );
  }

  /// Costruisce la sezione interessi
  Widget _buildInterestsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Interessi',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Seleziona i tuoi interessi (massimo 5)',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _availableInterests.map((interest) {
            final isSelected = _selectedInterests.contains(interest);
            final canSelect = _selectedInterests.length < 5 || isSelected;
            
            return FilterChip(
              label: Text(
                interest,
                style: TextStyle(
                  color: isSelected ? Colors.white : 
                         canSelect ? DrStaffilanoTheme.primaryGreen : Colors.grey,
                  fontSize: 12,
                ),
              ),
              selected: isSelected,
              onSelected: canSelect ? (selected) {
                setState(() {
                  if (selected) {
                    _selectedInterests.add(interest);
                  } else {
                    _selectedInterests.remove(interest);
                  }
                });
              } : null,
              backgroundColor: Colors.white,
              selectedColor: DrStaffilanoTheme.primaryGreen,
              checkmarkColor: Colors.white,
              side: BorderSide(
                color: canSelect ? DrStaffilanoTheme.primaryGreen : Colors.grey,
                width: 1,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// Costruisce il footer del modal
  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.05),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextButton(
              onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
              child: const Text(
                'Annulla',
                style: TextStyle(
                  color: Colors.grey,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton(
              onPressed: _isLoading ? null : _saveProfile,
              style: ElevatedButton.styleFrom(
                backgroundColor: DrStaffilanoTheme.primaryGreen,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : const Text(
                      'Salva',
                      style: TextStyle(fontWeight: FontWeight.w600),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  /// Salva le modifiche al profilo
  Future<void> _saveProfile() async {
    if (_displayNameController.text.trim().isEmpty) {
      _showError('Il nome visualizzato è obbligatorio');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final profileService = context.read<CommunityProfileService>();
      
      final success = await profileService.updateProfile(
        displayName: _displayNameController.text.trim(),
        bio: _bioController.text.trim().isEmpty ? null : _bioController.text.trim(),
        avatarUrl: _avatarUrlController.text.trim().isEmpty ? null : _avatarUrlController.text.trim(),
        interests: _selectedInterests,
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ Profilo aggiornato con successo!'),
            backgroundColor: DrStaffilanoTheme.primaryGreen,
          ),
        );
        Navigator.of(context).pop(true); // Indica che il profilo è stato aggiornato
      } else if (mounted) {
        _showError('Errore durante il salvataggio del profilo');
      }
    } catch (e) {
      if (mounted) {
        _showError('Errore: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// Mostra un errore
  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
