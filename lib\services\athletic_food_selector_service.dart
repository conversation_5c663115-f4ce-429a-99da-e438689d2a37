import '../models/food.dart';
import '../models/user_profile.dart';

/// Servizio per la selezione di alimenti ad alto contenuto proteico per atleti
/// Mantiene l'autenticità italiana mentre prioritizza le proteine per il recupero muscolare
class AthleticFoodSelectorService {
  static const double HIGH_PROTEIN_THRESHOLD = 15.0; // g per 100g
  static const double MEDIUM_PROTEIN_THRESHOLD = 8.0; // g per 100g

  /// Filtra e prioritizza gli alimenti per atleti basandosi sul contenuto proteico
  static List<Food> selectHighProteinItalianFoods(
    List<Food> availableFoods,
    UserProfile userProfile,
    MealType mealType,
  ) {
    // Determina se l'utente è un atleta che necessita di proteine extra
    final isAthlete = _isAthleteProfile(userProfile);
    
    if (!isAthlete) {
      // Per non atleti, usa la selezione standard
      return availableFoods;
    }

    // Filtra gli alimenti adatti al tipo di pasto
    final suitableFoods = availableFoods
        .where((food) => food.suitableForMeals.contains(mealType))
        .toList();

    // Categorizza gli alimenti per contenuto proteico
    final highProteinFoods = <Food>[];
    final mediumProteinFoods = <Food>[];
    final lowProteinFoods = <Food>[];

    for (final food in suitableFoods) {
      if (food.proteins >= HIGH_PROTEIN_THRESHOLD) {
        highProteinFoods.add(food);
      } else if (food.proteins >= MEDIUM_PROTEIN_THRESHOLD) {
        mediumProteinFoods.add(food);
      } else {
        lowProteinFoods.add(food);
      }
    }

    // Prioritizza gli alimenti ad alto contenuto proteico per atleti
    return _prioritizeForAthletes(
      highProteinFoods,
      mediumProteinFoods,
      lowProteinFoods,
      mealType,
    );
  }

  /// Determina se il profilo utente è quello di un atleta
  static bool _isAthleteProfile(UserProfile userProfile) {
    return userProfile.activityLevel == ActivityLevel.veryActive ||
           userProfile.activityLevel == ActivityLevel.extremelyActive;
  }

  /// Prioritizza gli alimenti per atleti mantenendo varietà e autenticità italiana
  static List<Food> _prioritizeForAthletes(
    List<Food> highProteinFoods,
    List<Food> mediumProteinFoods,
    List<Food> lowProteinFoods,
    MealType mealType,
  ) {
    final prioritizedFoods = <Food>[];

    // Aggiungi sempre gli alimenti ad alto contenuto proteico per primi
    prioritizedFoods.addAll(highProteinFoods);

    // Aggiungi alimenti a medio contenuto proteico
    prioritizedFoods.addAll(mediumProteinFoods);

    // Aggiungi alcuni alimenti a basso contenuto proteico per varietà
    // ma in quantità limitata per atleti
    final lowProteinLimit = _getLowProteinLimit(mealType);
    if (lowProteinFoods.length <= lowProteinLimit) {
      prioritizedFoods.addAll(lowProteinFoods);
    } else {
      prioritizedFoods.addAll(lowProteinFoods.take(lowProteinLimit));
    }

    return prioritizedFoods;
  }

  /// Determina il limite di alimenti a basso contenuto proteico per tipo di pasto
  static int _getLowProteinLimit(MealType mealType) {
    switch (mealType) {
      case MealType.breakfast:
        return 3; // Permetti alcuni carboidrati per energia mattutina
      case MealType.lunch:
        return 2; // Limita per massimizzare proteine a pranzo
      case MealType.dinner:
        return 2; // Limita per recupero notturno
      case MealType.snack:
        return 1; // Spuntini principalmente proteici
    }
  }

  /// Ottieni alimenti italiani ad alto contenuto proteico per categoria
  static List<Food> getItalianHighProteinFoodsByCategory(
    List<Food> availableFoods,
    FoodCategory category,
  ) {
    return availableFoods
        .where((food) => 
            food.categories.contains(category) &&
            food.proteins >= HIGH_PROTEIN_THRESHOLD &&
            _isTraditionalItalian(food))
        .toList()
        ..sort((a, b) => b.proteins.compareTo(a.proteins)); // Ordina per contenuto proteico decrescente
  }

  /// Verifica se un alimento è tradizionalmente italiano
  static bool _isTraditionalItalian(Food food) {
    // Controlla se l'alimento ha tag italiani o è marcato come tradizionale
    final italianKeywords = [
      'italiana', 'italiano', 'tradizionale', 'regionale',
      'siciliana', 'toscana', 'lombarda', 'napoletana',
      'parmigiano', 'mozzarella', 'prosciutto', 'bresaola'
    ];

    final foodName = food.name.toLowerCase();
    final foodDescription = food.description.toLowerCase();
    final foodTags = food.tags.map((tag) => tag.toLowerCase()).toList();

    return italianKeywords.any((keyword) =>
        foodName.contains(keyword) ||
        foodDescription.contains(keyword) ||
        foodTags.any((tag) => tag.contains(keyword))) ||
        food.isTraditionalItalian == true;
  }

  /// Calcola il punteggio proteico per un alimento considerando qualità e quantità
  static double calculateProteinScore(Food food) {
    double score = food.proteins;

    // Bonus per alimenti con proteine complete (carne, pesce, uova, latticini)
    if (_hasCompleteProteins(food)) {
      score *= 1.2;
    }

    // Bonus per alimenti tradizionali italiani
    if (_isTraditionalItalian(food)) {
      score *= 1.1;
    }

    // Penalità per alimenti processati
    if (_isProcessedFood(food)) {
      score *= 0.9;
    }

    return score;
  }

  /// Verifica se un alimento contiene proteine complete
  static bool _hasCompleteProteins(Food food) {
    final completeProteinCategories = [
      FoodCategory.protein, // Carne, pesce
      FoodCategory.dairy,   // Latticini
    ];

    return food.categories.any((category) => 
        completeProteinCategories.contains(category));
  }

  /// Verifica se un alimento è processato
  static bool _isProcessedFood(Food food) {
    final processedKeywords = [
      'processato', 'industriale', 'confezionato',
      'surgelato', 'inscatolato'
    ];

    final foodDescription = food.description.toLowerCase();
    return processedKeywords.any((keyword) => 
        foodDescription.contains(keyword));
  }

  /// Distribuisce le proteine ottimalmente tra i pasti per atleti
  static Map<MealType, double> distributeProteinAcrossMeals(
    int totalDailyProtein,
    int mealsPerDay,
  ) {
    final distribution = <MealType, double>{};

    switch (mealsPerDay) {
      case 3:
        // Distribuzione per 3 pasti: colazione, pranzo, cena
        distribution[MealType.breakfast] = totalDailyProtein * 0.25; // 25%
        distribution[MealType.lunch] = totalDailyProtein * 0.40;     // 40%
        distribution[MealType.dinner] = totalDailyProtein * 0.35;    // 35%
        break;

      case 4:
        // Distribuzione per 4 pasti: colazione, pranzo, spuntino, cena
        distribution[MealType.breakfast] = totalDailyProtein * 0.25; // 25%
        distribution[MealType.lunch] = totalDailyProtein * 0.30;     // 30%
        distribution[MealType.snack] = totalDailyProtein * 0.15;     // 15%
        distribution[MealType.dinner] = totalDailyProtein * 0.30;    // 30%
        break;

      case 5:
        // Distribuzione per 5 pasti: colazione, spuntino, pranzo, spuntino, cena
        distribution[MealType.breakfast] = totalDailyProtein * 0.20; // 20%
        distribution[MealType.snack] = totalDailyProtein * 0.15;     // 15% (mattina)
        distribution[MealType.lunch] = totalDailyProtein * 0.30;     // 30%
        distribution[MealType.snack] = totalDailyProtein * 0.15;     // 15% (pomeriggio)
        distribution[MealType.dinner] = totalDailyProtein * 0.20;    // 20%
        break;

      default:
        // Distribuzione uniforme per altri casi
        final proteinPerMeal = totalDailyProtein / mealsPerDay;
        for (final mealType in MealType.values) {
          distribution[mealType] = proteinPerMeal;
        }
    }

    return distribution;
  }

  /// Valida che la distribuzione proteica sia ottimale per il recupero muscolare
  static bool validateProteinDistribution(Map<MealType, double> distribution) {
    // Verifica che ogni pasto contenga almeno 20g di proteine per la sintesi proteica
    const minProteinPerMeal = 20.0;
    
    return distribution.values.every((protein) => protein >= minProteinPerMeal);
  }
}
