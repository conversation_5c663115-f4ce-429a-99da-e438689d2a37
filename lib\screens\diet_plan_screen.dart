import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import '../models/diet_plan.dart';
import '../models/food.dart';
import '../models/meal.dart';
import '../services/storage_service.dart';
import '../services/diet_statistics_service.dart';
import '../theme/app_theme.dart';
import 'food_detail_screen.dart';
import 'meal_nutrition_detail_screen.dart';

class DietPlanScreen extends StatefulWidget {
  final WeeklyDietPlan dietPlan;

  const DietPlanScreen({
    super.key,
    required this.dietPlan,
  });

  @override
  State<DietPlanScreen> createState() => _DietPlanScreenState();
}

class _DietPlanScreenState extends State<DietPlanScreen> {
  StorageService? _storageService;
  int _selectedDayIndex = 0;
  bool _isApplyingPlan = false;

  @override
  void initState() {
    super.initState();
    _initializeServices();

    // Imposta il giorno selezionato al giorno corrente
    final now = DateTime.now();
    final today = DateFormat('yyyy-MM-dd').format(now);

    int dayIndex = widget.dietPlan.dailyPlans.indexWhere((plan) => plan.date == today);
    if (dayIndex == -1) {
      dayIndex = 0; // Se non troviamo il giorno corrente, seleziona il primo giorno
    }

    _selectedDayIndex = dayIndex;
  }

  Future<void> _initializeServices() async {
    try {
      _storageService = await StorageService.getInstance();
    } catch (e) {
      print('Errore nell\'inizializzazione dei servizi: $e');
    }
  }

  Future<void> _applyDailyPlanToMeals() async {
    if (_storageService == null) return;

    setState(() {
      _isApplyingPlan = true;
    });

    try {
      final selectedDate = widget.dietPlan.dailyPlans[_selectedDayIndex].date;
      await _storageService!.applicaDietPlanGiornaliero(selectedDate);

      // Mostra un messaggio di successo
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Piano applicato ai pasti giornalieri'),
            backgroundColor: AppTheme.successColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      print('Errore nell\'applicazione del piano: $e');

      // Mostra un messaggio di errore
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Errore nell\'applicazione del piano'),
            backgroundColor: AppTheme.errorColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } finally {
      setState(() {
        _isApplyingPlan = false;
      });
    }
  }

  Future<void> _toggleMealCompleted(int mealIndex) async {
    final dailyPlan = widget.dietPlan.dailyPlans[_selectedDayIndex];
    final meal = dailyPlan.meals[mealIndex];

    // Crea una copia del pasto con lo stato completato invertito
    final updatedMeal = meal.copyWith(isCompleted: !meal.isCompleted);

    // Crea una copia dei pasti con il pasto aggiornato
    final updatedMeals = List<PlannedMeal>.from(dailyPlan.meals);
    updatedMeals[mealIndex] = updatedMeal;

    // Crea una copia del piano giornaliero con i pasti aggiornati
    final updatedDailyPlan = DailyDietPlan(
      date: dailyPlan.date,
      meals: updatedMeals,
      calorieTarget: dailyPlan.calorieTarget,
      macroTargets: dailyPlan.macroTargets,
    );

    // Crea una copia del piano settimanale con il piano giornaliero aggiornato
    final updatedWeeklyPlan = widget.dietPlan.copyWith(
      dailyPlans: List<DailyDietPlan>.from(widget.dietPlan.dailyPlans)
        ..[_selectedDayIndex] = updatedDailyPlan,
    );

    // Salva il piano aggiornato
    if (_storageService != null) {
      _storageService!.salvaDietPlan(updatedWeeklyPlan);
    }

    // Aggiorna le statistiche
    try {
      final statsService = DietStatisticsService();
      await statsService.updateStatsOnMealCompletion();
    } catch (e) {
      print('Errore nell\'aggiornamento delle statistiche: $e');
    }

    // Aggiorna lo stato
    setState(() {
      // Aggiorna il riferimento al piano
      widget.dietPlan.dailyPlans[_selectedDayIndex] = updatedDailyPlan;
    });
  }

  /// Salva il piano dietetico nel storage
  Future<void> _saveDietPlan() async {
    if (_storageService == null) return;

    try {
      await _storageService!.salvaDietPlan(widget.dietPlan);

      // Mostra un messaggio di conferma
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Piano alimentare salvato con successo!'),
            backgroundColor: AppTheme.successColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      print('Errore nel salvataggio del piano dietetico: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Errore nel salvataggio del piano'),
            backgroundColor: AppTheme.errorColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  String _formatDate(String dateString) {
    final date = DateTime.parse(dateString);
    return DateFormat('EEEE, d MMMM').format(date);
  }

  @override
  Widget build(BuildContext context) {
    final dailyPlan = widget.dietPlan.dailyPlans[_selectedDayIndex];

    return Scaffold(
      appBar: AppBar(
        title: const Text('Piano Dietetico'),
        actions: [
          IconButton(
            icon: const Icon(FontAwesomeIcons.fileExport),
            tooltip: 'Esporta piano',
            onPressed: () {
              // TODO: Implementare l'esportazione del piano
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Funzionalità di esportazione in arrivo'),
                ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Selettore giorni
          Container(
            height: 110,
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: widget.dietPlan.dailyPlans.length,
              itemBuilder: (context, index) {
                final day = widget.dietPlan.dailyPlans[index];
                final date = DateTime.parse(day.date);
                final isSelected = index == _selectedDayIndex;

                // Formatta il giorno della settimana in modo più breve
                final dayName = DateFormat('E').format(date).substring(0, 3);
                final monthName = DateFormat('MMM').format(date).substring(0, 3);

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedDayIndex = index;
                    });
                  },
                  child: Container(
                    width: 70,
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppTheme.primaryColor
                          : Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: isSelected
                          ? [
                              BoxShadow(
                                color: AppTheme.primaryColor.withOpacity(0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ]
                          : null,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Giorno della settimana
                          Container(
                            margin: const EdgeInsets.only(bottom: 4),
                            child: Text(
                              dayName,
                              style: TextStyle(
                                fontSize: 12,
                                color: isSelected ? Colors.white : Colors.black,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          // Numero del giorno
                          Container(
                            margin: const EdgeInsets.only(bottom: 4),
                            child: Text(
                              date.day.toString(),
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: isSelected ? Colors.white : Colors.black,
                              ),
                            ),
                          ),
                          // Mese
                          Text(
                            monthName,
                            style: TextStyle(
                              fontSize: 11,
                              color: isSelected
                                  ? Colors.white.withOpacity(0.8)
                                  : Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

          // Intestazione giorno selezionato
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _formatDate(dailyPlan.date),
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _isApplyingPlan ? null : _applyDailyPlanToMeals,
                  icon: _isApplyingPlan
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : const Icon(FontAwesomeIcons.check, size: 16),
                  label: Text(_isApplyingPlan ? 'Applicando...' : 'Applica piano'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Riepilogo nutrizionale
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  children: [
                    // Calorie
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Calorie:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '${dailyPlan.totalCaloriesPlanned} / ${dailyPlan.calorieTarget} kcal',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 8),

                    // Barra progresso calorie
                    LinearProgressIndicator(
                      value: dailyPlan.calorieTarget > 0
                          ? (dailyPlan.totalCaloriesPlanned / dailyPlan.calorieTarget).clamp(0.0, 1.0)
                          : 0.0,
                      backgroundColor: Colors.grey.shade200,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        dailyPlan.totalCaloriesPlanned <= dailyPlan.calorieTarget
                            ? AppTheme.successColor
                            : AppTheme.errorColor,
                      ),
                      minHeight: 8,
                      borderRadius: BorderRadius.circular(4),
                    ),

                    const SizedBox(height: 16),

                    // Macronutrienti
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        // Proteine
                        _buildMacronutrientInfo(
                          'Proteine',
                          dailyPlan.getMacroValuePlanned('proteins').round(),
                          dailyPlan.getMacroTarget('proteins'),
                          AppTheme.proteinColor,
                        ),

                        // Carboidrati
                        _buildMacronutrientInfo(
                          'Carboidrati',
                          dailyPlan.getMacroValuePlanned('carbs').round(),
                          dailyPlan.getMacroTarget('carbs'),
                          AppTheme.carbColor,
                        ),

                        // Grassi
                        _buildMacronutrientInfo(
                          'Grassi',
                          dailyPlan.getMacroValuePlanned('fats').round(),
                          dailyPlan.getMacroTarget('fats'),
                          AppTheme.fatColor,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Lista pasti
          Expanded(
            child: dailyPlan.meals.isEmpty
                ? const Center(
                    child: Text('Nessun pasto pianificato per questo giorno'),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: dailyPlan.meals.length,
                    itemBuilder: (context, index) {
                      final meal = dailyPlan.meals[index];
                      return _buildMealCard(meal, index);
                    },
                  ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: ElevatedButton.icon(
            onPressed: () async {
              // Salva il piano dietetico prima di tornare indietro
              await _saveDietPlan();
              // Conferma l'utilizzo del piano e torna indietro
              Navigator.pop(context, true);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            icon: const Icon(FontAwesomeIcons.check),
            label: const Text(
              'Utilizza questo Piano Alimentare',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMealCard(PlannedMeal meal, int mealIndex) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Intestazione pasto
          InkWell(
            onTap: () {
              // Converti il PlannedMeal in un Meal per la visualizzazione dettagliata
              final Meal mealForDetails = _convertPlannedMealToMeal(meal);

              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MealNutritionDetailScreen(
                    meal: mealForDetails,
                    mealTitle: meal.name,
                  ),
                ),
              );
            },
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(12),
              topRight: Radius.circular(12),
            ),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        _getMealTypeIcon(meal.type.toString().split('.').last),
                        color: Colors.white,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        meal.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Icon(
                        Icons.bar_chart,
                        color: Colors.white,
                        size: 16,
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Text(
                        meal.time,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${meal.totalCalories} kcal',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Contenuto pasto
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Lista alimenti
                ...meal.foods.map((food) => _buildFoodItem(food)).toList(),

                const SizedBox(height: 12),

                // Macronutrienti
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildMacroChip(
                      'P',
                      meal.getMacroValue('proteins').round(),
                      AppTheme.proteinColor,
                    ),
                    _buildMacroChip(
                      'C',
                      meal.getMacroValue('carbs').round(),
                      AppTheme.carbColor,
                    ),
                    _buildMacroChip(
                      'G',
                      meal.getMacroValue('fats').round(),
                      AppTheme.fatColor,
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // Pulsante completato
                Center(
                  child: ElevatedButton.icon(
                    onPressed: () async => await _toggleMealCompleted(mealIndex),
                    icon: Icon(
                      meal.isCompleted
                          ? FontAwesomeIcons.circleCheck
                          : FontAwesomeIcons.circle,
                      size: 16,
                    ),
                    label: Text(meal.isCompleted ? 'Completato' : 'Segna come completato'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: meal.isCompleted
                          ? AppTheme.successColor
                          : Colors.grey.shade300,
                      foregroundColor: meal.isCompleted
                          ? Colors.white
                          : Colors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFoodItem(FoodPortion foodPortion) {
    return InkWell(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => FoodDetailScreen(food: foodPortion.food),
          ),
        );
      },
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            // Icona categoria
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: _getFoodCategoryColor(foodPortion.food.categories.first).withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _getFoodCategoryIcon(foodPortion.food.categories.first),
                color: _getFoodCategoryColor(foodPortion.food.categories.first),
                size: 16,
              ),
            ),

            const SizedBox(width: 12),

            // Nome e quantità
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    foodPortion.food.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${foodPortion.grams}g',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),

            // Calorie
            Text(
              '${foodPortion.calories} kcal',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),

            // Icona per indicare che ci sono più dettagli
            const SizedBox(width: 8),
            Icon(
              Icons.info_outline,
              size: 16,
              color: Colors.blue.shade400,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMacronutrientInfo(String label, int value, int target, Color color) {
    final percentage = target > 0 ? (value / target).clamp(0.0, 1.0) : 0.0;

    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '$value / $target g',
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        SizedBox(
          width: 60,
          height: 4,
          child: LinearProgressIndicator(
            value: percentage,
            backgroundColor: color.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ),
      ],
    );
  }

  Widget _buildMacroChip(String label, int value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color, width: 1),
      ),
      child: Row(
        children: [
          Text(
            label,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            '$value g',
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getMealTypeIcon(String mealType) {
    if (mealType == 'breakfast') {
      return FontAwesomeIcons.mugSaucer;
    } else if (mealType == 'lunch') {
      return FontAwesomeIcons.bowlFood;
    } else if (mealType == 'dinner') {
      return FontAwesomeIcons.utensils;
    } else if (mealType == 'snack') {
      return FontAwesomeIcons.apple;
    } else {
      return FontAwesomeIcons.utensils;
    }
  }

  IconData _getFoodCategoryIcon(FoodCategory category) {
    switch (category) {
      case FoodCategory.fruit:
        return FontAwesomeIcons.apple;
      case FoodCategory.vegetable:
        return FontAwesomeIcons.carrot;
      case FoodCategory.grain:
        return FontAwesomeIcons.breadSlice;
      case FoodCategory.protein:
        return FontAwesomeIcons.drumstickBite;
      case FoodCategory.dairy:
        return FontAwesomeIcons.cheese;
      case FoodCategory.fat:
        return FontAwesomeIcons.oilWell;
      case FoodCategory.sweet:
        return FontAwesomeIcons.candyCane;
      case FoodCategory.beverage:
        return FontAwesomeIcons.wineGlass;
      case FoodCategory.mixed:
        return FontAwesomeIcons.bowlFood;
      case FoodCategory.condiment:
        return FontAwesomeIcons.pepperHot;
      case FoodCategory.other:
        return FontAwesomeIcons.utensils;
    }
  }

  Color _getFoodCategoryColor(FoodCategory category) {
    switch (category) {
      case FoodCategory.fruit:
        return Colors.red;
      case FoodCategory.vegetable:
        return Colors.green;
      case FoodCategory.grain:
        return Colors.amber;
      case FoodCategory.protein:
        return AppTheme.proteinColor;
      case FoodCategory.dairy:
        return Colors.blue.shade200;
      case FoodCategory.fat:
        return AppTheme.fatColor;
      case FoodCategory.sweet:
        return Colors.pink;
      case FoodCategory.beverage:
        return Colors.lightBlue;
      case FoodCategory.mixed:
        return Colors.purple;
      case FoodCategory.condiment:
        return Colors.deepOrange;
      case FoodCategory.other:
        return Colors.grey;
    }
  }

  // Converte un PlannedMeal in un Meal per la visualizzazione dettagliata
  Meal _convertPlannedMealToMeal(PlannedMeal plannedMeal) {
    // Converti i FoodPortion in FoodItem
    List<FoodItem> foodItems = plannedMeal.foods.map((foodPortion) {
      return FoodItem(
        food: foodPortion.food,
        quantity: foodPortion.grams.toDouble(),
      );
    }).toList();

    return Meal(
      nome: plannedMeal.name,
      orario: plannedMeal.time,
      calorie: plannedMeal.totalCalories,
      proteine: plannedMeal.getMacroValue('proteins'),
      carboidrati: plannedMeal.getMacroValue('carbs'),
      grassi: plannedMeal.getMacroValue('fats'),
      completato: plannedMeal.isCompleted,
      foods: foodItems,
    );
  }
}
