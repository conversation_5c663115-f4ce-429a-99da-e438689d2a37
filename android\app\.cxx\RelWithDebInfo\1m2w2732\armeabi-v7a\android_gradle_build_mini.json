{"buildFiles": ["C:\\Users\\<USER>\\Downloads\\flutter_windows_3.29.3-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\augment-projects\\app-dieta\\app_dieta\\android\\app\\.cxx\\RelWithDebInfo\\1m2w2732\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\augment-projects\\app-dieta\\app_dieta\\android\\app\\.cxx\\RelWithDebInfo\\1m2w2732\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}