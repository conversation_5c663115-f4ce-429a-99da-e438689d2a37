import '../models/user_profile.dart';
import '../models/food.dart';
import '../services/athletic_food_selector_service.dart';
import '../data/athletic_italian_proteins.dart';

/// Demo per mostrare le funzionalità del sistema di dieta atletica
class AthleticDietDemo {
  
  /// Dimostra il calcolo delle proteine per diversi livelli di attività
  static void demonstrateProteinCalculations() {
    print('=== DEMO: Calcolo Proteine per Livelli di Attività ===\n');

    // Profili di esempio
    final profiles = [
      UserProfile(
        id: 'sedentary_user',
        name: '<PERSON>',
        age: 35,
        gender: Gender.female,
        height: 165,
        weight: 60,
        activityLevel: ActivityLevel.sedentary,
        goal: Goal.weightLoss,
        dietType: DietType.omnivore,
        allergies: [],
        dislikedFoods: [],
        mealsPerDay: 3,
      ),
      UserProfile(
        id: 'moderate_user',
        name: '<PERSON>',
        age: 30,
        gender: Gender.male,
        height: 175,
        weight: 75,
        activityLevel: ActivityLevel.moderatelyActive,
        goal: Goal.maintenance,
        dietType: DietType.omnivore,
        allergies: [],
        dislikedFoods: [],
        mealsPerDay: 4,
      ),
      UserProfile(
        id: 'athlete_user',
        name: 'Sofia Atleta',
        age: 26,
        gender: Gender.female,
        height: 170,
        weight: 65,
        activityLevel: ActivityLevel.veryActive,
        goal: Goal.weightGain,
        dietType: DietType.omnivore,
        allergies: [],
        dislikedFoods: [],
        mealsPerDay: 5,
      ),
      UserProfile(
        id: 'elite_user',
        name: 'Luca Elite',
        age: 24,
        gender: Gender.male,
        height: 180,
        weight: 80,
        activityLevel: ActivityLevel.extremelyActive,
        goal: Goal.weightGain,
        dietType: DietType.omnivore,
        allergies: [],
        dislikedFoods: [],
        mealsPerDay: 6,
      ),
    ];

    for (final profile in profiles) {
      print('👤 ${profile.name} (${_getActivityLevelName(profile.activityLevel)})');
      print('   Peso: ${profile.weight}kg | Obiettivo: ${_getGoalName(profile.goal)}');
      
      final proteinPerKg = profile.calculateProteinPerKgBodyWeight();
      final totalProtein = profile.calculateOptimalProteinGrams();
      final macroDistribution = profile.calculateMacroDistribution();
      final proteinPercentage = (macroDistribution['proteins']! * 100).round();
      final calorieTarget = profile.calculateCalorieTarget();
      
      print('   🎯 Target Proteici:');
      print('      • ${proteinPerKg.toStringAsFixed(1)}g per kg di peso corporeo');
      print('      • ${totalProtein}g totali al giorno');
      print('      • $proteinPercentage% delle calorie totali (${calorieTarget} kcal)');
      print('      • Usa calcolo basato sul peso: ${profile.shouldUseWeightBasedProtein() ? "SÌ" : "NO"}');
      
      if (profile.activityLevel == ActivityLevel.veryActive || 
          profile.activityLevel == ActivityLevel.extremelyActive) {
        print('   ⭐ ATLETA: Proteine potenziate per recupero muscolare ottimale');
      }
      
      print('');
    }
  }

  /// Dimostra la selezione di alimenti proteici italiani per atleti
  static void demonstrateItalianProteinSelection() {
    print('=== DEMO: Alimenti Proteici Italiani per Atleti ===\n');

    final athleticProteins = AthleticItalianProteins.getAthleticProteins();
    
    print('📊 Database Alimenti Proteici Italiani:');
    print('   • Totale alimenti: ${athleticProteins.length}');
    
    final avgProtein = athleticProteins.map((f) => f.proteins).reduce((a, b) => a + b) / athleticProteins.length;
    print('   • Proteine medie: ${avgProtein.toStringAsFixed(1)}g per 100g');
    
    final highProteinFoods = athleticProteins.where((f) => f.proteins >= 20.0).length;
    print('   • Alimenti con >20g proteine: $highProteinFoods');
    
    print('\n🏆 Top 5 Alimenti Proteici Italiani:');
    final sortedByProtein = List<Food>.from(athleticProteins)
      ..sort((a, b) => b.proteins.compareTo(a.proteins));
    
    for (int i = 0; i < 5 && i < sortedByProtein.length; i++) {
      final food = sortedByProtein[i];
      print('   ${i + 1}. ${food.name}');
      print('      • ${food.proteins.toStringAsFixed(1)}g proteine per 100g');
      print('      • ${food.calories} kcal per 100g');
      print('      • Categorie: ${food.categories.map((c) => c.toString().split('.').last).join(', ')}');
      print('      • Pasti adatti: ${food.suitableForMeals.map((m) => _getMealTypeName(m)).join(', ')}');
      print('');
    }
  }

  /// Dimostra la distribuzione delle proteine tra i pasti per atleti
  static void demonstrateProteinDistribution() {
    print('=== DEMO: Distribuzione Proteine per Pasto ===\n');

    final testCases = [
      {'protein': 120, 'meals': 3, 'type': 'Standard (3 pasti)'},
      {'protein': 150, 'meals': 4, 'type': 'Atleta (4 pasti)'},
      {'protein': 180, 'meals': 5, 'type': 'Atleta Elite (5 pasti)'},
    ];

    for (final testCase in testCases) {
      final totalProtein = testCase['protein'] as int;
      final mealsPerDay = testCase['meals'] as int;
      final type = testCase['type'] as String;
      
      print('🍽️ $type - ${totalProtein}g proteine totali:');
      
      final distribution = AthleticFoodSelectorService.distributeProteinAcrossMeals(
        totalProtein,
        mealsPerDay,
      );
      
      distribution.forEach((mealType, proteinAmount) {
        final mealName = _getMealTypeName(mealType);
        final percentage = (proteinAmount / totalProtein * 100).round();
        print('   • $mealName: ${proteinAmount.toStringAsFixed(1)}g ($percentage%)');
      });
      
      final isValid = AthleticFoodSelectorService.validateProteinDistribution(distribution);
      print('   ✅ Distribuzione valida per sintesi proteica: ${isValid ? "SÌ" : "NO"}');
      print('');
    }
  }

  /// Dimostra la selezione intelligente di alimenti per atleti vs non atleti
  static void demonstrateFoodSelection() {
    print('=== DEMO: Selezione Alimenti Atleti vs Non Atleti ===\n');

    final athleteProfile = UserProfile(
      id: 'demo_athlete',
      name: 'Demo Atleta',
      age: 28,
      gender: Gender.male,
      height: 175,
      weight: 75,
      activityLevel: ActivityLevel.veryActive,
      goal: Goal.maintenance,
      dietType: DietType.omnivore,
      allergies: [],
      dislikedFoods: [],
      mealsPerDay: 4,
    );

    final regularProfile = UserProfile(
      id: 'demo_regular',
      name: 'Demo Normale',
      age: 35,
      gender: Gender.female,
      height: 165,
      weight: 60,
      activityLevel: ActivityLevel.lightlyActive,
      goal: Goal.maintenance,
      dietType: DietType.omnivore,
      allergies: [],
      dislikedFoods: [],
      mealsPerDay: 3,
    );

    final allFoods = AthleticItalianProteins.getAthleticProteins();
    
    print('🏃‍♂️ Selezione per ATLETA (pranzo):');
    final athleteFoods = AthleticFoodSelectorService.selectHighProteinItalianFoods(
      allFoods,
      athleteProfile,
      MealType.lunch,
    );
    
    print('   • Alimenti selezionati: ${athleteFoods.length}');
    print('   • Top 3 alimenti:');
    for (int i = 0; i < 3 && i < athleteFoods.length; i++) {
      final food = athleteFoods[i];
      final score = AthleticFoodSelectorService.calculateProteinScore(food);
      print('     ${i + 1}. ${food.name} (Score: ${score.toStringAsFixed(1)})');
      print('        ${food.proteins}g proteine, ${food.calories} kcal per 100g');
    }
    
    print('\n🚶‍♀️ Selezione per UTENTE NORMALE (pranzo):');
    final regularFoods = AthleticFoodSelectorService.selectHighProteinItalianFoods(
      allFoods,
      regularProfile,
      MealType.lunch,
    );
    
    print('   • Alimenti selezionati: ${regularFoods.length}');
    print('   • Differenza nella prioritizzazione: ${athleteFoods.length != regularFoods.length ? "SÌ" : "NO"}');
    
    if (athleteFoods.length == regularFoods.length) {
      print('   • Per utenti non atleti, viene usata la selezione standard');
    } else {
      print('   • Gli atleti ricevono una selezione prioritizzata per le proteine');
    }
    
    print('');
  }

  /// Dimostra i benefici nutrizionali per atleti
  static void demonstrateAthleticBenefits() {
    print('=== DEMO: Benefici Nutrizionali per Atleti ===\n');

    print('🎯 Vantaggi del Sistema Proteico Atletico Dr. Staffilano:');
    print('');
    
    print('1. 🧬 CALCOLO SCIENTIFICO PERSONALIZZATO:');
    print('   • Proteine basate sul peso corporeo (1.6-2.2g/kg)');
    print('   • Adattamento automatico per livello di attività');
    print('   • Rispetto dei limiti di sicurezza medica (max 35% calorie)');
    print('');
    
    print('2. 🇮🇹 AUTENTICITÀ ITALIANA:');
    print('   • Alimenti tradizionali italiani ad alto valore biologico');
    print('   • Bresaola della Valtellina, Parmigiano Reggiano DOP');
    print('   • Pesce del Mediterraneo, ricotta siciliana');
    print('   • Metodi di cottura tradizionali e sicuri');
    print('');
    
    print('3. ⚡ OTTIMIZZAZIONE PERFORMANCE:');
    print('   • Distribuzione proteica ottimale per sintesi muscolare');
    print('   • Minimo 20g proteine per pasto per massima efficacia');
    print('   • Timing nutrizionale per recupero post-allenamento');
    print('   • Bilanciamento con carboidrati per energia');
    print('');
    
    print('4. 🏥 SICUREZZA MEDICA DR. STAFFILANO:');
    print('   • Supervisione cardiologica per atleti di alto livello');
    print('   • Prevenzione sovraccarico renale');
    print('   • Mantenimento equilibrio elettrolitico');
    print('   • Supporto per salute cardiovascolare');
    print('');
    
    print('5. 📊 INTEGRAZIONE WELLJOURNEY:');
    print('   • Tracking automatico progressi proteici');
    print('   • Badge e sfide specifiche per atleti');
    print('   • Analisi NutriScore avanzata');
    print('   • Community di atleti italiani');
    print('');
  }

  // Metodi di utilità per la demo
  static String _getActivityLevelName(ActivityLevel level) {
    switch (level) {
      case ActivityLevel.sedentary: return 'Sedentario';
      case ActivityLevel.lightlyActive: return 'Leggermente Attivo';
      case ActivityLevel.moderatelyActive: return 'Moderatamente Attivo';
      case ActivityLevel.veryActive: return 'Molto Attivo';
      case ActivityLevel.extremelyActive: return 'Estremamente Attivo';
    }
  }

  static String _getGoalName(Goal goal) {
    switch (goal) {
      case Goal.weightLoss: return 'Perdita Peso';
      case Goal.maintenance: return 'Mantenimento';
      case Goal.weightGain: return 'Aumento Peso';
    }
  }

  static String _getMealTypeName(MealType mealType) {
    switch (mealType) {
      case MealType.breakfast: return 'Colazione';
      case MealType.lunch: return 'Pranzo';
      case MealType.dinner: return 'Cena';
      case MealType.snack: return 'Spuntino';
    }
  }

  /// Esegue tutte le demo
  static void runAllDemos() {
    print('🏃‍♂️ SISTEMA DIETA ATLETICA DR. STAFFILANO 🇮🇹');
    print('=' * 60);
    print('Potenziamento algoritmo generazione diete per atleti');
    print('Integrazione con database alimenti italiani tradizionali');
    print('=' * 60);
    print('');

    demonstrateProteinCalculations();
    print('');
    
    demonstrateItalianProteinSelection();
    print('');
    
    demonstrateProteinDistribution();
    print('');
    
    demonstrateFoodSelection();
    print('');
    
    demonstrateAthleticBenefits();
    
    print('🎉 DEMO COMPLETATA - Sistema Pronto per Atleti Italiani! 🇮🇹');
  }
}
