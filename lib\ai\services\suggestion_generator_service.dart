import '../../models/food.dart';
import '../../models/user_profile.dart';
import '../../services/food_database_service.dart';
import '../models/food_oracle_models.dart';
import 'nutrition_calculator_service.dart';

/// Servizio per la generazione di suggerimenti nutrizionali
class SuggestionGeneratorService {
  static final SuggestionGeneratorService _instance = SuggestionGeneratorService._internal();
  
  /// Servizio per il calcolo dei valori nutrizionali
  final NutritionCalculatorService _nutritionCalculator = NutritionCalculatorService();
  
  /// Servizio per il database degli alimenti
  late FoodDatabaseService _foodDatabaseService;
  
  /// Flag di inizializzazione
  bool _isInitialized = false;

  factory SuggestionGeneratorService() {
    return _instance;
  }

  SuggestionGeneratorService._internal();

  /// Inizializza il servizio
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    // Inizializza il servizio del database degli alimenti
    _foodDatabaseService = await FoodDatabaseService.getInstance();
    
    _isInitialized = true;
  }

  /// Genera suggerimenti per migliorare un pasto
  /// [analysisResult] - I risultati dell'analisi
  /// [userProfile] - Il profilo dell'utente
  /// Restituisce una lista di suggerimenti
  Future<List<FoodOracleSuggestion>> generateSuggestions(
    FoodOracleAnalysisResult analysisResult,
    UserProfile userProfile
  ) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    final suggestions = <FoodOracleSuggestion>[];
    
    // Analizza la distribuzione dei macronutrienti
    final macroSuggestions = await _generateMacroSuggestions(
      analysisResult,
      userProfile,
    );
    suggestions.addAll(macroSuggestions);
    
    // Analizza la varietà alimentare
    final varietySuggestions = await _generateVarietySuggestions(
      analysisResult,
      userProfile,
    );
    suggestions.addAll(varietySuggestions);
    
    // Analizza i micronutrienti
    final micronutrientSuggestions = await _generateMicronutrientSuggestions(
      analysisResult,
      userProfile,
    );
    suggestions.addAll(micronutrientSuggestions);
    
    // Analizza l'allineamento con gli obiettivi dell'utente
    final goalSuggestions = await _generateGoalAlignmentSuggestions(
      analysisResult,
      userProfile,
    );
    suggestions.addAll(goalSuggestions);
    
    // Ordina i suggerimenti per rilevanza
    suggestions.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));
    
    // Limita il numero di suggerimenti
    if (suggestions.length > 5) {
      return suggestions.sublist(0, 5);
    }
    
    return suggestions;
  }

  /// Genera suggerimenti per migliorare la distribuzione dei macronutrienti
  Future<List<FoodOracleSuggestion>> _generateMacroSuggestions(
    FoodOracleAnalysisResult analysisResult,
    UserProfile userProfile
  ) async {
    final suggestions = <FoodOracleSuggestion>[];
    
    // Calcola la distribuzione dei macronutrienti
    final macroDistribution = _nutritionCalculator.calculateMacroDistribution(
      analysisResult.totalNutritionalValues,
    );
    
    // Verifica se la distribuzione è sbilanciata
    final proteinPercentage = macroDistribution['proteins'] ?? 0;
    final carbPercentage = macroDistribution['carbs'] ?? 0;
    final fatPercentage = macroDistribution['fats'] ?? 0;
    
    // Suggerimenti per le proteine
    if (proteinPercentage < 15) {
      // Trova un alimento ricco di proteine
      final proteinFoods = await _foodDatabaseService.searchFoodsByNutrient(
        'proteins',
        minValue: 15,
        limit: 5,
      );
      
      if (proteinFoods.isNotEmpty) {
        final suggestedFood = proteinFoods.first;
        suggestions.add(FoodOracleSuggestion(
          type: SuggestionType.addFood,
          message: 'Questo pasto è povero di proteine. Considera di aggiungere ${suggestedFood.name} per un migliore equilibrio nutrizionale.',
          suggestedFood: suggestedFood,
          suggestedGrams: 100,
          relevanceScore: 0.9,
        ));
      } else {
        suggestions.add(FoodOracleSuggestion(
          type: SuggestionType.balanceMacros,
          message: 'Questo pasto è povero di proteine. Considera di aggiungere una fonte proteica come pollo, pesce, legumi o tofu.',
          relevanceScore: 0.8,
        ));
      }
    }
    
    // Suggerimenti per i carboidrati
    if (carbPercentage < 30) {
      // Trova un alimento ricco di carboidrati complessi
      final carbFoods = await _foodDatabaseService.searchFoodsByNutrient(
        'carbs',
        minValue: 20,
        limit: 5,
      );
      
      if (carbFoods.isNotEmpty) {
        final suggestedFood = carbFoods.first;
        suggestions.add(FoodOracleSuggestion(
          type: SuggestionType.addFood,
          message: 'Questo pasto è povero di carboidrati. Considera di aggiungere ${suggestedFood.name} per un migliore equilibrio energetico.',
          suggestedFood: suggestedFood,
          suggestedGrams: 80,
          relevanceScore: 0.7,
        ));
      }
    } else if (carbPercentage > 70) {
      suggestions.add(FoodOracleSuggestion(
        type: SuggestionType.balanceMacros,
        message: 'Questo pasto è molto ricco di carboidrati. Considera di bilanciarlo con più proteine e grassi sani.',
        relevanceScore: 0.7,
      ));
    }
    
    // Suggerimenti per i grassi
    if (fatPercentage < 15) {
      // Trova un alimento ricco di grassi sani
      final fatFoods = await _foodDatabaseService.searchFoodsByNutrient(
        'fats',
        minValue: 10,
        limit: 5,
      );
      
      if (fatFoods.isNotEmpty) {
        final suggestedFood = fatFoods.first;
        suggestions.add(FoodOracleSuggestion(
          type: SuggestionType.addFood,
          message: 'Questo pasto è povero di grassi sani. Considera di aggiungere ${suggestedFood.name} per un migliore equilibrio nutrizionale.',
          suggestedFood: suggestedFood,
          suggestedGrams: 30,
          relevanceScore: 0.6,
        ));
      }
    } else if (fatPercentage > 40) {
      suggestions.add(FoodOracleSuggestion(
        type: SuggestionType.balanceMacros,
        message: 'Questo pasto è molto ricco di grassi. Considera di bilanciarlo con più proteine e carboidrati complessi.',
        relevanceScore: 0.6,
      ));
    }
    
    return suggestions;
  }

  /// Genera suggerimenti per migliorare la varietà alimentare
  Future<List<FoodOracleSuggestion>> _generateVarietySuggestions(
    FoodOracleAnalysisResult analysisResult,
    UserProfile userProfile
  ) async {
    final suggestions = <FoodOracleSuggestion>[];
    
    // Verifica le categorie alimentari presenti nel pasto
    final presentCategories = <FoodCategory>{};
    for (final detectedFood in analysisResult.detectedFoods) {
      presentCategories.addAll(detectedFood.food.categories);
    }
    
    // Verifica se mancano categorie importanti
    if (!presentCategories.contains(FoodCategory.vegetable)) {
      // Trova un alimento della categoria mancante
      final vegetables = await _foodDatabaseService.searchFoodsByCategory(
        FoodCategory.vegetable,
        limit: 5,
      );
      
      if (vegetables.isNotEmpty) {
        final suggestedFood = vegetables.first;
        suggestions.add(FoodOracleSuggestion(
          type: SuggestionType.addFood,
          message: 'Questo pasto non contiene verdure. Considera di aggiungere ${suggestedFood.name} per aumentare la varietà nutrizionale.',
          suggestedFood: suggestedFood,
          suggestedGrams: 100,
          relevanceScore: 0.8,
        ));
      }
    }
    
    if (!presentCategories.contains(FoodCategory.fruit) && 
        !presentCategories.contains(FoodCategory.vegetable)) {
      // Trova un frutto
      final fruits = await _foodDatabaseService.searchFoodsByCategory(
        FoodCategory.fruit,
        limit: 5,
      );
      
      if (fruits.isNotEmpty) {
        final suggestedFood = fruits.first;
        suggestions.add(FoodOracleSuggestion(
          type: SuggestionType.addFood,
          message: 'Questo pasto non contiene frutta né verdura. Considera di aggiungere ${suggestedFood.name} per aumentare l\'apporto di vitamine e antiossidanti.',
          suggestedFood: suggestedFood,
          suggestedGrams: 150,
          relevanceScore: 0.7,
        ));
      }
    }
    
    // Se il pasto ha meno di 3 alimenti, suggerisci di aumentare la varietà
    if (analysisResult.detectedFoods.length < 3) {
      suggestions.add(FoodOracleSuggestion(
        type: SuggestionType.general,
        message: 'Questo pasto ha poca varietà. Considera di aggiungere più alimenti diversi per un migliore profilo nutrizionale.',
        relevanceScore: 0.6,
      ));
    }
    
    return suggestions;
  }

  /// Genera suggerimenti per migliorare l'apporto di micronutrienti
  Future<List<FoodOracleSuggestion>> _generateMicronutrientSuggestions(
    FoodOracleAnalysisResult analysisResult,
    UserProfile userProfile
  ) async {
    final suggestions = <FoodOracleSuggestion>[];
    
    // Verifica se ci sono micronutrienti importanti mancanti
    final micronutrients = analysisResult.totalNutritionalValues.micronutrients;
    
    // Verifica il calcio
    if (!micronutrients.containsKey('calcium') || micronutrients['calcium']! < 100) {
      // Trova un alimento ricco di calcio
      final calciumFoods = await _foodDatabaseService.searchFoodsByMicronutrient(
        'calcium',
        minValue: 200,
        limit: 5,
      );
      
      if (calciumFoods.isNotEmpty) {
        final suggestedFood = calciumFoods.first;
        suggestions.add(FoodOracleSuggestion(
          type: SuggestionType.improveMicronutrients,
          message: 'Questo pasto è povero di calcio. Considera di aggiungere ${suggestedFood.name} per migliorare la salute delle ossa.',
          suggestedFood: suggestedFood,
          suggestedGrams: 100,
          relevanceScore: 0.6,
        ));
      }
    }
    
    // Verifica il potassio
    if (!micronutrients.containsKey('potassium') || micronutrients['potassium']! < 300) {
      // Trova un alimento ricco di potassio
      final potassiumFoods = await _foodDatabaseService.searchFoodsByMicronutrient(
        'potassium',
        minValue: 400,
        limit: 5,
      );
      
      if (potassiumFoods.isNotEmpty) {
        final suggestedFood = potassiumFoods.first;
        suggestions.add(FoodOracleSuggestion(
          type: SuggestionType.improveMicronutrients,
          message: 'Questo pasto è povero di potassio. Considera di aggiungere ${suggestedFood.name} per migliorare la salute cardiovascolare.',
          suggestedFood: suggestedFood,
          suggestedGrams: 100,
          relevanceScore: 0.5,
        ));
      }
    }
    
    return suggestions;
  }

  /// Genera suggerimenti per allineare il pasto con gli obiettivi dell'utente
  Future<List<FoodOracleSuggestion>> _generateGoalAlignmentSuggestions(
    FoodOracleAnalysisResult analysisResult,
    UserProfile userProfile
  ) async {
    final suggestions = <FoodOracleSuggestion>[];
    
    // Verifica l'allineamento con l'obiettivo dell'utente
    switch (userProfile.goal) {
      case Goal.weightLoss:
        // Verifica se il pasto è troppo calorico
        final totalCalories = analysisResult.totalNutritionalValues.calories;
        final targetCalories = userProfile.calculateTDEE() * 0.8 / 3; // Approssimazione per un pasto
        
        if (totalCalories > targetCalories * 1.2) {
          // Trova l'alimento più calorico
          final mostCaloricFood = analysisResult.detectedFoods
              .reduce((a, b) => a.nutritionalValues.calories > b.nutritionalValues.calories ? a : b);
          
          suggestions.add(FoodOracleSuggestion(
            type: SuggestionType.adjustPortion,
            message: 'Questo pasto è piuttosto calorico per il tuo obiettivo di perdita di peso. Considera di ridurre la porzione di ${mostCaloricFood.food.name}.',
            suggestedFood: mostCaloricFood.food,
            suggestedGrams: (mostCaloricFood.estimatedGrams * 0.7).round(),
            relevanceScore: 0.9,
          ));
        }
        break;
        
      case Goal.weightGain:
        // Verifica se il pasto è poco calorico
        final totalCalories = analysisResult.totalNutritionalValues.calories;
        final targetCalories = userProfile.calculateTDEE() * 1.2 / 3; // Approssimazione per un pasto
        
        if (totalCalories < targetCalories * 0.8) {
          // Trova un alimento calorico da aggiungere
          final caloricFoods = await _foodDatabaseService.searchFoodsByNutrient(
            'calories',
            minValue: 300,
            limit: 5,
          );
          
          if (caloricFoods.isNotEmpty) {
            final suggestedFood = caloricFoods.first;
            suggestions.add(FoodOracleSuggestion(
              type: SuggestionType.addFood,
              message: 'Questo pasto è poco calorico per il tuo obiettivo di aumento di peso. Considera di aggiungere ${suggestedFood.name} per aumentare l\'apporto calorico.',
              suggestedFood: suggestedFood,
              suggestedGrams: 100,
              relevanceScore: 0.9,
            ));
          }
        }
        break;
        
      case Goal.maintenance:
        // Verifica l'equilibrio generale del pasto
        final qualityScore = _nutritionCalculator.calculateMealQualityScore(
          analysisResult.detectedFoods,
        );
        
        if (qualityScore < 60) {
          suggestions.add(FoodOracleSuggestion(
            type: SuggestionType.general,
            message: 'Questo pasto potrebbe essere più equilibrato. Cerca di includere una buona fonte di proteine, carboidrati complessi e grassi sani.',
            relevanceScore: 0.7,
          ));
        }
        break;
    }
    
    return suggestions;
  }

  /// Verifica se il servizio è inizializzato
  bool isInitialized() {
    return _isInitialized;
  }
}
