import 'package:flutter/foundation.dart';
import '../../models/food.dart';
import '../../models/user_profile.dart';
import '../../models/diet_plan.dart';

/// Rappresenta un feedback dell'utente su un alimento o un pasto
class UserFeedback {
  final String id;
  final String userId;
  final String? foodId;
  final String? mealId;
  final int rating; // 1-5
  final String? comment;
  final DateTime createdAt;

  UserFeedback({
    required this.id,
    required this.userId,
    this.foodId,
    this.mealId,
    required this.rating,
    this.comment,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'foodId': foodId,
      'mealId': mealId,
      'rating': rating,
      'comment': comment,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory UserFeedback.fromMap(Map<String, dynamic> map) {
    return UserFeedback(
      id: map['id'],
      userId: map['userId'],
      foodId: map['foodId'],
      mealId: map['mealId'],
      rating: map['rating'],
      comment: map['comment'],
      createdAt: DateTime.parse(map['createdAt']),
    );
  }
}

/// Rappresenta una preferenza alimentare appresa dall'AI
class LearnedPreference {
  final String id;
  final String userId;
  final String foodId;
  final double preferenceScore; // -1.0 (forte avversione) a 1.0 (forte preferenza)
  final DateTime updatedAt;

  LearnedPreference({
    required this.id,
    required this.userId,
    required this.foodId,
    required this.preferenceScore,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'foodId': foodId,
      'preferenceScore': preferenceScore,
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory LearnedPreference.fromMap(Map<String, dynamic> map) {
    return LearnedPreference(
      id: map['id'],
      userId: map['userId'],
      foodId: map['foodId'],
      preferenceScore: map['preferenceScore'],
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }
}

/// Rappresenta un modello di raccomandazione per un alimento
class FoodRecommendation {
  final Food food;
  final double score; // 0.0 (non raccomandato) a 1.0 (altamente raccomandato)
  final String reason; // Motivo della raccomandazione

  FoodRecommendation({
    required this.food,
    required this.score,
    required this.reason,
  });
}

/// Rappresenta un modello di raccomandazione per un pasto
class MealRecommendation {
  final PlannedMeal meal;
  final double score; // 0.0 (non raccomandato) a 1.0 (altamente raccomandato)
  final String reason; // Motivo della raccomandazione

  MealRecommendation({
    required this.meal,
    required this.score,
    required this.reason,
  });
}

/// Rappresenta un modello di raccomandazione per un piano dietetico
class DietPlanRecommendation {
  final WeeklyDietPlan dietPlan;
  final double score; // 0.0 (non raccomandato) a 1.0 (altamente raccomandato)
  final String reason; // Motivo della raccomandazione

  DietPlanRecommendation({
    required this.dietPlan,
    required this.score,
    required this.reason,
  });
}

/// Rappresenta un contesto di apprendimento per l'AI
class AILearningContext {
  final UserProfile userProfile;
  final List<UserFeedback> feedbacks;
  final List<LearnedPreference> preferences;
  final List<Food> recentFoods;
  final List<PlannedMeal> recentMeals;

  AILearningContext({
    required this.userProfile,
    required this.feedbacks,
    required this.preferences,
    required this.recentFoods,
    required this.recentMeals,
  });
}

/// Rappresenta una richiesta di raccomandazione all'AI
class AIRecommendationRequest {
  final UserProfile userProfile;
  final String? mealType;
  final DateTime? mealTime;
  final int? targetCalories;
  final Map<String, int>? targetMacros;
  final List<String>? excludeFoodIds;
  final List<FoodCategory>? preferredCategories;

  AIRecommendationRequest({
    required this.userProfile,
    this.mealType,
    this.mealTime,
    this.targetCalories,
    this.targetMacros,
    this.excludeFoodIds,
    this.preferredCategories,
  });
}

/// Rappresenta una risposta di raccomandazione dall'AI
class AIRecommendationResponse {
  final List<FoodRecommendation> recommendedFoods;
  final List<MealRecommendation> recommendedMeals;
  final DietPlanRecommendation? recommendedDietPlan;
  final String message;

  AIRecommendationResponse({
    this.recommendedFoods = const [],
    this.recommendedMeals = const [],
    this.recommendedDietPlan,
    required this.message,
  });
}
