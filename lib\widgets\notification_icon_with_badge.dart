import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/notification_service.dart';
import '../theme/dr_staffilano_theme.dart';
import 'notifications_dialog.dart';

/// Widget per l'icona delle notifiche con badge del contatore
class NotificationIconWithBadge extends StatefulWidget {
  /// Dimensione dell'icona
  final double iconSize;

  /// Colore dell'icona
  final Color? iconColor;

  /// Se mostrare l'animazione quando arriva una nuova notifica
  final bool showAnimation;

  const NotificationIconWithBadge({
    super.key,
    this.iconSize = 24,
    this.iconColor,
    this.showAnimation = true,
  });

  @override
  State<NotificationIconWithBadge> createState() => _NotificationIconWithBadgeState();
}

class _NotificationIconWithBadgeState extends State<NotificationIconWithBadge>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _shakeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _shakeAnimation;

  int _previousUnreadCount = 0;

  @override
  void initState() {
    super.initState();

    // Animazione di pulsazione per il badge
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Animazione di scuotimento per l'icona
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _shakeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _shakeController,
      curve: Curves.elasticOut,
    ));

    // Avvia l'animazione di pulsazione se ci sono notifiche non lette
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final notificationService = context.read<NotificationService>();
      _previousUnreadCount = notificationService.unreadCount;
      if (_previousUnreadCount > 0) {
        _startPulseAnimation();
      }
    });
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _shakeController.dispose();
    super.dispose();
  }

  void _startPulseAnimation() {
    if (widget.showAnimation && mounted) {
      _pulseController.repeat(reverse: true);
    }
  }

  void _stopPulseAnimation() {
    if (mounted) {
      _pulseController.stop();
      _pulseController.reset();
    }
  }

  void _startShakeAnimation() {
    if (widget.showAnimation && mounted) {
      _shakeController.forward().then((_) {
        if (mounted) {
          _shakeController.reset();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<NotificationService>(
      builder: (context, notificationService, child) {
        final unreadCount = notificationService.unreadCount;

        // Gestisci le animazioni in base al cambiamento del contatore
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (unreadCount > _previousUnreadCount) {
            // Nuova notifica: scuoti l'icona e avvia la pulsazione
            _startShakeAnimation();
            _startPulseAnimation();
          } else if (unreadCount == 0 && _previousUnreadCount > 0) {
            // Tutte le notifiche lette: ferma la pulsazione
            _stopPulseAnimation();
          } else if (unreadCount > 0) {
            // Ci sono ancora notifiche non lette: mantieni la pulsazione
            _startPulseAnimation();
          }
          _previousUnreadCount = unreadCount;
        });

        return AnimatedBuilder(
          animation: _shakeAnimation,
          builder: (context, child) {
            final shakeOffset = _shakeAnimation.value * 2.0 *
                ((_shakeController.value * 4).floor() % 2 == 0 ? 1 : -1);

            return Transform.translate(
              offset: Offset(shakeOffset, 0),
              child: InkWell(
                onTap: () => _onNotificationTap(context),
                borderRadius: BorderRadius.circular(20),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Stack(
                    clipBehavior: Clip.none,
                    children: [
                      // Icona delle notifiche
                      Icon(
                        unreadCount > 0 ? Icons.notifications : Icons.notifications_none,
                        size: widget.iconSize,
                        color: widget.iconColor ??
                               (unreadCount > 0 ? DrStaffilanoTheme.secondaryBlue : Colors.grey[600]),
                      ),
                      // Badge con contatore
                      if (unreadCount > 0)
                        Positioned(
                          right: -2,
                          top: -2,
                          child: AnimatedBuilder(
                            animation: _pulseAnimation,
                            builder: (context, child) {
                              return Transform.scale(
                                scale: _pulseAnimation.value,
                                child: Container(
                                  padding: EdgeInsets.all(unreadCount > 99 ? 4 : 6),
                                  decoration: BoxDecoration(
                                    color: DrStaffilanoTheme.accentGold,
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Colors.white,
                                      width: 2,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: DrStaffilanoTheme.accentGold.withOpacity(0.3),
                                        blurRadius: 4,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  constraints: const BoxConstraints(
                                    minWidth: 20,
                                    minHeight: 20,
                                  ),
                                  child: Text(
                                    unreadCount > 99 ? '99+' : unreadCount.toString(),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// Gestisce il tap sull'icona delle notifiche
  void _onNotificationTap(BuildContext context) {
    // Ferma le animazioni quando l'utente apre le notifiche
    _stopPulseAnimation();

    // Mostra il dialog delle notifiche
    showNotificationsDialog(context);
  }
}

/// Widget semplificato per l'icona delle notifiche senza animazioni
class SimpleNotificationIcon extends StatelessWidget {
  final double iconSize;
  final Color? iconColor;
  final VoidCallback? onTap;

  const SimpleNotificationIcon({
    super.key,
    this.iconSize = 24,
    this.iconColor,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<NotificationService>(
      builder: (context, notificationService, child) {
        final unreadCount = notificationService.unreadCount;

        return InkWell(
          onTap: onTap ?? () => showNotificationsDialog(context),
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                Icon(
                  unreadCount > 0 ? Icons.notifications : Icons.notifications_none,
                  size: iconSize,
                  color: iconColor ??
                         (unreadCount > 0 ? DrStaffilanoTheme.secondaryBlue : Colors.grey[600]),
                ),
                if (unreadCount > 0)
                  Positioned(
                    right: -2,
                    top: -2,
                    child: Container(
                      padding: EdgeInsets.all(unreadCount > 99 ? 4 : 6),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.white,
                          width: 2,
                        ),
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 18,
                        minHeight: 18,
                      ),
                      child: Text(
                        unreadCount > 99 ? '99+' : unreadCount.toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 9,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}
