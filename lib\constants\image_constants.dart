class ImageConstants {
  // Immagini di sfondo
  static const String backgroundImage = 'https://images.unsplash.com/photo-1490818387583-1baba5e638af?q=80&w=1000';
  
  // Immagini per categorie di cibo
  static const String fruitCategory = 'https://images.unsplash.com/photo-1610832958506-aa56368176cf?q=80&w=500';
  static const String vegetableCategory = 'https://images.unsplash.com/photo-1540420773420-3366772f4999?q=80&w=500';
  static const String proteinCategory = 'https://images.unsplash.com/photo-1607623814075-e51df1bdc82f?q=80&w=500';
  static const String dairyCategory = 'https://images.unsplash.com/photo-1628689469838-524a4a973b8e?q=80&w=500';
  static const String grainCategory = 'https://images.unsplash.com/photo-1486887396153-fa416526c108?q=80&w=500';
  
  // Immagini per pasti
  static const String breakfastImage = 'https://images.unsplash.com/photo-1533089860892-a7c6f0a88666?q=80&w=500';
  static const String lunchImage = 'https://images.unsplash.com/photo-**********-affa22d38842?q=80&w=500';
  static const String dinnerImage = 'https://images.unsplash.com/photo-1576402187878-974f70c890a5?q=80&w=500';
  static const String snackImage = 'https://images.unsplash.com/photo-**********-fa8fdf82db35?q=80&w=500';
  
  // Immagini per ricette
  static const String healthyBowlRecipe = 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?q=80&w=500';
  static const String saladRecipe = 'https://images.unsplash.com/photo-**********-ba9599a7e63c?q=80&w=500';
  static const String smoothieRecipe = 'https://images.unsplash.com/photo-1502741224143-90386d7f8c82?q=80&w=500';
  static const String proteinRecipe = 'https://images.unsplash.com/photo-1432139555190-58524dae6a55?q=80&w=500';
  
  // Immagini motivazionali
  static const String motivationalImage1 = 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=500';
  static const String motivationalImage2 = 'https://images.unsplash.com/photo-1579126038374-6064e9370f0f?q=80&w=500';
  
  // Immagini per profilo utente
  static const String defaultProfileImage = 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=500';
  
  // Funzione per ottenere un'immagine casuale per un pasto in base al nome
  static String getImageForMeal(String mealName) {
    final mealNameLower = mealName.toLowerCase();
    
    if (mealNameLower.contains('colazione') || mealNameLower.contains('breakfast')) {
      return breakfastImage;
    } else if (mealNameLower.contains('pranzo') || mealNameLower.contains('lunch')) {
      return lunchImage;
    } else if (mealNameLower.contains('cena') || mealNameLower.contains('dinner')) {
      return dinnerImage;
    } else if (mealNameLower.contains('spuntino') || mealNameLower.contains('snack') || 
               mealNameLower.contains('merenda')) {
      return snackImage;
    } else {
      // Scelta casuale tra le immagini disponibili
      final now = DateTime.now();
      final index = now.millisecondsSinceEpoch % 4;
      
      switch (index) {
        case 0:
          return breakfastImage;
        case 1:
          return lunchImage;
        case 2:
          return dinnerImage;
        default:
          return snackImage;
      }
    }
  }
}
