import '../models/food.dart';

/// Database di alimenti italiani con dati nutrizionali accurati
class ItalianFoods {
  static List<Food> getItalianFoods() {
    return [
      // PRIMI PIATTI
      Food(
        id: 'pasta_1',
        name: 'Pasta al pomodoro',
        description: 'Pasta con salsa di pomodoro fresco, basilico e olio extravergine d\'oliva',
        imageUrl: 'https://images.unsplash.com/photo-1608219992759-8d74ed8d76eb?q=80&w=500',
        calories: 350,
        proteins: 10.5,
        carbs: 70.2,
        fats: 5.8,
        fiber: 4.2,
        sugar: 6.5,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain, FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        allergens: const ['glutine'],
        servingSize: '1 porzione (80g pasta secca)',
        servingSizeGrams: 250, // pasta cotta con sugo
        preparationTimeMinutes: 20,
        recipe: 'Cuocere la pasta in acqua salata. Preparare il sugo con pomodori freschi, aglio, basilico e olio EVO.',
        ingredients: const ['pasta di semola di grano duro', 'pomodori freschi', 'basilico', 'aglio', 'olio extravergine d\'oliva', 'sale'],
        isRecipe: true,
        complexity: 1,
        foodState: FoodState.cooked,
        rawToCookedFactor: 2.5, // la pasta aumenta di peso durante la cottura
        glycemicIndex: 55,
        glycemicLoad: 24,
        micronutrients: const {
          'vitamina A': 68.0, // μg
          'vitamina C': 12.0, // mg
          'calcio': 36.0, // mg
          'ferro': 1.8, // mg
          'potassio': 320.0, // mg
        },
        dataSource: DataSource.crea,
        sourceId: 'CREA-AN-P001',
        sourceDescription: 'Tabelle di Composizione degli Alimenti CREA',
        validationStatus: ValidationStatus.validated,
        lastValidatedAt: DateTime(2023, 5, 15),
        validatedBy: 'Nutrizionista CREA',
        tags: const ['pasta', 'primo piatto', 'tradizionale', 'mediterraneo'],
        commonServingSize1Description: '1 piatto',
        commonServingSize1Grams: 250,
      ),
      
      Food(
        id: 'pasta_2',
        name: 'Risotto alla milanese',
        description: 'Risotto con zafferano, burro e parmigiano',
        imageUrl: 'https://images.unsplash.com/photo-1633964913295-ceb43826e7c7?q=80&w=500',
        calories: 380,
        proteins: 9.0,
        carbs: 65.0,
        fats: 12.0,
        fiber: 1.5,
        sugar: 1.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        allergens: const ['latticini'],
        servingSize: '1 porzione (320g)',
        servingSizeGrams: 320,
        preparationTimeMinutes: 30,
        recipe: 'Tostare il riso con cipolla, sfumare con vino bianco, aggiungere brodo caldo e zafferano. A fine cottura mantecare con burro e parmigiano.',
        ingredients: const ['riso carnaroli', 'zafferano', 'cipolla', 'vino bianco', 'brodo vegetale', 'burro', 'parmigiano reggiano', 'sale'],
        isRecipe: true,
        complexity: 3,
        foodState: FoodState.cooked,
        rawToCookedFactor: 3.0,
        glycemicIndex: 69,
        glycemicLoad: 30,
        micronutrients: const {
          'vitamina A': 120.0,
          'calcio': 150.0,
          'ferro': 1.2,
          'potassio': 180.0,
        },
        dataSource: DataSource.crea,
        sourceId: 'CREA-AN-R001',
        sourceDescription: 'Tabelle di Composizione degli Alimenti CREA',
        validationStatus: ValidationStatus.validated,
        lastValidatedAt: DateTime(2023, 6, 10),
        validatedBy: 'Nutrizionista CREA',
        tags: const ['riso', 'primo piatto', 'tradizionale', 'lombardo', 'zafferano'],
        commonServingSize1Description: '1 piatto',
        commonServingSize1Grams: 320,
      ),
      
      Food(
        id: 'pasta_3',
        name: 'Pasta e fagioli',
        description: 'Zuppa tradizionale con pasta e fagioli, arricchita con erbe aromatiche',
        imageUrl: 'https://images.unsplash.com/photo-1604152135912-04a022e73f35?q=80&w=500',
        calories: 320,
        proteins: 16.0,
        carbs: 55.0,
        fats: 4.5,
        fiber: 12.0,
        sugar: 3.5,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain, FoodCategory.protein, FoodCategory.vegetable],
        isVegetarian: true,
        allergens: const ['glutine'],
        servingSize: '1 porzione (350g)',
        servingSizeGrams: 350,
        preparationTimeMinutes: 45,
        recipe: 'Cuocere i fagioli con sedano, carota e cipolla. Aggiungere la pasta e completare la cottura.',
        ingredients: const ['pasta mista', 'fagioli borlotti', 'sedano', 'carota', 'cipolla', 'rosmarino', 'olio extravergine d\'oliva', 'sale', 'pepe'],
        isRecipe: true,
        complexity: 2,
        foodState: FoodState.cooked,
        glycemicIndex: 48,
        glycemicLoad: 18,
        micronutrients: const {
          'vitamina A': 85.0,
          'vitamina C': 8.0,
          'calcio': 95.0,
          'ferro': 3.5,
          'potassio': 450.0,
        },
        dataSource: DataSource.crea,
        sourceId: 'CREA-AN-PF001',
        sourceDescription: 'Tabelle di Composizione degli Alimenti CREA',
        validationStatus: ValidationStatus.validated,
        lastValidatedAt: DateTime(2023, 5, 20),
        validatedBy: 'Nutrizionista CREA',
        tags: const ['pasta', 'fagioli', 'zuppa', 'tradizionale', 'povera', 'legumi'],
        commonServingSize1Description: '1 piatto fondo',
        commonServingSize1Grams: 350,
      ),
      
      // SECONDI PIATTI
      Food(
        id: 'secondo_1',
        name: 'Cotoletta alla milanese',
        description: 'Costoletta di vitello impanata e fritta nel burro chiarificato',
        imageUrl: 'https://images.unsplash.com/photo-1599921841143-819065a55cc6?q=80&w=500',
        calories: 450,
        proteins: 32.0,
        carbs: 25.0,
        fats: 28.0,
        fiber: 1.0,
        sugar: 0.5,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        allergens: const ['glutine', 'uova', 'latticini'],
        servingSize: '1 porzione (200g)',
        servingSizeGrams: 200,
        preparationTimeMinutes: 25,
        recipe: 'Battere la costoletta, passarla nell\'uovo e nel pangrattato. Friggere nel burro chiarificato.',
        ingredients: const ['costoletta di vitello', 'uova', 'pangrattato', 'burro chiarificato', 'sale'],
        isRecipe: true,
        complexity: 3,
        foodState: FoodState.cooked,
        glycemicIndex: 40,
        glycemicLoad: 10,
        micronutrients: const {
          'vitamina A': 150.0,
          'vitamina B12': 2.0,
          'calcio': 80.0,
          'ferro': 3.0,
          'zinco': 4.5,
        },
        dataSource: DataSource.crea,
        sourceId: 'CREA-AN-CM001',
        sourceDescription: 'Tabelle di Composizione degli Alimenti CREA',
        validationStatus: ValidationStatus.validated,
        lastValidatedAt: DateTime(2023, 6, 15),
        validatedBy: 'Nutrizionista CREA',
        tags: const ['carne', 'vitello', 'fritto', 'tradizionale', 'lombardo'],
        commonServingSize1Description: '1 cotoletta',
        commonServingSize1Grams: 200,
      ),
      
      Food(
        id: 'secondo_2',
        name: 'Parmigiana di melanzane',
        description: 'Melanzane fritte a strati con pomodoro, mozzarella e parmigiano',
        imageUrl: 'https://images.unsplash.com/photo-1629115916087-7e8c114a24ed?q=80&w=500',
        calories: 380,
        proteins: 15.0,
        carbs: 18.0,
        fats: 28.0,
        fiber: 6.0,
        sugar: 8.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable, FoodCategory.dairy],
        isVegetarian: true,
        allergens: const ['latticini'],
        servingSize: '1 porzione (250g)',
        servingSizeGrams: 250,
        preparationTimeMinutes: 60,
        recipe: 'Friggere le melanzane a fette. Comporre a strati con salsa di pomodoro, mozzarella e parmigiano. Cuocere in forno.',
        ingredients: const ['melanzane', 'pomodori pelati', 'mozzarella', 'parmigiano reggiano', 'basilico', 'olio extravergine d\'oliva', 'sale'],
        isRecipe: true,
        complexity: 3,
        foodState: FoodState.cooked,
        glycemicIndex: 35,
        glycemicLoad: 6,
        micronutrients: const {
          'vitamina A': 180.0,
          'vitamina C': 15.0,
          'calcio': 320.0,
          'ferro': 1.5,
          'potassio': 380.0,
        },
        dataSource: DataSource.crea,
        sourceId: 'CREA-AN-PM001',
        sourceDescription: 'Tabelle di Composizione degli Alimenti CREA',
        validationStatus: ValidationStatus.validated,
        lastValidatedAt: DateTime(2023, 7, 5),
        validatedBy: 'Nutrizionista CREA',
        tags: const ['melanzane', 'vegetariano', 'tradizionale', 'siciliano', 'forno'],
        commonServingSize1Description: '1 porzione',
        commonServingSize1Grams: 250,
      ),
      
      Food(
        id: 'secondo_3',
        name: 'Baccalà alla vicentina',
        description: 'Baccalà cotto lentamente con latte, cipolla e olio d\'oliva',
        imageUrl: 'https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?q=80&w=500',
        calories: 310,
        proteins: 28.0,
        carbs: 8.0,
        fats: 18.0,
        fiber: 1.0,
        sugar: 3.0,
        suitableForMeals: const [MealType.dinner],
        categories: const [FoodCategory.protein],
        allergens: const ['pesce', 'latticini'],
        servingSize: '1 porzione (200g)',
        servingSizeGrams: 200,
        preparationTimeMinutes: 180,
        recipe: 'Ammollare il baccalà. Cuocere lentamente con cipolla, latte e olio d\'oliva.',
        ingredients: const ['baccalà', 'cipolla', 'latte', 'olio extravergine d\'oliva', 'sale', 'pepe'],
        isRecipe: true,
        complexity: 4,
        foodState: FoodState.cooked,
        glycemicIndex: 30,
        glycemicLoad: 2,
        micronutrients: const {
          'vitamina D': 5.0,
          'vitamina B12': 3.5,
          'calcio': 150.0,
          'ferro': 1.2,
          'potassio': 420.0,
          'omega 3': 1800.0,
        },
        dataSource: DataSource.crea,
        sourceId: 'CREA-AN-BV001',
        sourceDescription: 'Tabelle di Composizione degli Alimenti CREA',
        validationStatus: ValidationStatus.validated,
        lastValidatedAt: DateTime(2023, 6, 25),
        validatedBy: 'Nutrizionista CREA',
        tags: const ['pesce', 'baccalà', 'tradizionale', 'veneto', 'slow food'],
        commonServingSize1Description: '1 porzione',
        commonServingSize1Grams: 200,
      ),
      
      // CONTORNI
      Food(
        id: 'contorno_1',
        name: 'Caponata siciliana',
        description: 'Melanzane, sedano, capperi e olive in agrodolce',
        imageUrl: 'https://images.unsplash.com/photo-1598511726623-d2e9996e1ddb?q=80&w=500',
        calories: 180,
        proteins: 3.0,
        carbs: 15.0,
        fats: 12.0,
        fiber: 6.0,
        sugar: 9.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        servingSize: '1 porzione (150g)',
        servingSizeGrams: 150,
        preparationTimeMinutes: 45,
        recipe: 'Friggere le melanzane a cubetti. Aggiungere sedano, capperi, olive, pomodoro, aceto e zucchero.',
        ingredients: const ['melanzane', 'sedano', 'capperi', 'olive verdi', 'pomodori', 'cipolla', 'aceto', 'zucchero', 'olio extravergine d\'oliva', 'sale'],
        isRecipe: true,
        complexity: 3,
        foodState: FoodState.cooked,
        glycemicIndex: 25,
        glycemicLoad: 4,
        micronutrients: const {
          'vitamina A': 120.0,
          'vitamina C': 25.0,
          'calcio': 60.0,
          'ferro': 1.8,
          'potassio': 350.0,
        },
        dataSource: DataSource.crea,
        sourceId: 'CREA-AN-CS001',
        sourceDescription: 'Tabelle di Composizione degli Alimenti CREA',
        validationStatus: ValidationStatus.validated,
        lastValidatedAt: DateTime(2023, 7, 10),
        validatedBy: 'Nutrizionista CREA',
        tags: const ['melanzane', 'contorno', 'tradizionale', 'siciliano', 'agrodolce', 'vegetariano', 'vegano'],
        commonServingSize1Description: '1 porzione',
        commonServingSize1Grams: 150,
      ),
      
      Food(
        id: 'contorno_2',
        name: 'Carciofi alla romana',
        description: 'Carciofi ripieni di mentuccia e aglio, cotti lentamente',
        imageUrl: 'https://images.unsplash.com/photo-1551465222-21f29e52e10f?q=80&w=500',
        calories: 120,
        proteins: 4.0,
        carbs: 12.0,
        fats: 7.0,
        fiber: 8.0,
        sugar: 2.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        servingSize: '2 carciofi (180g)',
        servingSizeGrams: 180,
        preparationTimeMinutes: 40,
        recipe: 'Pulire i carciofi e riempirli con un trito di mentuccia, aglio e prezzemolo. Cuocere lentamente in padella con acqua e olio.',
        ingredients: const ['carciofi', 'mentuccia', 'aglio', 'prezzemolo', 'olio extravergine d\'oliva', 'sale', 'pepe', 'limone'],
        isRecipe: true,
        complexity: 3,
        foodState: FoodState.cooked,
        isSeasonal: true,
        seasonalMonths: const [1, 2, 3, 4, 5],
        glycemicIndex: 20,
        glycemicLoad: 2,
        micronutrients: const {
          'vitamina C': 15.0,
          'vitamina K': 18.0,
          'folati': 80.0,
          'magnesio': 60.0,
          'potassio': 320.0,
        },
        dataSource: DataSource.crea,
        sourceId: 'CREA-AN-CR001',
        sourceDescription: 'Tabelle di Composizione degli Alimenti CREA',
        validationStatus: ValidationStatus.validated,
        lastValidatedAt: DateTime(2023, 4, 15),
        validatedBy: 'Nutrizionista CREA',
        tags: const ['carciofi', 'contorno', 'tradizionale', 'romano', 'vegetariano', 'vegano', 'stagionale'],
        commonServingSize1Description: '2 carciofi',
        commonServingSize1Grams: 180,
      ),
    ];
  }
}
