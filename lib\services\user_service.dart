import 'package:uuid/uuid.dart';
import '../models/user_profile.dart';
import 'storage_service.dart';

/// Servizio per la gestione degli utenti
class UserService {
  static UserService? _instance;
  StorageService? _storageService;
  UserProfile? _currentUserProfile;
  final Uuid _uuid = Uuid();

  /// Ottiene l'istanza singleton del servizio utenti
  static Future<UserService> getInstance() async {
    if (_instance == null) {
      _instance = UserService._();
      await _instance!._initialize();
    }
    return _instance!;
  }

  UserService._();

  /// Inizializza il servizio utenti
  Future<void> _initialize() async {
    // Inizializza il servizio di storage
    _storageService = await StorageService.getInstance();

    // Carica il profilo utente corrente
    await _loadCurrentUserProfile();
  }

  /// Carica il profilo utente corrente
  Future<void> _loadCurrentUserProfile() async {
    try {
      // In una implementazione reale, qui caricheremmo il profilo dal database
      // Per ora, creiamo un profilo di default se non esiste
      if (_currentUserProfile == null) {
        _currentUserProfile = UserProfile(
          id: _uuid.v4(),
          name: 'Utente',
          age: 30,
          gender: Gender.male,
          height: 175,
          weight: 70,
          activityLevel: ActivityLevel.moderatelyActive,
          goal: Goal.maintenance,
          dietType: DietType.omnivore,
          allergies: [],
          mealsPerDay: 3,
        );

        // Salva il profilo
        await _saveCurrentUserProfile();
      }
    } catch (e) {
      print('Errore nel caricamento del profilo utente: $e');
    }
  }

  /// Salva il profilo utente corrente
  Future<void> _saveCurrentUserProfile() async {
    try {
      // In una implementazione reale, qui salveremmo il profilo nel database
      // Per ora, non facciamo nulla
    } catch (e) {
      print('Errore nel salvataggio del profilo utente: $e');
    }
  }

  /// Ottiene il profilo utente corrente
  Future<UserProfile> getCurrentUserProfile() async {
    if (_currentUserProfile == null) {
      await _loadCurrentUserProfile();
    }
    return _currentUserProfile!;
  }

  /// Aggiorna il profilo utente corrente
  Future<void> updateUserProfile(UserProfile userProfile) async {
    _currentUserProfile = userProfile;
    await _saveCurrentUserProfile();
  }

  /// Crea un nuovo profilo utente
  Future<UserProfile> createUserProfile({
    required String name,
    required int age,
    required Gender gender,
    required int height,
    required int weight,
    required ActivityLevel activityLevel,
    required Goal goal,
    required DietType dietType,
    required List<String> allergies,
    required int mealsPerDay,
  }) async {
    final userProfile = UserProfile(
      id: _uuid.v4(),
      name: name,
      age: age,
      gender: gender,
      height: height,
      weight: weight,
      activityLevel: activityLevel,
      goal: goal,
      dietType: dietType,
      allergies: allergies,
      mealsPerDay: mealsPerDay,
    );

    _currentUserProfile = userProfile;
    await _saveCurrentUserProfile();

    return userProfile;
  }
}
