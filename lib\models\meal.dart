import 'dart:convert';
import 'food.dart';

class FoodItem {
  final Food food;
  final double quantity; // in grammi

  FoodItem({
    required this.food,
    required this.quantity,
  });

  // Converti da Map a FoodItem
  factory FoodItem.fromMap(Map<String, dynamic> map) {
    return FoodItem(
      food: Food.fromMap(map['food'] as Map<String, dynamic>),
      quantity: map['quantity'] as double,
    );
  }

  // Converti da FoodItem a Map
  Map<String, dynamic> toMap() {
    return {
      'food': food.toMap(),
      'quantity': quantity,
    };
  }
}

class Meal {
  String nome;
  String orario;
  int calorie;
  double proteine; // in grammi
  double carboidrati; // in grammi
  double grassi; // in grammi
  bool completato;
  List<FoodItem> foods; // Alimenti che compongono il pasto

  Meal({
    required this.nome,
    required this.orario,
    required this.calorie,
    this.proteine = 0.0,
    this.carboidrati = 0.0,
    this.grassi = 0.0,
    this.completato = false,
    this.foods = const [],
  });

  // Calcola le calorie dai macronutrienti
  int calcolaCalorieDaMacro() {
    // 1g proteine = 4 kcal, 1g carboidrati = 4 kcal, 1g grassi = 9 kcal
    return (proteine * 4 + carboidrati * 4 + grassi * 9).round();
  }

  // Aggiorna le calorie in base ai macronutrienti
  void aggiornaCaloireDaMacro() {
    calorie = calcolaCalorieDaMacro();
  }

  // Calcola i valori nutrizionali dagli alimenti
  void calcolaNutrientiDaAlimenti() {
    if (foods.isEmpty) return;

    double totalProteins = 0;
    double totalCarbs = 0;
    double totalFats = 0;
    int totalCalories = 0;

    for (var foodItem in foods) {
      final food = foodItem.food;
      final servingRatio = foodItem.quantity / food.servingSizeGrams;

      totalProteins += food.proteins * servingRatio;
      totalCarbs += food.carbs * servingRatio;
      totalFats += food.fats * servingRatio;
      totalCalories += (food.calories * servingRatio).round();
    }

    proteine = totalProteins;
    carboidrati = totalCarbs;
    grassi = totalFats;
    calorie = totalCalories;
  }

  // Converti da Map a Meal
  factory Meal.fromMap(Map<String, dynamic> map) {
    List<FoodItem> foodItems = [];
    if (map.containsKey('foods') && map['foods'] != null) {
      final foodsList = map['foods'] as List;
      foodItems = foodsList
          .map((item) => FoodItem.fromMap(item as Map<String, dynamic>))
          .toList();
    }

    return Meal(
      nome: map['nome'] as String,
      orario: map['orario'] as String,
      calorie: map['calorie'] as int,
      proteine: (map['proteine'] ?? 0.0) as double,
      carboidrati: (map['carboidrati'] ?? 0.0) as double,
      grassi: (map['grassi'] ?? 0.0) as double,
      completato: map['completato'] as bool,
      foods: foodItems,
    );
  }

  // Converti da Meal a Map
  Map<String, dynamic> toMap() {
    return {
      'nome': nome,
      'orario': orario,
      'calorie': calorie,
      'proteine': proteine,
      'carboidrati': carboidrati,
      'grassi': grassi,
      'completato': completato,
      'foods': foods.map((item) => item.toMap()).toList(),
    };
  }

  // Converti da JSON a Meal
  factory Meal.fromJson(Map<String, dynamic> json) {
    return Meal.fromMap(json);
  }

  // Converti da Meal a JSON
  Map<String, dynamic> toJson() {
    return toMap();
  }

  // Crea una copia del pasto con possibilità di sovrascrivere alcuni campi
  Meal copyWith({
    String? nome,
    String? orario,
    int? calorie,
    double? proteine,
    double? carboidrati,
    double? grassi,
    bool? completato,
    List<FoodItem>? foods,
  }) {
    return Meal(
      nome: nome ?? this.nome,
      orario: orario ?? this.orario,
      calorie: calorie ?? this.calorie,
      proteine: proteine ?? this.proteine,
      carboidrati: carboidrati ?? this.carboidrati,
      grassi: grassi ?? this.grassi,
      completato: completato ?? this.completato,
      foods: foods ?? this.foods,
    );
  }
}
