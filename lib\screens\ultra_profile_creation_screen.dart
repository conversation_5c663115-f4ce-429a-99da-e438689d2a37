import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/ultra_detailed_profile.dart';
import '../models/user_profile.dart';
import '../services/ultra_profile_service.dart';
import '../theme/dr_staffilano_theme.dart';


/// SCHERMATA CREAZIONE PROFILO ULTRA-DETTAGLIATO
/// Guida l'utente attraverso la creazione di un profilo completo
class UltraProfileCreationScreen extends StatefulWidget {
  final UltraDetailedProfile? existingProfile;

  const UltraProfileCreationScreen({
    Key? key,
    this.existingProfile,
  }) : super(key: key);

  @override
  State<UltraProfileCreationScreen> createState() => _UltraProfileCreationScreenState();
}

class _UltraProfileCreationScreenState extends State<UltraProfileCreationScreen> {
  final PageController _pageController = PageController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  int _currentStep = 0;
  bool _isLoading = false;
  String _errorMessage = '';

  // Form controllers
  final _nameController = TextEditingController();
  final _ageController = TextEditingController();
  final _weightController = TextEditingController();
  final _heightController = TextEditingController();
  final _bodyFatController = TextEditingController();

  // Form data
  Gender _selectedGender = Gender.male;
  Goal _selectedGoal = Goal.maintenance;
  ActivityLevel _selectedActivityLevel = ActivityLevel.moderatelyActive;
  PrimaryGoal _selectedPrimaryGoal = PrimaryGoal.weightLoss;
  List<SecondaryGoal> _selectedSecondaryGoals = [];
  DietaryRegimen? _selectedDietaryRegimen = DietaryRegimen.mediterranean;
  List<String> _preferredFoods = [];
  List<String> _dislikedFoods = [];
  BudgetLevel _selectedBudgetLevel = BudgetLevel.medium;
  CookingTime _selectedCookingTime = CookingTime.medium;
  CookingSkillLevel _selectedCookingSkillLevel = CookingSkillLevel.intermediate;
  int _mealsPerDay = 4;
  bool _allowCheatMeals = false;
  bool _preferOrganicFoods = false;
  bool _enableCalorieCycling = false;
  bool _enableMealTiming = false;
  bool _hasConsultedDoctor = false;

  final List<String> _stepTitles = [
    'Informazioni Base',
    'Composizione Corporea',
    'Obiettivi',
    'Preferenze Alimentari',
    'Preferenze Avanzate',
    'Riepilogo',
  ];

  @override
  void initState() {
    super.initState();
    _loadExistingData();
  }

  void _loadExistingData() {
    if (widget.existingProfile != null) {
      final profile = widget.existingProfile!;
      _nameController.text = profile.baseProfile.name;
      _ageController.text = profile.baseProfile.age.toString();
      _weightController.text = profile.baseProfile.weight.toString();
      _heightController.text = profile.baseProfile.height.toString();
      _selectedGender = profile.baseProfile.gender;
      _selectedGoal = profile.baseProfile.goal;
      _selectedActivityLevel = profile.baseProfile.activityLevel;
      _selectedPrimaryGoal = profile.primaryGoal;
      _selectedSecondaryGoals = List.from(profile.secondaryGoals);
      _selectedDietaryRegimen = profile.dietaryRegimen;
      _preferredFoods = List.from(profile.preferredFoods);
      _dislikedFoods = List.from(profile.dislikedFoods);
      _selectedBudgetLevel = profile.budgetLevel;
      _selectedCookingTime = profile.cookingTime;
      _selectedCookingSkillLevel = profile.cookingSkillLevel;
      _mealsPerDay = profile.mealsPerDay;
      _allowCheatMeals = profile.allowCheatMeals;
      _preferOrganicFoods = profile.preferOrganicFoods;
      _enableCalorieCycling = profile.enableCalorieCycling;
      _enableMealTiming = profile.enableMealTiming;
      _hasConsultedDoctor = profile.hasConsultedDoctor;

      if (profile.bodyFatPercentage != null) {
        _bodyFatController.text = profile.bodyFatPercentage!.toString();
      }
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _nameController.dispose();
    _ageController.dispose();
    _weightController.dispose();
    _heightController.dispose();
    _bodyFatController.dispose();
    super.dispose();
  }

  void _nextStep() {
    if (_currentStep < _stepTitles.length - 1) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final profileService = UltraProfileService();

      final profile = await profileService.createUltraProfile(
        name: _nameController.text.trim(),
        age: int.parse(_ageController.text),
        gender: _selectedGender,
        weight: double.parse(_weightController.text),
        height: double.parse(_heightController.text),
        goal: _selectedGoal,
        bodyFatPercentage: _bodyFatController.text.isNotEmpty
            ? double.parse(_bodyFatController.text)
            : null,
        bodyCompositionMethod: _bodyFatController.text.isNotEmpty
            ? BodyCompositionMethod.bioelectricalImpedance
            : null,
        workActivity: _convertActivityLevelToWorkActivity(_selectedActivityLevel),
        neatLevel: NEATLevel.medium,
        primaryGoal: _selectedPrimaryGoal,
        secondaryGoals: _selectedSecondaryGoals,
        dietaryRegimen: _selectedDietaryRegimen,
        preferredFoods: _preferredFoods,
        dislikedFoods: _dislikedFoods,
        budgetLevel: _selectedBudgetLevel,
        cookingTime: _selectedCookingTime,
        mealsPerDay: _mealsPerDay,
        cookingSkillLevel: _selectedCookingSkillLevel,
        allowCheatMeals: _allowCheatMeals,
        preferOrganicFoods: _preferOrganicFoods,
        enableCalorieCycling: _enableCalorieCycling,
        enableMealTiming: _enableMealTiming,
        hasConsultedDoctor: _hasConsultedDoctor,
      );

      if (mounted) {
        Navigator.pop(context, profile);
      }

    } catch (e) {
      setState(() {
        _errorMessage = 'Errore salvataggio profilo: $e';
        _isLoading = false;
      });
    }
  }

  WorkActivity _convertActivityLevelToWorkActivity(ActivityLevel level) {
    switch (level) {
      case ActivityLevel.sedentary:
        return WorkActivity.sedentary;
      case ActivityLevel.lightlyActive:
        return WorkActivity.light;
      case ActivityLevel.moderatelyActive:
        return WorkActivity.moderate;
      case ActivityLevel.veryActive:
      case ActivityLevel.extremelyActive:
        return WorkActivity.heavy;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DrStaffilanoTheme.backgroundLight,
      appBar: AppBar(
        title: Text(widget.existingProfile != null ? 'Modifica Profilo' : 'Crea Profilo'),
        backgroundColor: DrStaffilanoTheme.primaryGreen,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          _buildProgressIndicator(),
          Expanded(
            child: Form(
              key: _formKey,
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  _buildBasicInfoStep(),
                  _buildBodyCompositionStep(),
                  _buildGoalsStep(),
                  _buildDietaryPreferencesStep(),
                  _buildAdvancedPreferencesStep(),
                  _buildSummaryStep(),
                ],
              ),
            ),
          ),
          _buildNavigationButtons(),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        children: [
          Row(
            children: [
              Text(
                'Passo ${_currentStep + 1} di ${_stepTitles.length}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
              Text(
                _stepTitles[_currentStep],
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: (_currentStep + 1) / _stepTitles.length,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(DrStaffilanoTheme.primaryGreen),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Informazioni di Base',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Iniziamo con le tue informazioni fondamentali',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),

          TextFormField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'Nome',
              border: OutlineInputBorder(),
              prefixIcon: Icon(FontAwesomeIcons.user),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Inserisci il tuo nome';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _ageController,
                  decoration: const InputDecoration(
                    labelText: 'Età',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(FontAwesomeIcons.calendar),
                    suffixText: 'anni',
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Inserisci la tua età';
                    }
                    final age = int.tryParse(value);
                    if (age == null || age < 16 || age > 100) {
                      return 'Età non valida';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<Gender>(
                  value: _selectedGender,
                  decoration: const InputDecoration(
                    labelText: 'Sesso',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(FontAwesomeIcons.venusMars),
                  ),
                  items: Gender.values.map((gender) {
                    return DropdownMenuItem(
                      value: gender,
                      child: Text(gender == Gender.male ? 'Maschio' : 'Femmina'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedGender = value!;
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _weightController,
                  decoration: const InputDecoration(
                    labelText: 'Peso',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(FontAwesomeIcons.weight),
                    suffixText: 'kg',
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Inserisci il tuo peso';
                    }
                    final weight = double.tryParse(value);
                    if (weight == null || weight < 30 || weight > 300) {
                      return 'Peso non valido';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _heightController,
                  decoration: const InputDecoration(
                    labelText: 'Altezza',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(FontAwesomeIcons.rulerVertical),
                    suffixText: 'cm',
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Inserisci la tua altezza';
                    }
                    final height = double.tryParse(value);
                    if (height == null || height < 100 || height > 250) {
                      return 'Altezza non valida';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          DropdownButtonFormField<ActivityLevel>(
            value: _selectedActivityLevel,
            decoration: const InputDecoration(
              labelText: 'Livello di Attività',
              border: OutlineInputBorder(),
              prefixIcon: Icon(FontAwesomeIcons.dumbbell),
            ),
            items: ActivityLevel.values.map((level) {
              return DropdownMenuItem(
                value: level,
                child: Text(_getActivityLevelLabel(level)),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedActivityLevel = value!;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBodyCompositionStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Composizione Corporea',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Informazioni opzionali per una maggiore precisione',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),

          TextFormField(
            controller: _bodyFatController,
            decoration: const InputDecoration(
              labelText: 'Percentuale di Grasso Corporeo (opzionale)',
              border: OutlineInputBorder(),
              prefixIcon: Icon(FontAwesomeIcons.percentage),
              suffixText: '%',
              helperText: 'Se non conosci questo valore, lascia vuoto',
            ),
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                final bodyFat = double.tryParse(value);
                if (bodyFat == null || bodyFat < 3 || bodyFat > 50) {
                  return 'Percentuale non valida (3-50%)';
                }
              }
              return null;
            },
          ),
          const SizedBox(height: 24),

          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      FontAwesomeIcons.lightbulb,
                      color: DrStaffilanoTheme.primaryGreen,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Suggerimento',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                const Text(
                  'La percentuale di grasso corporeo può essere misurata con bilance impedenziometriche, plicometri o scansioni DEXA. Se non hai questi dati, il sistema calcolerà automaticamente i valori ottimali.',
                  style: TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getActivityLevelLabel(ActivityLevel level) {
    switch (level) {
      case ActivityLevel.sedentary:
        return 'Sedentario (poco o nessun esercizio)';
      case ActivityLevel.lightlyActive:
        return 'Leggermente attivo (esercizio leggero 1-3 giorni/settimana)';
      case ActivityLevel.moderatelyActive:
        return 'Moderatamente attivo (esercizio moderato 3-5 giorni/settimana)';
      case ActivityLevel.veryActive:
        return 'Molto attivo (esercizio intenso 6-7 giorni/settimana)';
      case ActivityLevel.extremelyActive:
        return 'Estremamente attivo (esercizio molto intenso, lavoro fisico)';
    }
  }

  // Placeholder methods for remaining steps
  Widget _buildGoalsStep() {
    return const Center(child: Text('Goals Step - To be implemented'));
  }

  Widget _buildDietaryPreferencesStep() {
    return const Center(child: Text('Dietary Preferences Step - To be implemented'));
  }

  Widget _buildAdvancedPreferencesStep() {
    return const Center(child: Text('Advanced Preferences Step - To be implemented'));
  }

  Widget _buildSummaryStep() {
    return const Center(child: Text('Summary Step - To be implemented'));
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousStep,
                child: const Text('Indietro'),
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _currentStep == _stepTitles.length - 1
                  ? (_isLoading ? null : _saveProfile)
                  : _nextStep,
              style: ElevatedButton.styleFrom(
                backgroundColor: DrStaffilanoTheme.primaryGreen,
                foregroundColor: Colors.white,
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : Text(_currentStep == _stepTitles.length - 1 ? 'Salva Profilo' : 'Avanti'),
            ),
          ),
        ],
      ),
    );
  }
}