import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/ultra_detailed_profile.dart';
import '../models/user_profile.dart';
import '../services/ultra_profile_service.dart';
import '../theme/dr_staffilano_theme.dart';


/// SCHERMATA CREAZIONE PROFILO ULTRA-DETTAGLIATO
/// Guida l'utente attraverso la creazione di un profilo completo
class UltraProfileCreationScreen extends StatefulWidget {
  final UltraDetailedProfile? existingProfile;

  const UltraProfileCreationScreen({
    Key? key,
    this.existingProfile,
  }) : super(key: key);

  @override
  State<UltraProfileCreationScreen> createState() => _UltraProfileCreationScreenState();
}

class _UltraProfileCreationScreenState extends State<UltraProfileCreationScreen> {
  final PageController _pageController = PageController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  int _currentStep = 0;
  bool _isLoading = false;
  String _errorMessage = '';

  // Form controllers
  final _nameController = TextEditingController();
  final _ageController = TextEditingController();
  final _weightController = TextEditingController();
  final _heightController = TextEditingController();
  final _bodyFatController = TextEditingController();

  // Form data
  Gender _selectedGender = Gender.male;
  Goal _selectedGoal = Goal.maintenance;
  ActivityLevel _selectedActivityLevel = ActivityLevel.moderatelyActive;
  PrimaryGoal _selectedPrimaryGoal = PrimaryGoal.weightLoss;
  List<SecondaryGoal> _selectedSecondaryGoals = [];
  DietaryRegimen? _selectedDietaryRegimen = DietaryRegimen.mediterranean;
  List<String> _preferredFoods = [];
  List<String> _dislikedFoods = [];
  BudgetLevel _selectedBudgetLevel = BudgetLevel.medium;
  CookingTime _selectedCookingTime = CookingTime.medium;
  CookingSkillLevel _selectedCookingSkillLevel = CookingSkillLevel.intermediate;
  int _mealsPerDay = 4;
  bool _allowCheatMeals = false;
  bool _preferOrganicFoods = false;
  bool _enableCalorieCycling = false;
  bool _enableMealTiming = false;
  bool _hasConsultedDoctor = false;

  final List<String> _stepTitles = [
    'Informazioni Base',
    'Composizione Corporea',
    'Obiettivi',
    'Preferenze Alimentari',
    'Preferenze Avanzate',
    'Riepilogo',
  ];

  @override
  void initState() {
    super.initState();
    _loadExistingData();
  }

  void _loadExistingData() {
    if (widget.existingProfile != null) {
      final profile = widget.existingProfile!;
      _nameController.text = profile.baseProfile.name;
      _ageController.text = profile.baseProfile.age.toString();
      _weightController.text = profile.baseProfile.weight.toString();
      _heightController.text = profile.baseProfile.height.toString();
      _selectedGender = profile.baseProfile.gender;
      _selectedGoal = profile.baseProfile.goal;
      _selectedActivityLevel = profile.baseProfile.activityLevel;
      _selectedPrimaryGoal = profile.primaryGoal;
      _selectedSecondaryGoals = List.from(profile.secondaryGoals);
      _selectedDietaryRegimen = profile.dietaryRegimen;
      _preferredFoods = List.from(profile.preferredFoods);
      _dislikedFoods = List.from(profile.dislikedFoods);
      _selectedBudgetLevel = profile.budgetLevel;
      _selectedCookingTime = profile.cookingTime;
      _selectedCookingSkillLevel = profile.cookingSkillLevel;
      _mealsPerDay = profile.mealsPerDay;
      _allowCheatMeals = profile.allowCheatMeals;
      _preferOrganicFoods = profile.preferOrganicFoods;
      _enableCalorieCycling = profile.enableCalorieCycling;
      _enableMealTiming = profile.enableMealTiming;
      _hasConsultedDoctor = profile.hasConsultedDoctor;

      if (profile.bodyFatPercentage != null) {
        _bodyFatController.text = profile.bodyFatPercentage!.toString();
      }
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _nameController.dispose();
    _ageController.dispose();
    _weightController.dispose();
    _heightController.dispose();
    _bodyFatController.dispose();
    super.dispose();
  }

  void _nextStep() {
    if (_currentStep < _stepTitles.length - 1) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final profileService = UltraProfileService();

      final profile = await profileService.createUltraProfile(
        name: _nameController.text.trim(),
        age: int.parse(_ageController.text),
        gender: _selectedGender,
        weight: double.parse(_weightController.text),
        height: double.parse(_heightController.text),
        goal: _selectedGoal,
        bodyFatPercentage: _bodyFatController.text.isNotEmpty
            ? double.parse(_bodyFatController.text)
            : null,
        bodyCompositionMethod: _bodyFatController.text.isNotEmpty
            ? BodyCompositionMethod.bioelectricalImpedance
            : null,
        workActivity: _convertActivityLevelToWorkActivity(_selectedActivityLevel),
        neatLevel: NEATLevel.medium,
        primaryGoal: _selectedPrimaryGoal,
        secondaryGoals: _selectedSecondaryGoals,
        dietaryRegimen: _selectedDietaryRegimen,
        preferredFoods: _preferredFoods,
        dislikedFoods: _dislikedFoods,
        budgetLevel: _selectedBudgetLevel,
        cookingTime: _selectedCookingTime,
        mealsPerDay: _mealsPerDay,
        cookingSkillLevel: _selectedCookingSkillLevel,
        allowCheatMeals: _allowCheatMeals,
        preferOrganicFoods: _preferOrganicFoods,
        enableCalorieCycling: _enableCalorieCycling,
        enableMealTiming: _enableMealTiming,
        hasConsultedDoctor: _hasConsultedDoctor,
      );

      if (mounted) {
        Navigator.pop(context, profile);
      }

    } catch (e) {
      setState(() {
        _errorMessage = 'Errore salvataggio profilo: $e';
        _isLoading = false;
      });
    }
  }

  WorkActivity _convertActivityLevelToWorkActivity(ActivityLevel level) {
    switch (level) {
      case ActivityLevel.sedentary:
        return WorkActivity.sedentary;
      case ActivityLevel.lightlyActive:
        return WorkActivity.light;
      case ActivityLevel.moderatelyActive:
        return WorkActivity.moderate;
      case ActivityLevel.veryActive:
      case ActivityLevel.extremelyActive:
        return WorkActivity.heavy;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DrStaffilanoTheme.backgroundLight,
      appBar: AppBar(
        title: Text(widget.existingProfile != null ? 'Modifica Profilo' : 'Crea Profilo'),
        backgroundColor: DrStaffilanoTheme.primaryGreen,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          _buildProgressIndicator(),
          Expanded(
            child: Form(
              key: _formKey,
              child: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  _buildBasicInfoStep(),
                  _buildBodyCompositionStep(),
                  _buildGoalsStep(),
                  _buildDietaryPreferencesStep(),
                  _buildAdvancedPreferencesStep(),
                  _buildSummaryStep(),
                ],
              ),
            ),
          ),
          _buildNavigationButtons(),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        children: [
          Row(
            children: [
              Text(
                'Passo ${_currentStep + 1} di ${_stepTitles.length}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
              Text(
                _stepTitles[_currentStep],
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: (_currentStep + 1) / _stepTitles.length,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(DrStaffilanoTheme.primaryGreen),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Informazioni di Base',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Iniziamo con le tue informazioni fondamentali',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),

          TextFormField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'Nome',
              border: OutlineInputBorder(),
              prefixIcon: Icon(FontAwesomeIcons.user),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Inserisci il tuo nome';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _ageController,
                  decoration: const InputDecoration(
                    labelText: 'Età',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(FontAwesomeIcons.calendar),
                    suffixText: 'anni',
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Inserisci la tua età';
                    }
                    final age = int.tryParse(value);
                    if (age == null || age < 16 || age > 100) {
                      return 'Età non valida';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<Gender>(
                  value: _selectedGender,
                  decoration: const InputDecoration(
                    labelText: 'Sesso',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(FontAwesomeIcons.venusMars),
                  ),
                  items: Gender.values.map((gender) {
                    return DropdownMenuItem(
                      value: gender,
                      child: Text(gender == Gender.male ? 'Maschio' : 'Femmina'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedGender = value!;
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _weightController,
                  decoration: const InputDecoration(
                    labelText: 'Peso',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(FontAwesomeIcons.weightScale),
                    suffixText: 'kg',
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Inserisci il tuo peso';
                    }
                    final weight = double.tryParse(value);
                    if (weight == null || weight < 30 || weight > 300) {
                      return 'Peso non valido';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _heightController,
                  decoration: const InputDecoration(
                    labelText: 'Altezza',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(FontAwesomeIcons.rulerVertical),
                    suffixText: 'cm',
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Inserisci la tua altezza';
                    }
                    final height = double.tryParse(value);
                    if (height == null || height < 100 || height > 250) {
                      return 'Altezza non valida';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          DropdownButtonFormField<ActivityLevel>(
            value: _selectedActivityLevel,
            decoration: const InputDecoration(
              labelText: 'Livello di Attività',
              border: OutlineInputBorder(),
              prefixIcon: Icon(FontAwesomeIcons.dumbbell),
            ),
            items: ActivityLevel.values.map((level) {
              return DropdownMenuItem(
                value: level,
                child: Text(_getActivityLevelLabel(level)),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedActivityLevel = value!;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBodyCompositionStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Composizione Corporea',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Informazioni opzionali per una maggiore precisione',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),

          TextFormField(
            controller: _bodyFatController,
            decoration: const InputDecoration(
              labelText: 'Percentuale di Grasso Corporeo (opzionale)',
              border: OutlineInputBorder(),
              prefixIcon: Icon(FontAwesomeIcons.percent),
              suffixText: '%',
              helperText: 'Se non conosci questo valore, lascia vuoto',
            ),
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                final bodyFat = double.tryParse(value);
                if (bodyFat == null || bodyFat < 3 || bodyFat > 50) {
                  return 'Percentuale non valida (3-50%)';
                }
              }
              return null;
            },
          ),
          const SizedBox(height: 24),

          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      FontAwesomeIcons.lightbulb,
                      color: DrStaffilanoTheme.primaryGreen,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Suggerimento',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                const Text(
                  'La percentuale di grasso corporeo può essere misurata con bilance impedenziometriche, plicometri o scansioni DEXA. Se non hai questi dati, il sistema calcolerà automaticamente i valori ottimali.',
                  style: TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getActivityLevelLabel(ActivityLevel level) {
    switch (level) {
      case ActivityLevel.sedentary:
        return 'Sedentario (poco o nessun esercizio)';
      case ActivityLevel.lightlyActive:
        return 'Leggermente attivo (esercizio leggero 1-3 giorni/settimana)';
      case ActivityLevel.moderatelyActive:
        return 'Moderatamente attivo (esercizio moderato 3-5 giorni/settimana)';
      case ActivityLevel.veryActive:
        return 'Molto attivo (esercizio intenso 6-7 giorni/settimana)';
      case ActivityLevel.extremelyActive:
        return 'Estremamente attivo (esercizio molto intenso, lavoro fisico)';
    }
  }

  Widget _buildGoalsStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'I Tuoi Obiettivi',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Definisci i tuoi obiettivi per personalizzare al meglio il piano',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),

          // Obiettivo Primario
          const Text(
            'Obiettivo Primario',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),

          DropdownButtonFormField<PrimaryGoal>(
            value: _selectedPrimaryGoal,
            decoration: InputDecoration(
              labelText: 'Seleziona il tuo obiettivo principale',
              border: const OutlineInputBorder(),
              prefixIcon: Icon(
                FontAwesomeIcons.bullseye,
                color: DrStaffilanoTheme.primaryGreen,
              ),
            ),
            items: PrimaryGoal.values.map((goal) {
              return DropdownMenuItem(
                value: goal,
                child: Row(
                  children: [
                    Icon(
                      _getPrimaryGoalIcon(goal),
                      color: DrStaffilanoTheme.primaryGreen,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(child: Text(_getPrimaryGoalLabel(goal))),
                  ],
                ),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedPrimaryGoal = value!;
              });
            },
          ),
          const SizedBox(height: 24),

          // Obiettivi Secondari
          const Text(
            'Obiettivi Secondari (opzionali)',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Seleziona obiettivi aggiuntivi che vuoi raggiungere',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 12),

          ...SecondaryGoal.values.map((goal) => CheckboxListTile(
            title: Text(_getSecondaryGoalLabel(goal)),
            subtitle: Text(_getSecondaryGoalDescription(goal)),
            value: _selectedSecondaryGoals.contains(goal),
            onChanged: (value) {
              setState(() {
                if (value == true) {
                  _selectedSecondaryGoals.add(goal);
                } else {
                  _selectedSecondaryGoals.remove(goal);
                }
              });
            },
            activeColor: DrStaffilanoTheme.primaryGreen,
            secondary: Icon(
              _getSecondaryGoalIcon(goal),
              color: DrStaffilanoTheme.primaryGreen,
            ),
          )),

          const SizedBox(height: 24),

          // Informazioni aggiuntive per performance atletica
          if (_selectedPrimaryGoal == PrimaryGoal.performance ||
              _selectedSecondaryGoals.contains(SecondaryGoal.buildMuscle)) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: DrStaffilanoTheme.secondaryBlue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: DrStaffilanoTheme.secondaryBlue.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        FontAwesomeIcons.trophy,
                        color: DrStaffilanoTheme.secondaryBlue,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        'Performance Atletica',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    'Per obiettivi di performance atletica, il piano includerà strategie nutrizionali specifiche per ottimizzare energia, recupero e prestazioni.',
                    style: TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
          ],

          // Indicatori visivi obiettivi
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                  DrStaffilanoTheme.accentGold.withOpacity(0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      FontAwesomeIcons.heartPulse,
                      color: DrStaffilanoTheme.primaryGreen,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Il Tuo Percorso Dr. Staffilano',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'Obiettivo principale: ${_getPrimaryGoalLabel(_selectedPrimaryGoal)}',
                  style: const TextStyle(fontSize: 14),
                ),
                if (_selectedSecondaryGoals.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    'Obiettivi secondari: ${_selectedSecondaryGoals.length}',
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDietaryPreferencesStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Preferenze Alimentari',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Personalizza il piano in base alle tue preferenze culinarie',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),

          // Regime Alimentare
          const Text(
            'Regime Alimentare',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),

          DropdownButtonFormField<DietaryRegimen>(
            value: _selectedDietaryRegimen,
            decoration: InputDecoration(
              labelText: 'Seleziona il tuo regime alimentare',
              border: const OutlineInputBorder(),
              prefixIcon: Icon(
                FontAwesomeIcons.utensils,
                color: DrStaffilanoTheme.primaryGreen,
              ),
            ),
            items: DietaryRegimen.values.map((regimen) {
              return DropdownMenuItem(
                value: regimen,
                child: Row(
                  children: [
                    Icon(
                      _getDietaryRegimenIcon(regimen),
                      color: DrStaffilanoTheme.primaryGreen,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(child: Text(_getDietaryRegimenLabel(regimen))),
                  ],
                ),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedDietaryRegimen = value;
              });
            },
          ),
          const SizedBox(height: 24),

          // Budget Level
          const Text(
            'Budget Alimentare',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),

          DropdownButtonFormField<BudgetLevel>(
            value: _selectedBudgetLevel,
            decoration: InputDecoration(
              labelText: 'Seleziona il tuo budget',
              border: const OutlineInputBorder(),
              prefixIcon: Icon(
                FontAwesomeIcons.euroSign,
                color: DrStaffilanoTheme.accentGold,
              ),
            ),
            items: BudgetLevel.values.map((budget) {
              return DropdownMenuItem(
                value: budget,
                child: Row(
                  children: [
                    Icon(
                      _getBudgetLevelIcon(budget),
                      color: DrStaffilanoTheme.accentGold,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(child: Text(_getBudgetLevelLabel(budget))),
                  ],
                ),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedBudgetLevel = value!;
              });
            },
          ),
          const SizedBox(height: 24),

          // Cooking Preferences
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Tempo Cucina',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<CookingTime>(
                      value: _selectedCookingTime,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: CookingTime.values.map((time) {
                        return DropdownMenuItem(
                          value: time,
                          child: Text(_getCookingTimeLabel(time)),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedCookingTime = value!;
                        });
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Livello Cucina',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<CookingSkillLevel>(
                      value: _selectedCookingSkillLevel,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: CookingSkillLevel.values.map((skill) {
                        return DropdownMenuItem(
                          value: skill,
                          child: Text(_getCookingSkillLabel(skill)),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedCookingSkillLevel = value!;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Meals per day
          const Text(
            'Pasti al Giorno',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),

          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('Numero di pasti:'),
                    Text(
                      '$_mealsPerDay pasti',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: DrStaffilanoTheme.primaryGreen,
                      ),
                    ),
                  ],
                ),
                Slider(
                  value: _mealsPerDay.toDouble(),
                  min: 3,
                  max: 6,
                  divisions: 3,
                  activeColor: DrStaffilanoTheme.primaryGreen,
                  onChanged: (value) {
                    setState(() {
                      _mealsPerDay = value.round();
                    });
                  },
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('3', style: TextStyle(color: Colors.grey[600])),
                    Text('4', style: TextStyle(color: Colors.grey[600])),
                    Text('5', style: TextStyle(color: Colors.grey[600])),
                    Text('6', style: TextStyle(color: Colors.grey[600])),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Quick preferences summary
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                  DrStaffilanoTheme.secondaryBlue.withOpacity(0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      FontAwesomeIcons.utensils,
                      color: DrStaffilanoTheme.primaryGreen,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Riepilogo Preferenze',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                if (_selectedDietaryRegimen != null)
                  Text('Regime: ${_getDietaryRegimenLabel(_selectedDietaryRegimen!)}'),
                Text('Budget: ${_getBudgetLevelLabel(_selectedBudgetLevel)}'),
                Text('Tempo cucina: ${_getCookingTimeLabel(_selectedCookingTime)}'),
                Text('Livello: ${_getCookingSkillLabel(_selectedCookingSkillLevel)}'),
                Text('Pasti: $_mealsPerDay al giorno'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancedPreferencesStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Preferenze Avanzate',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Configura le opzioni avanzate per ottimizzare il tuo piano',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),

          // Cheat Meals
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      FontAwesomeIcons.cookie,
                      color: DrStaffilanoTheme.accentGold,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'Pasti Liberi (Cheat Meals)',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Switch(
                      value: _allowCheatMeals,
                      onChanged: (value) {
                        setState(() {
                          _allowCheatMeals = value;
                        });
                      },
                      activeColor: DrStaffilanoTheme.primaryGreen,
                    ),
                  ],
                ),
                if (_allowCheatMeals) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: DrStaffilanoTheme.accentGold.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      'I pasti liberi saranno inclusi strategicamente nel piano per mantenere la motivazione e il metabolismo attivo.',
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Organic Foods
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  FontAwesomeIcons.leaf,
                  color: Colors.green,
                  size: 20,
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Preferenza Alimenti Biologici',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Priorità per ingredienti biologici e sostenibili',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: _preferOrganicFoods,
                  onChanged: (value) {
                    setState(() {
                      _preferOrganicFoods = value;
                    });
                  },
                  activeColor: Colors.green,
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Calorie Cycling
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      FontAwesomeIcons.arrowsRotate,
                      color: DrStaffilanoTheme.secondaryBlue,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'Ciclizzazione Calorica',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Switch(
                      value: _enableCalorieCycling,
                      onChanged: (value) {
                        setState(() {
                          _enableCalorieCycling = value;
                        });
                      },
                      activeColor: DrStaffilanoTheme.secondaryBlue,
                    ),
                  ],
                ),
                if (_enableCalorieCycling) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: DrStaffilanoTheme.secondaryBlue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      'La ciclizzazione calorica alterna giorni ad alto e basso contenuto calorico per ottimizzare il metabolismo e prevenire l\'adattamento.',
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Meal Timing
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      FontAwesomeIcons.clock,
                      color: DrStaffilanoTheme.accentGold,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'Ottimizzazione Timing Pasti',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Switch(
                      value: _enableMealTiming,
                      onChanged: (value) {
                        setState(() {
                          _enableMealTiming = value;
                        });
                      },
                      activeColor: DrStaffilanoTheme.accentGold,
                    ),
                  ],
                ),
                if (_enableMealTiming) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: DrStaffilanoTheme.accentGold.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      'Il timing dei pasti sarà ottimizzato in base ai tuoi ritmi circadiani e agli allenamenti per massimizzare l\'assorbimento dei nutrienti.',
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Medical Consultation
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      FontAwesomeIcons.userDoctor,
                      color: DrStaffilanoTheme.primaryGreen,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'Consultazione Medica',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                CheckboxListTile(
                  title: const Text('Ho consultato un medico'),
                  subtitle: const Text(
                    'Confermo di aver consultato un professionista sanitario prima di iniziare questo piano nutrizionale',
                  ),
                  value: _hasConsultedDoctor,
                  onChanged: (value) {
                    setState(() {
                      _hasConsultedDoctor = value ?? false;
                    });
                  },
                  activeColor: DrStaffilanoTheme.primaryGreen,
                  contentPadding: EdgeInsets.zero,
                ),
                if (!_hasConsultedDoctor) ...[
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.orange.withOpacity(0.3)),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.warning, color: Colors.orange, size: 16),
                        const SizedBox(width: 8),
                        const Expanded(
                          child: Text(
                            'Si raccomanda di consultare un medico prima di iniziare qualsiasi piano nutrizionale.',
                            style: TextStyle(fontSize: 12, color: Colors.orange),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Summary of advanced preferences
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                  DrStaffilanoTheme.accentGold.withOpacity(0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      FontAwesomeIcons.gear,
                      color: DrStaffilanoTheme.primaryGreen,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Configurazione Avanzata',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                _buildPreferenceSummaryItem('Pasti liberi', _allowCheatMeals),
                _buildPreferenceSummaryItem('Alimenti biologici', _preferOrganicFoods),
                _buildPreferenceSummaryItem('Ciclizzazione calorica', _enableCalorieCycling),
                _buildPreferenceSummaryItem('Timing ottimizzato', _enableMealTiming),
                _buildPreferenceSummaryItem('Consultazione medica', _hasConsultedDoctor),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreferenceSummaryItem(String label, bool enabled) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            enabled ? Icons.check_circle : Icons.cancel,
            color: enabled ? Colors.green : Colors.grey,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            '$label: ${enabled ? "Abilitato" : "Disabilitato"}',
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Riepilogo Profilo',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Verifica i tuoi dati prima di salvare il profilo',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),

          // Profile Overview Card
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: DrStaffilanoTheme.primaryGradient,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                CircleAvatar(
                  backgroundColor: Colors.white,
                  radius: 30,
                  child: Text(
                    _nameController.text.isNotEmpty
                        ? _nameController.text[0].toUpperCase()
                        : 'U',
                    style: TextStyle(
                      color: DrStaffilanoTheme.primaryGreen,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  _nameController.text.isNotEmpty ? _nameController.text : 'Nome Utente',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${_ageController.text} anni • ${_selectedGender == Gender.male ? "Maschio" : "Femmina"}',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 16),
                _buildMetricsPreview(),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Validation Status
          _buildValidationStatus(),
          const SizedBox(height: 24),

          // Sections Summary
          _buildSectionSummary('Informazioni Base', _buildBasicInfoSummary(), 0),
          const SizedBox(height: 16),
          _buildSectionSummary('Composizione Corporea', _buildBodyCompositionSummary(), 1),
          const SizedBox(height: 16),
          _buildSectionSummary('Obiettivi', _buildGoalsSummary(), 2),
          const SizedBox(height: 16),
          _buildSectionSummary('Preferenze Alimentari', _buildDietaryPreferencesSummary(), 3),
          const SizedBox(height: 16),
          _buildSectionSummary('Preferenze Avanzate', _buildAdvancedPreferencesSummary(), 4),

          const SizedBox(height: 24),

          // Final confirmation
          if (_errorMessage.isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.red.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.error_outline, color: Colors.red),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _errorMessage,
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      FontAwesomeIcons.circleCheck,
                      color: DrStaffilanoTheme.primaryGreen,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'Pronto per il Salvataggio',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                const Text(
                  'Il tuo profilo ultra-dettagliato è completo e pronto per generare piani nutrizionali personalizzati con l\'algoritmo avanzato del Dr. Staffilano.',
                  style: TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricsPreview() {
    final weight = double.tryParse(_weightController.text) ?? 0;
    final height = double.tryParse(_heightController.text) ?? 0;
    final age = int.tryParse(_ageController.text) ?? 0;

    double bmi = 0;
    double bmr = 0;

    if (weight > 0 && height > 0) {
      bmi = weight / ((height / 100) * (height / 100));
    }

    if (weight > 0 && height > 0 && age > 0) {
      // Simplified BMR calculation
      if (_selectedGender == Gender.male) {
        bmr = 10 * weight + 6.25 * height - 5 * age + 5;
      } else {
        bmr = 10 * weight + 6.25 * height - 5 * age - 161;
      }
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildMetricChip('Peso', '${_weightController.text}kg'),
        _buildMetricChip('Altezza', '${_heightController.text}cm'),
        if (bmi > 0) _buildMetricChip('BMI', bmi.toStringAsFixed(1)),
        if (bmr > 0) _buildMetricChip('BMR', '${bmr.round()}'),
      ],
    );
  }

  Widget _buildMetricChip(String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValidationStatus() {
    final hasBasicInfo = _nameController.text.isNotEmpty &&
                        _ageController.text.isNotEmpty &&
                        _weightController.text.isNotEmpty &&
                        _heightController.text.isNotEmpty;

    final hasGoals = _selectedPrimaryGoal != null;
    final hasPreferences = _selectedDietaryRegimen != null;

    final isComplete = hasBasicInfo && hasGoals && hasPreferences;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isComplete ? Colors.green.withOpacity(0.1) : Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isComplete ? Colors.green.withOpacity(0.3) : Colors.orange.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            isComplete ? Icons.check_circle : Icons.warning,
            color: isComplete ? Colors.green : Colors.orange,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isComplete ? 'Profilo Completo' : 'Profilo Incompleto',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isComplete ? Colors.green : Colors.orange,
                  ),
                ),
                Text(
                  isComplete
                      ? 'Tutti i dati essenziali sono stati inseriti correttamente'
                      : 'Alcuni dati essenziali mancano o sono incompleti',
                  style: TextStyle(
                    fontSize: 14,
                    color: isComplete ? Colors.green : Colors.orange,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Row(
        children: [
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousStep,
                child: const Text('Indietro'),
              ),
            ),
          if (_currentStep > 0) const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _currentStep == _stepTitles.length - 1
                  ? (_isLoading ? null : _saveProfile)
                  : _nextStep,
              style: ElevatedButton.styleFrom(
                backgroundColor: DrStaffilanoTheme.primaryGreen,
                foregroundColor: Colors.white,
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : Text(_currentStep == _stepTitles.length - 1 ? 'Salva Profilo' : 'Avanti'),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods for Goals Step
  IconData _getPrimaryGoalIcon(PrimaryGoal goal) {
    switch (goal) {
      case PrimaryGoal.weightLoss:
        return FontAwesomeIcons.weightScale;
      case PrimaryGoal.weightGain:
        return FontAwesomeIcons.dumbbell;
      case PrimaryGoal.maintenance:
        return FontAwesomeIcons.scaleBalanced;
      case PrimaryGoal.bodyRecomposition:
        return FontAwesomeIcons.arrowsRotate;
      case PrimaryGoal.performance:
        return FontAwesomeIcons.trophy;
    }
  }

  String _getPrimaryGoalLabel(PrimaryGoal goal) {
    switch (goal) {
      case PrimaryGoal.weightLoss:
        return 'Perdita di Peso';
      case PrimaryGoal.weightGain:
        return 'Aumento di Peso';
      case PrimaryGoal.maintenance:
        return 'Mantenimento';
      case PrimaryGoal.bodyRecomposition:
        return 'Ricomposizione Corporea';
      case PrimaryGoal.performance:
        return 'Performance Atletica';
    }
  }

  IconData _getSecondaryGoalIcon(SecondaryGoal goal) {
    switch (goal) {
      case SecondaryGoal.improveHealth:
        return FontAwesomeIcons.heartPulse;
      case SecondaryGoal.increaseEnergy:
        return FontAwesomeIcons.bolt;
      case SecondaryGoal.betterSleep:
        return FontAwesomeIcons.bed;
      case SecondaryGoal.reduceInflammation:
        return FontAwesomeIcons.shield;
      case SecondaryGoal.improveDigestion:
        return FontAwesomeIcons.leaf;
      case SecondaryGoal.buildMuscle:
        return FontAwesomeIcons.dumbbell;
      case SecondaryGoal.loseFat:
        return FontAwesomeIcons.fire;
    }
  }

  String _getSecondaryGoalLabel(SecondaryGoal goal) {
    switch (goal) {
      case SecondaryGoal.improveHealth:
        return 'Migliorare Salute';
      case SecondaryGoal.increaseEnergy:
        return 'Aumentare Energia';
      case SecondaryGoal.betterSleep:
        return 'Migliorare Sonno';
      case SecondaryGoal.reduceInflammation:
        return 'Ridurre Infiammazione';
      case SecondaryGoal.improveDigestion:
        return 'Migliorare Digestione';
      case SecondaryGoal.buildMuscle:
        return 'Costruire Muscolo';
      case SecondaryGoal.loseFat:
        return 'Perdere Grasso';
    }
  }

  String _getSecondaryGoalDescription(SecondaryGoal goal) {
    switch (goal) {
      case SecondaryGoal.improveHealth:
        return 'Migliorare la salute generale e il benessere';
      case SecondaryGoal.increaseEnergy:
        return 'Aumentare i livelli di energia durante la giornata';
      case SecondaryGoal.betterSleep:
        return 'Migliorare la qualità del riposo notturno';
      case SecondaryGoal.reduceInflammation:
        return 'Ridurre i processi infiammatori';
      case SecondaryGoal.improveDigestion:
        return 'Ottimizzare la salute digestiva';
      case SecondaryGoal.buildMuscle:
        return 'Costruire massa muscolare magra';
      case SecondaryGoal.loseFat:
        return 'Ridurre il grasso corporeo';
    }
  }

  // Helper methods for Dietary Preferences Step
  IconData _getDietaryRegimenIcon(DietaryRegimen regimen) {
    switch (regimen) {
      case DietaryRegimen.mediterranean:
        return FontAwesomeIcons.fish;
      case DietaryRegimen.paleo:
        return FontAwesomeIcons.drumstickBite;
      case DietaryRegimen.ketogenic:
        return FontAwesomeIcons.cheese;
      case DietaryRegimen.lowCarb:
        return FontAwesomeIcons.wheatAwn;
      case DietaryRegimen.dash:
        return FontAwesomeIcons.heartPulse;
      case DietaryRegimen.plantBased:
        return FontAwesomeIcons.seedling;
      case DietaryRegimen.intermittentFasting:
        return FontAwesomeIcons.clock;
      case DietaryRegimen.none:
        return FontAwesomeIcons.scaleBalanced;
    }
  }

  String _getDietaryRegimenLabel(DietaryRegimen regimen) {
    switch (regimen) {
      case DietaryRegimen.mediterranean:
        return 'Dieta Mediterranea';
      case DietaryRegimen.paleo:
        return 'Dieta Paleolitica';
      case DietaryRegimen.ketogenic:
        return 'Dieta Chetogenica';
      case DietaryRegimen.lowCarb:
        return 'Low Carb';
      case DietaryRegimen.dash:
        return 'Dieta DASH';
      case DietaryRegimen.plantBased:
        return 'A Base Vegetale';
      case DietaryRegimen.intermittentFasting:
        return 'Digiuno Intermittente';
      case DietaryRegimen.none:
        return 'Nessun Regime Specifico';
    }
  }

  IconData _getBudgetLevelIcon(BudgetLevel budget) {
    switch (budget) {
      case BudgetLevel.low:
        return FontAwesomeIcons.coins;
      case BudgetLevel.medium:
        return FontAwesomeIcons.euroSign;
      case BudgetLevel.high:
        return FontAwesomeIcons.gem;
    }
  }

  String _getBudgetLevelLabel(BudgetLevel budget) {
    switch (budget) {
      case BudgetLevel.low:
        return 'Economico (€15-25/settimana)';
      case BudgetLevel.medium:
        return 'Medio (€25-40/settimana)';
      case BudgetLevel.high:
        return 'Alto (€40+/settimana)';
    }
  }

  String _getCookingTimeLabel(CookingTime time) {
    switch (time) {
      case CookingTime.minimal:
        return 'Veloce (<20 min)';
      case CookingTime.medium:
        return 'Medio (20-45 min)';
      case CookingTime.extended:
        return 'Lungo (>45 min)';
    }
  }

  String _getCookingSkillLabel(CookingSkillLevel skill) {
    switch (skill) {
      case CookingSkillLevel.beginner:
        return 'Principiante';
      case CookingSkillLevel.intermediate:
        return 'Intermedio';
      case CookingSkillLevel.advanced:
        return 'Avanzato';
      case CookingSkillLevel.expert:
        return 'Esperto';
    }
  }

  // Helper methods for Summary Step
  Widget _buildSectionSummary(String title, Widget content, int stepIndex) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: DrStaffilanoTheme.primaryGreen,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${stepIndex + 1}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              TextButton(
                onPressed: () {
                  setState(() {
                    _currentStep = stepIndex;
                  });
                  _pageController.animateToPage(
                    stepIndex,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                },
                child: const Text('Modifica'),
              ),
            ],
          ),
          const SizedBox(height: 12),
          content,
        ],
      ),
    );
  }

  Widget _buildBasicInfoSummary() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSummaryItem('Nome', _nameController.text),
        _buildSummaryItem('Età', '${_ageController.text} anni'),
        _buildSummaryItem('Sesso', _selectedGender == Gender.male ? 'Maschio' : 'Femmina'),
        _buildSummaryItem('Peso', '${_weightController.text} kg'),
        _buildSummaryItem('Altezza', '${_heightController.text} cm'),
        _buildSummaryItem('Attività', _getActivityLevelLabel(_selectedActivityLevel)),
      ],
    );
  }

  Widget _buildBodyCompositionSummary() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSummaryItem(
          'Grasso corporeo',
          _bodyFatController.text.isNotEmpty
              ? '${_bodyFatController.text}%'
              : 'Non specificato'
        ),
        const Text(
          'Le informazioni sulla composizione corporea aiutano a calcolare con maggiore precisione il fabbisogno calorico.',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey,
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }

  Widget _buildGoalsSummary() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSummaryItem('Obiettivo primario', _getPrimaryGoalLabel(_selectedPrimaryGoal)),
        if (_selectedSecondaryGoals.isNotEmpty) ...[
          const SizedBox(height: 8),
          const Text(
            'Obiettivi secondari:',
            style: TextStyle(fontWeight: FontWeight.w500),
          ),
          ...(_selectedSecondaryGoals.map((goal) => Padding(
            padding: const EdgeInsets.only(left: 16, top: 4),
            child: Text('• ${_getSecondaryGoalLabel(goal)}'),
          ))),
        ],
      ],
    );
  }

  Widget _buildDietaryPreferencesSummary() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSummaryItem(
          'Regime alimentare',
          _selectedDietaryRegimen != null
              ? _getDietaryRegimenLabel(_selectedDietaryRegimen!)
              : 'Non specificato'
        ),
        _buildSummaryItem('Budget', _getBudgetLevelLabel(_selectedBudgetLevel)),
        _buildSummaryItem('Tempo cucina', _getCookingTimeLabel(_selectedCookingTime)),
        _buildSummaryItem('Livello cucina', _getCookingSkillLabel(_selectedCookingSkillLevel)),
        _buildSummaryItem('Pasti al giorno', '$_mealsPerDay'),
      ],
    );
  }

  Widget _buildAdvancedPreferencesSummary() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSummaryItem('Pasti liberi', _allowCheatMeals ? 'Abilitati' : 'Disabilitati'),
        _buildSummaryItem('Alimenti biologici', _preferOrganicFoods ? 'Preferiti' : 'Standard'),
        _buildSummaryItem('Ciclizzazione calorica', _enableCalorieCycling ? 'Abilitata' : 'Disabilitata'),
        _buildSummaryItem('Timing pasti', _enableMealTiming ? 'Ottimizzato' : 'Standard'),
        _buildSummaryItem('Consultazione medica', _hasConsultedDoctor ? 'Confermata' : 'Non confermata'),
      ],
    );
  }

  Widget _buildSummaryItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }
}