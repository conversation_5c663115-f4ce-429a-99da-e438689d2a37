import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/diet_plan.dart';
import '../models/meal.dart';
import '../models/ultra_detailed_profile.dart';
import '../models/food.dart';
import '../widgets/detailed_food_table.dart';
import '../screens/comprehensive_food_nutrition_screen.dart';
import '../theme/dr_staffilano_theme.dart';
import '../theme/app_theme.dart';

/// SCHERMATA VISUALIZZAZIONE CLASSICA DETTAGLIATA
/// Backend ultra-avanzato + Frontend classico con tabelle, grafici e dettagli cliccabili
class ClassicDetailedDietViewScreen extends StatefulWidget {
  final DailyDietPlan dailyPlan;
  final UltraDetailedProfile profile;

  const ClassicDetailedDietViewScreen({
    Key? key,
    required this.dailyPlan,
    required this.profile,
  }) : super(key: key);

  @override
  State<ClassicDetailedDietViewScreen> createState() => _ClassicDetailedDietViewScreenState();
}

class _ClassicDetailedDietViewScreenState extends State<ClassicDetailedDietViewScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  Food? _selectedFood;
  int _selectedMealIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Piano Dietetico Dettagliato'),
        backgroundColor: DrStaffilanoTheme.primaryGreen,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(FontAwesomeIcons.chartPie), text: 'Panoramica'),
            Tab(icon: Icon(FontAwesomeIcons.table), text: 'Tabelle'),
            Tab(icon: Icon(FontAwesomeIcons.utensils), text: 'Alimenti'),
            Tab(icon: Icon(FontAwesomeIcons.chartBar), text: 'Grafici'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildTablesTab(),
          _buildFoodsTab(),
          _buildChartsTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    final totalCalories = _calculateTotalCalories();
    final totalProteins = _calculateTotalProteins();
    final totalCarbs = _calculateTotalCarbs();
    final totalFats = _calculateTotalFats();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header profilo
          _buildProfileHeader(),
          const SizedBox(height: 24),

          // Riepilogo nutrizionale
          _buildNutritionalSummaryCard(totalCalories, totalProteins, totalCarbs, totalFats),
          const SizedBox(height: 24),

          // Grafico macronutrienti principale
          _buildMainMacroChart(totalProteins, totalCarbs, totalFats),
          const SizedBox(height: 24),

          // Lista pasti compatta
          _buildMealsOverview(),
        ],
      ),
    );
  }

  Widget _buildTablesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Selettore pasto
          _buildMealSelector(),
          const SizedBox(height: 16),

          // Tabella dettagliata del pasto selezionato
          DetailedFoodTable(
            meal: _convertToMeal(widget.dailyPlan.meals[_selectedMealIndex]),
            onFoodTapped: (food) {
              setState(() {
                _selectedFood = food;
              });
              // Mostra dettagli nutrizionali completi
              ComprehensiveFoodNutritionScreen.showAsModal(context, food);
            },
          ),

          const SizedBox(height: 24),

          // Tabella riepilogo giornaliero
          _buildDailySummaryTable(),
        ],
      ),
    );
  }

  Widget _buildFoodsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Tutti gli Alimenti del Piano',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Lista completa alimenti con dettagli
          ...widget.dailyPlan.meals.asMap().entries.map((mealEntry) {
            final mealIndex = mealEntry.key;
            final meal = mealEntry.value;

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header pasto
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 8),
                  decoration: BoxDecoration(
                    color: DrStaffilanoTheme.primaryGreen,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    meal.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),

                // Alimenti del pasto
                ...meal.foods.map((portion) => _buildDetailedFoodCard(portion, mealIndex)),
                const SizedBox(height: 16),
              ],
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildChartsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Grafici macronutrienti per pasto
          const Text(
            'Distribuzione Macronutrienti per Pasto',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildMealMacroCharts(),

          const SizedBox(height: 32),

          // Grafico micronutrienti
          const Text(
            'Micronutrienti Totali',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildMicronutrientsChart(),

          const SizedBox(height: 32),

          // Grafico distribuzione calorica
          const Text(
            'Distribuzione Calorica Giornaliera',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildCalorieDistributionChart(),
        ],
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: DrStaffilanoTheme.primaryGradient,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: Colors.white,
                child: Icon(
                  FontAwesomeIcons.userDoctor,
                  color: DrStaffilanoTheme.primaryGreen,
                  size: 30,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.profile.baseProfile.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Piano Ultra-Personalizzato',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      'Obiettivo: ${widget.profile.primaryGoal.toString().split('.').last}',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildProfileStat('BMR', '${widget.profile.calculateBMR().round()}', 'kcal'),
              _buildProfileStat('TDEE', '${widget.profile.calculateTDEE().round()}', 'kcal'),
              _buildProfileStat('Target', '${widget.dailyPlan.calorieTarget}', 'kcal'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProfileStat(String label, String value, String unit) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          unit,
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 12,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withOpacity(0.9),
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildNutritionalSummaryCard(int totalCalories, double totalProteins, double totalCarbs, double totalFats) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  FontAwesomeIcons.chartLine,
                  color: DrStaffilanoTheme.primaryGreen,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Riepilogo Nutrizionale Giornaliero',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Calorie principali
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: DrStaffilanoTheme.accentGold.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: DrStaffilanoTheme.accentGold.withOpacity(0.3)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(FontAwesomeIcons.fire, color: DrStaffilanoTheme.accentGold, size: 20),
                      const SizedBox(width: 8),
                      const Text(
                        'Calorie Totali',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                  Text(
                    '$totalCalories kcal',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: DrStaffilanoTheme.accentGold,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Macronutrienti
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildMacroStat('Proteine', totalProteins, 'g', DrStaffilanoTheme.primaryGreen, FontAwesomeIcons.dumbbell),
                _buildMacroStat('Carboidrati', totalCarbs, 'g', DrStaffilanoTheme.secondaryBlue, FontAwesomeIcons.wheatAwn),
                _buildMacroStat('Grassi', totalFats, 'g', DrStaffilanoTheme.accentGold, FontAwesomeIcons.droplet),
              ],
            ),

            const SizedBox(height: 16),

            // Barra progresso vs target
            _buildTargetProgressBar(totalCalories),
          ],
        ),
      ),
    );
  }

  Widget _buildMacroStat(String label, double value, String unit, Color color, IconData icon) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(height: 8),
        Text(
          '${value.round()}$unit',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildTargetProgressBar(int totalCalories) {
    final target = widget.dailyPlan.calorieTarget;
    final progress = (totalCalories / target).clamp(0.0, 1.0);
    final isOnTarget = (totalCalories - target).abs() <= target * 0.05; // ±5%

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Progresso vs Target',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            Text(
              '${(progress * 100).round()}%',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: isOnTarget ? Colors.green : Colors.orange,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey.shade200,
          valueColor: AlwaysStoppedAnimation<Color>(
            isOnTarget ? Colors.green : Colors.orange,
          ),
          minHeight: 8,
        ),
        const SizedBox(height: 4),
        Text(
          'Target: $target kcal',
          style: const TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ],
    );
  }

  Widget _buildMainMacroChart(double totalProteins, double totalCarbs, double totalFats) {
    final proteinCalories = totalProteins * 4;
    final carbCalories = totalCarbs * 4;
    final fatCalories = totalFats * 9;
    final totalCalories = proteinCalories + carbCalories + fatCalories;

    if (totalCalories == 0) return const SizedBox.shrink();

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Distribuzione Macronutrienti',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: [
                    PieChartSectionData(
                      color: DrStaffilanoTheme.primaryGreen,
                      value: proteinCalories,
                      title: '${(proteinCalories / totalCalories * 100).round()}%',
                      radius: 80,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    PieChartSectionData(
                      color: DrStaffilanoTheme.secondaryBlue,
                      value: carbCalories,
                      title: '${(carbCalories / totalCalories * 100).round()}%',
                      radius: 80,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    PieChartSectionData(
                      color: DrStaffilanoTheme.accentGold,
                      value: fatCalories,
                      title: '${(fatCalories / totalCalories * 100).round()}%',
                      radius: 80,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ],
                  sectionsSpace: 2,
                  centerSpaceRadius: 40,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildLegendItem('Proteine', '${totalProteins.round()}g', DrStaffilanoTheme.primaryGreen),
                _buildLegendItem('Carboidrati', '${totalCarbs.round()}g', DrStaffilanoTheme.secondaryBlue),
                _buildLegendItem('Grassi', '${totalFats.round()}g', DrStaffilanoTheme.accentGold),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItem(String label, String value, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            ),
            Text(
              value,
              style: TextStyle(fontSize: 12, color: color, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMealsOverview() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Pasti del Giorno',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...widget.dailyPlan.meals.asMap().entries.map((entry) {
              final index = entry.key;
              final meal = entry.value;
              final mealCalories = meal.foods.fold(0, (sum, portion) => sum + portion.calories);

              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Row(
                  children: [
                    Icon(
                      _getMealIcon(meal.type),
                      color: DrStaffilanoTheme.primaryGreen,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            meal.name,
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            '${meal.foods.length} alimenti • $mealCalories kcal',
                            style: const TextStyle(fontSize: 12, color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.arrow_forward_ios, size: 16),
                      onPressed: () {
                        setState(() {
                          _selectedMealIndex = index;
                          _tabController.animateTo(1); // Vai al tab tabelle
                        });
                      },
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildMealSelector() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Row(
          children: [
            const Text(
              'Pasto: ',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            Expanded(
              child: DropdownButton<int>(
                value: _selectedMealIndex,
                isExpanded: true,
                underline: const SizedBox(),
                items: widget.dailyPlan.meals.asMap().entries.map((entry) {
                  final index = entry.key;
                  final meal = entry.value;
                  return DropdownMenuItem<int>(
                    value: index,
                    child: Text(meal.name),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedMealIndex = value;
                    });
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDailySummaryTable() {
    final totalCalories = _calculateTotalCalories();
    final totalProteins = _calculateTotalProteins();
    final totalCarbs = _calculateTotalCarbs();
    final totalFats = _calculateTotalFats();
    final totalFiber = _calculateTotalFiber();

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Riepilogo Nutrizionale Giornaliero',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Table(
              border: TableBorder.all(color: Colors.grey.shade300),
              columnWidths: const {
                0: FlexColumnWidth(2),
                1: FlexColumnWidth(1),
                2: FlexColumnWidth(1),
              },
              children: [
                _buildTableHeader(),
                _buildTableRow('Calorie', '$totalCalories', 'kcal', DrStaffilanoTheme.accentGold),
                _buildTableRow('Proteine', '${totalProteins.round()}', 'g', DrStaffilanoTheme.primaryGreen),
                _buildTableRow('Carboidrati', '${totalCarbs.round()}', 'g', DrStaffilanoTheme.secondaryBlue),
                _buildTableRow('Grassi', '${totalFats.round()}', 'g', DrStaffilanoTheme.accentGold),
                _buildTableRow('Fibre', '${totalFiber.round()}', 'g', Colors.green),
              ],
            ),
          ],
        ),
      ),
    );
  }

  TableRow _buildTableHeader() {
    return TableRow(
      decoration: BoxDecoration(color: DrStaffilanoTheme.primaryGreen),
      children: const [
        Padding(
          padding: EdgeInsets.all(12),
          child: Text(
            'Nutriente',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        Padding(
          padding: EdgeInsets.all(12),
          child: Text(
            'Quantità',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
        ),
        Padding(
          padding: EdgeInsets.all(12),
          child: Text(
            'Unità',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  TableRow _buildTableRow(String nutrient, String value, String unit, Color color) {
    return TableRow(
      children: [
        Padding(
          padding: const EdgeInsets.all(12),
          child: Text(
            nutrient,
            style: TextStyle(fontWeight: FontWeight.w500, color: color),
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(12),
          child: Text(
            value,
            style: TextStyle(fontWeight: FontWeight.bold, color: color),
            textAlign: TextAlign.center,
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(12),
          child: Text(
            unit,
            style: const TextStyle(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  Widget _buildDetailedFoodCard(FoodPortion portion, int mealIndex) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: InkWell(
        onTap: () {
          ComprehensiveFoodNutritionScreen.showAsModal(context, portion.food);
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // Icona alimento
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getFoodIcon(portion.food),
                  color: DrStaffilanoTheme.primaryGreen,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),

              // Dettagli alimento
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      portion.food.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${portion.grams}g • ${portion.calories} kcal',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        _buildNutrientChip('P', (portion.food.proteins * portion.grams / 100).toStringAsFixed(1), DrStaffilanoTheme.primaryGreen),
                        const SizedBox(width: 4),
                        _buildNutrientChip('C', (portion.food.carbs * portion.grams / 100).toStringAsFixed(1), DrStaffilanoTheme.secondaryBlue),
                        const SizedBox(width: 4),
                        _buildNutrientChip('G', (portion.food.fats * portion.grams / 100).toStringAsFixed(1), DrStaffilanoTheme.accentGold),
                      ],
                    ),
                  ],
                ),
              ),

              // Icona per aprire dettagli
              Icon(
                Icons.info_outline,
                color: DrStaffilanoTheme.primaryGreen,
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNutrientChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        '$label: ${value}g',
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: color,
        ),
      ),
    );
  }

  Widget _buildMealMacroCharts() {
    return Column(
      children: widget.dailyPlan.meals.asMap().entries.map((entry) {
        final index = entry.key;
        final meal = entry.value;

        final mealProteins = meal.foods.fold(0.0, (sum, portion) => sum + (portion.food.proteins * portion.grams / 100));
        final mealCarbs = meal.foods.fold(0.0, (sum, portion) => sum + (portion.food.carbs * portion.grams / 100));
        final mealFats = meal.foods.fold(0.0, (sum, portion) => sum + (portion.food.fats * portion.grams / 100));

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  meal.name,
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                SizedBox(
                  height: 120,
                  child: _buildMealPieChart(mealProteins, mealCarbs, mealFats),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildMealPieChart(double proteins, double carbs, double fats) {
    final proteinCalories = proteins * 4;
    final carbCalories = carbs * 4;
    final fatCalories = fats * 9;
    final totalCalories = proteinCalories + carbCalories + fatCalories;

    if (totalCalories == 0) {
      return const Center(child: Text('Nessun dato disponibile'));
    }

    return PieChart(
      PieChartData(
        sections: [
          PieChartSectionData(
            color: DrStaffilanoTheme.primaryGreen,
            value: proteinCalories,
            title: '${(proteinCalories / totalCalories * 100).round()}%',
            radius: 40,
            titleStyle: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
          ),
          PieChartSectionData(
            color: DrStaffilanoTheme.secondaryBlue,
            value: carbCalories,
            title: '${(carbCalories / totalCalories * 100).round()}%',
            radius: 40,
            titleStyle: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
          ),
          PieChartSectionData(
            color: DrStaffilanoTheme.accentGold,
            value: fatCalories,
            title: '${(fatCalories / totalCalories * 100).round()}%',
            radius: 40,
            titleStyle: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
          ),
        ],
        sectionsSpace: 1,
        centerSpaceRadius: 20,
      ),
    );
  }

  Widget _buildMicronutrientsChart() {
    // Calcola i micronutrienti totali
    final micronutrients = <String, double>{};

    for (final meal in widget.dailyPlan.meals) {
      for (final portion in meal.foods) {
        final ratio = portion.grams / 100;

        // Aggiungi minerali principali
        if (portion.food.calcium != null) {
          micronutrients['Calcio'] = (micronutrients['Calcio'] ?? 0) + (portion.food.calcium! * ratio);
        }
        if (portion.food.phosphorus != null) {
          micronutrients['Fosforo'] = (micronutrients['Fosforo'] ?? 0) + (portion.food.phosphorus! * ratio);
        }
        if (portion.food.magnesium != null) {
          micronutrients['Magnesio'] = (micronutrients['Magnesio'] ?? 0) + (portion.food.magnesium! * ratio);
        }
        if (portion.food.sodium != null) {
          micronutrients['Sodio'] = (micronutrients['Sodio'] ?? 0) + (portion.food.sodium! * ratio);
        }
        if (portion.food.potassium != null) {
          micronutrients['Potassio'] = (micronutrients['Potassio'] ?? 0) + (portion.food.potassium! * ratio);
        }
      }
    }

    if (micronutrients.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Text('Dati sui micronutrienti non disponibili'),
        ),
      );
    }

    final maxValue = micronutrients.values.reduce((a, b) => a > b ? a : b);

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Micronutrienti Principali (mg)',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  maxY: maxValue * 1.1,
                  titlesData: FlTitlesData(
                    show: true,
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final keys = micronutrients.keys.toList();
                          if (value.toInt() < keys.length) {
                            return Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Text(
                                keys[value.toInt()],
                                style: const TextStyle(fontSize: 10),
                              ),
                            );
                          }
                          return const SizedBox();
                        },
                        reservedSize: 30,
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 40,
                        getTitlesWidget: (value, meta) {
                          return Text(
                            '${value.toInt()}',
                            style: const TextStyle(fontSize: 10),
                          );
                        },
                      ),
                    ),
                    rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  ),
                  borderData: FlBorderData(show: false),
                  barGroups: List.generate(micronutrients.length, (index) {
                    final value = micronutrients.values.elementAt(index);
                    return BarChartGroupData(
                      x: index,
                      barRods: [
                        BarChartRodData(
                          toY: value,
                          color: DrStaffilanoTheme.primaryGreen,
                          width: 20,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(4),
                            topRight: Radius.circular(4),
                          ),
                        ),
                      ],
                    );
                  }),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalorieDistributionChart() {
    final mealCalories = widget.dailyPlan.meals.map((meal) {
      return meal.foods.fold(0, (sum, portion) => sum + portion.calories);
    }).toList();

    final mealNames = widget.dailyPlan.meals.map((meal) => meal.name).toList();

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Distribuzione Calorie per Pasto',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  maxY: mealCalories.isNotEmpty ? mealCalories.reduce((a, b) => a > b ? a : b).toDouble() * 1.1 : 100,
                  titlesData: FlTitlesData(
                    show: true,
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          if (value.toInt() < mealNames.length) {
                            return Padding(
                              padding: const EdgeInsets.only(top: 8.0),
                              child: Text(
                                mealNames[value.toInt()],
                                style: const TextStyle(fontSize: 10),
                              ),
                            );
                          }
                          return const SizedBox();
                        },
                        reservedSize: 30,
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 40,
                        getTitlesWidget: (value, meta) {
                          return Text(
                            '${value.toInt()}',
                            style: const TextStyle(fontSize: 10),
                          );
                        },
                      ),
                    ),
                    rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  ),
                  borderData: FlBorderData(show: false),
                  barGroups: List.generate(mealCalories.length, (index) {
                    return BarChartGroupData(
                      x: index,
                      barRods: [
                        BarChartRodData(
                          toY: mealCalories[index].toDouble(),
                          color: DrStaffilanoTheme.accentGold,
                          width: 30,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(6),
                            topRight: Radius.circular(6),
                          ),
                        ),
                      ],
                    );
                  }),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Metodi di utilità per calcoli nutrizionali
  int _calculateTotalCalories() {
    return widget.dailyPlan.meals.fold(0, (sum, meal) {
      return sum + meal.foods.fold(0, (mealSum, portion) => mealSum + portion.calories);
    });
  }

  double _calculateTotalProteins() {
    return widget.dailyPlan.meals.fold(0.0, (sum, meal) {
      return sum + meal.foods.fold(0.0, (mealSum, portion) {
        return mealSum + (portion.food.proteins * portion.grams / 100);
      });
    });
  }

  double _calculateTotalCarbs() {
    return widget.dailyPlan.meals.fold(0.0, (sum, meal) {
      return sum + meal.foods.fold(0.0, (mealSum, portion) {
        return mealSum + (portion.food.carbs * portion.grams / 100);
      });
    });
  }

  double _calculateTotalFats() {
    return widget.dailyPlan.meals.fold(0.0, (sum, meal) {
      return sum + meal.foods.fold(0.0, (mealSum, portion) {
        return mealSum + (portion.food.fats * portion.grams / 100);
      });
    });
  }

  double _calculateTotalFiber() {
    return widget.dailyPlan.meals.fold(0.0, (sum, meal) {
      return sum + meal.foods.fold(0.0, (mealSum, portion) {
        return mealSum + (portion.food.fiber * portion.grams / 100);
      });
    });
  }

  // Metodi di utilità per icone e conversioni
  IconData _getMealIcon(MealType type) {
    switch (type) {
      case MealType.breakfast:
        return FontAwesomeIcons.mugHot;
      case MealType.lunch:
        return FontAwesomeIcons.utensils;
      case MealType.dinner:
        return FontAwesomeIcons.bowlFood;
      case MealType.snack:
        return FontAwesomeIcons.cookie;
      default:
        return FontAwesomeIcons.utensils;
    }
  }

  IconData _getFoodIcon(Food food) {
    // Logica semplificata per icone alimenti
    final name = food.name.toLowerCase();
    if (name.contains('pasta') || name.contains('riso') || name.contains('cereali')) {
      return FontAwesomeIcons.wheatAwn;
    } else if (name.contains('carne') || name.contains('pollo') || name.contains('pesce')) {
      return FontAwesomeIcons.drumstickBite;
    } else if (name.contains('latte') || name.contains('formaggio') || name.contains('yogurt')) {
      return FontAwesomeIcons.glassWater;
    } else if (name.contains('frutta') || name.contains('mela') || name.contains('banana')) {
      return FontAwesomeIcons.apple;
    } else if (name.contains('verdura') || name.contains('insalata') || name.contains('pomodoro')) {
      return FontAwesomeIcons.leaf;
    } else if (name.contains('pane') || name.contains('biscotti') || name.contains('cracker')) {
      return FontAwesomeIcons.breadSlice;
    } else if (name.contains('olio') || name.contains('burro') || name.contains('grasso')) {
      return FontAwesomeIcons.droplet;
    } else {
      return FontAwesomeIcons.utensils;
    }
  }

  // Conversione da PlannedMeal a Meal per compatibilità con DetailedFoodTable
  Meal _convertToMeal(PlannedMeal plannedMeal) {
    return plannedMeal.toMeal();
  }
}