import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/badge_models.dart';
import '../services/badge_service.dart';
import '../controllers/welljourney_controller.dart';
import '../controllers/advanced_challenges_controller.dart';
import '../services/nutriscore_service.dart';

/// Controller per gestire l'integrazione dei badge con tutti i sistemi
class BadgeController extends ChangeNotifier {
  static BadgeController? _instance;
  static BadgeController get instance => _instance ??= BadgeController._();

  BadgeController._();

  // Servizi
  final BadgeService _badgeService = BadgeService.instance;

  // Timer per controlli periodici
  Timer? _progressCheckTimer;

  // Stato
  bool _isInitialized = false;
  List<String> _recentlyUnlockedBadges = [];

  // Getters
  bool get isInitialized => _isInitialized;
  List<String> get recentlyUnlockedBadges => _recentlyUnlockedBadges;
  BadgeService get badgeService => _badgeService;

  /// Inizializza il controller badge
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      print('🏆 Inizializzazione BadgeController...');

      // Inizializza il servizio badge
      await _badgeService.initialize();

      // Avvia il controllo periodico dei progressi
      _startProgressTracking();

      // Controlla immediatamente i progressi
      await _checkAllBadgeProgress();

      _isInitialized = true;
      print('✅ BadgeController inizializzato');

      notifyListeners();
    } catch (e) {
      print('❌ Errore nell\'inizializzazione BadgeController: $e');
      _isInitialized = false;
    }
  }

  /// Avvia il tracking periodico dei progressi
  void _startProgressTracking() {
    _progressCheckTimer?.cancel();
    _progressCheckTimer = Timer.periodic(const Duration(minutes: 2), (timer) {
      _checkAllBadgeProgress();
    });
  }

  /// Controlla i progressi di tutti i badge
  Future<void> _checkAllBadgeProgress() async {
    if (!_isInitialized) return;

    try {
      // Controlla badge punti WellJourney usando i dati attuali
      final prefs = await SharedPreferences.getInstance();
      final currentPoints = prefs.getInt('welljourney_total_points') ?? 0;
      await checkWellJourneyPointsBadges(currentPoints);

      // Controlla badge percorsi
      await _checkPathwayBadges();

      // Controlla badge streak
      await _checkStreakBadges();

      // Controlla badge Food Oracle
      await _checkFoodOracleBadges();

      // Controlla badge NutriScore
      await _checkNutriScoreBadges();

      // Controlla badge sfide
      await _checkChallengeBadges();

      // Controlla badge speciali
      await _checkSpecialBadges();

    } catch (e) {
      print('❌ Errore nel controllo progressi badge: $e');
    }
  }

  /// Controlla badge per punti WellJourney (metodo pubblico)
  Future<void> checkWellJourneyPointsBadges(int currentPoints) async {
    try {
      print('🎯 Controllo badge punti WellJourney: $currentPoints punti');

      // Badge punti da controllare
      final pointsBadges = [
        ('points_first_step', 100),
        ('points_on_the_way', 500),
        ('points_determined', 1000),
        ('points_expert', 2500),
        ('points_master', 5000),
      ];

      for (final (badgeId, requiredPoints) in pointsBadges) {
        final wasUnlocked = await _updateBadgeProgress(badgeId, currentPoints);
        if (wasUnlocked) {
          await _showBadgeUnlockedNotification(badgeId);
        }
      }
    } catch (e) {
      print('❌ Errore nel controllo badge punti WellJourney: $e');
    }
  }

  /// Controlla badge per percorsi completati (metodo pubblico)
  Future<void> checkPathwayBadges(List<String> completedPathways) async {
    try {
      print('🛤️ Controllo badge percorsi: ${completedPathways.length} completati');

      // Badge percorsi da controllare
      final pathwayBadges = [
        ('pathway_heart_health', 'heart_health_pathway'),
        ('pathway_weight_management', 'weight_management_pathway'),
        ('pathway_sports_nutrition', 'sports_nutrition_pathway'),
        ('pathway_mediterranean_diet', 'mediterranean_diet_pathway'),
        ('pathway_prevention_care', 'prevention_care_pathway'),
      ];

      for (final (badgeId, pathwayId) in pathwayBadges) {
        final isCompleted = completedPathways.contains(pathwayId);
        if (isCompleted) {
          final wasUnlocked = await _updateBadgeProgress(badgeId, 1);
          if (wasUnlocked) {
            await _showBadgeUnlockedNotification(badgeId);
          }
        }
      }
    } catch (e) {
      print('❌ Errore nel controllo badge percorsi: $e');
    }
  }

  /// Controlla badge per streak (metodo pubblico)
  Future<void> checkStreakBadges(int currentStreak) async {
    try {
      print('🔥 Controllo badge streak: $currentStreak giorni');

      // Badge streak da controllare
      final streakBadges = [
        ('streak_consistency', 7),
        ('streak_dedication', 30),
        ('streak_legend', 100),
      ];

      for (final (badgeId, requiredDays) in streakBadges) {
        if (currentStreak >= requiredDays) {
          final wasUnlocked = await _updateBadgeProgress(badgeId, currentStreak);
          if (wasUnlocked) {
            await _showBadgeUnlockedNotification(badgeId);
          }
        }
      }
    } catch (e) {
      print('❌ Errore nel controllo badge streak: $e');
    }
  }

  /// Controlla badge per Food Oracle (metodo pubblico)
  Future<void> checkFoodOracleBadges(int totalScans) async {
    try {
      print('📸 Controllo badge Food Oracle: $totalScans scansioni');

      // Badge Food Oracle da controllare
      final foodOracleBadges = [
        ('food_oracle_explorer', 10),
        ('food_oracle_analyst', 50),
        ('food_oracle_guru', 100),
      ];

      for (final (badgeId, requiredScans) in foodOracleBadges) {
        if (totalScans >= requiredScans) {
          final wasUnlocked = await _updateBadgeProgress(badgeId, totalScans);
          if (wasUnlocked) {
            await _showBadgeUnlockedNotification(badgeId);
          }
        }
      }
    } catch (e) {
      print('❌ Errore nel controllo badge Food Oracle: $e');
    }
  }

  /// Controlla badge per percorsi completati
  Future<void> _checkPathwayBadges() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Badge percorsi da controllare
      final pathwayBadges = [
        ('pathway_heart_health', 'heart_health_completed'),
        ('pathway_weight_management', 'weight_management_completed'),
        ('pathway_sports_nutrition', 'sports_nutrition_completed'),
        ('pathway_mediterranean_diet', 'mediterranean_diet_completed'),
        ('pathway_prevention_care', 'prevention_care_completed'),
      ];

      for (final (badgeId, prefKey) in pathwayBadges) {
        final isCompleted = prefs.getBool(prefKey) ?? false;
        if (isCompleted) {
          await _updateBadgeProgress(badgeId, 1);
        }
      }
    } catch (e) {
      print('❌ Errore nel controllo badge percorsi: $e');
    }
  }

  /// Controlla badge per streak consecutivi
  Future<void> _checkStreakBadges() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentStreak = prefs.getInt('current_streak_days') ?? 0;

      // Badge streak da controllare
      final streakBadges = [
        ('streak_consistency', 7),
        ('streak_dedication', 30),
        ('streak_legend', 100),
      ];

      for (final (badgeId, requiredDays) in streakBadges) {
        await _updateBadgeProgress(badgeId, currentStreak);
      }
    } catch (e) {
      print('❌ Errore nel controllo badge streak: $e');
    }
  }

  /// Controlla badge per Food Oracle
  Future<void> _checkFoodOracleBadges() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final totalScans = prefs.getInt('food_oracle_total_scans') ?? 0;

      // Badge Food Oracle da controllare
      final foodOracleBadges = [
        ('food_oracle_explorer', 10),
        ('food_oracle_analyst', 50),
        ('food_oracle_guru', 100),
      ];

      for (final (badgeId, requiredScans) in foodOracleBadges) {
        await _updateBadgeProgress(badgeId, totalScans);
      }
    } catch (e) {
      print('❌ Errore nel controllo badge Food Oracle: $e');
    }
  }

  /// Controlla badge per NutriScore
  Future<void> _checkNutriScoreBadges() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentNutriScore = prefs.getInt('current_nutriscore') ?? 0;

      // Badge NutriScore da controllare
      final nutriScoreBadges = [
        ('nutriscore_beginner', 25),
        ('nutriscore_competent', 50),
        ('nutriscore_advanced', 75),
        ('nutriscore_nutritionist', 100),
      ];

      for (final (badgeId, requiredScore) in nutriScoreBadges) {
        await _updateBadgeProgress(badgeId, currentNutriScore);
      }
    } catch (e) {
      print('❌ Errore nel controllo badge NutriScore: $e');
    }
  }

  /// Controlla badge per sfide completate
  Future<void> _checkChallengeBadges() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final completedChallenges = prefs.getInt('total_challenges_completed') ?? 0;

      // Badge sfide da controllare
      final challengeBadges = [
        ('challenge_first_win', 1),
        ('challenge_champion', 10),
        ('challenge_legend', 25),
      ];

      for (final (badgeId, requiredChallenges) in challengeBadges) {
        await _updateBadgeProgress(badgeId, completedChallenges);
      }
    } catch (e) {
      print('❌ Errore nel controllo badge sfide: $e');
    }
  }

  /// Controlla badge speciali
  Future<void> _checkSpecialBadges() async {
    try {
      // Badge speciali con logica complessa
      await _checkHeartGuardianBadge();
      await _checkWellnessAmbassadorBadge();
    } catch (e) {
      print('❌ Errore nel controllo badge speciali: $e');
    }
  }

  /// Controlla il badge "Guardiano del Cuore"
  Future<void> _checkHeartGuardianBadge() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Criteri per il badge Guardiano del Cuore
      final heartHealthCompleted = prefs.getBool('heart_health_completed') ?? false;
      final nutriScoreHigh = (prefs.getInt('current_nutriscore') ?? 0) >= 75;
      final streakGood = (prefs.getInt('current_streak_days') ?? 0) >= 30;

      if (heartHealthCompleted && nutriScoreHigh && streakGood) {
        await _updateBadgeProgress('special_heart_guardian', 1);
      }
    } catch (e) {
      print('❌ Errore nel controllo badge Guardiano del Cuore: $e');
    }
  }

  /// Controlla il badge "Ambasciatore del Benessere"
  Future<void> _checkWellnessAmbassadorBadge() async {
    try {
      // Controlla se tutti i prerequisiti sono soddisfatti
      final pointsMaster = _badgeService.isBadgeUnlocked('points_master');
      final heartHealthPathway = _badgeService.isBadgeUnlocked('pathway_heart_health');
      final streakLegend = _badgeService.isBadgeUnlocked('streak_legend');

      if (pointsMaster && heartHealthPathway && streakLegend) {
        await _updateBadgeProgress('special_wellness_ambassador', 1);
      }
    } catch (e) {
      print('❌ Errore nel controllo badge Ambasciatore del Benessere: $e');
    }
  }

  /// Aggiorna il progresso di un badge
  Future<bool> _updateBadgeProgress(String badgeId, int newValue) async {
    try {
      final wasUnlocked = await _badgeService.updateBadgeProgress(badgeId, newValue);

      if (wasUnlocked) {
        _recentlyUnlockedBadges.add(badgeId);
        await _showBadgeUnlockedNotification(badgeId);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      print('❌ Errore nell\'aggiornamento progresso badge $badgeId: $e');
      return false;
    }
  }

  /// Mostra notifica di badge sbloccato
  Future<void> _showBadgeUnlockedNotification(String badgeId) async {
    try {
      final badge = _badgeService.getBadgeById(badgeId);
      if (badge != null) {
        print('🏆 BADGE SBLOCCATO: ${badge.name}');
        // TODO: Implementare notifica visiva nell'app
      }
    } catch (e) {
      print('❌ Errore nella notifica badge sbloccato: $e');
    }
  }

  /// Forza il controllo di tutti i badge (per testing)
  Future<void> forceCheckAllBadges() async {
    await _checkAllBadgeProgress();
    notifyListeners();
  }

  /// Simula il guadagno di punti WellJourney (per testing)
  Future<void> simulateWellJourneyPoints(int points) async {
    final prefs = await SharedPreferences.getInstance();
    final currentPoints = prefs.getInt('welljourney_total_points') ?? 0;
    final newPoints = currentPoints + points;
    await prefs.setInt('welljourney_total_points', newPoints);
    await checkWellJourneyPointsBadges(newPoints);
    notifyListeners();
  }

  /// Simula il completamento di una sfida (per testing)
  Future<void> simulateChallengeCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    final completed = prefs.getInt('total_challenges_completed') ?? 0;
    await prefs.setInt('total_challenges_completed', completed + 1);
    await _checkChallengeBadges();
    notifyListeners();
  }

  /// Simula una scansione Food Oracle (per testing)
  Future<void> simulateFoodOracleScan() async {
    final prefs = await SharedPreferences.getInstance();
    final scans = prefs.getInt('food_oracle_total_scans') ?? 0;
    await prefs.setInt('food_oracle_total_scans', scans + 1);
    await _checkFoodOracleBadges();
    notifyListeners();
  }

  /// Pulisce i badge sbloccati di recente
  void clearRecentlyUnlockedBadges() {
    _recentlyUnlockedBadges.clear();
    notifyListeners();
  }

  @override
  void dispose() {
    _progressCheckTimer?.cancel();
    super.dispose();
  }
}
