import 'dart:convert';

enum MealType { breakfast, lunch, dinner, snack }

enum FoodCategory {
  fruit,
  vegetable,
  grain,
  protein,
  dairy,
  fat,
  sweet,
  beverage,
  mixed,
  condiment,
  other
}

enum Season {
  spring,  // Primavera
  summer,  // Estate
  autumn,  // Autunno
  winter   // Inverno
}

enum ItalianRegion {
  abruzzo,
  basilicata,
  calabria,
  campania,
  emiliaRomagna,
  friuliVeneziaGiulia,
  lazio,
  liguria,
  lombardia,
  marche,
  molise,
  piemonte,
  puglia,
  sardegna,
  sicilia,
  toscana,
  trentinoAltoAdige,
  umbria,
  valleDAosta,
  veneto
}

enum GeographicArea {
  north,   // Nord Italia
  center,  // Centro Italia
  south,   // Sud Italia
  islands  // Isole
}

enum FoodState {
  raw,      // crudo
  cooked,   // cotto
  prepared, // preparato (es. tagliato, lavato)
  processed // lavorato industrialmente
}

enum DataSource {
  usda,     // USDA FoodData Central
  crea,     // Tabelle di Composizione degli Alimenti CREA (Italia)
  uk_cofid, // UK Composition of Foods Integrated Dataset
  fr_ciqual, // French CIQUAL food composition table
  producer, // Dati del produttore (etichetta)
  open_food_facts, // Open Food Facts
  custom,   // Inserito manualmente
  unknown   // Fonte sconosciuta
}

enum ValidationStatus {
  validated,    // Dati verificati e confermati
  pending,      // In attesa di verifica
  unverified,   // Non verificato
  rejected      // Dati rifiutati (non accurati)
}

class Food {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final int calories; // per 100g/ml
  final double proteins; // in g per 100g/ml
  final double carbs; // in g per 100g/ml
  final double fats; // in g per 100g/ml
  final double fiber; // in g per 100g/ml
  final double sugar; // in g per 100g/ml
  final double? sugars; // alias per sugar
  final double? saturatedFats; // grassi saturi in g per 100g/ml
  final double? sodium; // sodio in mg per 100g/ml
  final double? potassium; // potassio in mg per 100g/ml
  final double? calcium; // calcio in mg per 100g/ml
  final double? magnesium; // magnesio in mg per 100g/ml
  final double? phosphorus; // fosforo in mg per 100g/ml
  final List<MealType> suitableForMeals;
  final List<FoodCategory> categories;
  final List<String> extendedCategories; // Categorie estese (usando i valori enum come stringhe)
  final bool isVegetarian;
  final bool isVegan;
  final bool isGlutenFree;
  final bool isDairyFree;
  final List<String> allergens;
  final String servingSize; // es. "100g", "1 porzione (80g)"
  final int servingSizeGrams; // peso in grammi di una porzione
  final int preparationTimeMinutes; // tempo di preparazione in minuti
  final String recipe; // istruzioni per la preparazione (se è una ricetta)
  final List<String> ingredients; // ingredienti (se è una ricetta)
  final bool isRecipe; // indica se è una ricetta o un alimento singolo
  final int complexity; // complessità da 1 (semplice) a 5 (complesso)
  final bool isSeasonal; // indica se è un alimento stagionale
  final List<int> seasonalMonths; // mesi in cui è disponibile (1-12)

  // Nuovi campi per l'algoritmo di alta precisione
  final FoodState foodState; // stato dell'alimento (crudo/cotto)
  final double rawToCookedFactor; // fattore di conversione da crudo a cotto (es. 2.5 per riso)
  final double volumeToWeightFactor; // fattore di conversione da volume a peso (es. 1.03 per latte)
  final int glycemicIndex; // indice glicemico (0-100)
  final int glycemicLoad; // carico glicemico
  final Map<String, double> micronutrients; // micronutrienti (vitamine, minerali) in mg/mcg per 100g
  final Map<String, dynamic> advancedProperties; // proprietà nutrizionali avanzate (PRAL, indice infiammatorio, ecc.)

  // Campi per la gestione delle fonti e validazione
  final DataSource dataSource; // fonte dei dati nutrizionali (USDA, CREA, ecc.)
  final String sourceId; // ID di riferimento nella fonte originale (es. FDC ID dell'USDA)
  final String sourceDescription; // Descrizione dettagliata della fonte
  final String brandName; // Nome del marchio/produttore (se applicabile)
  final ValidationStatus validationStatus; // Stato di validazione
  final DateTime lastValidatedAt; // Data dell'ultima validazione
  final String validatedBy; // Chi ha validato i dati
  final List<String> tags; // Tag per categorizzazione e ricerca

  // Campi per la regionalità italiana
  final List<ItalianRegion> italianRegions; // Regioni italiane associate all'alimento
  final bool isTraditionalItalian; // Indica se è un alimento tradizionale italiano

  // Porzioni comuni
  final String commonServingSize1Description; // es. "1 fetta", "1 vasetto"
  final int commonServingSize1Grams; // peso in grammi
  final String commonServingSize2Description; // es. "1 cucchiaio"
  final int commonServingSize2Grams; // peso in grammi

  // Campo per il nome tradotto (non persistente, calcolato a runtime)
  String? _translatedName;

  Food({
    required this.id,
    required this.name,
    this.description = '',
    this.imageUrl = '',
    required this.calories,
    required this.proteins,
    required this.carbs,
    required this.fats,
    this.fiber = 0,
    this.sugar = 0,
    this.sugars,
    this.saturatedFats,
    this.sodium,
    this.potassium,
    this.calcium,
    this.magnesium,
    this.phosphorus,
    required this.suitableForMeals,
    required this.categories,
    this.extendedCategories = const [],
    this.isVegetarian = false,
    this.isVegan = false,
    this.isGlutenFree = false,
    this.isDairyFree = false,
    this.allergens = const [],
    this.servingSize = '100g',
    this.servingSizeGrams = 100,
    this.preparationTimeMinutes = 0,
    this.recipe = '',
    this.ingredients = const [],
    this.isRecipe = false,
    this.complexity = 1,
    this.isSeasonal = false,
    this.seasonalMonths = const [],
    this.foodState = FoodState.raw,
    this.rawToCookedFactor = 1.0,
    this.volumeToWeightFactor = 1.0,
    this.glycemicIndex = 0,
    this.glycemicLoad = 0,
    this.micronutrients = const {},
    this.advancedProperties = const {},
    this.dataSource = DataSource.usda,
    this.sourceId = '',
    this.sourceDescription = 'USDA FoodData Central',
    this.brandName = '',
    this.validationStatus = ValidationStatus.unverified,
    DateTime? lastValidatedAt,
    this.validatedBy = '',
    this.tags = const [],
    this.italianRegions = const [],
    this.isTraditionalItalian = false,
    this.commonServingSize1Description = '',
    this.commonServingSize1Grams = 0,
    this.commonServingSize2Description = '',
    this.commonServingSize2Grams = 0,
  }) : this.lastValidatedAt = lastValidatedAt ?? DateTime.fromMillisecondsSinceEpoch(0);

  // Calcola le calorie per una porzione specifica
  int calculateCaloriesForServing(int grams) {
    return (calories * grams / 100).round();
  }

  // Calcola i macronutrienti per una porzione specifica
  Map<String, double> calculateMacrosForServing(int grams) {
    final factor = grams / 100;
    return {
      'proteins': proteins * factor,
      'carbs': carbs * factor,
      'fats': fats * factor,
      'fiber': fiber * factor,
      'sugar': sugar * factor,
    };
  }

  // Verifica se l'alimento è adatto a un tipo di dieta
  bool isSuitableForDiet(String dietType) {
    switch (dietType) {
      case 'vegetarian':
        return isVegetarian;
      case 'vegan':
        return isVegan;
      case 'glutenFree':
        return isGlutenFree;
      case 'dairyFree':
        return isDairyFree;
      default:
        return true;
    }
  }

  // Verifica se l'alimento contiene allergeni specifici
  bool containsAllergen(String allergen) {
    return allergens.contains(allergen.toLowerCase());
  }

  // Verifica se l'alimento è di stagione nel mese corrente
  bool isInSeason() {
    if (!isSeasonal) return true;
    final currentMonth = DateTime.now().month;
    return seasonalMonths.contains(currentMonth);
  }

  // Alias per isInSeason() per compatibilità con il nuovo codice
  bool isCurrentlySeasonal() {
    return isInSeason();
  }

  // Converti da Food a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'calories': calories,
      'proteins': proteins,
      'carbs': carbs,
      'fats': fats,
      'fiber': fiber,
      'sugar': sugar,
      'sugars': sugars,
      'saturatedFats': saturatedFats,
      'sodium': sodium,
      'potassium': potassium,
      'calcium': calcium,
      'magnesium': magnesium,
      'phosphorus': phosphorus,
      'suitableForMeals': suitableForMeals.map((e) => e.toString().split('.').last).toList(),
      'categories': categories.map((e) => e.toString().split('.').last).toList(),
      'extendedCategories': extendedCategories,
      'isVegetarian': isVegetarian,
      'isVegan': isVegan,
      'isGlutenFree': isGlutenFree,
      'isDairyFree': isDairyFree,
      'allergens': allergens,
      'servingSize': servingSize,
      'servingSizeGrams': servingSizeGrams,
      'preparationTimeMinutes': preparationTimeMinutes,
      'recipe': recipe,
      'ingredients': ingredients,
      'isRecipe': isRecipe,
      'complexity': complexity,
      'isSeasonal': isSeasonal,
      'seasonalMonths': seasonalMonths,
      'foodState': foodState.toString().split('.').last,
      'rawToCookedFactor': rawToCookedFactor,
      'volumeToWeightFactor': volumeToWeightFactor,
      'glycemicIndex': glycemicIndex,
      'glycemicLoad': glycemicLoad,
      'micronutrients': micronutrients,
      'advancedProperties': advancedProperties,
      'dataSource': dataSource.toString().split('.').last,
      'sourceId': sourceId,
      'sourceDescription': sourceDescription,
      'brandName': brandName,
      'validationStatus': validationStatus.toString().split('.').last,
      'lastValidatedAt': lastValidatedAt.millisecondsSinceEpoch,
      'validatedBy': validatedBy,
      'tags': tags,
      'italianRegions': italianRegions.map((e) => e.toString().split('.').last).toList(),
      'isTraditionalItalian': isTraditionalItalian,
      'commonServingSize1Description': commonServingSize1Description,
      'commonServingSize1Grams': commonServingSize1Grams,
      'commonServingSize2Description': commonServingSize2Description,
      'commonServingSize2Grams': commonServingSize2Grams,
    };
  }

  // Converti da Map a Food
  factory Food.fromMap(Map<String, dynamic> map) {
    return Food(
      id: map['id'] as String,
      name: map['name'] as String,
      description: map['description'] as String? ?? '',
      imageUrl: map['imageUrl'] as String? ?? '',
      calories: map['calories'] as int,
      proteins: map['proteins'] as double,
      carbs: map['carbs'] as double,
      fats: map['fats'] as double,
      fiber: map['fiber'] as double? ?? 0,
      sugar: map['sugar'] as double? ?? 0,
      sugars: map['sugars'] as double?,
      saturatedFats: map['saturatedFats'] as double?,
      sodium: map['sodium'] as double?,
      potassium: map['potassium'] as double?,
      calcium: map['calcium'] as double?,
      magnesium: map['magnesium'] as double?,
      phosphorus: map['phosphorus'] as double?,
      suitableForMeals: (map['suitableForMeals'] as List).map((e) =>
        MealType.values.firstWhere((type) => type.toString().split('.').last == e)).toList(),
      categories: (map['categories'] as List).map((e) =>
        FoodCategory.values.firstWhere((cat) => cat.toString().split('.').last == e)).toList(),
      extendedCategories: map['extendedCategories'] != null ? List<String>.from(map['extendedCategories']) : [],
      isVegetarian: map['isVegetarian'] as bool? ?? false,
      isVegan: map['isVegan'] as bool? ?? false,
      isGlutenFree: map['isGlutenFree'] as bool? ?? false,
      isDairyFree: map['isDairyFree'] as bool? ?? false,
      allergens: List<String>.from(map['allergens'] ?? []),
      servingSize: map['servingSize'] as String? ?? '100g',
      servingSizeGrams: map['servingSizeGrams'] as int? ?? 100,
      preparationTimeMinutes: map['preparationTimeMinutes'] as int? ?? 0,
      recipe: map['recipe'] as String? ?? '',
      ingredients: List<String>.from(map['ingredients'] ?? []),
      isRecipe: map['isRecipe'] as bool? ?? false,
      complexity: map['complexity'] as int? ?? 1,
      isSeasonal: map['isSeasonal'] as bool? ?? false,
      seasonalMonths: List<int>.from(map['seasonalMonths'] ?? []),
      foodState: map['foodState'] != null
          ? FoodState.values.firstWhere(
              (state) => state.toString().split('.').last == map['foodState'],
              orElse: () => FoodState.raw)
          : FoodState.raw,
      rawToCookedFactor: map['rawToCookedFactor'] as double? ?? 1.0,
      volumeToWeightFactor: map['volumeToWeightFactor'] as double? ?? 1.0,
      glycemicIndex: map['glycemicIndex'] as int? ?? 0,
      glycemicLoad: map['glycemicLoad'] as int? ?? 0,
      micronutrients: map['micronutrients'] != null
          ? Map<String, double>.from(map['micronutrients'])
          : const {},
      advancedProperties: map['advancedProperties'] != null
          ? Map<String, dynamic>.from(map['advancedProperties'] as Map)
          : {},
      dataSource: map['dataSource'] != null
          ? DataSource.values.firstWhere(
              (source) => source.toString().split('.').last == map['dataSource'],
              orElse: () => DataSource.unknown)
          : DataSource.unknown,
      sourceId: map['sourceId'] as String? ?? '',
      sourceDescription: map['sourceDescription'] as String? ?? '',
      brandName: map['brandName'] as String? ?? '',
      validationStatus: map['validationStatus'] != null
          ? ValidationStatus.values.firstWhere(
              (status) => status.toString().split('.').last == map['validationStatus'],
              orElse: () => ValidationStatus.unverified)
          : ValidationStatus.unverified,
      lastValidatedAt: map['lastValidatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['lastValidatedAt'] as int)
          : DateTime.fromMillisecondsSinceEpoch(0),
      validatedBy: map['validatedBy'] as String? ?? '',
      tags: List<String>.from(map['tags'] ?? []),
      italianRegions: map['italianRegions'] != null
          ? (map['italianRegions'] as List).map((e) =>
              ItalianRegion.values.firstWhere(
                (region) => region.toString().split('.').last == e,
                orElse: () => ItalianRegion.values.first))
              .toList()
          : const [],
      isTraditionalItalian: map['isTraditionalItalian'] as bool? ?? false,
      commonServingSize1Description: map['commonServingSize1Description'] as String? ?? '',
      commonServingSize1Grams: map['commonServingSize1Grams'] as int? ?? 0,
      commonServingSize2Description: map['commonServingSize2Description'] as String? ?? '',
      commonServingSize2Grams: map['commonServingSize2Grams'] as int? ?? 0,
    );
  }

  // Converti da Food a JSON
  String toJson() => json.encode(toMap());

  // Converti da JSON a Food
  factory Food.fromJson(String source) =>
      Food.fromMap(json.decode(source) as Map<String, dynamic>);

  // Crea una copia dell'alimento con possibilità di sovrascrivere alcuni campi
  Food copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    int? calories,
    double? proteins,
    double? carbs,
    double? fats,
    double? fiber,
    double? sugar,
    double? sugars,
    double? saturatedFats,
    double? sodium,
    double? potassium,
    double? calcium,
    double? magnesium,
    double? phosphorus,
    List<MealType>? suitableForMeals,
    List<FoodCategory>? categories,
    List<String>? extendedCategories,
    bool? isVegetarian,
    bool? isVegan,
    bool? isGlutenFree,
    bool? isDairyFree,
    List<String>? allergens,
    String? servingSize,
    int? servingSizeGrams,
    int? preparationTimeMinutes,
    String? recipe,
    List<String>? ingredients,
    bool? isRecipe,
    int? complexity,
    bool? isSeasonal,
    List<int>? seasonalMonths,
    FoodState? foodState,
    double? rawToCookedFactor,
    double? volumeToWeightFactor,
    int? glycemicIndex,
    int? glycemicLoad,
    Map<String, double>? micronutrients,
    Map<String, dynamic>? advancedProperties,
    DataSource? dataSource,
    String? sourceId,
    String? sourceDescription,
    String? brandName,
    ValidationStatus? validationStatus,
    DateTime? lastValidatedAt,
    String? validatedBy,
    List<String>? tags,
    String? commonServingSize1Description,
    int? commonServingSize1Grams,
    String? commonServingSize2Description,
    int? commonServingSize2Grams,
  }) {
    return Food(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      calories: calories ?? this.calories,
      proteins: proteins ?? this.proteins,
      carbs: carbs ?? this.carbs,
      fats: fats ?? this.fats,
      fiber: fiber ?? this.fiber,
      sugar: sugar ?? this.sugar,
      sugars: sugars ?? this.sugars,
      saturatedFats: saturatedFats ?? this.saturatedFats,
      sodium: sodium ?? this.sodium,
      potassium: potassium ?? this.potassium,
      calcium: calcium ?? this.calcium,
      magnesium: magnesium ?? this.magnesium,
      phosphorus: phosphorus ?? this.phosphorus,
      suitableForMeals: suitableForMeals ?? this.suitableForMeals,
      categories: categories ?? this.categories,
      extendedCategories: extendedCategories ?? this.extendedCategories,
      isVegetarian: isVegetarian ?? this.isVegetarian,
      isVegan: isVegan ?? this.isVegan,
      isGlutenFree: isGlutenFree ?? this.isGlutenFree,
      isDairyFree: isDairyFree ?? this.isDairyFree,
      allergens: allergens ?? this.allergens,
      servingSize: servingSize ?? this.servingSize,
      servingSizeGrams: servingSizeGrams ?? this.servingSizeGrams,
      preparationTimeMinutes: preparationTimeMinutes ?? this.preparationTimeMinutes,
      recipe: recipe ?? this.recipe,
      ingredients: ingredients ?? this.ingredients,
      isRecipe: isRecipe ?? this.isRecipe,
      complexity: complexity ?? this.complexity,
      isSeasonal: isSeasonal ?? this.isSeasonal,
      seasonalMonths: seasonalMonths ?? this.seasonalMonths,
      foodState: foodState ?? this.foodState,
      rawToCookedFactor: rawToCookedFactor ?? this.rawToCookedFactor,
      volumeToWeightFactor: volumeToWeightFactor ?? this.volumeToWeightFactor,
      glycemicIndex: glycemicIndex ?? this.glycemicIndex,
      glycemicLoad: glycemicLoad ?? this.glycemicLoad,
      micronutrients: micronutrients ?? this.micronutrients,
      advancedProperties: advancedProperties ?? this.advancedProperties,
      dataSource: dataSource ?? this.dataSource,
      sourceId: sourceId ?? this.sourceId,
      sourceDescription: sourceDescription ?? this.sourceDescription,
      brandName: brandName ?? this.brandName,
      validationStatus: validationStatus ?? this.validationStatus,
      lastValidatedAt: lastValidatedAt ?? this.lastValidatedAt,
      validatedBy: validatedBy ?? this.validatedBy,
      tags: tags ?? this.tags,
      commonServingSize1Description: commonServingSize1Description ?? this.commonServingSize1Description,
      commonServingSize1Grams: commonServingSize1Grams ?? this.commonServingSize1Grams,
      commonServingSize2Description: commonServingSize2Description ?? this.commonServingSize2Description,
      commonServingSize2Grams: commonServingSize2Grams ?? this.commonServingSize2Grams,
    );
  }

  // Verifica se l'alimento è validato
  bool isValidated() {
    return validationStatus == ValidationStatus.validated;
  }

  // Crea una copia dell'alimento con stato di validazione aggiornato
  Food withValidation({
    required ValidationStatus status,
    required String validator,
  }) {
    return copyWith(
      validationStatus: status,
      lastValidatedAt: DateTime.now(),
      validatedBy: validator,
    );
  }

  // Verifica se l'alimento ha una fonte affidabile
  bool hasReliableSource() {
    return dataSource == DataSource.usda ||
           dataSource == DataSource.crea ||
           dataSource == DataSource.uk_cofid ||
           dataSource == DataSource.fr_ciqual;
  }

  // Calcola il peso cotto a partire dal peso crudo
  int calculateCookedWeight(int rawWeight) {
    return (rawWeight * rawToCookedFactor).round();
  }

  // Calcola il peso crudo a partire dal peso cotto
  int calculateRawWeight(int cookedWeight) {
    return (cookedWeight / rawToCookedFactor).round();
  }

  // Calcola il peso in grammi a partire dal volume in ml
  int calculateWeightFromVolume(int volumeInMl) {
    return (volumeInMl * volumeToWeightFactor).round();
  }

  // Getter per il nome tradotto
  String getDisplayName({bool useTranslation = true}) {
    if (useTranslation && _translatedName != null) {
      return _translatedName!;
    }
    return name;
  }

  // Setter per il nome tradotto
  void setTranslatedName(String? translatedName) {
    _translatedName = translatedName;
  }
}
