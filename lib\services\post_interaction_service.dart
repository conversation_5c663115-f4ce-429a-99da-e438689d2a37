import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Servizio per gestire le interazioni dei post (like, commenti, condivisioni)
class PostInteractionService extends ChangeNotifier {
  static const String _likedPostsKey = 'liked_posts';
  static const String _postLikesCountKey = 'post_likes_count';
  
  final Set<String> _likedPosts = <String>{};
  final Map<String, int> _postLikesCount = <String, int>{};
  
  /// Set dei post che l'utente ha messo like
  Set<String> get likedPosts => Set.unmodifiable(_likedPosts);
  
  /// Mappa dei conteggi like per ogni post
  Map<String, int> get postLikesCount => Map.unmodifiable(_postLikesCount);

  /// Inizializza il servizio caricando i dati salvati
  Future<void> initialize() async {
    try {
      await _loadLikedPosts();
      await _loadPostLikesCount();
      
      if (kDebugMode) {
        print('✅ PostInteractionService inizializzato');
        print('📊 Post con like: ${_likedPosts.length}');
        print('📈 Conteggi like caricati: ${_postLikesCount.length}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore inizializzazione PostInteractionService: $e');
      }
    }
  }

  /// Carica i post con like da SharedPreferences
  Future<void> _loadLikedPosts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final likedPostsJson = prefs.getString(_likedPostsKey);
      
      if (likedPostsJson != null) {
        final List<dynamic> likedPostsList = jsonDecode(likedPostsJson);
        _likedPosts.clear();
        _likedPosts.addAll(likedPostsList.cast<String>());
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore caricamento post con like: $e');
      }
    }
  }

  /// Carica i conteggi like da SharedPreferences
  Future<void> _loadPostLikesCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final likesCountJson = prefs.getString(_postLikesCountKey);
      
      if (likesCountJson != null) {
        final Map<String, dynamic> likesCountMap = jsonDecode(likesCountJson);
        _postLikesCount.clear();
        _postLikesCount.addAll(
          likesCountMap.map((key, value) => MapEntry(key, value as int))
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore caricamento conteggi like: $e');
      }
    }
  }

  /// Salva i post con like in SharedPreferences
  Future<void> _saveLikedPosts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final likedPostsJson = jsonEncode(_likedPosts.toList());
      await prefs.setString(_likedPostsKey, likedPostsJson);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore salvataggio post con like: $e');
      }
    }
  }

  /// Salva i conteggi like in SharedPreferences
  Future<void> _savePostLikesCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final likesCountJson = jsonEncode(_postLikesCount);
      await prefs.setString(_postLikesCountKey, likesCountJson);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore salvataggio conteggi like: $e');
      }
    }
  }

  /// Verifica se un post è stato messo like dall'utente corrente
  bool isPostLiked(String postId) {
    return _likedPosts.contains(postId);
  }

  /// Ottieni il conteggio like per un post
  int getPostLikesCount(String postId) {
    return _postLikesCount[postId] ?? 0;
  }

  /// Toggle del like per un post
  Future<bool> toggleLike(String postId, int currentLikesCount) async {
    try {
      final isCurrentlyLiked = _likedPosts.contains(postId);
      final newLikedState = !isCurrentlyLiked;
      
      if (newLikedState) {
        // Aggiungi like
        _likedPosts.add(postId);
        _postLikesCount[postId] = currentLikesCount + 1;
      } else {
        // Rimuovi like
        _likedPosts.remove(postId);
        _postLikesCount[postId] = (currentLikesCount - 1).clamp(0, double.infinity).toInt();
      }

      // Salva i cambiamenti
      await _saveLikedPosts();
      await _savePostLikesCount();
      
      // Notifica i listener
      notifyListeners();

      if (kDebugMode) {
        print('${newLikedState ? '❤️' : '💔'} Like ${newLikedState ? 'aggiunto' : 'rimosso'} per post $postId');
        print('📊 Nuovo conteggio: ${_postLikesCount[postId]}');
      }

      return newLikedState;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore toggle like: $e');
      }
      rethrow;
    }
  }

  /// Inizializza il conteggio like per un post (se non esiste)
  void initializePostLikesCount(String postId, int initialCount) {
    if (!_postLikesCount.containsKey(postId)) {
      _postLikesCount[postId] = initialCount;
      _savePostLikesCount();
    }
  }

  /// Aggiorna il conteggio like per un post (da server)
  Future<void> updatePostLikesCount(String postId, int newCount) async {
    try {
      _postLikesCount[postId] = newCount;
      await _savePostLikesCount();
      notifyListeners();
      
      if (kDebugMode) {
        print('📊 Conteggio like aggiornato per post $postId: $newCount');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore aggiornamento conteggio like: $e');
      }
    }
  }

  /// Pulisce tutti i dati delle interazioni
  Future<void> clearAllInteractions() async {
    try {
      _likedPosts.clear();
      _postLikesCount.clear();
      
      await _saveLikedPosts();
      await _savePostLikesCount();
      
      notifyListeners();
      
      if (kDebugMode) {
        print('🧹 Tutte le interazioni pulite');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore pulizia interazioni: $e');
      }
    }
  }

  /// Ottieni statistiche delle interazioni
  Map<String, dynamic> getInteractionStats() {
    return {
      'totalLikedPosts': _likedPosts.length,
      'totalPostsWithLikes': _postLikesCount.length,
      'totalLikesGiven': _likedPosts.length,
      'averageLikesPerPost': _postLikesCount.isNotEmpty 
          ? _postLikesCount.values.reduce((a, b) => a + b) / _postLikesCount.length
          : 0.0,
    };
  }
}
