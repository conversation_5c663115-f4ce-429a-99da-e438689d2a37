import 'package:flutter/material.dart';
import '../utils/responsive_utils.dart';

/// Responsive layout wrapper that adapts content based on screen size
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  final bool useMaxWidth;

  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.useMaxWidth = true,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);

    Widget child = switch (deviceType) {
      DeviceType.mobile => mobile,
      DeviceType.tablet => tablet ?? mobile,
      DeviceType.desktop => desktop ?? tablet ?? mobile,
    };

    if (useMaxWidth && !ResponsiveUtils.isMobile(context)) {
      return Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: ResponsiveUtils.getMaxContentWidth(context),
          ),
          child: child,
        ),
      );
    }

    return child;
  }
}

/// Responsive container with adaptive padding and margins
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final Color? color;
  final Decoration? decoration;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double? width;
  final double? height;
  final bool adaptivePadding;
  final bool adaptiveMargin;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.color,
    this.decoration,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.adaptivePadding = true,
    this.adaptiveMargin = true,
  });

  @override
  Widget build(BuildContext context) {
    final responsivePadding = adaptivePadding && padding == null
        ? ResponsiveUtils.getResponsivePadding(context)
        : padding;

    final responsiveMargin = adaptiveMargin && margin == null
        ? ResponsiveUtils.getResponsiveMargin(context)
        : margin;

    return Container(
      width: width,
      height: height,
      padding: responsivePadding,
      margin: responsiveMargin,
      decoration: decoration,
      color: color,
      child: child,
    );
  }
}

/// Responsive card widget
class ResponsiveCard extends StatelessWidget {
  final Widget child;
  final Color? color;
  final double? elevation;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final VoidCallback? onTap;

  const ResponsiveCard({
    super.key,
    required this.child,
    this.color,
    this.elevation,
    this.padding,
    this.margin,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final responsiveElevation = elevation ??
        ResponsiveUtils.getResponsiveElevation(context, 2);
    final responsiveBorderRadius =
        ResponsiveUtils.getResponsiveBorderRadius(context, 12);
    final responsivePadding = padding ??
        ResponsiveUtils.getResponsivePadding(context);
    final responsiveMargin = margin ??
        ResponsiveUtils.getResponsiveMargin(context);

    return Container(
      margin: responsiveMargin,
      child: Card(
        color: color,
        elevation: responsiveElevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(responsiveBorderRadius),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(responsiveBorderRadius),
          child: Padding(
            padding: responsivePadding,
            child: child,
          ),
        ),
      ),
    );
  }
}

/// Responsive grid view
class ResponsiveGridView extends StatelessWidget {
  final List<Widget> children;
  final double? childAspectRatio;
  final EdgeInsets? padding;
  final double? mainAxisSpacing;
  final double? crossAxisSpacing;
  final ScrollPhysics? physics;

  const ResponsiveGridView({
    super.key,
    required this.children,
    this.childAspectRatio,
    this.padding,
    this.mainAxisSpacing,
    this.crossAxisSpacing,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    final columns = ResponsiveUtils.getResponsiveGridColumns(context);
    final responsivePadding = padding ??
        ResponsiveUtils.getResponsivePadding(context);

    return GridView.count(
      crossAxisCount: columns,
      childAspectRatio: childAspectRatio ?? 1.0,
      padding: responsivePadding,
      mainAxisSpacing: mainAxisSpacing ?? 16,
      crossAxisSpacing: crossAxisSpacing ?? 16,
      physics: physics,
      children: children,
    );
  }
}

/// Responsive list view with adaptive spacing
class ResponsiveListView extends StatelessWidget {
  final List<Widget> children;
  final EdgeInsets? padding;
  final double? itemSpacing;
  final ScrollPhysics? physics;
  final bool shrinkWrap;

  const ResponsiveListView({
    super.key,
    required this.children,
    this.padding,
    this.itemSpacing,
    this.physics,
    this.shrinkWrap = false,
  });

  @override
  Widget build(BuildContext context) {
    final responsivePadding = padding ??
        ResponsiveUtils.getResponsivePadding(context);
    final spacing = itemSpacing ??
        (ResponsiveUtils.isMobile(context) ? 8.0 : 12.0);

    return ListView.separated(
      padding: responsivePadding,
      physics: physics,
      shrinkWrap: shrinkWrap,
      itemCount: children.length,
      separatorBuilder: (context, index) => SizedBox(height: spacing),
      itemBuilder: (context, index) => children[index],
    );
  }
}

/// Responsive app bar
class ResponsiveAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final bool centerTitle;
  final PreferredSizeWidget? bottom;

  const ResponsiveAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.backgroundColor,
    this.foregroundColor,
    this.centerTitle = true,
    this.bottom,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);

    double titleFontSize = switch (deviceType) {
      DeviceType.mobile => 20,
      DeviceType.tablet => 22,
      DeviceType.desktop => 24,
    };

    return AppBar(
      title: Text(
        title,
        style: TextStyle(
          fontSize: titleFontSize,
          fontWeight: FontWeight.bold,
        ),
      ),
      actions: actions,
      leading: leading,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      centerTitle: centerTitle,
      bottom: bottom,
      elevation: ResponsiveUtils.getResponsiveElevation(context, 0),
    );
  }

  @override
  Size get preferredSize {
    final height = kToolbarHeight + (bottom?.preferredSize.height ?? 0);
    return Size.fromHeight(height);
  }
}

/// Responsive safe area wrapper
class ResponsiveSafeArea extends StatelessWidget {
  final Widget child;
  final bool top;
  final bool bottom;
  final bool left;
  final bool right;

  const ResponsiveSafeArea({
    super.key,
    required this.child,
    this.top = true,
    this.bottom = true,
    this.left = true,
    this.right = true,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: top,
      bottom: bottom,
      left: left,
      right: right,
      child: child,
    );
  }
}

/// Responsive orientation builder
class ResponsiveOrientationBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, Orientation orientation) builder;

  const ResponsiveOrientationBuilder({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(
      builder: (context, orientation) {
        return builder(context, orientation);
      },
    );
  }
}
