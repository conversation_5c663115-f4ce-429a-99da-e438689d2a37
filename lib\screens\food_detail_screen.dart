import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/food.dart';
import '../utils/micronutrients_helper.dart';
import '../utils/advanced_nutrition_properties.dart';
import '../services/advanced_nutrition_service.dart';
import '../widgets/advanced_nutrition_card.dart';

class FoodDetailScreen extends StatefulWidget {
  final Food food;

  const FoodDetailScreen({Key? key, required this.food}) : super(key: key);

  @override
  _FoodDetailScreenState createState() => _FoodDetailScreenState();
}

class _FoodDetailScreenState extends State<FoodDetailScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.food.name),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Panoramica'),
            Tab(text: 'Nutrienti'),
            Tab(text: 'Micronutrienti'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildNutrientsTab(),
          _buildMicronutrientsTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Immagine dell'alimento
          if (widget.food.imageUrl.isNotEmpty)
            ClipRRect(
              borderRadius: BorderRadius.circular(8.0),
              child: Image.network(
                widget.food.imageUrl,
                height: 200,
                width: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  height: 200,
                  width: double.infinity,
                  color: Colors.grey[300],
                  child: const Icon(Icons.restaurant, size: 64, color: Colors.grey),
                ),
              ),
            ),
          const SizedBox(height: 16),

          // Descrizione
          Text(
            widget.food.description,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          const SizedBox(height: 24),

          // Macronutrienti in grafico a torta
          const Text(
            'Distribuzione dei macronutrienti',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 200,
            child: _buildMacronutrientsPieChart(),
          ),
          const SizedBox(height: 24),

          // Informazioni di base
          const Text(
            'Informazioni di base',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          _buildInfoCard(),
          const SizedBox(height: 24),

          // Categorie e tag
          const Text(
            'Categorie e tag',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              ...widget.food.categories.map((category) => Chip(
                label: Text(category.toString().split('.').last),
                backgroundColor: Colors.blue[100],
              )),
              ...widget.food.tags.map((tag) => Chip(
                label: Text(tag),
                backgroundColor: Colors.green[100],
              )),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            _buildInfoRow('Calorie', '${widget.food.calories} kcal'),
            _buildInfoRow('Porzione', '${widget.food.servingSize} (${widget.food.servingSizeGrams}g)'),
            if (widget.food.isVegetarian) _buildInfoRow('Vegetariano', 'Sì'),
            if (widget.food.isVegan) _buildInfoRow('Vegano', 'Sì'),
            if (widget.food.isGlutenFree) _buildInfoRow('Senza glutine', 'Sì'),
            if (widget.food.isDairyFree) _buildInfoRow('Senza latticini', 'Sì'),
            if (widget.food.allergens.isNotEmpty) _buildInfoRow('Allergeni', widget.food.allergens.join(', ')),
            if (widget.food.isSeasonal) _buildInfoRow('Stagionale', 'Sì'),
            if (widget.food.isTraditionalItalian) _buildInfoRow('Tradizionale italiano', 'Sì'),
            if (widget.food.italianRegions.isNotEmpty) _buildInfoRow(
              'Regioni italiane',
              widget.food.italianRegions.map((r) => r.toString().split('.').last).join(', ')
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.bold)),
          Text(value),
        ],
      ),
    );
  }

  Widget _buildMacronutrientsPieChart() {
    // Calcola i grammi totali di macronutrienti
    final double totalGrams = widget.food.proteins + widget.food.carbs + widget.food.fats;

    // Se non ci sono macronutrienti, mostra un messaggio
    if (totalGrams == 0) {
      return const Center(child: Text('Nessun dato sui macronutrienti disponibile'));
    }

    // Calcola le percentuali
    final proteinPercentage = (widget.food.proteins / totalGrams * 100).toStringAsFixed(1);
    final carbsPercentage = (widget.food.carbs / totalGrams * 100).toStringAsFixed(1);
    final fatsPercentage = (widget.food.fats / totalGrams * 100).toStringAsFixed(1);

    return SingleChildScrollView(
      child: Column(
        children: [
          // Grafico a torta
          SizedBox(
            height: 200,
            child: PieChart(
              PieChartData(
                sections: [
                  PieChartSectionData(
                    color: Colors.red,
                    value: widget.food.proteins,
                    title: 'P: $proteinPercentage%',
                    radius: 60,
                    titleStyle: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                  ),
                  PieChartSectionData(
                    color: Colors.blue,
                    value: widget.food.carbs,
                    title: 'C: $carbsPercentage%',
                    radius: 60,
                    titleStyle: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                  ),
                  PieChartSectionData(
                    color: Colors.yellow,
                    value: widget.food.fats,
                    title: 'G: $fatsPercentage%',
                    radius: 60,
                    titleStyle: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                  ),
                ],
                sectionsSpace: 0,
                centerSpaceRadius: 40,
                startDegreeOffset: 180,
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Tabella dettagliata dei macronutrienti
          Table(
            border: TableBorder.all(
              color: Colors.grey.shade300,
              width: 1,
            ),
            columnWidths: const {
              0: FlexColumnWidth(3),
              1: FlexColumnWidth(2),
              2: FlexColumnWidth(2),
              3: FlexColumnWidth(2),
            },
            children: [
              TableRow(
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                ),
                children: const [
                  Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Text('Macronutriente', style: TextStyle(fontWeight: FontWeight.bold)),
                  ),
                  Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Text('Grammi', style: TextStyle(fontWeight: FontWeight.bold)),
                  ),
                  Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Text('Calorie', style: TextStyle(fontWeight: FontWeight.bold)),
                  ),
                  Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Text('% del totale', style: TextStyle(fontWeight: FontWeight.bold)),
                  ),
                ],
              ),
              _buildMacronutrientRow(
                'Proteine',
                widget.food.proteins.toStringAsFixed(1),
                (widget.food.proteins * 4).toStringAsFixed(0),
                Colors.red,
                proteinPercentage,
              ),
              _buildMacronutrientRow(
                'Carboidrati',
                widget.food.carbs.toStringAsFixed(1),
                (widget.food.carbs * 4).toStringAsFixed(0),
                Colors.blue,
                carbsPercentage,
              ),
              _buildMacronutrientRow(
                'Grassi',
                widget.food.fats.toStringAsFixed(1),
                (widget.food.fats * 9).toStringAsFixed(0),
                Colors.yellow,
                fatsPercentage,
              ),
              TableRow(
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                ),
                children: [
                  const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Text('TOTALE', style: TextStyle(fontWeight: FontWeight.bold)),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                      '${totalGrams.toStringAsFixed(1)}g',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                      '${widget.food.calories} kcal',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Text('100%', style: TextStyle(fontWeight: FontWeight.bold)),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  TableRow _buildMacronutrientRow(String name, String grams, String calories, Color color, String percentage) {
    return TableRow(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            children: [
              Container(
                width: 12,
                height: 12,
                color: color,
                margin: const EdgeInsets.only(right: 8),
              ),
              Text(name),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text('${grams}g'),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text('$calories kcal'),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text('$percentage%'),
        ),
      ],
    );
  }

  Widget _buildNutrientsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Tabella dei macronutrienti
          const Text(
            'Macronutrienti',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          _buildNutrientsTable(),
          const SizedBox(height: 24),

          // Grafico a barre per i macronutrienti
          const Text(
            'Distribuzione calorica',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 200,
            child: _buildCaloriesBarChart(),
          ),
          const SizedBox(height: 24),

          // Indice glicemico e carico glicemico
          if (widget.food.glycemicIndex > 0 || widget.food.glycemicLoad > 0) ...[
            const Text(
              'Indice glicemico e carico glicemico',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    if (widget.food.glycemicIndex > 0)
                      _buildInfoRow('Indice glicemico', '${widget.food.glycemicIndex}'),
                    if (widget.food.glycemicLoad > 0)
                      _buildInfoRow('Carico glicemico', '${widget.food.glycemicLoad}'),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNutrientsTable() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Table(
          columnWidths: const {
            0: FlexColumnWidth(3),
            1: FlexColumnWidth(2),
            2: FlexColumnWidth(2),
          },
          border: TableBorder.all(
            color: Colors.grey.shade300,
            width: 1,
          ),
          children: [
            const TableRow(
              decoration: BoxDecoration(color: Colors.grey),
              children: [
                Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text('Nutriente', style: TextStyle(fontWeight: FontWeight.bold)),
                ),
                Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text('Quantità', style: TextStyle(fontWeight: FontWeight.bold)),
                ),
                Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text('% Calorie', style: TextStyle(fontWeight: FontWeight.bold)),
                ),
              ],
            ),
            _buildNutrientRow('Calorie', '${widget.food.calories} kcal', '100%'),
            _buildNutrientRow('Proteine', '${widget.food.proteins} g',
              '${_calculatePercentage(widget.food.proteins * 4, widget.food.calories)}%'),
            _buildNutrientRow('Carboidrati', '${widget.food.carbs} g',
              '${_calculatePercentage(widget.food.carbs * 4, widget.food.calories)}%'),
            if (widget.food.sugar > 0)
              _buildNutrientRow('di cui zuccheri', '${widget.food.sugar} g',
                '${_calculatePercentage(widget.food.sugar * 4, widget.food.calories)}%'),
            _buildNutrientRow('Grassi', '${widget.food.fats} g',
              '${_calculatePercentage(widget.food.fats * 9, widget.food.calories)}%'),
            if (widget.food.fiber > 0)
              _buildNutrientRow('Fibre', '${widget.food.fiber} g', '-'),
          ],
        ),
      ),
    );
  }

  TableRow _buildNutrientRow(String nutrient, String amount, String percentage) {
    return TableRow(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(nutrient),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(amount),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(percentage),
        ),
      ],
    );
  }

  String _calculatePercentage(double value, int total) {
    if (total == 0) return '0';
    return ((value / total) * 100).toStringAsFixed(1);
  }

  Widget _buildCaloriesBarChart() {
    // Calcola le calorie da proteine, carboidrati e grassi
    final double proteinCalories = widget.food.proteins * 4;
    final double carbCalories = widget.food.carbs * 4;
    final double fatCalories = widget.food.fats * 9;

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: widget.food.calories.toDouble(),
        barTouchData: BarTouchData(enabled: false),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                String text = '';
                if (value == 0) text = 'Proteine';
                else if (value == 1) text = 'Carboidrati';
                else if (value == 2) text = 'Grassi';

                return Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(text, style: const TextStyle(fontSize: 12)),
                );
              },
              reservedSize: 40,
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text('${value.toInt()} kcal',
                  style: const TextStyle(fontSize: 10),
                );
              },
            ),
          ),
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false),
        barGroups: [
          BarChartGroupData(
            x: 0,
            barRods: [
              BarChartRodData(
                toY: proteinCalories,
                color: Colors.red,
                width: 20,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(6),
                  topRight: Radius.circular(6),
                ),
              ),
            ],
          ),
          BarChartGroupData(
            x: 1,
            barRods: [
              BarChartRodData(
                toY: carbCalories,
                color: Colors.blue,
                width: 20,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(6),
                  topRight: Radius.circular(6),
                ),
              ),
            ],
          ),
          BarChartGroupData(
            x: 2,
            barRods: [
              BarChartRodData(
                toY: fatCalories,
                color: Colors.yellow,
                width: 20,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(6),
                  topRight: Radius.circular(6),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMicronutrientsTab() {
    // Se non ci sono micronutrienti, mostra un messaggio
    if (widget.food.micronutrients.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('Nessun dato sui micronutrienti disponibile per questo alimento.'),
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Micronutrienti',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          _buildMicronutrientsTable(widget.food.micronutrients),
          const SizedBox(height: 24),

          // Grafico a barre per i minerali principali
          if (_hasMinerals()) ...[
            const Text(
              'Minerali principali',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 200,
              child: _buildMineralsBarChart(),
            ),
            const SizedBox(height: 24),
          ],

          // Rapporti tra minerali
          if (_hasMultipleMinerals()) ...[
            const Text(
              'Rapporti tra minerali',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            _buildMineralRatiosCard(),
          ],
        ],
      ),
    );
  }

  bool _hasMinerals() {
    return widget.food.micronutrients.containsKey(MicronutrientsHelper.CALCIUM) ||
           widget.food.micronutrients.containsKey(MicronutrientsHelper.PHOSPHORUS) ||
           widget.food.micronutrients.containsKey(MicronutrientsHelper.MAGNESIUM) ||
           widget.food.micronutrients.containsKey(MicronutrientsHelper.SODIUM) ||
           widget.food.micronutrients.containsKey(MicronutrientsHelper.POTASSIUM);
  }

  bool _hasMultipleMinerals() {
    int count = 0;
    if (widget.food.micronutrients.containsKey(MicronutrientsHelper.CALCIUM)) count++;
    if (widget.food.micronutrients.containsKey(MicronutrientsHelper.PHOSPHORUS)) count++;
    if (widget.food.micronutrients.containsKey(MicronutrientsHelper.SODIUM)) count++;
    return count >= 2;
  }

  Widget _buildMicronutrientsTable(Map<String, dynamic> micronutrients) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Table(
          columnWidths: const {
            0: FlexColumnWidth(3),
            1: FlexColumnWidth(2),
            2: FlexColumnWidth(2),
          },
          border: TableBorder.all(
            color: Colors.grey.shade300,
            width: 1,
          ),
          children: [
            const TableRow(
              decoration: BoxDecoration(color: Colors.grey),
              children: [
                Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text('Micronutriente', style: TextStyle(fontWeight: FontWeight.bold)),
                ),
                Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text('Quantità', style: TextStyle(fontWeight: FontWeight.bold)),
                ),
                Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text('% VNR', style: TextStyle(fontWeight: FontWeight.bold)),
                ),
              ],
            ),
            ...micronutrients.entries.map((entry) {
              final String nutrientKey = entry.key;
              final double value = entry.value is double ? entry.value : (entry.value as num).toDouble();
              final String displayName = MicronutrientsHelper.getDisplayName(nutrientKey);
              final String unit = MicronutrientsHelper.UNITS[nutrientKey] ?? '';
              final double percentage = MicronutrientsHelper.calculateDailyValuePercentage(nutrientKey, value);

              return TableRow(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(displayName),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text('$value $unit'),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text('${percentage.toStringAsFixed(1)}%'),
                  ),
                ],
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildMineralsBarChart() {
    final minerals = <String, double>{};

    if (widget.food.micronutrients.containsKey(MicronutrientsHelper.CALCIUM)) {
      final value = widget.food.micronutrients[MicronutrientsHelper.CALCIUM]!;
      minerals['Calcio'] = value is double ? value : (value as num).toDouble();
    }
    if (widget.food.micronutrients.containsKey(MicronutrientsHelper.PHOSPHORUS)) {
      final value = widget.food.micronutrients[MicronutrientsHelper.PHOSPHORUS]!;
      minerals['Fosforo'] = value is double ? value : (value as num).toDouble();
    }
    if (widget.food.micronutrients.containsKey(MicronutrientsHelper.MAGNESIUM)) {
      final value = widget.food.micronutrients[MicronutrientsHelper.MAGNESIUM]!;
      minerals['Magnesio'] = value is double ? value : (value as num).toDouble();
    }
    if (widget.food.micronutrients.containsKey(MicronutrientsHelper.SODIUM)) {
      final value = widget.food.micronutrients[MicronutrientsHelper.SODIUM]!;
      minerals['Sodio'] = value is double ? value : (value as num).toDouble();
    }
    if (widget.food.micronutrients.containsKey(MicronutrientsHelper.POTASSIUM)) {
      final value = widget.food.micronutrients[MicronutrientsHelper.POTASSIUM]!;
      minerals['Potassio'] = value is double ? value : (value as num).toDouble();
    }

    if (minerals.isEmpty) {
      return const Center(child: Text('Nessun dato sui minerali disponibile'));
    }

    // Trova il valore massimo per la scala
    final double maxValue = minerals.values.reduce((a, b) => a > b ? a : b);

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: maxValue * 1.1, // Aggiungi un po' di spazio in alto
        barTouchData: BarTouchData(enabled: false),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                final keys = minerals.keys.toList();
                if (value.toInt() < keys.length) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      keys[value.toInt()],
                      style: const TextStyle(fontSize: 10),
                      textAlign: TextAlign.center,
                    ),
                  );
                }
                return const SizedBox();
              },
              reservedSize: 40,
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text('${value.toInt()} mg',
                  style: const TextStyle(fontSize: 10),
                );
              },
            ),
          ),
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false),
        barGroups: List.generate(minerals.length, (index) {
          final value = minerals.values.elementAt(index);
          return BarChartGroupData(
            x: index,
            barRods: [
              BarChartRodData(
                toY: value,
                color: Colors.teal,
                width: 20,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(6),
                  topRight: Radius.circular(6),
                ),
              ),
            ],
          );
        }),
      ),
    );
  }

  Widget _buildMineralRatiosCard() {
    final ratios = <String, double>{};

    // Calcola il rapporto Ca/P
    if (widget.food.micronutrients.containsKey(MicronutrientsHelper.CALCIUM) &&
        widget.food.micronutrients.containsKey(MicronutrientsHelper.PHOSPHORUS)) {

      final caValue = widget.food.micronutrients[MicronutrientsHelper.CALCIUM]!;
      final pValue = widget.food.micronutrients[MicronutrientsHelper.PHOSPHORUS]!;

      final ca = caValue is double ? caValue : (caValue as num).toDouble();
      final p = pValue is double ? pValue : (pValue as num).toDouble();

      if (p > 0) {
        ratios['Ca/P'] = ca / p;
      }
    }

    // Calcola il rapporto Ca/Na
    if (widget.food.micronutrients.containsKey(MicronutrientsHelper.CALCIUM) &&
        widget.food.micronutrients.containsKey(MicronutrientsHelper.SODIUM)) {

      final caValue = widget.food.micronutrients[MicronutrientsHelper.CALCIUM]!;
      final naValue = widget.food.micronutrients[MicronutrientsHelper.SODIUM]!;

      final ca = caValue is double ? caValue : (caValue as num).toDouble();
      final na = naValue is double ? naValue : (naValue as num).toDouble();

      if (na > 0) {
        ratios['Ca/Na'] = ca / na;
      }
    }

    if (ratios.isEmpty) {
      return const SizedBox();
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: ratios.entries.map((entry) {
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('Rapporto ${entry.key}', style: const TextStyle(fontWeight: FontWeight.bold)),
                  Text(entry.value.toStringAsFixed(2)),
                ],
              ),
            );
          }).toList(),
        ),
      ),
    );
  }




}
