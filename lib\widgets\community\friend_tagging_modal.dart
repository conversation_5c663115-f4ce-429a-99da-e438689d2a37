import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../models/community_user.dart';
import '../../theme/dr_staffilano_theme.dart';

/// Modal per il tagging degli amici nei post
class FriendTaggingModal extends StatefulWidget {
  final List<CommunityUser> availableUsers;
  final List<String> selectedUserIds;
  final Function(List<String>) onUsersSelected;

  const FriendTaggingModal({
    Key? key,
    required this.availableUsers,
    required this.selectedUserIds,
    required this.onUsersSelected,
  }) : super(key: key);

  static Future<void> show(
    BuildContext context, {
    required List<CommunityUser> availableUsers,
    required List<String> selectedUserIds,
    required Function(List<String>) onUsersSelected,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: FriendTaggingModal(
          availableUsers: availableUsers,
          selectedUserIds: selectedUserIds,
          onUsersSelected: onUsersSelected,
        ),
      ),
    );
  }

  @override
  State<FriendTaggingModal> createState() => _FriendTaggingModalState();
}

class _FriendTaggingModalState extends State<FriendTaggingModal> {
  final TextEditingController _searchController = TextEditingController();
  List<String> _selectedUserIds = [];
  List<CommunityUser> _filteredUsers = [];

  @override
  void initState() {
    super.initState();
    _selectedUserIds = List.from(widget.selectedUserIds);
    _filteredUsers = widget.availableUsers;
    _searchController.addListener(_filterUsers);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterUsers() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredUsers = widget.availableUsers.where((user) {
        return user.displayName.toLowerCase().contains(query) ||
               user.username.toLowerCase().contains(query);
      }).toList();
    });
  }

  void _toggleUserSelection(String userId) {
    HapticFeedback.lightImpact();
    setState(() {
      if (_selectedUserIds.contains(userId)) {
        _selectedUserIds.remove(userId);
      } else {
        _selectedUserIds.add(userId);
      }
    });
  }

  void _confirmSelection() {
    HapticFeedback.mediumImpact();
    widget.onUsersSelected(_selectedUserIds);
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final modalWidth = screenSize.width > 600 ? 500.0 : screenSize.width * 0.85;
    final modalHeight = screenSize.height * 0.7;

    return Container(
      width: modalWidth,
      height: modalHeight,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(),
          _buildSearchBar(),
          _buildSelectedUsers(),
          Expanded(child: _buildUsersList()),
          _buildFooter(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.withOpacity(0.2)),
        ),
      ),
      child: Row(
        children: [
          Icon(
            FontAwesomeIcons.userTag,
            color: DrStaffilanoTheme.professionalBlue,
            size: 20,
          ),
          const SizedBox(width: 12),
          const Text(
            'Tagga amici',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close),
            style: IconButton.styleFrom(
              backgroundColor: Colors.grey.withOpacity(0.1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Cerca amici...',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.withOpacity(0.3)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: DrStaffilanoTheme.professionalBlue),
          ),
        ),
      ),
    );
  }

  Widget _buildSelectedUsers() {
    if (_selectedUserIds.isEmpty) return const SizedBox.shrink();

    final selectedUsers = widget.availableUsers
        .where((user) => _selectedUserIds.contains(user.id))
        .toList();

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: selectedUsers.length,
        itemBuilder: (context, index) {
          final user = selectedUsers[index];
          return Container(
            margin: const EdgeInsets.only(right: 8),
            child: Chip(
              avatar: CircleAvatar(
                backgroundImage: user.avatarUrl != null
                    ? NetworkImage(user.avatarUrl!)
                    : null,
                child: user.avatarUrl == null
                    ? Text(user.displayName[0].toUpperCase())
                    : null,
              ),
              label: Text(user.displayName),
              deleteIcon: const Icon(Icons.close, size: 16),
              onDeleted: () => _toggleUserSelection(user.id),
              backgroundColor: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
              deleteIconColor: DrStaffilanoTheme.primaryGreen,
            ),
          );
        },
      ),
    );
  }

  Widget _buildUsersList() {
    return ListView.builder(
      itemCount: _filteredUsers.length,
      itemBuilder: (context, index) {
        final user = _filteredUsers[index];
        final isSelected = _selectedUserIds.contains(user.id);

        return ListTile(
          leading: CircleAvatar(
            backgroundImage: user.avatarUrl != null
                ? NetworkImage(user.avatarUrl!)
                : null,
            child: user.avatarUrl == null
                ? Text(user.displayName[0].toUpperCase())
                : null,
          ),
          title: Text(
            user.displayName,
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
          subtitle: Text('@${user.username}'),
          trailing: isSelected
              ? Icon(
                  Icons.check_circle,
                  color: DrStaffilanoTheme.primaryGreen,
                )
              : Icon(
                  Icons.radio_button_unchecked,
                  color: Colors.grey[400],
                ),
          onTap: () => _toggleUserSelection(user.id),
        );
      },
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey.withOpacity(0.2)),
        ),
      ),
      child: Row(
        children: [
          Text(
            '${_selectedUserIds.length} selezionati',
            style: TextStyle(
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const Spacer(),
          ElevatedButton(
            onPressed: _confirmSelection,
            style: ElevatedButton.styleFrom(
              backgroundColor: DrStaffilanoTheme.primaryGreen,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Conferma'),
          ),
        ],
      ),
    );
  }
}
