import 'dart:math';
import '../models/ultra_detailed_profile.dart';
import '../models/diet_plan.dart';
import '../models/food.dart';
import '../data/food_database.dart';
import '../services/meal_appropriateness_validator.dart';
import 'portion_calculator_service.dart';

/// GENERATORE DI DIETE ULTRA-AVANZATO
/// Implementa tutte le specifiche della SEZIONE 2 del documento
class UltraAdvancedDietGenerator {
  static UltraAdvancedDietGenerator? _instance;
  final FoodDatabase _foodDatabase = FoodDatabase();
  final Random _random = Random();

  UltraAdvancedDietGenerator._();

  static Future<UltraAdvancedDietGenerator> getInstance() async {
    _instance ??= UltraAdvancedDietGenerator._();
    return _instance!;
  }

  /// METODO PRINCIPALE: Genera piano dietetico ultra-personalizzato
  Future<DailyDietPlan> generateUltraPersonalizedDiet({
    required UltraDetailedProfile profile,
    required DateTime targetDate,
    bool enableRefeedDay = false,
  }) async {
    print('🚀 GENERAZIONE DIETA ULTRA-PERSONALIZZATA');
    print('   Profilo: ${profile.baseProfile.name}');
    print('   Obiettivo: ${profile.primaryGoal}');
    print('   Data: ${targetDate.toIso8601String().split('T')[0]}');

    // STEP 1: VALIDAZIONE SICUREZZA NUTRIZIONALE
    final safetyValidation = profile.validateNutritionalSafety();
    if (!safetyValidation['isValid']) {
      throw Exception('Profilo non sicuro: ${safetyValidation['errors'].join(', ')}');
    }

    // STEP 2: CALCOLO AVANZATO CALORIE E MACRONUTRIENTI
    final calorieTarget = _calculateAdvancedCalorieTarget(profile, targetDate, enableRefeedDay);
    final macroTargets = _calculateAdvancedMacroTargets(profile, calorieTarget);

    print('📊 TARGET NUTRIZIONALI AVANZATI:');
    print('   Calorie: ${calorieTarget}kcal (BMR: ${profile.calculateBMR().round()}, TDEE: ${profile.calculateTDEE().round()})');
    print('   Proteine: ${macroTargets['proteins']}g');
    print('   Carboidrati: ${macroTargets['carbs']}g');
    print('   Grassi: ${macroTargets['fats']}g');

    // STEP 3: SELEZIONE ALIMENTI INTELLIGENTE
    final availableFoods = await _getFilteredFoods(profile);
    print('🥗 Alimenti disponibili dopo filtri: ${availableFoods.length}');

    // STEP 4: DISTRIBUZIONE CALORICA AVANZATA TRA PASTI
    final mealDistribution = _calculateAdvancedMealDistribution(profile, calorieTarget);
    final macroDistribution = _calculateAdvancedMacroDistribution(profile, macroTargets);

    // STEP 5: GENERAZIONE PASTI CON LOGICA AVANZATA
    final meals = <PlannedMeal>[];
    final mealTimes = _getOptimalMealTimes(profile);

    for (int i = 0; i < profile.mealsPerDay; i++) {
      final mealType = _getMealType(i, profile.mealsPerDay);
      final mealCalories = mealDistribution[i];
      final mealMacros = macroDistribution[i];
      final mealTime = mealTimes[i];

      final meal = await _generateAdvancedMeal(
        profile: profile,
        mealType: mealType,
        targetCalories: mealCalories,
        targetMacros: mealMacros,
        mealTime: mealTime,
        availableFoods: availableFoods,
        mealIndex: i,
      );

      meals.add(meal);
    }

    // STEP 6: VALIDAZIONE E OTTIMIZZAZIONE FINALE
    final optimizedMeals = await _optimizeMealPlan(meals, profile, calorieTarget, macroTargets);

    // STEP 7: CREAZIONE PIANO FINALE
    final dailyPlan = DailyDietPlan(
      date: targetDate.toIso8601String().split('T')[0], // Convert DateTime to String (YYYY-MM-DD)
      meals: optimizedMeals,
      calorieTarget: calorieTarget,
      macroTargets: {
        'proteins': macroTargets['proteins']!.round(),
        'carbs': macroTargets['carbs']!.round(),
        'fats': macroTargets['fats']!.round(),
      },
    );

    print('✅ PIANO GENERATO CON SUCCESSO');
    _logPlanSummary(dailyPlan);

    return dailyPlan;
  }

  /// CALCOLO AVANZATO TARGET CALORICO con cycling e refeed
  int _calculateAdvancedCalorieTarget(UltraDetailedProfile profile, DateTime date, bool enableRefeedDay) {
    final baseTarget = profile.calculateCalorieTarget();

    // CALORIE CYCLING per utenti avanzati
    if (profile.enableCalorieCycling && profile.primaryGoal == PrimaryGoal.weightLoss) {
      final dayOfWeek = date.weekday;

      // Refeed day (sabato = 6)
      if (enableRefeedDay && dayOfWeek == 6) {
        final tdee = profile.calculateTDEE();
        print('🔄 REFEED DAY: Calorie aumentate a TDEE');
        return tdee.round();
      }

      // Giorni di allenamento vs riposo
      final hasWorkout = _hasWorkoutOnDay(profile, dayOfWeek);
      if (hasWorkout) {
        final bonus = (baseTarget * 0.1).round(); // +10% nei giorni di allenamento
        print('💪 GIORNO ALLENAMENTO: +${bonus}kcal');
        return baseTarget + bonus;
      }
    }

    return baseTarget;
  }

  /// CALCOLO AVANZATO MACRONUTRIENTI con timing e regimi specifici
  Map<String, double> _calculateAdvancedMacroTargets(UltraDetailedProfile profile, int calories) {
    final baseMacros = profile.calculateMacroDistribution();

    // AGGIUSTAMENTI PER REGIME ALIMENTARE SPECIFICO
    switch (profile.dietaryRegimen) {
      case DietaryRegimen.ketogenic:
        return _adjustForKetogenic(baseMacros, calories);
      case DietaryRegimen.lowCarb:
        return _adjustForLowCarb(baseMacros, calories);
      case DietaryRegimen.mediterranean:
        return _adjustForMediterranean(baseMacros, calories);
      case DietaryRegimen.dash:
        return _adjustForDASH(baseMacros, calories, profile);
      default:
        break;
    }

    // AGGIUSTAMENTI PER CONDIZIONI MEDICHE
    return _adjustForMedicalConditions(baseMacros, calories, profile);
  }

  /// SELEZIONE ALIMENTI CON FILTRI AVANZATI
  Future<List<Food>> _getFilteredFoods(UltraDetailedProfile profile) async {
    final allFoods = await _foodDatabase.getAllFoods();

    return allFoods.where((food) {
      // FILTRO ALLERGIE (CRITICO)
      for (final allergy in profile.foodAllergies) {
        if (_containsAllergen(food, allergy)) {
          return false;
        }
      }

      // FILTRO INTOLLERANZE
      for (final intolerance in profile.foodIntolerances) {
        if (_containsIntolerance(food, intolerance)) {
          return false;
        }
      }

      // FILTRO ALIMENTI NON GRADITI
      if (profile.dislikedFoods.contains(food.name)) {
        return false;
      }

      // FILTRO REGIME ALIMENTARE
      if (!_isCompatibleWithRegimen(food, profile.dietaryRegimen)) {
        return false;
      }

      // FILTRO BUDGET
      if (!_isWithinBudget(food, profile.budgetLevel)) {
        return false;
      }

      // FILTRO CONDIZIONI MEDICHE
      if (!_isSafeForMedicalConditions(food, profile.medicalConditions)) {
        return false;
      }

      return true;
    }).toList();
  }

  /// DISTRIBUZIONE CALORICA AVANZATA TRA PASTI
  List<int> _calculateAdvancedMealDistribution(UltraDetailedProfile profile, int totalCalories) {
    // Usa il servizio esistente come base
    final baseDistribution = PortionCalculatorService.getMealCalorieDistribution(
      profile.mealsPerDay,
      preference: _getUserPreference(profile),
    );

    final mealCalories = <int>[];
    int remainingCalories = totalCalories;

    final mealTypes = baseDistribution.keys.toList();
    for (int i = 0; i < mealTypes.length - 1; i++) {
      final percentage = baseDistribution[mealTypes[i]]!;
      final calories = (totalCalories * percentage).round();
      mealCalories.add(calories);
      remainingCalories -= calories;
    }

    // Ultimo pasto prende le calorie rimanenti
    mealCalories.add(remainingCalories);

    return mealCalories;
  }

  /// DISTRIBUZIONE MACRONUTRIENTI AVANZATA TRA PASTI
  List<Map<String, double>> _calculateAdvancedMacroDistribution(
    UltraDetailedProfile profile,
    Map<String, double> totalMacros,
  ) {
    final mealMacros = <Map<String, double>>[];
    final remainingMacros = Map<String, double>.from(totalMacros);

    for (int i = 0; i < profile.mealsPerDay - 1; i++) {
      final mealType = _getMealTypeString(i, profile.mealsPerDay);

      // Usa distribuzione specifica per tipo di pasto
      final distribution = PortionCalculatorService.getMacroDistributionForMeal(
        mealType,
        profile.baseProfile.goal,
      );

      final macros = <String, double>{};
      totalMacros.forEach((key, totalValue) {
        final percentage = distribution[key] ?? (1.0 / profile.mealsPerDay);
        final amount = totalValue * percentage;
        macros[key] = amount;
        remainingMacros[key] = remainingMacros[key]! - amount;
      });

      mealMacros.add(macros);
    }

    // Ultimo pasto prende i macro rimanenti
    mealMacros.add(remainingMacros);

    return mealMacros;
  }

  /// GENERAZIONE PASTO AVANZATO
  Future<PlannedMeal> _generateAdvancedMeal({
    required UltraDetailedProfile profile,
    required MealType mealType,
    required int targetCalories,
    required Map<String, double> targetMacros,
    required String mealTime,
    required List<Food> availableFoods,
    required int mealIndex,
  }) async {
    print('🍽️ Generazione pasto avanzato: ${mealType.toString().split('.').last}');

    // STEP 1: Filtra alimenti appropriati per il pasto
    final suitableFoods = MealAppropriatenessValidator.filterAppropriateForMeal(
      availableFoods.where((food) => food.suitableForMeals.contains(mealType)).toList(),
      mealType,
    );

    if (suitableFoods.isEmpty) {
      throw Exception('Nessun alimento adatto per ${mealType.toString().split('.').last}');
    }

    // STEP 2: Selezione intelligente con priorità
    final selectedFoods = await _selectFoodsWithAdvancedLogic(
      suitableFoods,
      targetCalories,
      targetMacros,
      mealType,
      profile,
    );

    // STEP 3: Calcolo porzioni ottimali
    final optimizedPortions = PortionCalculatorService.calculateOptimalPortions(
      selectedFoods: selectedFoods,
      targetCalories: targetCalories,
      targetMacros: targetMacros.map((k, v) => MapEntry(k, v.round())),
      mealType: mealType.toString().split('.').last,
    );

    return PlannedMeal(
      id: 'meal_${DateTime.now().millisecondsSinceEpoch}_$mealIndex',
      name: _getMealName(mealType.toString().split('.').last),
      type: mealType,
      foods: optimizedPortions,
      time: mealTime,
    );
  }

  /// SELEZIONE ALIMENTI CON LOGICA AVANZATA
  Future<List<Food>> _selectFoodsWithAdvancedLogic(
    List<Food> suitableFoods,
    int targetCalories,
    Map<String, double> targetMacros,
    MealType mealType,
    UltraDetailedProfile profile,
  ) async {
    final selectedFoods = <Food>[];

    // PRIORITÀ 1: Alimenti preferiti dell'utente
    final preferredAvailable = suitableFoods.where(
      (food) => profile.preferredFoods.contains(food.name)
    ).toList();

    if (preferredAvailable.isNotEmpty) {
      selectedFoods.add(preferredAvailable[_random.nextInt(preferredAvailable.length)]);
    }

    // PRIORITÀ 2: Fonte proteica di qualità
    final proteinSources = suitableFoods.where((food) => food.proteins > 15).toList();
    if (proteinSources.isNotEmpty && !selectedFoods.any((f) => f.proteins > 15)) {
      selectedFoods.add(proteinSources[_random.nextInt(proteinSources.length)]);
    }

    // PRIORITÀ 3: Carboidrati complessi (se non keto)
    if (profile.dietaryRegimen != DietaryRegimen.ketogenic) {
      final carbSources = suitableFoods.where((food) =>
        food.carbs > 50 && food.fiber > 3
      ).toList();
      if (carbSources.isNotEmpty && !selectedFoods.any((f) => f.carbs > 50)) {
        selectedFoods.add(carbSources[_random.nextInt(carbSources.length)]);
      }
    }

    // PRIORITÀ 4: Verdure (sempre)
    final vegetables = suitableFoods.where((food) =>
      food.categories.contains(FoodCategory.vegetable)
    ).toList();
    if (vegetables.isNotEmpty && !selectedFoods.any((f) => f.categories.contains(FoodCategory.vegetable))) {
      selectedFoods.add(vegetables[_random.nextInt(vegetables.length)]);
    }

    // PRIORITÀ 5: Grassi sani
    final healthyFats = suitableFoods.where((food) =>
      food.categories.contains(FoodCategory.fat) &&
      food.name.toLowerCase().contains('olio')
    ).toList();
    if (healthyFats.isNotEmpty && !selectedFoods.any((f) => f.categories.contains(FoodCategory.fat))) {
      selectedFoods.add(healthyFats[_random.nextInt(healthyFats.length)]);
    }

    return selectedFoods.take(4).toList(); // Massimo 4 alimenti per pasto
  }

  // METODI DI SUPPORTO

  bool _hasWorkoutOnDay(UltraDetailedProfile profile, int dayOfWeek) {
    // Logica semplificata: assume allenamenti nei giorni feriali
    return profile.plannedExercises.isNotEmpty && dayOfWeek <= 5;
  }

  String _getUserPreference(UltraDetailedProfile profile) {
    // Analizza le preferenze dell'utente per determinare la distribuzione
    if (profile.primaryGoal == PrimaryGoal.weightLoss) {
      return 'cena_leggera';
    }
    return ''; // Default
  }

  MealType _getMealType(int index, int mealsPerDay) {
    switch (mealsPerDay) {
      case 3:
        return [MealType.breakfast, MealType.lunch, MealType.dinner][index];
      case 4:
        return [MealType.breakfast, MealType.snack, MealType.lunch, MealType.dinner][index];
      case 5:
        return [MealType.breakfast, MealType.snack, MealType.lunch, MealType.snack, MealType.dinner][index];
      default:
        return MealType.breakfast;
    }
  }

  String _getMealTypeString(int index, int mealsPerDay) {
    return _getMealType(index, mealsPerDay).toString().split('.').last;
  }

  List<String> _getOptimalMealTimes(UltraDetailedProfile profile) {
    // Orari ottimali basati su ricerca scientifica
    switch (profile.mealsPerDay) {
      case 3:
        return ['07:30', '13:00', '19:30'];
      case 4:
        return ['07:30', '10:30', '13:00', '19:30'];
      case 5:
        return ['07:30', '10:30', '13:00', '16:30', '19:30'];
      default:
        return ['08:00', '13:00', '19:00'];
    }
  }

  String _getMealName(String mealType) {
    switch (mealType) {
      case 'breakfast':
        return 'Colazione';
      case 'lunch':
        return 'Pranzo';
      case 'dinner':
        return 'Cena';
      case 'snack':
        return 'Spuntino';
      default:
        return 'Pasto';
    }
  }

  // METODI PER AGGIUSTAMENTI SPECIFICI (da implementare)
  Map<String, double> _adjustForKetogenic(Map<String, double> macros, int calories) {
    // Implementazione per dieta chetogenica
    return {
      'proteins': macros['proteins']!,
      'carbs': (calories * 0.05 / 4), // 5% carboidrati
      'fats': (calories * 0.75 / 9),  // 75% grassi
    };
  }

  Map<String, double> _adjustForLowCarb(Map<String, double> macros, int calories) {
    // Implementazione per low-carb
    return macros; // Placeholder
  }

  Map<String, double> _adjustForMediterranean(Map<String, double> macros, int calories) {
    // Implementazione per dieta mediterranea
    return macros; // Placeholder
  }

  Map<String, double> _adjustForDASH(Map<String, double> macros, int calories, UltraDetailedProfile profile) {
    // Implementazione per dieta DASH
    return macros; // Placeholder
  }

  Map<String, double> _adjustForMedicalConditions(Map<String, double> macros, int calories, UltraDetailedProfile profile) {
    // Implementazione per condizioni mediche
    return macros; // Placeholder
  }

  bool _containsAllergen(Food food, FoodAllergy allergy) {
    // Implementazione controllo allergeni
    return false; // Placeholder
  }

  bool _containsIntolerance(Food food, FoodIntolerance intolerance) {
    // Implementazione controllo intolleranze
    return false; // Placeholder
  }

  bool _isCompatibleWithRegimen(Food food, DietaryRegimen? regimen) {
    // Implementazione compatibilità regime
    return true; // Placeholder
  }

  bool _isWithinBudget(Food food, BudgetLevel budget) {
    // Implementazione controllo budget
    return true; // Placeholder
  }

  bool _isSafeForMedicalConditions(Food food, List<String> conditions) {
    // Implementazione sicurezza per condizioni mediche
    return true; // Placeholder
  }

  Future<List<PlannedMeal>> _optimizeMealPlan(
    List<PlannedMeal> meals,
    UltraDetailedProfile profile,
    int calorieTarget,
    Map<String, double> macroTargets,
  ) async {
    // Implementazione ottimizzazione finale
    return meals; // Placeholder
  }

  String _generatePlanNotes(UltraDetailedProfile profile, Map<String, dynamic> safetyValidation) {
    final notes = <String>[];

    if (safetyValidation['warnings'].isNotEmpty) {
      notes.add('⚠️ Avvertenze: ${safetyValidation['warnings'].join(', ')}');
    }

    if (safetyValidation['requiresMedicalSupervision']) {
      notes.add('🏥 Si raccomanda supervisione medica per le condizioni indicate');
    }

    notes.add('📊 Piano generato con algoritmo ultra-avanzato Dr. Staffilano');

    return notes.join('\n');
  }

  void _logPlanSummary(DailyDietPlan plan) {
    final totalCalories = plan.meals.fold(0, (sum, meal) =>
      sum + meal.foods.fold(0, (mealSum, portion) => mealSum + portion.calories));

    print('📋 RIEPILOGO PIANO:');
    print('   Pasti: ${plan.meals.length}');
    print('   Calorie totali: ${totalCalories}kcal (target: ${plan.calorieTarget}kcal)');
    print('   Scostamento: ${((totalCalories - plan.calorieTarget) / plan.calorieTarget * 100).toStringAsFixed(1)}%');
  }
}
