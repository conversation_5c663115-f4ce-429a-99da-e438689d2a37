import 'dart:math';
import '../models/welljourney_models.dart';
import '../controllers/welljourney_controller.dart';

/// Servizio robusto per il calcolo del NutriScore Dr. Staffilano
/// Implementa defensive programming e null safety completa
class NutriScoreService {
  static final NutriScoreService _instance = NutriScoreService._internal();
  factory NutriScoreService() => _instance;
  NutriScoreService._internal();

  static NutriScoreService get instance => _instance;

  // Costanti per valori di default sicuri
  static const double _defaultEducationalScore = 15.0;
  static const double _defaultConsistencyScore = 10.0;
  static const double _defaultApplicationScore = 8.0;
  static const double _defaultExpertiseScore = 12.0;
  static const double _maxScorePerCategory = 25.0;
  static const double _maxTotalScore = 100.0;

  /// Calcola il NutriScore con protezione completa contro null values
  NutriScoreResult calculateNutriScore(WellJourneyController? controller) {
    try {
      print('🔄 Inizio calcolo NutriScore robusto...');

      // Validazione input con fallback sicuro
      if (controller == null) {
        print('⚠️ Controller null, usando valori di default');
        return _createSafeDefaultScore('Controller non disponibile');
      }

      final userProgress = controller.userProgress;
      if (userProgress == null) {
        print('⚠️ UserProgress null, usando valori di default');
        return _createSafeDefaultScore('Dati utente non disponibili');
      }

      // Calcolo sicuro di ogni componente del punteggio
      final educationalScore = _calculateEducationalScoreSafe(
        userProgress.completedModules?.length ?? 0,
        controller.enrolledPathways?.length ?? 0,
      );

      final consistencyScore = _calculateConsistencyScoreSafe(
        userProgress.currentStreak ?? 0,
        userProgress.longestStreak ?? 0,
      );

      final applicationScore = _calculateApplicationScoreSafe(
        controller.earnedBadges?.length ?? 0,
        userProgress.completedPathways?.length ?? 0,
      );

      final expertiseScore = _calculateExpertiseScoreSafe(
        userProgress.totalPoints ?? 0,
        userProgress.level ?? 1,
      );

      final totalScore = _clampScore(
        educationalScore + consistencyScore + applicationScore + expertiseScore
      );

      return NutriScoreResult(
        totalScore: totalScore,
        educationalScore: educationalScore,
        consistencyScore: consistencyScore,
        applicationScore: applicationScore,
        expertiseScore: expertiseScore,
        level: _getNutriScoreLevel(totalScore),
        recommendations: _generateRecommendations(totalScore, educationalScore, consistencyScore, applicationScore, expertiseScore),
        nextMilestone: _getNextMilestone(totalScore),
        strengthAreas: _getStrengthAreas(educationalScore, consistencyScore, applicationScore, expertiseScore),
        improvementAreas: _getImprovementAreas(educationalScore, consistencyScore, applicationScore, expertiseScore),
      );
    } catch (e) {
      print('❌ Errore nel calcolo NutriScore: $e');
      return _createDefaultNutriScoreResult();
    }
  }

  /// Crea un punteggio di default sicuro
  NutriScoreResult _createSafeDefaultScore(String reason) {
    print('📝 Creando punteggio di default: $reason');

    return NutriScoreResult(
      totalScore: _defaultEducationalScore + _defaultConsistencyScore + _defaultApplicationScore + _defaultExpertiseScore,
      educationalScore: _defaultEducationalScore,
      consistencyScore: _defaultConsistencyScore,
      applicationScore: _defaultApplicationScore,
      expertiseScore: _defaultExpertiseScore,
      level: NutriScoreLevel.principiante,
      recommendations: [
        'Benvenuto nel sistema NutriScore Dr. Staffilano!',
        'Inizia iscrivendoti al percorso "Alimentazione per il Cuore"',
        'Completa il tuo primo modulo per iniziare a guadagnare punti',
        'Mantieni la consistenza per migliorare il tuo punteggio',
      ],
      nextMilestone: NutriScoreMilestone(
        targetScore: 60,
        title: 'Intermedio',
        description: 'Completa più moduli per raggiungere il livello Intermedio',
        pointsNeeded: 15,
      ),
      strengthAreas: ['Motivazione iniziale', 'Potenziale di crescita'],
      improvementAreas: ['Inizia il percorso educativo', 'Stabilisci una routine'],
    );
  }

  /// Crea un risultato NutriScore di default per utenti nuovi
  NutriScoreResult _createDefaultNutriScoreResult() {
    return NutriScoreResult(
      totalScore: 0.0,
      educationalScore: 0.0,
      consistencyScore: 0.0,
      applicationScore: 0.0,
      expertiseScore: 0.0,
      level: NutriScoreLevel.novizio,
      recommendations: [
        'Benvenuto nel sistema NutriScore Dr. Staffilano!',
        'Inizia iscrivendoti al percorso "Alimentazione per il Cuore"',
        'Completa il tuo primo modulo per iniziare a guadagnare punti',
        'Mantieni la consistenza per migliorare il tuo punteggio',
      ],
      nextMilestone: NutriScoreMilestone(
        targetScore: 40,
        title: 'Principiante',
        description: 'Completa i primi moduli per raggiungere il livello Principiante',
        pointsNeeded: 40,
      ),
      strengthAreas: [],
      improvementAreas: [
        'Inizia il tuo percorso educativo',
        'Stabilisci una routine di apprendimento',
        'Applica i principi nutrizionali nella vita quotidiana',
      ],
    );
  }

  /// Calcolo sicuro del punteggio educativo
  double _calculateEducationalScoreSafe(int completedModules, int enrolledPathways) {
    try {
      // Formula migliorata che premia l'apprendimento
      final moduleScore = min(completedModules * 3.0, 18.0); // Max 18 punti (più generoso)
      final pathwayScore = min(enrolledPathways * 2.0, 7.0); // Max 7 punti

      print('📚 Calcolo educativo: $completedModules moduli, $enrolledPathways pathway → ${(moduleScore + pathwayScore).toStringAsFixed(1)} punti');

      return _clampScore(moduleScore + pathwayScore);
    } catch (e) {
      print('⚠️ Errore calcolo educativo: $e');
      return _defaultEducationalScore;
    }
  }

  /// Calcolo sicuro del punteggio di consistenza
  double _calculateConsistencyScoreSafe(int currentStreak, int longestStreak) {
    try {
      final streakScore = min(currentStreak * 1.5, 15.0); // Max 15 punti
      final longestScore = min(longestStreak * 0.5, 10.0); // Max 10 punti
      return _clampScore(streakScore + longestScore);
    } catch (e) {
      print('⚠️ Errore calcolo consistenza: $e');
      return _defaultConsistencyScore;
    }
  }

  /// Calcolo sicuro del punteggio di applicazione
  double _calculateApplicationScoreSafe(int earnedBadges, int completedPathways) {
    try {
      final badgeScore = min(earnedBadges * 3.0, 15.0); // Max 15 punti
      final pathwayScore = min(completedPathways * 2.0, 10.0); // Max 10 punti
      return _clampScore(badgeScore + pathwayScore);
    } catch (e) {
      print('⚠️ Errore calcolo applicazione: $e');
      return _defaultApplicationScore;
    }
  }

  /// Calcolo sicuro del punteggio di expertise
  double _calculateExpertiseScoreSafe(int totalPoints, int level) {
    try {
      // Formula migliorata che considera i punti WellJourney reali
      final pointsScore = min(totalPoints / 20.0, 20.0); // Max 20 punti (più generoso)
      final levelScore = min(level * 1.0, 5.0); // Max 5 punti

      print('🎯 Calcolo expertise: $totalPoints punti → ${pointsScore.toStringAsFixed(1)} punti NutriScore');

      return _clampScore(pointsScore + levelScore);
    } catch (e) {
      print('⚠️ Errore calcolo expertise: $e');
      return _defaultExpertiseScore;
    }
  }

  /// Limita il punteggio ai valori validi
  double _clampScore(double score) {
    if (score.isNaN || score.isInfinite) {
      print('⚠️ Punteggio non valido: $score, usando default');
      return 0.0;
    }
    return score.clamp(0.0, _maxScorePerCategory);
  }

  /// Calcola il punteggio educativo basato sui percorsi completati e quiz WellJourney
  double _calculateEducationalScore(WellJourneyController controller) {
    try {
      final userProgress = controller.userProgress;
      if (userProgress == null) return 0.0;

      // Punteggio base dai moduli completati
      final completedModules = userProgress.completedModules?.length ?? 0;
      final enrolledPathways = controller.enrolledPathways?.length ?? 0;

      // Calcolo migliorato che considera la qualità dell'apprendimento
      double moduleScore = completedModules * 8.0; // 8 punti per modulo completato
      double pathwayScore = enrolledPathways * 3.0; // 3 punti per pathway iscritto

      // Bonus per completamento di pathway interi
      final completedPathways = controller.getCompletedPathways().length;
      double pathwayCompletionBonus = completedPathways * 15.0; // 15 punti bonus per pathway completato

      // Integrazione punti WellJourney (convertiti in scala 0-30)
      double wellJourneyScore = _calculateWellJourneyEducationalContribution(userProgress);

      // Bonus per consistenza nell'apprendimento
      double consistencyBonus = _calculateEducationalConsistencyBonus(controller);

      final totalScore = moduleScore + pathwayScore + pathwayCompletionBonus + wellJourneyScore + consistencyBonus;

      print('📚 Punteggio educativo dettagliato:');
      print('   - Moduli: $moduleScore ($completedModules moduli)');
      print('   - Pathway iscritti: $pathwayScore ($enrolledPathways pathway)');
      print('   - Pathway completati: $pathwayCompletionBonus ($completedPathways pathway)');
      print('   - WellJourney: $wellJourneyScore');
      print('   - Consistenza: $consistencyBonus');
      print('   - Totale: $totalScore');

      return totalScore.clamp(0, 100);
    } catch (e) {
      print('❌ Errore nel calcolo punteggio educativo: $e');
      return 0.0;
    }
  }

  /// Calcola il contributo educativo dai punti WellJourney
  double _calculateWellJourneyEducationalContribution(WellJourneyProgress userProgress) {
    try {
      // Converte i punti totali WellJourney in contributo educativo (max 30 punti)
      final totalPoints = userProgress.totalPoints;

      // Formula progressiva: più punti = maggiore contributo, ma con rendimenti decrescenti
      double contribution = 0;

      if (totalPoints >= 5000) {
        contribution = 30; // Massimo contributo per utenti esperti
      } else if (totalPoints >= 3000) {
        contribution = 25; // Utenti avanzati
      } else if (totalPoints >= 2000) {
        contribution = 20; // Utenti intermedi
      } else if (totalPoints >= 1000) {
        contribution = 15; // Utenti principianti avanzati
      } else if (totalPoints >= 500) {
        contribution = 10; // Utenti principianti
      } else if (totalPoints >= 100) {
        contribution = 5;  // Utenti novizi attivi
      } else {
        contribution = (totalPoints / 20).clamp(0, 5); // Scaling lineare per i primi punti
      }

      return contribution;
    } catch (e) {
      print('❌ Errore nel calcolo contributo WellJourney: $e');
      return 0.0;
    }
  }

  /// Calcola bonus per consistenza nell'apprendimento
  double _calculateEducationalConsistencyBonus(WellJourneyController controller) {
    try {
      final userProgress = controller.userProgress;
      if (userProgress == null) return 0.0;

      // Bonus per streak di apprendimento
      final currentStreak = userProgress.currentStreak;
      double streakBonus = 0;

      if (currentStreak >= 30) {
        streakBonus = 10; // Bonus massimo per 30+ giorni consecutivi
      } else if (currentStreak >= 14) {
        streakBonus = 7;  // Bonus per 2+ settimane
      } else if (currentStreak >= 7) {
        streakBonus = 5;  // Bonus per 1+ settimana
      } else if (currentStreak >= 3) {
        streakBonus = 2;  // Bonus minimo per 3+ giorni
      }

      // Bonus per varietà di badge educativi
      final badges = controller.earnedBadges;
      final educationalBadges = badges.where((badge) =>
        badge.contains('knowledge') ||
        badge.contains('pathway') ||
        badge.contains('quiz') ||
        badge.contains('learning')
      ).length;

      double badgeBonus = (educationalBadges * 2.0).clamp(0, 8); // Max 8 punti da badge

      return (streakBonus + badgeBonus).clamp(0, 15); // Max 15 punti bonus totali
    } catch (e) {
      print('❌ Errore nel calcolo bonus consistenza: $e');
      return 0.0;
    }
  }

  /// Calcola il punteggio di consistenza basato sull'attività regolare
  double _calculateConsistencyScore(WellJourneyController controller) {
    try {
      final userProgress = controller.userProgress;
      if (userProgress == null) return 0.0;

      final daysActive = _calculateActiveDays(controller);
      final streakDays = _calculateCurrentStreak(controller);

      // Punteggio base sulla frequenza di attività
      double frequencyScore = (daysActive / 30 * 100).clamp(0, 100); // Ultimi 30 giorni

      // Bonus per streak consecutivi
      double streakBonus = min(streakDays * 2, 30); // Max 30 punti bonus

      // Bonus per completamento regolare di moduli
      double completionBonus = _calculateCompletionConsistency(controller);

      return (frequencyScore + streakBonus + completionBonus).clamp(0, 100);
    } catch (e) {
      print('❌ Errore nel calcolo punteggio consistenza: $e');
      return 0.0;
    }
  }

  /// Calcola il punteggio di applicazione pratica
  double _calculateApplicationScore(WellJourneyController controller) {
    try {
      final userProgress = controller.userProgress;
      if (userProgress == null) return 0.0;

      // Task pratici completati
      double practicalTasksScore = _calculatePracticalTasksScore(controller);

      // Sfide completate
      double challengesScore = _calculateChallengesScore(controller);

      // Applicazione principi Dr. Staffilano
      double principlesScore = _calculatePrinciplesApplicationScore(controller);

      return ((practicalTasksScore + challengesScore + principlesScore) / 3).clamp(0, 100);
    } catch (e) {
      print('❌ Errore nel calcolo punteggio applicazione: $e');
      return 0.0;
    }
  }

  /// Calcola il punteggio di expertise avanzata
  double _calculateExpertiseScore(WellJourneyController controller) {
    try {
      final userProgress = controller.userProgress;
      final badges = controller.earnedBadges;

      // Punteggio basato sui badge ottenuti
      double badgeScore = (badges.length * 10).clamp(0, 60).toDouble(); // Max 60 punti

      // Punteggio basato sui punti totali
      double pointsScore = min((userProgress?.totalPoints ?? 0) / 50, 40); // Max 40 punti

      return (badgeScore + pointsScore).clamp(0, 100);
    } catch (e) {
      print('❌ Errore nel calcolo punteggio expertise: $e');
      return 0.0;
    }
  }

  /// Determina il livello NutriScore
  NutriScoreLevel _getNutriScoreLevel(double score) {
    if (score >= 90) return NutriScoreLevel.esperto;
    if (score >= 75) return NutriScoreLevel.avanzato;
    if (score >= 60) return NutriScoreLevel.intermedio;
    if (score >= 40) return NutriScoreLevel.principiante;
    return NutriScoreLevel.novizio;
  }

  /// Genera raccomandazioni personalizzate
  List<String> _generateRecommendations(double total, double educational, double consistency, double application, double expertise) {
    List<String> recommendations = [];

    if (total < 40) {
      recommendations.add('Inizia con i percorsi base "Alimentazione per il Cuore" per costruire le fondamenta');
      recommendations.add('Dedica 10-15 minuti al giorno all\'apprendimento nutrizionale');
    } else if (total < 60) {
      recommendations.add('Completa almeno un modulo WellJourney™ ogni 2-3 giorni per migliorare la consistenza');
      recommendations.add('Applica i principi appresi con le sfide pratiche giornaliere');
    } else if (total < 75) {
      recommendations.add('Concentrati sui percorsi avanzati per approfondire l\'expertise cardiovascolare');
      recommendations.add('Mantieni la consistenza quotidiana per raggiungere il livello Avanzato');
    } else if (total < 90) {
      recommendations.add('Completa tutti i percorsi WellJourney™ per diventare un esperto');
      recommendations.add('Aiuta altri utenti condividendo la tua esperienza nella community');
    } else {
      recommendations.add('Congratulazioni! Sei un esperto in nutrizione cardiovascolare Dr. Staffilano');
      recommendations.add('Continua a mantenere le tue eccellenti abitudini nutrizionali');
    }

    // Raccomandazioni specifiche per aree deboli
    if (educational < 50) {
      recommendations.add('Priorità: Completa più moduli educativi nei percorsi WellJourney™');
    }
    if (consistency < 50) {
      recommendations.add('Priorità: Migliora la consistenza con sessioni di studio regolari');
    }
    if (application < 50) {
      recommendations.add('Priorità: Partecipa a più sfide pratiche per applicare le conoscenze');
    }

    return recommendations;
  }

  /// Calcola il prossimo traguardo
  NutriScoreMilestone _getNextMilestone(double currentScore) {
    if (currentScore < 40) {
      return NutriScoreMilestone(
        targetScore: 40,
        title: 'Principiante',
        description: 'Completa i percorsi base per raggiungere il livello Principiante',
        pointsNeeded: (40 - currentScore).ceil(),
      );
    } else if (currentScore < 60) {
      return NutriScoreMilestone(
        targetScore: 60,
        title: 'Intermedio',
        description: 'Mantieni la consistenza per raggiungere il livello Intermedio',
        pointsNeeded: (60 - currentScore).ceil(),
      );
    } else if (currentScore < 75) {
      return NutriScoreMilestone(
        targetScore: 75,
        title: 'Avanzato',
        description: 'Approfondisci l\'expertise per raggiungere il livello Avanzato',
        pointsNeeded: (75 - currentScore).ceil(),
      );
    } else if (currentScore < 90) {
      return NutriScoreMilestone(
        targetScore: 90,
        title: 'Esperto',
        description: 'Completa tutti i percorsi per diventare un Esperto',
        pointsNeeded: (90 - currentScore).ceil(),
      );
    } else {
      return NutriScoreMilestone(
        targetScore: 100,
        title: 'Maestro',
        description: 'Mantieni l\'eccellenza in tutti gli aspetti nutrizionali',
        pointsNeeded: (100 - currentScore).ceil(),
      );
    }
  }

  /// Identifica le aree di forza
  List<String> _getStrengthAreas(double educational, double consistency, double application, double expertise) {
    List<String> strengths = [];

    if (educational >= 70) strengths.add('Eccellente conoscenza teorica');
    if (consistency >= 70) strengths.add('Ottima consistenza nell\'apprendimento');
    if (application >= 70) strengths.add('Forte applicazione pratica');
    if (expertise >= 70) strengths.add('Expertise avanzata riconosciuta');

    return strengths;
  }

  /// Identifica le aree di miglioramento
  List<String> _getImprovementAreas(double educational, double consistency, double application, double expertise) {
    List<String> improvements = [];

    if (educational < 50) improvements.add('Conoscenza teorica da approfondire');
    if (consistency < 50) improvements.add('Consistenza nell\'apprendimento da migliorare');
    if (application < 50) improvements.add('Applicazione pratica da sviluppare');
    if (expertise < 50) improvements.add('Expertise da costruire con più esperienza');

    return improvements;
  }

  // Metodi di supporto per calcoli specifici
  int _calculateActiveDays(WellJourneyController controller) {
    // TODO: Implementare calcolo giorni attivi basato sui dati reali
    return 15; // Placeholder
  }

  int _calculateCurrentStreak(WellJourneyController controller) {
    // TODO: Implementare calcolo streak basato sui dati reali
    return 7; // Placeholder
  }

  double _calculateCompletionConsistency(WellJourneyController controller) {
    // TODO: Implementare calcolo consistenza completamenti
    return 20; // Placeholder
  }

  double _calculatePracticalTasksScore(WellJourneyController controller) {
    // TODO: Implementare calcolo task pratici
    return 60; // Placeholder
  }

  double _calculateChallengesScore(WellJourneyController controller) {
    // TODO: Implementare calcolo sfide completate
    return 45; // Placeholder
  }

  double _calculatePrinciplesApplicationScore(WellJourneyController controller) {
    // TODO: Implementare calcolo applicazione principi Dr. Staffilano
    return 55; // Placeholder
  }

  /// Calcolo sicuro del livello
  NutriScoreLevel _calculateLevelSafe(double totalScore) {
    try {
      if (totalScore >= 80) return NutriScoreLevel.esperto;
      if (totalScore >= 65) return NutriScoreLevel.avanzato;
      if (totalScore >= 50) return NutriScoreLevel.intermedio;
      if (totalScore >= 30) return NutriScoreLevel.principiante;
      return NutriScoreLevel.novizio;
    } catch (e) {
      print('⚠️ Errore calcolo livello: $e');
      return NutriScoreLevel.principiante;
    }
  }

  /// Calcola il NutriScore corrente per le sfide avanzate
  Future<double> calculateCurrentNutriScore() async {
    try {
      // Simulazione del calcolo del NutriScore corrente
      // In un'implementazione reale, questo dovrebbe calcolare il punteggio
      // basato sui dati dell'utente, progressi, sfide completate, etc.

      // Per ora restituiamo un valore simulato basato su fattori casuali
      final random = Random();
      final baseScore = 45.0; // Punteggio base
      final variability = random.nextDouble() * 30.0; // Variabilità 0-30

      final currentScore = (baseScore + variability).clamp(0.0, 100.0);

      print('📊 NutriScore corrente calcolato: ${currentScore.toStringAsFixed(1)}');
      return currentScore;
    } catch (e) {
      print('❌ Errore nel calcolo NutriScore corrente: $e');
      return 50.0; // Valore di fallback
    }
  }

  /// Generazione sicura delle raccomandazioni
  List<String> _generateSafeRecommendations(double totalScore, WellJourneyProgress? userProgress) {
    try {
      List<String> recommendations = [];

      if (totalScore < 30) {
        recommendations.addAll([
          'Inizia con i percorsi base per costruire le fondamenta',
          'Dedica 10-15 minuti al giorno all\'apprendimento nutrizionale',
          'Completa almeno un modulo questa settimana',
        ]);
      } else if (totalScore < 50) {
        recommendations.addAll([
          'Mantieni la consistenza nell\'apprendimento',
          'Applica i principi appresi nella vita quotidiana',
          'Partecipa alle sfide settimanali',
        ]);
      } else if (totalScore < 65) {
        recommendations.addAll([
          'Approfondisci i percorsi avanzati',
          'Condividi la tua esperienza con altri utenti',
          'Mantieni la routine quotidiana',
        ]);
      } else {
        recommendations.addAll([
          'Eccellente! Continua così',
          'Aiuta altri utenti nella community',
          'Esplora contenuti avanzati',
        ]);
      }

      return recommendations;
    } catch (e) {
      print('⚠️ Errore generazione raccomandazioni: $e');
      return ['Continua il tuo percorso di apprendimento!'];
    }
  }

  /// Calcolo sicuro del prossimo traguardo
  NutriScoreMilestone _getNextMilestoneSafe(double currentScore) {
    try {
      if (currentScore < 30) {
        return NutriScoreMilestone(
          targetScore: 30,
          title: 'Principiante',
          description: 'Completa i primi moduli',
          pointsNeeded: max(0, (30 - currentScore).ceil()),
        );
      } else if (currentScore < 50) {
        return NutriScoreMilestone(
          targetScore: 50,
          title: 'Intermedio',
          description: 'Mantieni la consistenza',
          pointsNeeded: max(0, (50 - currentScore).ceil()),
        );
      } else if (currentScore < 65) {
        return NutriScoreMilestone(
          targetScore: 65,
          title: 'Avanzato',
          description: 'Approfondisci l\'expertise',
          pointsNeeded: max(0, (65 - currentScore).ceil()),
        );
      } else {
        return NutriScoreMilestone(
          targetScore: 80,
          title: 'Esperto',
          description: 'Raggiungi la maestria',
          pointsNeeded: max(0, (80 - currentScore).ceil()),
        );
      }
    } catch (e) {
      print('⚠️ Errore calcolo milestone: $e');
      return NutriScoreMilestone(
        targetScore: 50,
        title: 'Prossimo Livello',
        description: 'Continua il tuo percorso',
        pointsNeeded: 10,
      );
    }
  }

  /// Calcolo sicuro delle aree di forza
  List<String> _getStrengthAreasSafe(double educational, double consistency, double application, double expertise) {
    try {
      List<String> strengths = [];

      if (educational >= 15) strengths.add('Ottima conoscenza teorica');
      if (consistency >= 15) strengths.add('Eccellente consistenza');
      if (application >= 15) strengths.add('Forte applicazione pratica');
      if (expertise >= 15) strengths.add('Expertise riconosciuta');

      if (strengths.isEmpty) {
        strengths.add('Potenziale di crescita');
      }

      return strengths;
    } catch (e) {
      print('⚠️ Errore calcolo punti di forza: $e');
      return ['Motivazione e impegno'];
    }
  }

  /// Calcolo sicuro delle aree di miglioramento
  List<String> _getImprovementAreasSafe(double educational, double consistency, double application, double expertise) {
    try {
      List<String> improvements = [];

      if (educational < 10) improvements.add('Approfondire la conoscenza teorica');
      if (consistency < 10) improvements.add('Migliorare la consistenza');
      if (application < 10) improvements.add('Aumentare l\'applicazione pratica');
      if (expertise < 10) improvements.add('Sviluppare maggiore expertise');

      if (improvements.isEmpty) {
        improvements.add('Mantenere l\'eccellenza raggiunta');
      }

      return improvements;
    } catch (e) {
      print('⚠️ Errore calcolo aree miglioramento: $e');
      return ['Continuare il percorso di crescita'];
    }
  }
}

/// Risultato del calcolo NutriScore
class NutriScoreResult {
  final double totalScore;
  final double educationalScore;
  final double consistencyScore;
  final double applicationScore;
  final double expertiseScore;
  final NutriScoreLevel level;
  final List<String> recommendations;
  final NutriScoreMilestone nextMilestone;
  final List<String> strengthAreas;
  final List<String> improvementAreas;

  NutriScoreResult({
    required this.totalScore,
    required this.educationalScore,
    required this.consistencyScore,
    required this.applicationScore,
    required this.expertiseScore,
    required this.level,
    required this.recommendations,
    required this.nextMilestone,
    required this.strengthAreas,
    required this.improvementAreas,
  });
}

/// Livelli del NutriScore
enum NutriScoreLevel {
  novizio,
  principiante,
  intermedio,
  avanzato,
  esperto,
}

extension NutriScoreLevelExtension on NutriScoreLevel {
  String get displayName {
    switch (this) {
      case NutriScoreLevel.novizio:
        return 'Novizio';
      case NutriScoreLevel.principiante:
        return 'Principiante';
      case NutriScoreLevel.intermedio:
        return 'Intermedio';
      case NutriScoreLevel.avanzato:
        return 'Avanzato';
      case NutriScoreLevel.esperto:
        return 'Esperto';
    }
  }

  String get description {
    switch (this) {
      case NutriScoreLevel.novizio:
        return 'Inizia il tuo percorso nutrizionale';
      case NutriScoreLevel.principiante:
        return 'Stai costruendo le basi solide';
      case NutriScoreLevel.intermedio:
        return 'Buona conoscenza nutrizionale';
      case NutriScoreLevel.avanzato:
        return 'Expertise nutrizionale consolidata';
      case NutriScoreLevel.esperto:
        return 'Maestria in nutrizione cardiovascolare';
    }
  }
}

/// Traguardo del NutriScore
class NutriScoreMilestone {
  final double targetScore;
  final String title;
  final String description;
  final int pointsNeeded;

  NutriScoreMilestone({
    required this.targetScore,
    required this.title,
    required this.description,
    required this.pointsNeeded,
  });
}
