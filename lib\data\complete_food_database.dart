import '../models/food.dart';

/// Database completo di alimenti con valori nutrizionali esatti verificati
class CompleteFoodDatabase {
  static List<Food> getFruits() {
    return [
      Food(
        id: 'fruit_1',
        name: '<PERSON><PERSON>',
        description: 'Mela fresca con buccia',
        imageUrl: 'https://images.unsplash.com/photo-1570913149827-d2ac84ab3f9a?q=80&w=500',
        calories: 52,
        proteins: 0.3,
        carbs: 14.0,
        fats: 0.2,
        fiber: 2.4,
        sugar: 10.0,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [9, 10, 11, 12, 1, 2, 3],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'fresco', 'stagionale'],
      ),

      Food(
        id: 'fruit_2',
        name: '<PERSON>ana',
        description: 'Banana fresca',
        imageUrl: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?q=80&w=500',
        calories: 89,
        proteins: 1.1,
        carbs: 23.0,
        fats: 0.3,
        fiber: 2.6,
        sugar: 12.0,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'fresco'],
      ),

      Food(
        id: 'fruit_3',
        name: 'Arancia',
        description: 'Arancia fresca',
        imageUrl: 'https://images.unsplash.com/photo-1611080626919-7cf5a9dbab12?q=80&w=500',
        calories: 47,
        proteins: 0.9,
        carbs: 12.0,
        fats: 0.1,
        fiber: 2.4,
        sugar: 9.0,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [11, 12, 1, 2, 3, 4],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'fresco', 'agrumi', 'stagionale'],
      ),

      Food(
        id: 'fruit_4',
        name: 'Pera',
        description: 'Pera fresca',
        imageUrl: 'https://images.unsplash.com/photo-1514756331096-242fdeb70d4a?q=80&w=500',
        calories: 43,
        proteins: 0.4,
        carbs: 11.0,
        fats: 0.1,
        fiber: 3.1,
        sugar: 9.8,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [8, 9, 10, 11, 12, 1, 2],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'fresco', 'stagionale'],
      ),

      Food(
        id: 'fruit_5',
        name: 'Pesca',
        description: 'Pesca fresca',
        imageUrl: 'https://images.unsplash.com/photo-1595743825637-cdafc8ad4173?q=80&w=500',
        calories: 39,
        proteins: 0.9,
        carbs: 10.0,
        fats: 0.3,
        fiber: 1.5,
        sugar: 8.4,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [6, 7, 8, 9],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'fresco', 'stagionale', 'estate'],
      ),

      Food(
        id: 'fruit_6',
        name: 'Albicocca',
        description: 'Albicocca fresca',
        imageUrl: 'https://images.unsplash.com/photo-1560806175-c9e0cabae839?q=80&w=500',
        calories: 48,
        proteins: 1.4,
        carbs: 11.0,
        fats: 0.4,
        fiber: 2.0,
        sugar: 9.0,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [5, 6, 7, 8],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'fresco', 'stagionale', 'estate'],
      ),

      Food(
        id: 'fruit_7',
        name: 'Fragole',
        description: 'Fragole fresche',
        imageUrl: 'https://images.unsplash.com/photo-1518635017498-87f514b751ba?q=80&w=500',
        calories: 32,
        proteins: 0.7,
        carbs: 7.7,
        fats: 0.3,
        fiber: 2.0,
        sugar: 4.9,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [4, 5, 6],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'fresco', 'stagionale', 'primavera'],
      ),

      Food(
        id: 'fruit_8',
        name: 'Melone',
        description: 'Melone fresco',
        imageUrl: 'https://images.unsplash.com/photo-1571575173700-afb9492e6a50?q=80&w=500',
        calories: 34,
        proteins: 0.8,
        carbs: 8.0,
        fats: 0.2,
        fiber: 0.9,
        sugar: 7.9,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [6, 7, 8, 9],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'fresco', 'stagionale', 'estate'],
      ),

      Food(
        id: 'fruit_9',
        name: 'Anguria',
        description: 'Anguria fresca',
        imageUrl: 'https://images.unsplash.com/photo-1589984662646-e7b2e4962f18?q=80&w=500',
        calories: 30,
        proteins: 0.6,
        carbs: 8.0,
        fats: 0.2,
        fiber: 0.4,
        sugar: 6.0,
        suitableForMeals: const [MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [6, 7, 8],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'fresco', 'stagionale', 'estate', 'idratante'],
      ),

      Food(
        id: 'fruit_10',
        name: 'Uva',
        description: 'Uva fresca',
        imageUrl: 'https://images.unsplash.com/photo-1537640538966-79f369143f8f?q=80&w=500',
        calories: 69,
        proteins: 0.7,
        carbs: 18.0,
        fats: 0.2,
        fiber: 0.9,
        sugar: 15.0,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [8, 9, 10, 11],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'fresco', 'stagionale', 'autunno'],
      ),

      Food(
        id: 'fruit_11',
        name: 'Kiwi',
        description: 'Kiwi fresco',
        imageUrl: 'https://images.unsplash.com/photo-1618897996318-5a901fa6ca71?q=80&w=500',
        calories: 61,
        proteins: 1.1,
        carbs: 15.0,
        fats: 0.5,
        fiber: 3.0,
        sugar: 9.0,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [11, 12, 1, 2, 3, 4],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'fresco', 'stagionale', 'inverno', 'vitamina C'],
      ),

      Food(
        id: 'fruit_12',
        name: 'Mirtilli',
        description: 'Mirtilli freschi',
        imageUrl: 'https://images.unsplash.com/photo-1498557850523-fd3d118b962e?q=80&w=500',
        calories: 57,
        proteins: 0.7,
        carbs: 14.0,
        fats: 0.3,
        fiber: 2.4,
        sugar: 10.0,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [6, 7, 8, 9],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'fresco', 'stagionale', 'estate', 'antiossidante', 'bacche'],
      ),
    ];
  }

  static List<Food> getVegetables() {
    return [
      Food(
        id: 'veg_1',
        name: 'Carota',
        description: 'Carota fresca',
        imageUrl: 'https://images.unsplash.com/photo-1598170845058-32b9d6a5da37?q=80&w=500',
        calories: 41,
        proteins: 0.9,
        carbs: 10.0,
        fats: 0.2,
        fiber: 2.8,
        sugar: 4.7,
        suitableForMeals: const [MealType.lunch, MealType.dinner, MealType.snack],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'fresco', 'radice'],
      ),

      Food(
        id: 'veg_2',
        name: 'Patata',
        description: 'Patata fresca',
        imageUrl: 'https://images.unsplash.com/photo-1518977676601-b53f82aba655?q=80&w=500',
        calories: 77,
        proteins: 2.0,
        carbs: 17.0,
        fats: 0.1,
        fiber: 2.2,
        sugar: 0.8,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'fresco', 'tubero', 'amido'],
      ),

      Food(
        id: 'veg_3',
        name: 'Pomodoro',
        description: 'Pomodoro fresco',
        imageUrl: 'https://images.unsplash.com/photo-1582284540020-8acbe03f4924?q=80&w=500',
        calories: 18,
        proteins: 0.9,
        carbs: 3.9,
        fats: 0.2,
        fiber: 1.2,
        sugar: 2.6,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [6, 7, 8, 9],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'fresco', 'stagionale', 'estate'],
      ),

      Food(
        id: 'veg_4',
        name: 'Zucchina',
        description: 'Zucchina fresca',
        imageUrl: 'https://images.unsplash.com/photo-1583687355032-89b902b7335f?q=80&w=500',
        calories: 17,
        proteins: 1.2,
        carbs: 3.1,
        fats: 0.3,
        fiber: 1.0,
        sugar: 2.5,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [5, 6, 7, 8, 9],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'fresco', 'stagionale', 'estate'],
      ),

      Food(
        id: 'veg_5',
        name: 'Spinaci',
        description: 'Spinaci freschi crudi',
        imageUrl: 'https://images.unsplash.com/photo-1576064535185-c2526884001c?q=80&w=500',
        calories: 23,
        proteins: 2.9,
        carbs: 1.1,
        fats: 0.4,
        fiber: 2.2,
        sugar: 0.4,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [9, 10, 11, 12, 1, 2, 3, 4, 5],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'fresco', 'foglia', 'stagionale'],
      ),

      Food(
        id: 'veg_6',
        name: 'Melanzana',
        description: 'Melanzana fresca',
        imageUrl: 'https://images.unsplash.com/photo-1613499563689-8f8b2b733a6f?q=80&w=500',
        calories: 25,
        proteins: 1.0,
        carbs: 6.0,
        fats: 0.2,
        fiber: 3.0,
        sugar: 3.2,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [6, 7, 8, 9],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'fresco', 'stagionale', 'estate'],
      ),

      Food(
        id: 'veg_7',
        name: 'Peperone',
        description: 'Peperone fresco',
        imageUrl: 'https://images.unsplash.com/photo-1563565375-f3fdfdbefa83?q=80&w=500',
        calories: 31,
        proteins: 1.0,
        carbs: 6.0,
        fats: 0.3,
        fiber: 2.1,
        sugar: 4.2,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [6, 7, 8, 9, 10],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'fresco', 'stagionale', 'estate', 'vitamina C'],
      ),

      Food(
        id: 'veg_8',
        name: 'Lattuga',
        description: 'Lattuga fresca',
        imageUrl: 'https://images.unsplash.com/photo-1622206151226-18ca2c9ab4a1?q=80&w=500',
        calories: 15,
        proteins: 1.4,
        carbs: 2.9,
        fats: 0.2,
        fiber: 1.3,
        sugar: 0.8,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [3, 4, 5, 6, 7, 8, 9, 10],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'fresco', 'foglia', 'insalata', 'stagionale'],
      ),

      Food(
        id: 'veg_9',
        name: 'Broccoli',
        description: 'Broccoli freschi',
        imageUrl: 'https://images.unsplash.com/photo-1584270354949-c26b0d5b4a0c?q=80&w=500',
        calories: 34,
        proteins: 2.8,
        carbs: 7.0,
        fats: 0.4,
        fiber: 2.6,
        sugar: 1.7,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [10, 11, 12, 1, 2, 3],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'fresco', 'crucifere', 'stagionale', 'inverno'],
      ),

      Food(
        id: 'veg_10',
        name: 'Zucca',
        description: 'Zucca fresca',
        imageUrl: 'https://images.unsplash.com/photo-1570586437263-ab629fccc818?q=80&w=500',
        calories: 26,
        proteins: 1.0,
        carbs: 6.5,
        fats: 0.1,
        fiber: 0.5,
        sugar: 2.8,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [9, 10, 11, 12, 1],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'fresco', 'stagionale', 'autunno', 'inverno'],
      ),

      Food(
        id: 'veg_11',
        name: 'Piselli freschi',
        description: 'Piselli freschi',
        imageUrl: 'https://images.unsplash.com/photo-1615485500834-bc10199bc727?q=80&w=500',
        calories: 81,
        proteins: 5.4,
        carbs: 14.0,
        fats: 0.4,
        fiber: 5.1,
        sugar: 5.7,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable, FoodCategory.protein],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [4, 5, 6],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'fresco', 'legumi', 'stagionale', 'primavera', 'proteico'],
      ),
    ];
  }

  static List<Food> getGrains() {
    return [
      Food(
        id: 'grain_1',
        name: 'Pane bianco',
        description: 'Pane bianco comune',
        imageUrl: 'https://images.unsplash.com/photo-1549931319-a545dcf3bc7b?q=80&w=500',
        calories: 265,
        proteins: 8.8,
        carbs: 49.0,
        fats: 3.2,
        fiber: 2.7,
        sugar: 5.0,
        suitableForMeals: const [MealType.breakfast, MealType.lunch, MealType.dinner, MealType.snack],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        allergens: const ['glutine'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['cereale', 'pane', 'base'],
      ),

      Food(
        id: 'grain_2',
        name: 'Pane integrale',
        description: 'Pane integrale',
        imageUrl: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?q=80&w=500',
        calories: 247,
        proteins: 8.9,
        carbs: 41.0,
        fats: 3.3,
        fiber: 6.9,
        sugar: 4.3,
        suitableForMeals: const [MealType.breakfast, MealType.lunch, MealType.dinner, MealType.snack],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        allergens: const ['glutine'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['cereale', 'pane', 'integrale', 'base', 'fibra'],
      ),

      Food(
        id: 'grain_3',
        name: 'Pasta di semola cruda',
        description: 'Pasta di semola di grano duro cruda',
        imageUrl: 'https://images.unsplash.com/photo-1551462147-ff29053bfc14?q=80&w=500',
        calories: 353,
        proteins: 12.0,
        carbs: 73.0,
        fats: 1.5,
        fiber: 3.0,
        sugar: 2.7,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        allergens: const ['glutine'],
        servingSize: '100g cruda',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        rawToCookedFactor: 2.5,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['cereale', 'pasta', 'base'],
      ),

      Food(
        id: 'grain_4',
        name: 'Pasta integrale cruda',
        description: 'Pasta integrale di grano duro cruda',
        imageUrl: 'https://images.unsplash.com/photo-1612966948332-fc611bd71e0a?q=80&w=500',
        calories: 324,
        proteins: 12.0,
        carbs: 64.0,
        fats: 2.5,
        fiber: 8.0,
        sugar: 3.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        allergens: const ['glutine'],
        servingSize: '100g cruda',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        rawToCookedFactor: 2.5,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['cereale', 'pasta', 'integrale', 'base', 'fibra'],
      ),

      Food(
        id: 'grain_5',
        name: 'Riso bianco crudo',
        description: 'Riso bianco crudo',
        imageUrl: 'https://images.unsplash.com/photo-1536304993881-ff6e9eefa2a6?q=80&w=500',
        calories: 362,
        proteins: 7.0,
        carbs: 80.0,
        fats: 0.6,
        fiber: 1.3,
        sugar: 0.1,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g crudo',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        rawToCookedFactor: 3.0,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['cereale', 'riso', 'base'],
      ),

      Food(
        id: 'grain_6',
        name: 'Riso integrale crudo',
        description: 'Riso integrale crudo',
        imageUrl: 'https://images.unsplash.com/photo-1595424265370-5778640e25b3?q=80&w=500',
        calories: 337,
        proteins: 7.5,
        carbs: 74.0,
        fats: 2.2,
        fiber: 3.8,
        sugar: 0.9,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g crudo',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        rawToCookedFactor: 3.0,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['cereale', 'riso', 'integrale', 'base', 'fibra'],
      ),

      Food(
        id: 'grain_7',
        name: 'Riso basmati crudo',
        description: 'Riso basmati crudo',
        imageUrl: 'https://images.unsplash.com/photo-1586201375761-83865001e8ac?q=80&w=500',
        calories: 365,
        proteins: 8.1,
        carbs: 77.0,
        fats: 0.6,
        fiber: 1.3,
        sugar: 0.1,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g crudo',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        rawToCookedFactor: 3.0,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['cereale', 'riso', 'basmati', 'base'],
      ),

      Food(
        id: 'grain_8',
        name: 'Farro crudo',
        description: 'Farro crudo',
        imageUrl: 'https://images.unsplash.com/photo-1622621746668-59fb299bc4d7?q=80&w=500',
        calories: 335,
        proteins: 15.0,
        carbs: 67.0,
        fats: 2.5,
        fiber: 6.7,
        sugar: 0.7,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        allergens: const ['glutine'],
        servingSize: '100g crudo',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        rawToCookedFactor: 2.5,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['cereale', 'farro', 'antico', 'base', 'fibra'],
        italianRegions: const [ItalianRegion.toscana, ItalianRegion.umbria, ItalianRegion.lazio],
        isTraditionalItalian: true,
      ),

      Food(
        id: 'grain_9',
        name: 'Orzo crudo',
        description: 'Orzo crudo',
        imageUrl: 'https://images.unsplash.com/photo-1615485290382-441e4d049cb5?q=80&w=500',
        calories: 354,
        proteins: 10.0,
        carbs: 73.0,
        fats: 2.3,
        fiber: 17.3,
        sugar: 0.8,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        allergens: const ['glutine'],
        servingSize: '100g crudo',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        rawToCookedFactor: 3.0,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['cereale', 'orzo', 'antico', 'base', 'fibra'],
      ),
    ];
  }

  static List<Food> getProteins() {
    return [
      // RIMOSSO: Petto di pollo crudo - SOSTITUITO CON VERSIONE SICURA COTTA
      Food(
        id: 'protein_1_safe',
        name: 'Petto di pollo alla griglia',
        description: 'Petto di pollo grigliato, senza pelle, cotto',
        imageUrl: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?q=80&w=500',
        calories: 165,
        proteins: 31.0,
        carbs: 0.0,
        fats: 3.6,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['carne', 'pollo', 'griglia', 'sicuro', 'proteico'],
      ),

      Food(
        id: 'protein_2_safe',
        name: 'Tacchino arrosto',
        description: 'Petto di tacchino arrosto, senza pelle, cotto',
        imageUrl: 'https://images.unsplash.com/photo-1602470520998-f4a52199a3d6?q=80&w=500',
        calories: 135,
        proteins: 30.0,
        carbs: 0.0,
        fats: 1.2,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['carne', 'tacchino', 'arrosto', 'sicuro', 'proteico'],
      ),

      Food(
        id: 'protein_3_safe',
        name: 'Manzo ai ferri',
        description: 'Bistecca di manzo ai ferri, magra, cotta',
        imageUrl: 'https://images.unsplash.com/photo-1588168333986-5078d3ae3976?q=80&w=500',
        calories: 158,
        proteins: 26.0,
        carbs: 0.0,
        fats: 5.4,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['carne', 'manzo', 'ferri', 'sicuro', 'proteico'],
      ),

      Food(
        id: 'protein_4_safe',
        name: 'Scaloppine di vitello',
        description: 'Scaloppine di vitello ai ferri, cotte',
        imageUrl: 'https://images.unsplash.com/photo-1615937657715-bc7b4b7962c1?q=80&w=500',
        calories: 172,
        proteins: 24.0,
        carbs: 2.0,
        fats: 7.0,
        fiber: 0.0,
        sugar: 1.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['carne', 'vitello', 'scaloppine', 'sicuro', 'proteico'],
      ),

      Food(
        id: 'protein_5_safe',
        name: 'Filetto di maiale alla griglia',
        description: 'Filetto di maiale grigliato, cotto',
        imageUrl: 'https://images.unsplash.com/photo-1602470520998-f4a52199a3d6?q=80&w=500',
        calories: 190,
        proteins: 26.0,
        carbs: 0.0,
        fats: 8.5,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['carne', 'maiale', 'griglia', 'sicuro', 'proteico'],
      ),

      Food(
        id: 'protein_6_safe',
        name: 'Uova sode',
        description: 'Uova di gallina sode, completamente cotte',
        imageUrl: 'https://images.unsplash.com/photo-1607690424560-35d967d6ad7a?q=80&w=500',
        calories: 155,
        proteins: 12.6,
        carbs: 1.1,
        fats: 10.6,
        fiber: 0.0,
        sugar: 1.1,
        suitableForMeals: const [MealType.breakfast, MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isVegetarian: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['uova'],
        servingSize: '100g (circa 2 uova)',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['uova', 'sode', 'sicuro', 'proteico', 'colazione'],
      ),

      Food(
        id: 'protein_7_safe',
        name: 'Albumi cotti',
        description: 'Albumi d\'uovo cotti (frittata di albumi)',
        imageUrl: 'https://images.unsplash.com/photo-1607690424560-35d967d6ad7a?q=80&w=500',
        calories: 52,
        proteins: 11.0,
        carbs: 0.7,
        fats: 0.2,
        fiber: 0.0,
        sugar: 0.7,
        suitableForMeals: const [MealType.breakfast, MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isVegetarian: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['uova'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['uova', 'albumi', 'cotti', 'sicuro', 'proteico'],
      ),

      Food(
        id: 'protein_8',
        name: 'Ceci secchi',
        description: 'Ceci secchi crudi',
        imageUrl: 'https://images.unsplash.com/photo-1515543904379-3d757afe72e4?q=80&w=500',
        calories: 364,
        proteins: 19.0,
        carbs: 61.0,
        fats: 6.0,
        fiber: 17.0,
        sugar: 11.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein, FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g secchi',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        rawToCookedFactor: 2.5,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['legumi', 'ceci', 'base', 'proteico', 'vegetale', 'vegano'],
      ),

      Food(
        id: 'protein_9',
        name: 'Lenticchie secche',
        description: 'Lenticchie secche crude',
        imageUrl: 'https://images.unsplash.com/photo-1615485290382-441e4d049cb5?q=80&w=500',
        calories: 353,
        proteins: 25.0,
        carbs: 60.0,
        fats: 1.1,
        fiber: 31.0,
        sugar: 2.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein, FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g secche',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        rawToCookedFactor: 2.5,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['legumi', 'lenticchie', 'base', 'proteico', 'vegetale', 'vegano'],
      ),

      Food(
        id: 'protein_10',
        name: 'Fagioli borlotti secchi',
        description: 'Fagioli borlotti secchi crudi',
        imageUrl: 'https://images.unsplash.com/photo-1563636619-e9143da7973b?q=80&w=500',
        calories: 335,
        proteins: 23.0,
        carbs: 54.0,
        fats: 1.6,
        fiber: 17.0,
        sugar: 2.2,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein, FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g secchi',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        rawToCookedFactor: 2.5,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['legumi', 'fagioli', 'base', 'proteico', 'vegetale', 'vegano'],
        italianRegions: const [ItalianRegion.toscana, ItalianRegion.emiliaRomagna],
        isTraditionalItalian: true,
      ),

      Food(
        id: 'protein_11',
        name: 'Soia secca',
        description: 'Semi di soia secchi',
        imageUrl: 'https://images.unsplash.com/photo-1612257999756-61d09b9a4de4?q=80&w=500',
        calories: 416,
        proteins: 36.0,
        carbs: 30.0,
        fats: 20.0,
        fiber: 9.0,
        sugar: 7.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein, FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['soia'],
        servingSize: '100g secchi',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        rawToCookedFactor: 2.5,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['legumi', 'soia', 'base', 'proteico', 'vegetale', 'vegano'],
      ),
    ];
  }

  static List<Food> getDairy() {
    return [
      Food(
        id: 'dairy_1',
        name: 'Latte vaccino intero',
        description: 'Latte vaccino intero (3,6% grassi)',
        imageUrl: 'https://images.unsplash.com/photo-1550583724-b2692b85b150?q=80&w=500',
        calories: 64,
        proteins: 3.3,
        carbs: 4.8,
        fats: 3.6,
        fiber: 0.0,
        sugar: 4.8,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.dairy, FoodCategory.beverage],
        isVegetarian: true,
        allergens: const ['latticini'],
        servingSize: '100ml',
        servingSizeGrams: 103,
        foodState: FoodState.prepared,
        volumeToWeightFactor: 1.03,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['latte', 'intero', 'base', 'colazione'],
      ),

      Food(
        id: 'dairy_2',
        name: 'Latte parzialmente scremato',
        description: 'Latte vaccino parzialmente scremato (1,5% grassi)',
        imageUrl: 'https://images.unsplash.com/photo-1550583724-b2692b85b150?q=80&w=500',
        calories: 46,
        proteins: 3.3,
        carbs: 4.8,
        fats: 1.5,
        fiber: 0.0,
        sugar: 4.8,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.dairy, FoodCategory.beverage],
        isVegetarian: true,
        allergens: const ['latticini'],
        servingSize: '100ml',
        servingSizeGrams: 103,
        foodState: FoodState.prepared,
        volumeToWeightFactor: 1.03,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['latte', 'parzialmente scremato', 'base', 'colazione'],
      ),

      Food(
        id: 'dairy_3',
        name: 'Latte scremato',
        description: 'Latte vaccino scremato (0,1% grassi)',
        imageUrl: 'https://images.unsplash.com/photo-1550583724-b2692b85b150?q=80&w=500',
        calories: 35,
        proteins: 3.4,
        carbs: 5.0,
        fats: 0.1,
        fiber: 0.0,
        sugar: 5.0,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.dairy, FoodCategory.beverage],
        isVegetarian: true,
        allergens: const ['latticini'],
        servingSize: '100ml',
        servingSizeGrams: 103,
        foodState: FoodState.prepared,
        volumeToWeightFactor: 1.03,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['latte', 'scremato', 'base', 'colazione', 'magro'],
      ),

      Food(
        id: 'dairy_4',
        name: 'Yogurt bianco intero',
        description: 'Yogurt bianco intero',
        imageUrl: 'https://images.unsplash.com/photo-1488477181946-6428a0291777?q=80&w=500',
        calories: 62,
        proteins: 3.5,
        carbs: 4.7,
        fats: 3.3,
        fiber: 0.0,
        sugar: 4.7,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.dairy],
        isVegetarian: true,
        allergens: const ['latticini'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['yogurt', 'intero', 'base', 'colazione'],
      ),

      Food(
        id: 'dairy_5',
        name: 'Yogurt magro',
        description: 'Yogurt bianco magro (0,1% grassi)',
        imageUrl: 'https://images.unsplash.com/photo-1488477181946-6428a0291777?q=80&w=500',
        calories: 36,
        proteins: 3.8,
        carbs: 4.9,
        fats: 0.1,
        fiber: 0.0,
        sugar: 4.9,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.dairy],
        isVegetarian: true,
        allergens: const ['latticini'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['yogurt', 'magro', 'base', 'colazione', 'dietetico'],
      ),

      Food(
        id: 'dairy_6',
        name: 'Ricotta vaccina',
        description: 'Ricotta fresca vaccina',
        imageUrl: 'https://images.unsplash.com/photo-1551504734-5ee1c4a1479b?q=80&w=500',
        calories: 174,
        proteins: 11.0,
        carbs: 3.5,
        fats: 13.0,
        fiber: 0.0,
        sugar: 3.5,
        suitableForMeals: const [MealType.breakfast, MealType.lunch, MealType.dinner, MealType.snack],
        categories: const [FoodCategory.dairy],
        isVegetarian: true,
        allergens: const ['latticini'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['formaggio', 'fresco', 'ricotta', 'italiano'],
        italianRegions: const [],
        isTraditionalItalian: true,
      ),

      Food(
        id: 'dairy_7',
        name: 'Mozzarella vaccina',
        description: 'Mozzarella fresca vaccina',
        imageUrl: 'https://images.unsplash.com/photo-1551504734-5ee1c4a1479b?q=80&w=500',
        calories: 253,
        proteins: 18.0,
        carbs: 2.5,
        fats: 19.0,
        fiber: 0.0,
        sugar: 2.5,
        suitableForMeals: const [MealType.lunch, MealType.dinner, MealType.snack],
        categories: const [FoodCategory.dairy],
        isVegetarian: true,
        allergens: const ['latticini'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['formaggio', 'fresco', 'mozzarella', 'italiano'],
        italianRegions: const [ItalianRegion.campania],
        isTraditionalItalian: true,
      ),

      Food(
        id: 'dairy_8',
        name: 'Parmigiano Reggiano',
        description: 'Parmigiano Reggiano DOP stagionato',
        imageUrl: 'https://images.unsplash.com/photo-1528747045269-390fe33c19f2?q=80&w=500',
        calories: 402,
        proteins: 33.0,
        carbs: 0.0,
        fats: 30.0,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner, MealType.snack],
        categories: const [FoodCategory.dairy],
        isVegetarian: true,
        allergens: const ['latticini'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['formaggio', 'stagionato', 'dop', 'italiano'],
        italianRegions: const [ItalianRegion.emiliaRomagna, ItalianRegion.lombardia],
        isTraditionalItalian: true,
      ),

      Food(
        id: 'dairy_9',
        name: 'Grana Padano',
        description: 'Grana Padano DOP stagionato',
        imageUrl: 'https://images.unsplash.com/photo-1528747045269-390fe33c19f2?q=80&w=500',
        calories: 398,
        proteins: 33.0,
        carbs: 0.0,
        fats: 29.0,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner, MealType.snack],
        categories: const [FoodCategory.dairy],
        isVegetarian: true,
        allergens: const ['latticini'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['formaggio', 'stagionato', 'dop', 'italiano'],
        italianRegions: const [ItalianRegion.lombardia, ItalianRegion.piemonte, ItalianRegion.veneto],
        isTraditionalItalian: true,
      ),

      Food(
        id: 'dairy_10',
        name: 'Fiocchi di latte',
        description: 'Fiocchi di latte freschi',
        imageUrl: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?q=80&w=500',
        calories: 98,
        proteins: 11.0,
        carbs: 3.4,
        fats: 4.3,
        fiber: 0.0,
        sugar: 3.4,
        suitableForMeals: const [MealType.breakfast, MealType.lunch, MealType.dinner, MealType.snack],
        categories: const [FoodCategory.dairy],
        isVegetarian: true,
        allergens: const ['latticini'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['formaggio', 'fresco', 'fiocchi', 'proteico', 'magro'],
      ),
    ];
  }

  static List<Food> getFats() {
    return [
      Food(
        id: 'fat_1',
        name: 'Olio extravergine di oliva',
        description: 'Olio extravergine di oliva italiano',
        imageUrl: 'https://images.unsplash.com/photo-1474979266404-7eaacbcd87c5?q=80&w=500',
        calories: 899,
        proteins: 0.0,
        carbs: 0.0,
        fats: 99.9,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.fat],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g (circa 110ml)',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        volumeToWeightFactor: 0.91, // densità dell'olio
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['olio', 'extravergine', 'oliva', 'condimento', 'italiano'],
        italianRegions: const [],
        isTraditionalItalian: true,
      ),

      Food(
        id: 'fat_2',
        name: 'Olio di semi vari',
        description: 'Olio di semi vari (girasole, mais, ecc.)',
        imageUrl: 'https://images.unsplash.com/photo-1616484880726-ab38a7de7c5b?q=80&w=500',
        calories: 884,
        proteins: 0.0,
        carbs: 0.0,
        fats: 99.9,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.fat],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g (circa 110ml)',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        volumeToWeightFactor: 0.92, // densità dell'olio
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['olio', 'semi', 'condimento'],
      ),

      Food(
        id: 'fat_3',
        name: 'Burro',
        description: 'Burro vaccino',
        imageUrl: 'https://images.unsplash.com/photo-1589985270958-a664865d0800?q=80&w=500',
        calories: 758,
        proteins: 0.7,
        carbs: 0.7,
        fats: 82.0,
        fiber: 0.0,
        sugar: 0.7,
        suitableForMeals: const [MealType.breakfast, MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.fat, FoodCategory.dairy],
        isVegetarian: true,
        allergens: const ['latticini'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['burro', 'grasso', 'condimento', 'latticini'],
      ),
    ];
  }

  static List<Food> getSweets() {
    return [
      Food(
        id: 'sweet_1',
        name: 'Cioccolato fondente',
        description: 'Cioccolato fondente (min. 70% cacao)',
        imageUrl: 'https://images.unsplash.com/photo-1606312619070-d48b4c652a52?q=80&w=500',
        calories: 546,
        proteins: 4.9,
        carbs: 61.0,
        fats: 31.0,
        fiber: 7.0,
        sugar: 48.0,
        suitableForMeals: const [MealType.snack],
        categories: const [FoodCategory.sweet],
        isVegetarian: true,
        allergens: const ['soia'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['cioccolato', 'fondente', 'dolce', 'snack'],
      ),

      Food(
        id: 'sweet_2',
        name: 'Miele',
        description: 'Miele di fiori',
        imageUrl: 'https://images.unsplash.com/photo-1587049352851-8d4e89133924?q=80&w=500',
        calories: 304,
        proteins: 0.3,
        carbs: 82.0,
        fats: 0.0,
        fiber: 0.0,
        sugar: 82.0,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.sweet],
        isVegetarian: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['miele', 'dolcificante', 'naturale', 'dolce'],
      ),
    ];
  }

  static List<Food> getAllFoods() {
    return [
      ...getFruits(),
      ...getVegetables(),
      ...getGrains(),
      ...getProteins(),
      ...getDairy(),
      ...getFats(),
      ...getSweets(),
    ];
  }
}
