import 'package:flutter/material.dart';
import '../models/food.dart';
import '../services/food_validation_service.dart';

/// Widget per la validazione di un alimento
class FoodValidationForm extends StatefulWidget {
  final Food food;
  final FoodValidationService validationService;
  final Function(ValidationStatus, String) onValidate;

  const FoodValidationForm({
    super.key,
    required this.food,
    required this.validationService,
    required this.onValidate,
  });

  @override
  State<FoodValidationForm> createState() => _FoodValidationFormState();
}

class _FoodValidationFormState extends State<FoodValidationForm> {
  late Map<String, dynamic> _validationReport;
  final TextEditingController _validatorController = TextEditingController();
  ValidationStatus _selectedStatus = ValidationStatus.validated;
  
  @override
  void initState() {
    super.initState();
    _validationReport = widget.validationService.generateFoodValidationReport(widget.food);
    _validatorController.text = 'Admin'; // Valore predefinito
  }
  
  @override
  void dispose() {
    _validatorController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Validazione: ${widget.food.name}'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Informazioni sull'alimento
            _buildFoodInfoSection(),
            
            const Divider(),
            
            // Rapporto di validazione
            _buildValidationReportSection(),
            
            const Divider(),
            
            // Form di validazione
            _buildValidationFormSection(),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Annulla'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_validatorController.text.isEmpty) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Inserisci il nome del validatore'),
                  backgroundColor: Colors.red,
                ),
              );
              return;
            }
            
            widget.onValidate(_selectedStatus, _validatorController.text);
          },
          child: const Text('Conferma'),
        ),
      ],
    );
  }
  
  Widget _buildFoodInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Informazioni Alimento',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Text('Nome: ${widget.food.name}'),
        Text('Descrizione: ${widget.food.description}'),
        Text('Calorie: ${widget.food.calories} kcal'),
        Text('Proteine: ${widget.food.proteins.toStringAsFixed(1)}g'),
        Text('Carboidrati: ${widget.food.carbs.toStringAsFixed(1)}g'),
        Text('Grassi: ${widget.food.fats.toStringAsFixed(1)}g'),
        Text('Fibre: ${widget.food.fiber.toStringAsFixed(1)}g'),
        Text('Zuccheri: ${widget.food.sugar.toStringAsFixed(1)}g'),
        Text('Fonte: ${widget.food.dataSource.toString().split('.').last}'),
        Text('ID Fonte: ${widget.food.sourceId}'),
        Text('Descrizione Fonte: ${widget.food.sourceDescription}'),
        Text('Marca: ${widget.food.brandName}'),
        Text('Stato Validazione: ${widget.food.validationStatus.toString().split('.').last}'),
        if (widget.food.lastValidatedAt.millisecondsSinceEpoch > 0)
          Text('Ultima Validazione: ${widget.food.lastValidatedAt.toString().split('.').first}'),
        if (widget.food.validatedBy.isNotEmpty)
          Text('Validato da: ${widget.food.validatedBy}'),
      ],
    );
  }
  
  Widget _buildValidationReportSection() {
    final bool isPlausible = _validationReport['hasPlausibleNutrients'] as bool;
    final bool hasReliableSource = _validationReport['hasReliableSource'] as bool;
    final bool isComplete = _validationReport['hasCompleteData'] as bool;
    final bool isReadyForValidation = _validationReport['isReadyForValidation'] as bool;
    final List<String> issues = List<String>.from(_validationReport['issues']);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Rapporto di Validazione',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        _buildValidationItem('Valori nutrizionali plausibili', isPlausible),
        _buildValidationItem('Fonte affidabile', hasReliableSource),
        _buildValidationItem('Dati completi', isComplete),
        _buildValidationItem('Pronto per la validazione', isReadyForValidation),
        
        if (issues.isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            'Problemi rilevati:',
            style: Theme.of(context).textTheme.titleSmall,
          ),
          ...issues.map((issue) => Padding(
            padding: const EdgeInsets.only(left: 16.0, top: 4.0),
            child: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.red, size: 16),
                const SizedBox(width: 8),
                Expanded(child: Text(issue)),
              ],
            ),
          )),
        ],
      ],
    );
  }
  
  Widget _buildValidationItem(String label, bool value) {
    return Row(
      children: [
        Icon(
          value ? Icons.check_circle : Icons.cancel,
          color: value ? Colors.green : Colors.red,
          size: 16,
        ),
        const SizedBox(width: 8),
        Text(label),
      ],
    );
  }
  
  Widget _buildValidationFormSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Validazione',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<ValidationStatus>(
          value: _selectedStatus,
          decoration: const InputDecoration(
            labelText: 'Stato di validazione',
            border: OutlineInputBorder(),
          ),
          items: [
            DropdownMenuItem(
              value: ValidationStatus.validated,
              child: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.green),
                  const SizedBox(width: 8),
                  Text(ValidationStatus.validated.toString().split('.').last),
                ],
              ),
            ),
            DropdownMenuItem(
              value: ValidationStatus.pending,
              child: Row(
                children: [
                  const Icon(Icons.hourglass_empty, color: Colors.orange),
                  const SizedBox(width: 8),
                  Text(ValidationStatus.pending.toString().split('.').last),
                ],
              ),
            ),
            DropdownMenuItem(
              value: ValidationStatus.rejected,
              child: Row(
                children: [
                  const Icon(Icons.cancel, color: Colors.red),
                  const SizedBox(width: 8),
                  Text(ValidationStatus.rejected.toString().split('.').last),
                ],
              ),
            ),
          ],
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedStatus = value;
              });
            }
          },
        ),
        const SizedBox(height: 16),
        TextField(
          controller: _validatorController,
          decoration: const InputDecoration(
            labelText: 'Nome validatore',
            border: OutlineInputBorder(),
          ),
        ),
      ],
    );
  }
}
