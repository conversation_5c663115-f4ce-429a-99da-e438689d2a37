import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/welljourney_models.dart';
import '../theme/dr_staffilano_theme.dart';

/// Widget premium per la visualizzazione delle sfide Dr. Staffilano
class PremiumChallengeCard extends StatefulWidget {
  final AdvancedChallenge challenge;
  final ChallengeProgress? progress;
  final bool isActive;
  final bool isDarkMode;
  final VoidCallback? onTap;
  final VoidCallback? onAction;

  const PremiumChallengeCard({
    Key? key,
    required this.challenge,
    this.progress,
    required this.isActive,
    this.isDarkMode = false,
    this.onTap,
    this.onAction,
  }) : super(key: key);

  @override
  State<PremiumChallengeCard> createState() => _PremiumChallengeCardState();
}

class _PremiumChallengeCardState extends State<PremiumChallengeCard>
    with TickerProviderStateMixin {
  
  late AnimationController _hoverController;
  late AnimationController _progressController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _progressAnimation;
  
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeInOut,
    ));
    
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.progress?.currentProgress ?? 0.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeOutCubic,
    ));

    // Avvia l'animazione del progresso
    if (widget.isActive && widget.progress != null) {
      _progressController.forward();
    }
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: widget.challenge.category.primaryColor.withOpacity(0.2),
                  blurRadius: _isHovered ? 20 : 12,
                  offset: const Offset(0, 4),
                  spreadRadius: _isHovered ? 2 : 0,
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(20),
                onTap: () {
                  HapticFeedback.lightImpact();
                  widget.onTap?.call();
                },
                onHover: (hovering) {
                  setState(() {
                    _isHovered = hovering;
                  });
                  if (hovering) {
                    _hoverController.forward();
                  } else {
                    _hoverController.reverse();
                  }
                },
                child: Container(
                  decoration: BoxDecoration(
                    gradient: widget.isActive
                        ? _buildActiveGradient()
                        : _buildAvailableGradient(),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: widget.challenge.category.primaryColor.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildHeader(),
                        const SizedBox(height: 16),
                        _buildDescription(),
                        const SizedBox(height: 16),
                        if (widget.isActive && widget.progress != null) ...[
                          _buildProgressSection(),
                          const SizedBox(height: 16),
                        ],
                        _buildFooter(),
                        const SizedBox(height: 16),
                        _buildMotivationQuote(),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Gradiente per sfide attive
  LinearGradient _buildActiveGradient() {
    return LinearGradient(
      colors: [
        widget.challenge.category.primaryColor,
        widget.challenge.category.primaryColor.withOpacity(0.8),
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }

  /// Gradiente per sfide disponibili
  LinearGradient _buildAvailableGradient() {
    return LinearGradient(
      colors: [
        widget.isDarkMode ? DrStaffilanoTheme.backgroundDark : Colors.white,
        widget.isDarkMode 
            ? DrStaffilanoTheme.backgroundDark.withOpacity(0.9)
            : Colors.white.withOpacity(0.9),
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }

  /// Header della card
  Widget _buildHeader() {
    final textColor = widget.isActive 
        ? Colors.white 
        : (widget.isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary);
    
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: widget.isActive 
                ? Colors.white.withOpacity(0.2)
                : widget.challenge.category.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            widget.challenge.category.icon,
            color: widget.isActive 
                ? Colors.white
                : widget.challenge.category.primaryColor,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      widget.challenge.title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: textColor,
                      ),
                    ),
                  ),
                  _buildDifficultyBadge(),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(
                    Icons.stars,
                    size: 16,
                    color: widget.isActive 
                        ? Colors.white.withOpacity(0.8)
                        : DrStaffilanoTheme.accentGold,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${widget.challenge.points} punti',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: widget.isActive 
                          ? Colors.white.withOpacity(0.9)
                          : DrStaffilanoTheme.accentGold,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Icon(
                    Icons.schedule,
                    size: 16,
                    color: widget.isActive 
                        ? Colors.white.withOpacity(0.8)
                        : (widget.isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${widget.challenge.durationDays} giorni',
                    style: TextStyle(
                      fontSize: 14,
                      color: widget.isActive 
                          ? Colors.white.withOpacity(0.9)
                          : (widget.isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Badge difficoltà
  Widget _buildDifficultyBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: widget.isActive 
            ? Colors.white.withOpacity(0.2)
            : widget.challenge.difficulty.color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            widget.challenge.difficulty.icon,
            size: 12,
            color: widget.isActive 
                ? Colors.white
                : widget.challenge.difficulty.color,
          ),
          const SizedBox(width: 4),
          Text(
            widget.challenge.difficulty.displayName,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: widget.isActive 
                  ? Colors.white
                  : widget.challenge.difficulty.color,
            ),
          ),
        ],
      ),
    );
  }

  /// Descrizione
  Widget _buildDescription() {
    final textColor = widget.isActive 
        ? Colors.white.withOpacity(0.9)
        : (widget.isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary);
    
    return Text(
      widget.challenge.description,
      style: TextStyle(
        fontSize: 14,
        color: textColor,
        height: 1.4,
      ),
    );
  }

  /// Sezione progresso (solo per sfide attive)
  Widget _buildProgressSection() {
    if (widget.progress == null) return const SizedBox.shrink();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progresso',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.white.withOpacity(0.9),
              ),
            ),
            Text(
              '${widget.progress!.progressPercentage}%',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        AnimatedBuilder(
          animation: _progressAnimation,
          builder: (context, child) {
            return LinearProgressIndicator(
              value: _progressAnimation.value,
              backgroundColor: Colors.white.withOpacity(0.3),
              valueColor: AlwaysStoppedAnimation<Color>(DrStaffilanoTheme.accentGold),
              minHeight: 8,
            );
          },
        ),
        const SizedBox(height: 8),
        Text(
          'Ultimo aggiornamento: ${_formatLastUpdate(widget.progress!.lastUpdated)}',
          style: TextStyle(
            fontSize: 12,
            color: Colors.white.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  /// Footer con informazioni aggiuntive
  Widget _buildFooter() {
    return Row(
      children: [
        _buildTrackingMethodChip(),
        const Spacer(),
        if (widget.isActive && widget.challenge.daysRemaining > 0) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.timer,
                  size: 12,
                  color: Colors.white,
                ),
                const SizedBox(width: 4),
                Text(
                  '${widget.challenge.daysRemaining} giorni',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
        const SizedBox(width: 8),
        _buildActionButton(),
      ],
    );
  }

  /// Chip metodo tracking
  Widget _buildTrackingMethodChip() {
    final bgColor = widget.isActive 
        ? Colors.white.withOpacity(0.2)
        : widget.challenge.trackingMethod.icon == Icons.camera_alt
            ? DrStaffilanoTheme.secondaryBlue.withOpacity(0.1)
            : DrStaffilanoTheme.primaryGreen.withOpacity(0.1);
    
    final iconColor = widget.isActive 
        ? Colors.white
        : widget.challenge.trackingMethod.icon == Icons.camera_alt
            ? DrStaffilanoTheme.secondaryBlue
            : DrStaffilanoTheme.primaryGreen;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            widget.challenge.trackingMethod.icon,
            size: 12,
            color: iconColor,
          ),
          const SizedBox(width: 4),
          Text(
            widget.challenge.trackingMethod.displayName,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: iconColor,
            ),
          ),
        ],
      ),
    );
  }

  /// Pulsante azione
  Widget _buildActionButton() {
    return Container(
      decoration: BoxDecoration(
        color: widget.isActive 
            ? Colors.white.withOpacity(0.2)
            : DrStaffilanoTheme.primaryGreen,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () {
            HapticFeedback.mediumImpact();
            widget.onAction?.call();
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  widget.isActive ? Icons.pause : Icons.play_arrow,
                  size: 16,
                  color: widget.isActive ? Colors.white : Colors.white,
                ),
                const SizedBox(width: 4),
                Text(
                  widget.isActive ? 'Pausa' : 'Inizia',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: widget.isActive ? Colors.white : Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Citazione motivazionale Dr. Staffilano
  Widget _buildMotivationQuote() {
    final bgColor = widget.isActive 
        ? Colors.white.withOpacity(0.1)
        : DrStaffilanoTheme.secondaryBlue.withOpacity(0.1);
    
    final textColor = widget.isActive 
        ? Colors.white.withOpacity(0.9)
        : DrStaffilanoTheme.secondaryBlue;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: widget.isActive 
              ? Colors.white.withOpacity(0.2)
              : DrStaffilanoTheme.secondaryBlue.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.format_quote,
            color: textColor,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              widget.challenge.drStaffilanoMotivation,
              style: TextStyle(
                fontSize: 12,
                fontStyle: FontStyle.italic,
                color: textColor,
                height: 1.3,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Formatta ultimo aggiornamento
  String _formatLastUpdate(DateTime lastUpdate) {
    final now = DateTime.now();
    final difference = now.difference(lastUpdate);
    
    if (difference.inMinutes < 1) {
      return 'Ora';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m fa';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h fa';
    } else {
      return '${difference.inDays}g fa';
    }
  }
}
