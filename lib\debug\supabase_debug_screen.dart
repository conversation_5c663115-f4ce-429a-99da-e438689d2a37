import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';
import '../services/supabase_auth_service.dart';

class SupabaseDebugScreen extends StatefulWidget {
  const SupabaseDebugScreen({super.key});

  @override
  State<SupabaseDebugScreen> createState() => _SupabaseDebugScreenState();
}

class _SupabaseDebugScreenState extends State<SupabaseDebugScreen> {
  final _authService = SupabaseAuthService();
  final _emailController = TextEditingController(text: '<EMAIL>');
  final _passwordController = TextEditingController(text: 'password123');
  
  String _debugInfo = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _runDebugTests();
  }

  Future<void> _runDebugTests() async {
    setState(() {
      _isLoading = true;
      _debugInfo = '🧪 Avvio test debug Supabase...\n\n';
    });

    try {
      // Test 1: Configurazione
      _addDebugInfo('1️⃣ Test Configurazione');
      final client = SupabaseConfig.client;
      _addDebugInfo('📍 URL: ${SupabaseConfig.supabaseUrl}');
      _addDebugInfo('🔑 Anon Key: ${SupabaseConfig.supabaseAnonKey.substring(0, 20)}...');
      _addDebugInfo('✅ Configurazione OK\n');

      // Test 2: Connessione
      _addDebugInfo('2️⃣ Test Connessione');
      try {
        await client.auth.getUser();
        _addDebugInfo('✅ Connessione al server riuscita');
      } catch (e) {
        if (e.toString().contains('JWT') || e.toString().contains('session')) {
          _addDebugInfo('✅ Connessione OK (nessun utente loggato)');
        } else {
          _addDebugInfo('❌ Errore connessione: $e');
        }
      }
      _addDebugInfo('');

      // Test 3: Stato autenticazione
      _addDebugInfo('3️⃣ Test Stato Autenticazione');
      final currentUser = client.auth.currentUser;
      if (currentUser != null) {
        _addDebugInfo('👤 Utente loggato: ${currentUser.email}');
        _addDebugInfo('🆔 ID: ${currentUser.id}');
      } else {
        _addDebugInfo('👤 Nessun utente loggato');
      }
      _addDebugInfo('');

      _addDebugInfo('🎉 Test completati con successo!');

    } catch (e) {
      _addDebugInfo('❌ ERRORE CRITICO: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  void _addDebugInfo(String info) {
    setState(() {
      _debugInfo += '$info\n';
    });
  }

  Future<void> _testRegistration() async {
    setState(() {
      _isLoading = true;
    });

    _addDebugInfo('\n📝 Test Registrazione...');
    _addDebugInfo('📧 Email: ${_emailController.text}');

    try {
      final response = await _authService.signUp(
        email: _emailController.text,
        password: _passwordController.text,
        fullName: 'Test User',
        username: 'testuser',
      );

      if (response.user != null) {
        _addDebugInfo('✅ Registrazione riuscita!');
        _addDebugInfo('👤 User ID: ${response.user!.id}');
        _addDebugInfo('📧 Email: ${response.user!.email}');
      } else {
        _addDebugInfo('❌ Registrazione fallita: nessun utente restituito');
      }
    } catch (e) {
      _addDebugInfo('❌ Errore registrazione: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _testLogin() async {
    setState(() {
      _isLoading = true;
    });

    _addDebugInfo('\n🔐 Test Login...');
    _addDebugInfo('📧 Email: ${_emailController.text}');

    try {
      final response = await _authService.signIn(
        email: _emailController.text,
        password: _passwordController.text,
      );

      if (response.user != null) {
        _addDebugInfo('✅ Login riuscito!');
        _addDebugInfo('👤 User ID: ${response.user!.id}');
        _addDebugInfo('📧 Email: ${response.user!.email}');
      } else {
        _addDebugInfo('❌ Login fallito: nessun utente restituito');
      }
    } catch (e) {
      _addDebugInfo('❌ Errore login: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Supabase Debug'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Form di test
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    TextField(
                      controller: _emailController,
                      decoration: const InputDecoration(
                        labelText: 'Email Test',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _passwordController,
                      decoration: const InputDecoration(
                        labelText: 'Password Test',
                        border: OutlineInputBorder(),
                      ),
                      obscureText: true,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _testRegistration,
                            child: const Text('Test Registrazione'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _testLogin,
                            child: const Text('Test Login'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            // Debug info
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Text(
                            'Debug Info',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          if (_isLoading)
                            const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                        ],
                      ),
                      const Divider(),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            _debugInfo,
                            style: const TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
}
