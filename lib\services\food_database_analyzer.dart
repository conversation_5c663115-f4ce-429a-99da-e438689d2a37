import '../models/food.dart';
import '../data/food_database.dart';
import 'food_variety_manager.dart';

/// Analizza l'utilizzo del database degli alimenti per identificare
/// alimenti sottoutilizzati e migliorare la varietà dei piani dietetici
class FoodDatabaseAnalyzer {
  final FoodDatabase _foodDatabase;
  final FoodVarietyManager _varietyManager;

  FoodDatabaseAnalyzer(this._foodDatabase, this._varietyManager);

  /// Factory method per creare un'istanza con dipendenze
  static Future<FoodDatabaseAnalyzer> create() async {
    final foodDatabase = FoodDatabase();
    final varietyManager = await FoodVarietyManager.getInstance();
    return FoodDatabaseAnalyzer(foodDatabase, varietyManager);
  }

  /// Analizza l'utilizzo complessivo del database degli alimenti
  Future<DatabaseUtilizationReport> analyzeDatabaseUtilization() async {
    final allFoods = await _foodDatabase.getAllFoods();
    final usageStats = _varietyManager.getUsageStatistics();
    final recentlyUsedFoods = _varietyManager.getRecentlyUsedFoods(days: 7);

    // Categorizza gli alimenti per utilizzo
    final neverUsedFoods = <Food>[];
    final underutilizedFoods = <Food>[];
    final wellUtilizedFoods = <Food>[];

    for (final food in allFoods) {
      if (!usageStats.containsKey(food.id)) {
        neverUsedFoods.add(food);
      } else if (recentlyUsedFoods.contains(food.id)) {
        wellUtilizedFoods.add(food);
      } else {
        underutilizedFoods.add(food);
      }
    }

    // Analizza per categoria
    final categoryUtilization = <FoodCategory, CategoryUtilization>{};
    for (final category in FoodCategory.values) {
      final categoryFoods = allFoods.where((food) => food.categories.contains(category)).toList();
      final categoryUsedFoods = categoryFoods.where((food) => recentlyUsedFoods.contains(food.id)).toList();
      
      categoryUtilization[category] = CategoryUtilization(
        totalFoods: categoryFoods.length,
        usedFoods: categoryUsedFoods.length,
        utilizationRate: categoryFoods.isNotEmpty ? categoryUsedFoods.length / categoryFoods.length : 0.0,
      );
    }

    // Identifica alimenti stagionali sottoutilizzati
    final currentMonth = DateTime.now().month;
    final seasonalUnderutilized = neverUsedFoods.where((food) =>
      food.isSeasonal && food.seasonalMonths.contains(currentMonth)
    ).toList();

    // Identifica alimenti tradizionali italiani sottoutilizzati
    final traditionalUnderutilized = neverUsedFoods.where((food) =>
      food.isTraditionalItalian
    ).toList();

    return DatabaseUtilizationReport(
      totalFoods: allFoods.length,
      neverUsedFoods: neverUsedFoods,
      underutilizedFoods: underutilizedFoods,
      wellUtilizedFoods: wellUtilizedFoods,
      categoryUtilization: categoryUtilization,
      seasonalUnderutilized: seasonalUnderutilized,
      traditionalUnderutilized: traditionalUnderutilized,
      overallUtilizationRate: allFoods.isNotEmpty ? (allFoods.length - neverUsedFoods.length) / allFoods.length : 0.0,
    );
  }

  /// Suggerisce alimenti da promuovere per aumentare la varietà
  Future<List<Food>> suggestFoodsToPromote({
    List<FoodCategory>? preferredCategories,
    bool prioritizeSeasonal = true,
    bool prioritizeTraditional = true,
    int maxSuggestions = 10,
  }) async {
    final report = await analyzeDatabaseUtilization();
    final suggestions = <Food>[];

    // Priorità 1: Alimenti stagionali mai utilizzati
    if (prioritizeSeasonal) {
      suggestions.addAll(report.seasonalUnderutilized.take(maxSuggestions ~/ 3));
    }

    // Priorità 2: Alimenti tradizionali italiani mai utilizzati
    if (prioritizeTraditional) {
      final traditionalToAdd = report.traditionalUnderutilized
          .where((food) => !suggestions.contains(food))
          .take(maxSuggestions ~/ 3);
      suggestions.addAll(traditionalToAdd);
    }

    // Priorità 3: Alimenti delle categorie preferite mai utilizzati
    if (preferredCategories != null) {
      for (final category in preferredCategories) {
        final categoryNeverUsed = report.neverUsedFoods
            .where((food) => food.categories.contains(category) && !suggestions.contains(food))
            .take(2);
        suggestions.addAll(categoryNeverUsed);
      }
    }

    // Riempi con altri alimenti mai utilizzati
    final remainingSlots = maxSuggestions - suggestions.length;
    if (remainingSlots > 0) {
      final otherNeverUsed = report.neverUsedFoods
          .where((food) => !suggestions.contains(food))
          .take(remainingSlots);
      suggestions.addAll(otherNeverUsed);
    }

    return suggestions.take(maxSuggestions).toList();
  }

  /// Genera un report dettagliato sull'utilizzo del database
  Future<String> generateUtilizationReport() async {
    final report = await analyzeDatabaseUtilization();
    final buffer = StringBuffer();

    buffer.writeln('=== REPORT UTILIZZO DATABASE ALIMENTI ===\n');
    
    buffer.writeln('📊 STATISTICHE GENERALI:');
    buffer.writeln('• Alimenti totali: ${report.totalFoods}');
    buffer.writeln('• Alimenti mai utilizzati: ${report.neverUsedFoods.length} (${(report.neverUsedFoods.length / report.totalFoods * 100).toStringAsFixed(1)}%)');
    buffer.writeln('• Alimenti sottoutilizzati: ${report.underutilizedFoods.length}');
    buffer.writeln('• Alimenti ben utilizzati: ${report.wellUtilizedFoods.length}');
    buffer.writeln('• Tasso di utilizzo generale: ${(report.overallUtilizationRate * 100).toStringAsFixed(1)}%\n');

    buffer.writeln('📈 UTILIZZO PER CATEGORIA:');
    for (final entry in report.categoryUtilization.entries) {
      final category = entry.key;
      final utilization = entry.value;
      buffer.writeln('• ${category.toString().split('.').last}: ${utilization.usedFoods}/${utilization.totalFoods} (${(utilization.utilizationRate * 100).toStringAsFixed(1)}%)');
    }

    buffer.writeln('\n🌱 ALIMENTI STAGIONALI SOTTOUTILIZZATI:');
    if (report.seasonalUnderutilized.isEmpty) {
      buffer.writeln('• Nessun alimento stagionale sottoutilizzato');
    } else {
      for (final food in report.seasonalUnderutilized.take(5)) {
        buffer.writeln('• ${food.name} (${food.categories.map((c) => c.toString().split('.').last).join(', ')})');
      }
    }

    buffer.writeln('\n🇮🇹 ALIMENTI TRADIZIONALI ITALIANI SOTTOUTILIZZATI:');
    if (report.traditionalUnderutilized.isEmpty) {
      buffer.writeln('• Nessun alimento tradizionale sottoutilizzato');
    } else {
      for (final food in report.traditionalUnderutilized.take(5)) {
        buffer.writeln('• ${food.name} (${food.categories.map((c) => c.toString().split('.').last).join(', ')})');
      }
    }

    buffer.writeln('\n💡 RACCOMANDAZIONI:');
    if (report.overallUtilizationRate < 0.3) {
      buffer.writeln('• Tasso di utilizzo molto basso - considerare miglioramenti all\'algoritmo di selezione');
    } else if (report.overallUtilizationRate < 0.6) {
      buffer.writeln('• Tasso di utilizzo moderato - promuovere alimenti sottoutilizzati');
    } else {
      buffer.writeln('• Buon tasso di utilizzo del database');
    }

    if (report.seasonalUnderutilized.length > 10) {
      buffer.writeln('• Molti alimenti stagionali non utilizzati - migliorare la promozione stagionale');
    }

    if (report.traditionalUnderutilized.length > 15) {
      buffer.writeln('• Molti alimenti tradizionali italiani non utilizzati - enfatizzare la cucina italiana');
    }

    return buffer.toString();
  }
}

/// Report sull'utilizzo del database degli alimenti
class DatabaseUtilizationReport {
  final int totalFoods;
  final List<Food> neverUsedFoods;
  final List<Food> underutilizedFoods;
  final List<Food> wellUtilizedFoods;
  final Map<FoodCategory, CategoryUtilization> categoryUtilization;
  final List<Food> seasonalUnderutilized;
  final List<Food> traditionalUnderutilized;
  final double overallUtilizationRate;

  DatabaseUtilizationReport({
    required this.totalFoods,
    required this.neverUsedFoods,
    required this.underutilizedFoods,
    required this.wellUtilizedFoods,
    required this.categoryUtilization,
    required this.seasonalUnderutilized,
    required this.traditionalUnderutilized,
    required this.overallUtilizationRate,
  });
}

/// Utilizzo di una categoria di alimenti
class CategoryUtilization {
  final int totalFoods;
  final int usedFoods;
  final double utilizationRate;

  CategoryUtilization({
    required this.totalFoods,
    required this.usedFoods,
    required this.utilizationRate,
  });
}
