import 'dart:math' as math;
import 'services/specific_diet_generator_service.dart';
import 'services/food_variety_manager.dart';
import 'data/specific_diet_foods.dart';
import 'models/user_profile.dart';

/// TEST RAPIDO PER VERIFICARE IL SISTEMA DI VARIETÀ CORRETTO
Future<void> main() async {
  print('⚡ TEST RAPIDO SISTEMA VARIETÀ CORRETTO');
  print('=' * 45);

  try {
    // Verifica database
    final allFoods = SpecificDietFoods.getAllFoods();
    print('📊 Database: ${allFoods.length} alimenti disponibili');

    // Inizializza servizi
    final varietyManager = await FoodVarietyManager.getInstance();
    await varietyManager.resetUsageHistory();
    final generator = await SpecificDietGeneratorService.getInstance();
    print('✅ Servizi inizializzati');

    // Crea profilo test
    final profile = UserProfile(
      id: 'quick_test',
      name: '<PERSON> <PERSON><PERSON>',
      age: 30,
      gender: Gender.male,
      height: 175,
      weight: 70,
      activityLevel: ActivityLevel.moderate,
      goal: Goal.maintain,
      dietType: DietType.omnivore,
      allergies: [],
      dislikedFoods: [],
      mealsPerDay: 3,
    );
    print('👤 Profilo: ${profile.calculateCalorieTarget()} kcal/giorno');

    // Genera 3 piani consecutivi
    final allUsedFoods = <String>[];
    
    for (int i = 1; i <= 3; i++) {
      print('\n🔄 Generazione $i:');
      
      final plan = await generator.generateWeeklyDietPlan(profile, weeks: 1);
      
      if (plan.dailyPlans.isNotEmpty) {
        final daily = plan.dailyPlans.first;
        final dayFoods = <String>[];
        
        for (final meal in daily.meals) {
          final foods = meal.foods.map((fp) => fp.food.name).toList();
          dayFoods.addAll(foods);
          print('   ${_getMealName(meal.type)}: ${foods.join(', ')}');
        }
        
        allUsedFoods.addAll(dayFoods);
      }
      
      await Future.delayed(Duration(milliseconds: 100));
    }

    // Analisi risultati
    final uniqueFoods = allUsedFoods.toSet();
    final varietyRatio = uniqueFoods.length / allUsedFoods.length;
    final dbUtilization = uniqueFoods.length / allFoods.length;

    print('\n📈 RISULTATI:');
    print('   Alimenti totali: ${allUsedFoods.length}');
    print('   Alimenti unici: ${uniqueFoods.length}');
    print('   Varietà: ${(varietyRatio * 100).toStringAsFixed(1)}%');
    print('   Utilizzo DB: ${(dbUtilization * 100).toStringAsFixed(1)}%');

    // Verifica tracking
    final stats = varietyManager.getUsageStatistics();
    print('   Tracking: ${stats['totalTrackedFoods']} alimenti tracciati');

    // Valutazione
    print('\n🎯 VALUTAZIONE:');
    
    if (dbUtilization >= 0.3 && varietyRatio >= 0.5 && stats['totalTrackedFoods'] as int > 0) {
      print('✅ SUCCESSO! Sistema di varietà funzionante');
      print('   🎉 Varietà significativamente migliorata');
      print('   📊 Tracking attivo');
      print('   🍽️ Buon utilizzo del database');
    } else {
      print('⚠️ PROBLEMI RILEVATI:');
      if (dbUtilization < 0.3) print('   ❌ Utilizzo database basso');
      if (varietyRatio < 0.5) print('   ❌ Varietà insufficiente');
      if (stats['totalTrackedFoods'] as int == 0) print('   ❌ Tracking non attivo');
    }

    // Mostra alcuni alimenti utilizzati
    if (uniqueFoods.isNotEmpty) {
      print('\n🍽️ Alimenti utilizzati (primi 10):');
      final sortedFoods = uniqueFoods.toList()..sort();
      for (int i = 0; i < math.min(10, sortedFoods.length); i++) {
        print('   ${i + 1}. ${sortedFoods[i]}');
      }
    }

  } catch (e) {
    print('\n❌ ERRORE: $e');
  }
}

String _getMealName(String type) {
  switch (type) {
    case 'breakfast': return 'Colazione';
    case 'lunch': return 'Pranzo';
    case 'dinner': return 'Cena';
    case 'snack': return 'Spuntino';
    default: return type;
  }
}
