import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/welljourney_models.dart';
import '../models/food.dart';
import '../models/meal.dart';
import '../models/diet_plan.dart';
import 'storage_service.dart';
import 'food_database_service.dart';
import 'nutriscore_service.dart';
import 'welljourney_service.dart';

/// Ser<PERSON><PERSON> avanzato per la gestione delle sfide Dr. Staffilano
/// Integra AI, personalizzazione e tracking real-time
class AdvancedChallengesService extends ChangeNotifier {
  static const String _activeChallengesKey = 'advanced_active_challenges';
  static const String _challengeProgressKey = 'challenge_progress';
  static const String _challengeHistoryKey = 'challenge_history';
  static const String _personalizedChallengesKey = 'personalized_challenges';

  static AdvancedChallengesService? _instance;
  static AdvancedChallengesService get instance {
    _instance ??= AdvancedChallengesService._();
    return _instance!;
  }
  AdvancedChallengesService._();

  // Stato interno
  List<AdvancedChallenge> _activeChallenges = [];
  List<AdvancedChallenge> _availableChallenges = [];
  Map<String, ChallengeProgress> _challengeProgress = {};
  List<ChallengeHistory> _challengeHistory = [];
  bool _isInitialized = false;
  Timer? _progressTimer;

  // Getters
  List<AdvancedChallenge> get activeChallenges => List.unmodifiable(_activeChallenges);
  List<AdvancedChallenge> get availableChallenges => List.unmodifiable(_availableChallenges);
  Map<String, ChallengeProgress> get challengeProgress => Map.unmodifiable(_challengeProgress);
  List<ChallengeHistory> get challengeHistory => List.unmodifiable(_challengeHistory);
  bool get isInitialized => _isInitialized;

  /// Inizializza il servizio
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      print('🔄 Inizializzazione AdvancedChallengesService...');

      await _loadStoredData();
      await _generatePersonalizedChallenges();
      _startProgressTracking();

      _isInitialized = true;
      print('✅ AdvancedChallengesService inizializzato con successo');
      notifyListeners();
    } catch (e) {
      print('❌ Errore nell\'inizializzazione AdvancedChallengesService: $e');
      rethrow;
    }
  }

  /// Carica i dati salvati
  Future<void> _loadStoredData() async {
    final storage = await StorageService.getInstance();

    // Carica sfide attive
    final activeChallengesData = await storage.getString(_activeChallengesKey);
    if (activeChallengesData != null) {
      // TODO: Implementare deserializzazione JSON
    }

    // Carica progressi
    final progressData = await storage.getString(_challengeProgressKey);
    if (progressData != null) {
      // TODO: Implementare deserializzazione JSON
    }

    // Carica storico
    final historyData = await storage.getString(_challengeHistoryKey);
    if (historyData != null) {
      // TODO: Implementare deserializzazione JSON
    }
  }

  /// Genera sfide personalizzate basate sui dati dell'utente
  Future<void> _generatePersonalizedChallenges() async {
    try {
      final nutriScoreService = NutriScoreService.instance;
      final currentScore = await nutriScoreService.calculateCurrentNutriScore();

      _availableChallenges = await _createPersonalizedChallenges(currentScore);

      print('📊 Generate ${_availableChallenges.length} sfide personalizzate');
    } catch (e) {
      print('❌ Errore nella generazione sfide personalizzate: $e');
      _availableChallenges = _getDefaultChallenges();
    }
  }

  /// Crea sfide personalizzate basate sul NutriScore
  Future<List<AdvancedChallenge>> _createPersonalizedChallenges(double nutriScore) async {
    final challenges = <AdvancedChallenge>[];
    final now = DateTime.now();

    // Sfide basate sul livello NutriScore
    if (nutriScore < 60) {
      // Utente principiante - sfide base
      challenges.addAll(_getBeginnerChallenges(now));
    } else if (nutriScore < 80) {
      // Utente intermedio - sfide moderate
      challenges.addAll(_getIntermediateChallenges(now));
    } else {
      // Utente avanzato - sfide complesse
      challenges.addAll(_getAdvancedChallenges(now));
    }

    // Sfide stagionali
    challenges.addAll(_getSeasonalChallenges(now));

    // Sfide giornaliere
    challenges.addAll(_getDailyChallenges(now));

    // Sfide settimanali
    challenges.addAll(_getWeeklyChallenges(now));

    // Sfide mensili
    challenges.addAll(_getMonthlyChallenges(now));

    // Sfide personali
    challenges.addAll(_getPersonalChallenges(now));

    // Sfide cardiologiche (specialità Dr. Staffilano)
    challenges.addAll(_getCardiologicalChallenges(now));

    return challenges;
  }

  /// Sfide per principianti
  List<AdvancedChallenge> _getBeginnerChallenges(DateTime now) {
    return [
      AdvancedChallenge(
        id: 'beginner_hydration',
        title: 'Idratazione Quotidiana',
        description: 'Bevi almeno 6 bicchieri d\'acqua al giorno per 5 giorni',
        category: ChallengeCategory.hydration,
        difficulty: ChallengeDifficulty.easy,
        type: ChallengeType.individual,
        durationDays: 5,
        targetValue: 6,
        unit: 'bicchieri/giorno',
        points: 100,
        startDate: now,
        endDate: now.add(const Duration(days: 5)),
        drStaffilanoMotivation: 'L\'acqua è il primo farmaco naturale. Iniziamo con piccoli passi verso una migliore idratazione!',
        trackingMethod: ChallengeTrackingMethod.manual,
        integrationFeatures: [ChallengeIntegration.nutriscore],
        rewards: [
          ChallengeReward(
            type: RewardType.points,
            value: 100,
            description: '100 punti WellJourney',
          ),
          ChallengeReward(
            type: RewardType.badge,
            value: 1,
            description: 'Badge "Primo Passo"',
          ),
        ],
      ),
    ];
  }

  /// Sfide per utenti intermedi
  List<AdvancedChallenge> _getIntermediateChallenges(DateTime now) {
    return [
      AdvancedChallenge(
        id: 'intermediate_vegetables',
        title: 'Settimana Verde Plus',
        description: 'Consuma almeno 5 porzioni di verdura al giorno per una settimana',
        category: ChallengeCategory.nutrition,
        difficulty: ChallengeDifficulty.medium,
        type: ChallengeType.individual,
        durationDays: 7,
        targetValue: 5,
        unit: 'porzioni/giorno',
        points: 200,
        startDate: now,
        endDate: now.add(const Duration(days: 7)),
        drStaffilanoMotivation: 'Le verdure sono medicina naturale. Ogni porzione è un investimento nella vostra salute cardiovascolare!',
        trackingMethod: ChallengeTrackingMethod.foodOracle,
        integrationFeatures: [
          ChallengeIntegration.foodOracle,
          ChallengeIntegration.mealPlan,
          ChallengeIntegration.nutriscore,
        ],
        rewards: [
          ChallengeReward(
            type: RewardType.points,
            value: 200,
            description: '200 punti WellJourney',
          ),
          ChallengeReward(
            type: RewardType.nutriScoreBoost,
            value: 5,
            description: '+5 punti NutriScore',
          ),
        ],
      ),
    ];
  }

  /// Sfide per utenti avanzati
  List<AdvancedChallenge> _getAdvancedChallenges(DateTime now) {
    return [
      AdvancedChallenge(
        id: 'advanced_mediterranean',
        title: 'Maestro Mediterraneo',
        description: 'Segui perfettamente la dieta mediterranea per 14 giorni',
        category: ChallengeCategory.lifestyle,
        difficulty: ChallengeDifficulty.hard,
        type: ChallengeType.individual,
        durationDays: 14,
        targetValue: 14,
        unit: 'giorni perfetti',
        points: 500,
        startDate: now,
        endDate: now.add(const Duration(days: 14)),
        drStaffilanoMotivation: 'La dieta mediterranea è patrimonio dell\'umanità. Diventate ambasciatori di questo stile di vita!',
        trackingMethod: ChallengeTrackingMethod.aiAnalysis,
        integrationFeatures: [
          ChallengeIntegration.foodOracle,
          ChallengeIntegration.mealPlan,
          ChallengeIntegration.nutriscore,
          ChallengeIntegration.welljourney,
        ],
        rewards: [
          ChallengeReward(
            type: RewardType.points,
            value: 500,
            description: '500 punti WellJourney',
          ),
          ChallengeReward(
            type: RewardType.badge,
            value: 1,
            description: 'Badge "Maestro Mediterraneo"',
          ),
          ChallengeReward(
            type: RewardType.nutriScoreBoost,
            value: 10,
            description: '+10 punti NutriScore',
          ),
        ],
      ),
    ];
  }

  /// Sfide stagionali
  List<AdvancedChallenge> _getSeasonalChallenges(DateTime now) {
    final month = now.month;
    final challenges = <AdvancedChallenge>[];

    if (month >= 3 && month <= 5) {
      // Primavera
      challenges.add(_createSeasonalChallenge(
        'spring_detox',
        'Detox Primaverile',
        'Consuma alimenti detox per 7 giorni',
        now,
      ));
    } else if (month >= 6 && month <= 8) {
      // Estate
      challenges.add(_createSeasonalChallenge(
        'summer_hydration',
        'Estate Idratata',
        'Mantieni un\'idratazione perfetta durante l\'estate',
        now,
      ));
    } else if (month >= 9 && month <= 11) {
      // Autunno
      challenges.add(_createSeasonalChallenge(
        'autumn_immunity',
        'Immunità Autunnale',
        'Rafforza il sistema immunitario con alimenti stagionali',
        now,
      ));
    } else {
      // Inverno
      challenges.add(_createSeasonalChallenge(
        'winter_warmth',
        'Calore Invernale',
        'Mantieni energia e calore con alimenti nutrienti',
        now,
      ));
    }

    return challenges;
  }

  /// Sfide cardiologiche specialistiche
  List<AdvancedChallenge> _getCardiologicalChallenges(DateTime now) {
    return [
      AdvancedChallenge(
        id: 'heart_omega3',
        title: 'Cuore Omega-3',
        description: 'Consuma pesce ricco di omega-3 almeno 3 volte a settimana',
        category: ChallengeCategory.heartHealth,
        difficulty: ChallengeDifficulty.medium,
        type: ChallengeType.individual,
        durationDays: 7,
        targetValue: 3,
        unit: 'porzioni/settimana',
        points: 250,
        startDate: now,
        endDate: now.add(const Duration(days: 7)),
        drStaffilanoMotivation: 'Come cardiologo, posso affermare che gli omega-3 sono medicina liquida per il cuore. Ogni porzione protegge le vostre arterie.',
        trackingMethod: ChallengeTrackingMethod.foodOracle,
        integrationFeatures: [
          ChallengeIntegration.foodOracle,
          ChallengeIntegration.mealPlan,
        ],
        rewards: [
          ChallengeReward(
            type: RewardType.points,
            value: 250,
            description: '250 punti WellJourney',
          ),
          ChallengeReward(
            type: RewardType.badge,
            value: 1,
            description: 'Badge "Protettore del Cuore"',
          ),
        ],
      ),
    ];
  }

  /// Sfide giornaliere (reset ogni 24 ore)
  List<AdvancedChallenge> _getDailyChallenges(DateTime now) {
    return [
      AdvancedChallenge(
        id: 'daily_water_${now.day}',
        title: 'Idratazione Giornaliera',
        description: 'Bevi 8 bicchieri d\'acqua oggi',
        category: ChallengeCategory.daily,
        difficulty: ChallengeDifficulty.easy,
        type: ChallengeType.individual,
        durationDays: 1,
        targetValue: 8,
        unit: 'bicchieri',
        points: 50,
        startDate: now,
        endDate: DateTime(now.year, now.month, now.day, 23, 59, 59),
        drStaffilanoMotivation: 'Ogni giorno è una nuova opportunità per prendersi cura del proprio corpo. L\'acqua è vita!',
        trackingMethod: ChallengeTrackingMethod.manual,
        integrationFeatures: [ChallengeIntegration.nutriscore],
        rewards: [
          ChallengeReward(
            type: RewardType.points,
            value: 50,
            description: '50 punti WellJourney',
          ),
        ],
      ),
      AdvancedChallenge(
        id: 'daily_vegetables_${now.day}',
        title: 'Verde Quotidiano',
        description: 'Consuma almeno 3 porzioni di verdura oggi',
        category: ChallengeCategory.daily,
        difficulty: ChallengeDifficulty.easy,
        type: ChallengeType.individual,
        durationDays: 1,
        targetValue: 3,
        unit: 'porzioni',
        points: 75,
        startDate: now,
        endDate: DateTime(now.year, now.month, now.day, 23, 59, 59),
        drStaffilanoMotivation: 'Le verdure sono farmaci naturali. Ogni porzione è un investimento nella vostra salute!',
        trackingMethod: ChallengeTrackingMethod.foodOracle,
        integrationFeatures: [ChallengeIntegration.foodOracle, ChallengeIntegration.nutriscore],
        rewards: [
          ChallengeReward(
            type: RewardType.points,
            value: 75,
            description: '75 punti WellJourney',
          ),
        ],
      ),
    ];
  }

  /// Sfide settimanali (reset ogni settimana)
  List<AdvancedChallenge> _getWeeklyChallenges(DateTime now) {
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    final weekId = '${weekStart.year}_${weekStart.month}_${weekStart.day}';

    return [
      AdvancedChallenge(
        id: 'weekly_fish_$weekId',
        title: 'Settimana del Pesce',
        description: 'Consuma pesce almeno 3 volte questa settimana',
        category: ChallengeCategory.weekly,
        difficulty: ChallengeDifficulty.medium,
        type: ChallengeType.individual,
        durationDays: 7,
        targetValue: 3,
        unit: 'porzioni/settimana',
        points: 200,
        startDate: weekStart,
        endDate: weekStart.add(const Duration(days: 7)),
        drStaffilanoMotivation: 'Il pesce è ricco di omega-3, essenziali per la salute cardiovascolare. Una scelta saggia per il vostro cuore!',
        trackingMethod: ChallengeTrackingMethod.foodOracle,
        integrationFeatures: [ChallengeIntegration.foodOracle, ChallengeIntegration.mealPlan],
        rewards: [
          ChallengeReward(
            type: RewardType.points,
            value: 200,
            description: '200 punti WellJourney',
          ),
          ChallengeReward(
            type: RewardType.nutriScoreBoost,
            value: 3,
            description: '+3 punti NutriScore',
          ),
        ],
      ),
      AdvancedChallenge(
        id: 'weekly_exercise_$weekId',
        title: 'Movimento Settimanale',
        description: 'Fai attività fisica almeno 5 giorni questa settimana',
        category: ChallengeCategory.weekly,
        difficulty: ChallengeDifficulty.medium,
        type: ChallengeType.individual,
        durationDays: 7,
        targetValue: 5,
        unit: 'giorni attivi',
        points: 250,
        startDate: weekStart,
        endDate: weekStart.add(const Duration(days: 7)),
        drStaffilanoMotivation: 'Il movimento è medicina. Ogni passo è un battito di salute per il vostro cuore!',
        trackingMethod: ChallengeTrackingMethod.manual,
        integrationFeatures: [ChallengeIntegration.nutriscore],
        rewards: [
          ChallengeReward(
            type: RewardType.points,
            value: 250,
            description: '250 punti WellJourney',
          ),
          ChallengeReward(
            type: RewardType.badge,
            value: 1,
            description: 'Badge "Cuore Attivo"',
          ),
        ],
      ),
    ];
  }

  /// Sfide mensili (obiettivi a lungo termine)
  List<AdvancedChallenge> _getMonthlyChallenges(DateTime now) {
    final monthId = '${now.year}_${now.month}';

    return [
      AdvancedChallenge(
        id: 'monthly_mediterranean_$monthId',
        title: 'Mese Mediterraneo',
        description: 'Segui la dieta mediterranea per tutto il mese',
        category: ChallengeCategory.monthly,
        difficulty: ChallengeDifficulty.hard,
        type: ChallengeType.individual,
        durationDays: 30,
        targetValue: 25,
        unit: 'giorni conformi',
        points: 1000,
        startDate: DateTime(now.year, now.month, 1),
        endDate: DateTime(now.year, now.month + 1, 1).subtract(const Duration(days: 1)),
        drStaffilanoMotivation: 'La dieta mediterranea è patrimonio UNESCO. Un mese di dedizione può trasformare la vostra salute!',
        trackingMethod: ChallengeTrackingMethod.aiAnalysis,
        integrationFeatures: [
          ChallengeIntegration.foodOracle,
          ChallengeIntegration.mealPlan,
          ChallengeIntegration.nutriscore,
          ChallengeIntegration.welljourney,
        ],
        rewards: [
          ChallengeReward(
            type: RewardType.points,
            value: 1000,
            description: '1000 punti WellJourney',
          ),
          ChallengeReward(
            type: RewardType.badge,
            value: 1,
            description: 'Badge "Maestro Mediterraneo"',
          ),
          ChallengeReward(
            type: RewardType.nutriScoreBoost,
            value: 15,
            description: '+15 punti NutriScore',
          ),
        ],
      ),
    ];
  }

  /// Sfide personali (basate sui dati dell'utente)
  List<AdvancedChallenge> _getPersonalChallenges(DateTime now) {
    // TODO: Implementare logica basata sui dati dell'utente
    // Per ora restituiamo sfide generiche personalizzabili

    return [
      AdvancedChallenge(
        id: 'personal_improvement_${now.millisecondsSinceEpoch}',
        title: 'Miglioramento Personale',
        description: 'Migliora il tuo NutriScore di 5 punti',
        category: ChallengeCategory.personal,
        difficulty: ChallengeDifficulty.medium,
        type: ChallengeType.individual,
        durationDays: 14,
        targetValue: 5,
        unit: 'punti NutriScore',
        points: 300,
        startDate: now,
        endDate: now.add(const Duration(days: 14)),
        drStaffilanoMotivation: 'Il miglioramento continuo è la chiave del successo. Ogni piccolo passo conta!',
        trackingMethod: ChallengeTrackingMethod.automatic,
        integrationFeatures: [ChallengeIntegration.nutriscore, ChallengeIntegration.welljourney],
        rewards: [
          ChallengeReward(
            type: RewardType.points,
            value: 300,
            description: '300 punti WellJourney',
          ),
          ChallengeReward(
            type: RewardType.badge,
            value: 1,
            description: 'Badge "Miglioramento Continuo"',
          ),
        ],
      ),
    ];
  }

  /// Crea una sfida stagionale
  AdvancedChallenge _createSeasonalChallenge(
    String id,
    String title,
    String description,
    DateTime now,
  ) {
    return AdvancedChallenge(
      id: id,
      title: title,
      description: description,
      category: ChallengeCategory.seasonal,
      difficulty: ChallengeDifficulty.medium,
      type: ChallengeType.individual,
      durationDays: 7,
      targetValue: 7,
      unit: 'giorni',
      points: 150,
      startDate: now,
      endDate: now.add(const Duration(days: 7)),
      drStaffilanoMotivation: 'Seguire i ritmi della natura è la chiave per una salute ottimale.',
      trackingMethod: ChallengeTrackingMethod.foodOracle,
      integrationFeatures: [ChallengeIntegration.foodOracle],
      rewards: [
        ChallengeReward(
          type: RewardType.points,
          value: 150,
          description: '150 punti WellJourney',
        ),
      ],
    );
  }

  /// Ottieni sfide di default in caso di errore
  List<AdvancedChallenge> _getDefaultChallenges() {
    final now = DateTime.now();
    return [
      AdvancedChallenge(
        id: 'default_hydration',
        title: 'Idratazione Base',
        description: 'Bevi almeno 8 bicchieri d\'acqua al giorno',
        category: ChallengeCategory.hydration,
        difficulty: ChallengeDifficulty.easy,
        type: ChallengeType.individual,
        durationDays: 7,
        targetValue: 8,
        unit: 'bicchieri/giorno',
        points: 100,
        startDate: now,
        endDate: now.add(const Duration(days: 7)),
        drStaffilanoMotivation: 'L\'acqua è vita! Una corretta idratazione è fondamentale per ogni funzione del nostro organismo.',
        trackingMethod: ChallengeTrackingMethod.manual,
        integrationFeatures: [ChallengeIntegration.nutriscore],
        rewards: [
          ChallengeReward(
            type: RewardType.points,
            value: 100,
            description: '100 punti WellJourney',
          ),
        ],
      ),
    ];
  }

  /// Avvia il tracking automatico dei progressi
  void _startProgressTracking() {
    _progressTimer?.cancel();
    _progressTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      _updateAllChallengeProgress();
    });
  }

  /// Aggiorna i progressi di tutte le sfide attive
  Future<void> _updateAllChallengeProgress() async {
    for (final challenge in _activeChallenges) {
      await _updateChallengeProgress(challenge);
    }
    notifyListeners();
  }

  /// Aggiorna il progresso di una sfida specifica
  Future<void> _updateChallengeProgress(AdvancedChallenge challenge) async {
    try {
      double progress = 0.0;

      switch (challenge.trackingMethod) {
        case ChallengeTrackingMethod.manual:
          // Il progresso manuale viene aggiornato dall'utente
          progress = _challengeProgress[challenge.id]?.currentProgress ?? 0.0;
          break;

        case ChallengeTrackingMethod.foodOracle:
          // Integrazione con Food Oracle per tracking automatico
          progress = await _calculateFoodOracleProgress(challenge);
          break;

        case ChallengeTrackingMethod.mealPlan:
          // Integrazione con Piano Alimentare
          progress = await _calculateMealPlanProgress(challenge);
          break;

        case ChallengeTrackingMethod.aiAnalysis:
          // Analisi AI avanzata
          progress = await _calculateAIProgress(challenge);
          break;

        case ChallengeTrackingMethod.automatic:
          // Tracking automatico generico
          progress = await _calculateAutomaticProgress(challenge);
          break;
      }

      _challengeProgress[challenge.id] = ChallengeProgress(
        challengeId: challenge.id,
        currentProgress: progress,
        lastUpdated: DateTime.now(),
        dailyProgress: _challengeProgress[challenge.id]?.dailyProgress ?? {},
        milestones: _challengeProgress[challenge.id]?.milestones ?? [],
      );

      // Controlla se la sfida è completata
      if (progress >= 1.0) {
        await _completeChallenge(challenge);
      }
    } catch (e) {
      print('❌ Errore nell\'aggiornamento progresso sfida ${challenge.id}: $e');
    }
  }

  /// Calcola il progresso basato su Food Oracle
  Future<double> _calculateFoodOracleProgress(AdvancedChallenge challenge) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now();
      final todayKey = '${today.year}-${today.month}-${today.day}';

      // Ottieni i dati delle scansioni Food Oracle di oggi
      final foodOracleDataJson = prefs.getString('food_oracle_scans_$todayKey');
      if (foodOracleDataJson == null) return 0.0;

      final foodOracleData = jsonDecode(foodOracleDataJson) as Map<String, dynamic>;
      final scannedFoods = List<Map<String, dynamic>>.from(foodOracleData['scanned_foods'] ?? []);

      // Calcola il progresso in base al tipo di sfida
      double progress = 0.0;

      switch (challenge.id.split('_')[1]) { // Estrae il tipo dalla sfida (es. 'daily_vegetables')
        case 'vegetables':
          progress = _calculateVegetableProgress(scannedFoods, challenge.targetValue.toDouble());
          break;
        case 'fish':
          progress = _calculateFishProgress(scannedFoods, challenge.targetValue.toDouble());
          break;
        case 'water':
          progress = _calculateWaterProgress(scannedFoods, challenge.targetValue.toDouble());
          break;
        case 'mediterranean':
          progress = _calculateMediterraneanProgress(scannedFoods, challenge.targetValue.toDouble());
          break;
        default:
          progress = _calculateGenericFoodProgress(scannedFoods, challenge);
      }

      return min(1.0, progress);
    } catch (e) {
      print('❌ Errore nel calcolo progresso Food Oracle: $e');
      return 0.0;
    }
  }

  /// Calcola progresso per sfide di verdure
  double _calculateVegetableProgress(List<Map<String, dynamic>> scannedFoods, double targetValue) {
    int vegetablePortions = 0;

    for (final foodData in scannedFoods) {
      final foodName = (foodData['name'] as String? ?? '').toLowerCase();
      final category = foodData['category'] as String? ?? '';

      // Identifica verdure per nome o categoria
      if (category.contains('verdura') ||
          foodName.contains('insalata') ||
          foodName.contains('pomodoro') ||
          foodName.contains('carota') ||
          foodName.contains('zucchina') ||
          foodName.contains('peperone') ||
          foodName.contains('spinaci') ||
          foodName.contains('broccoli')) {
        vegetablePortions++;
      }
    }

    return vegetablePortions / targetValue;
  }

  /// Calcola progresso per sfide di pesce
  double _calculateFishProgress(List<Map<String, dynamic>> scannedFoods, double targetValue) {
    int fishPortions = 0;

    for (final foodData in scannedFoods) {
      final foodName = (foodData['name'] as String? ?? '').toLowerCase();
      final category = foodData['category'] as String? ?? '';

      // Identifica pesce per nome o categoria
      if (category.contains('pesce') ||
          foodName.contains('salmone') ||
          foodName.contains('tonno') ||
          foodName.contains('branzino') ||
          foodName.contains('orata') ||
          foodName.contains('merluzzo') ||
          foodName.contains('sgombro')) {
        fishPortions++;
      }
    }

    return fishPortions / targetValue;
  }

  /// Calcola progresso per sfide di idratazione
  double _calculateWaterProgress(List<Map<String, dynamic>> scannedFoods, double targetValue) {
    int waterGlasses = 0;

    for (final foodData in scannedFoods) {
      final foodName = (foodData['name'] as String? ?? '').toLowerCase();

      // Identifica bevande idratanti
      if (foodName.contains('acqua') ||
          foodName.contains('tè') ||
          foodName.contains('tisana')) {
        waterGlasses++;
      }
    }

    return waterGlasses / targetValue;
  }

  /// Calcola progresso per sfide dieta mediterranea
  double _calculateMediterraneanProgress(List<Map<String, dynamic>> scannedFoods, double targetValue) {
    int mediterraneanFoods = 0;

    for (final foodData in scannedFoods) {
      final foodName = (foodData['name'] as String? ?? '').toLowerCase();

      // Identifica cibi mediterranei
      if (foodName.contains('olio d\'oliva') ||
          foodName.contains('pomodoro') ||
          foodName.contains('basilico') ||
          foodName.contains('mozzarella') ||
          foodName.contains('pasta') ||
          foodName.contains('pesce') ||
          foodName.contains('verdura') ||
          foodName.contains('frutta')) {
        mediterraneanFoods++;
      }
    }

    return mediterraneanFoods / targetValue;
  }

  /// Calcola progresso generico per altre sfide alimentari
  double _calculateGenericFoodProgress(List<Map<String, dynamic>> scannedFoods, AdvancedChallenge challenge) {
    // Progresso basato sul numero di scansioni effettuate
    return min(1.0, scannedFoods.length / challenge.targetValue);
  }

  /// Calcola il progresso basato su Piano Alimentare
  Future<double> _calculateMealPlanProgress(AdvancedChallenge challenge) async {
    // TODO: Implementare integrazione con Piano Alimentare
    // Per ora restituiamo un progresso simulato
    return min(1.0, Random().nextDouble() * 0.7 + 0.2);
  }

  /// Calcola il progresso con analisi AI
  Future<double> _calculateAIProgress(AdvancedChallenge challenge) async {
    // TODO: Implementare analisi AI avanzata
    // Per ora restituiamo un progresso simulato
    return min(1.0, Random().nextDouble() * 0.9 + 0.05);
  }

  /// Calcola il progresso automatico generico
  Future<double> _calculateAutomaticProgress(AdvancedChallenge challenge) async {
    // TODO: Implementare tracking automatico generico
    // Per ora restituiamo un progresso simulato
    return min(1.0, Random().nextDouble() * 0.6 + 0.3);
  }

  /// Completa una sfida
  Future<void> _completeChallenge(AdvancedChallenge challenge) async {
    try {
      // Rimuovi dalle sfide attive
      _activeChallenges.removeWhere((c) => c.id == challenge.id);

      // Aggiungi allo storico
      _challengeHistory.add(ChallengeHistory(
        challengeId: challenge.id,
        completedAt: DateTime.now(),
        finalProgress: 1.0,
        pointsEarned: challenge.points,
        rewardsEarned: challenge.rewards,
      ));

      // Assegna ricompense
      await _awardRewards(challenge.rewards);

      // Salva i dati
      await _saveData();

      print('🏆 Sfida completata: ${challenge.title} (+${challenge.points} punti)');
      notifyListeners();
    } catch (e) {
      print('❌ Errore nel completamento sfida: $e');
    }
  }

  /// Assegna le ricompense
  Future<void> _awardRewards(List<ChallengeReward> rewards) async {
    for (final reward in rewards) {
      switch (reward.type) {
        case RewardType.points:
          // Integrazione con WellJourney per punti
          await _awardWellJourneyPoints(reward.value.toInt());
          break;
        case RewardType.badge:
          // Integrazione con sistema badge
          await _awardBadge(reward.description);
          break;
        case RewardType.nutriScoreBoost:
          // Integrazione con NutriScore
          await _applyNutriScoreBoost(reward.value.toDouble());
          break;
        case RewardType.unlockFeature:
          // Sblocca funzionalità premium
          await _unlockFeature(reward.description);
          break;
        case RewardType.discount:
          // Applica sconto o offerta speciale
          await _applyDiscount(reward.description);
          break;
      }
    }
  }

  /// Assegna punti WellJourney
  Future<void> _awardWellJourneyPoints(int points) async {
    try {
      // TODO: Integrazione diretta con WellJourneyController
      // Per ora simuliamo l'assegnazione
      print('🎯 Assegnati $points punti WellJourney per completamento sfida');
    } catch (e) {
      print('❌ Errore nell\'assegnazione punti WellJourney: $e');
    }
  }

  /// Assegna un badge
  Future<void> _awardBadge(String badgeDescription) async {
    try {
      // TODO: Integrazione con sistema badge
      print('🏆 Badge assegnato: $badgeDescription');
    } catch (e) {
      print('❌ Errore nell\'assegnazione badge: $e');
    }
  }

  /// Applica boost al NutriScore
  Future<void> _applyNutriScoreBoost(double boost) async {
    try {
      // TODO: Integrazione con NutriScore
      print('📈 Boost NutriScore applicato: +$boost punti');
    } catch (e) {
      print('❌ Errore nell\'applicazione boost NutriScore: $e');
    }
  }

  /// Sblocca una funzionalità
  Future<void> _unlockFeature(String feature) async {
    try {
      // TODO: Implementare sblocco funzionalità
      print('🔓 Funzionalità sbloccata: $feature');
    } catch (e) {
      print('❌ Errore nello sblocco funzionalità: $e');
    }
  }

  /// Applica uno sconto
  Future<void> _applyDiscount(String discount) async {
    try {
      // TODO: Implementare sistema sconti
      print('💰 Sconto applicato: $discount');
    } catch (e) {
      print('❌ Errore nell\'applicazione sconto: $e');
    }
  }

  /// Partecipa a una sfida
  Future<bool> joinChallenge(String challengeId) async {
    try {
      final challenge = _availableChallenges.firstWhere(
        (c) => c.id == challengeId,
        orElse: () => throw Exception('Sfida non trovata'),
      );

      // Sposta dalle disponibili alle attive
      _availableChallenges.removeWhere((c) => c.id == challengeId);
      _activeChallenges.add(challenge);

      // Inizializza il progresso
      _challengeProgress[challengeId] = ChallengeProgress(
        challengeId: challengeId,
        currentProgress: 0.0,
        lastUpdated: DateTime.now(),
        dailyProgress: {},
        milestones: [],
      );

      await _saveData();
      notifyListeners();

      print('✅ Partecipazione alla sfida: ${challenge.title}');
      return true;
    } catch (e) {
      print('❌ Errore nella partecipazione alla sfida: $e');
      return false;
    }
  }

  /// Abbandona una sfida
  Future<bool> leaveChallenge(String challengeId) async {
    try {
      final challenge = _activeChallenges.firstWhere(
        (c) => c.id == challengeId,
        orElse: () => throw Exception('Sfida attiva non trovata'),
      );

      // Sposta dalle attive alle disponibili
      _activeChallenges.removeWhere((c) => c.id == challengeId);
      _availableChallenges.add(challenge);

      // Rimuovi il progresso
      _challengeProgress.remove(challengeId);

      await _saveData();
      notifyListeners();

      print('⚠️ Abbandonata sfida: ${challenge.title}');
      return true;
    } catch (e) {
      print('❌ Errore nell\'abbandono della sfida: $e');
      return false;
    }
  }

  /// Aggiorna manualmente il progresso di una sfida
  Future<void> updateManualProgress(String challengeId, double progress) async {
    if (_challengeProgress.containsKey(challengeId)) {
      final currentProgress = _challengeProgress[challengeId]!;
      _challengeProgress[challengeId] = currentProgress.copyWith(
        currentProgress: min(1.0, max(0.0, progress)),
        lastUpdated: DateTime.now(),
      );

      await _saveData();
      notifyListeners();
    }
  }

  /// Salva tutti i dati
  Future<void> _saveData() async {
    final storage = await StorageService.getInstance();

    // TODO: Implementare serializzazione JSON
    // await storage.setString(_activeChallengesKey, jsonEncode(_activeChallenges));
    // await storage.setString(_challengeProgressKey, jsonEncode(_challengeProgress));
    // await storage.setString(_challengeHistoryKey, jsonEncode(_challengeHistory));
  }

  /// Pulisce le risorse
  void dispose() {
    _progressTimer?.cancel();
    super.dispose();
  }
}
