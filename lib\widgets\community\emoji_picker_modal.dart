import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../theme/dr_staffilano_theme.dart';

/// Modal per la selezione degli emoji
class EmojiPickerModal extends StatefulWidget {
  final Function(String) onEmojiSelected;

  const EmojiPickerModal({
    Key? key,
    required this.onEmojiSelected,
  }) : super(key: key);

  static Future<void> show(
    BuildContext context, {
    required Function(String) onEmojiSelected,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: EmojiPickerModal(
          onEmojiSelected: onEmojiSelected,
        ),
      ),
    );
  }

  @override
  State<EmojiPickerModal> createState() => _EmojiPickerModalState();
}

class _EmojiPickerModalState extends State<EmojiPickerModal>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<String> _recentEmojis = [];

  // Categorie di emoji
  final Map<String, List<String>> _emojiCategories = {
    'Recenti': [],
    'Sorrisi': [
      '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
      '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
      '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
    ],
    'Persone': [
      '👶', '🧒', '👦', '👧', '🧑', '👱', '👨', '🧔', '👩', '🧓',
      '👴', '👵', '🙍', '🙎', '🙅', '🙆', '💁', '🙋', '🧏', '🙇',
      '🤦', '🤷', '👮', '🕵', '💂', '👷', '🤴', '👸', '👳', '👲',
    ],
    'Animali': [
      '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯',
      '🦁', '🐮', '🐷', '🐽', '🐸', '🐵', '🙈', '🙉', '🙊', '🐒',
      '🐔', '🐧', '🐦', '🐤', '🐣', '🐥', '🦆', '🦅', '🦉', '🦇',
    ],
    'Cibo': [
      '🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🫐', '🍈',
      '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦',
      '🥬', '🥒', '🌶', '🫑', '🌽', '🥕', '🫒', '🧄', '🧅', '🥔',
    ],
    'Attività': [
      '⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱',
      '🪀', '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🪃', '🥅', '⛳',
      '🪁', '🏹', '🎣', '🤿', '🥊', '🥋', '🎽', '🛹', '🛷', '⛸',
    ],
    'Viaggi': [
      '🚗', '🚕', '🚙', '🚌', '🚎', '🏎', '🚓', '🚑', '🚒', '🚐',
      '🛻', '🚚', '🚛', '🚜', '🏍', '🛵', '🚲', '🛴', '🛺', '🚨',
      '🚔', '🚍', '🚘', '🚖', '🚡', '🚠', '🚟', '🚃', '🚋', '🚞',
    ],
    'Oggetti': [
      '⌚', '📱', '📲', '💻', '⌨', '🖥', '🖨', '🖱', '🖲', '🕹',
      '🗜', '💽', '💾', '💿', '📀', '📼', '📷', '📸', '📹', '🎥',
      '📽', '🎞', '📞', '☎', '📟', '📠', '📺', '📻', '🎙', '🎚',
    ],
    'Simboli': [
      '❤', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
      '❣', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮',
      '✝', '☪', '🕉', '☸', '✡', '🔯', '🕎', '☯', '☦', '🛐',
    ],
    'Bandiere': [
      '🏁', '🚩', '🎌', '🏴', '🏳', '🏳‍🌈', '🏳‍⚧', '🏴‍☠', '🇦🇨', '🇦🇩',
      '🇦🇪', '🇦🇫', '🇦🇬', '🇦🇮', '🇦🇱', '🇦🇲', '🇦🇴', '🇦🇶', '🇦🇷', '🇦🇸',
      '🇦🇹', '🇦🇺', '🇦🇼', '🇦🇽', '🇦🇿', '🇧🇦', '🇧🇧', '🇧🇩', '🇧🇪', '🇧🇫',
    ],
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: _emojiCategories.length,
      vsync: this,
    );
    _loadRecentEmojis();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadRecentEmojis() {
    // TODO: Load from SharedPreferences
    _recentEmojis = ['😀', '❤', '👍', '😂', '🎉'];
    _emojiCategories['Recenti'] = _recentEmojis;
  }

  void _onEmojiTap(String emoji) {
    HapticFeedback.lightImpact();

    // Add to recent emojis
    if (!_recentEmojis.contains(emoji)) {
      _recentEmojis.insert(0, emoji);
      if (_recentEmojis.length > 20) {
        _recentEmojis = _recentEmojis.take(20).toList();
      }
      _emojiCategories['Recenti'] = _recentEmojis;
      // TODO: Save to SharedPreferences
    }

    widget.onEmojiSelected(emoji);
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final modalWidth = screenSize.width > 600 ? 500.0 : screenSize.width * 0.85;
    final modalHeight = screenSize.height * 0.6;

    return Container(
      width: modalWidth,
      height: modalHeight,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          Expanded(child: _buildEmojiGrid()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.withOpacity(0.2)),
        ),
      ),
      child: Row(
        children: [
          Icon(
            FontAwesomeIcons.faceSmile,
            color: DrStaffilanoTheme.accentGold,
            size: 20,
          ),
          const SizedBox(width: 12),
          const Text(
            'Seleziona emoji',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close),
            style: IconButton.styleFrom(
              backgroundColor: Colors.grey.withOpacity(0.1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return TabBar(
      controller: _tabController,
      isScrollable: true,
      labelColor: DrStaffilanoTheme.primaryGreen,
      unselectedLabelColor: Colors.grey[600],
      indicatorColor: DrStaffilanoTheme.primaryGreen,
      tabs: _emojiCategories.keys.map((category) {
        return Tab(text: category);
      }).toList(),
    );
  }

  Widget _buildEmojiGrid() {
    return TabBarView(
      controller: _tabController,
      children: _emojiCategories.values.map((emojis) {
        return GridView.builder(
          padding: const EdgeInsets.all(16),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 8,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          itemCount: emojis.length,
          itemBuilder: (context, index) {
            final emoji = emojis[index];
            return GestureDetector(
              onTap: () => _onEmojiTap(emoji),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey.withOpacity(0.1),
                ),
                child: Center(
                  child: Text(
                    emoji,
                    style: const TextStyle(fontSize: 24),
                  ),
                ),
              ),
            );
          },
        );
      }).toList(),
    );
  }
}
