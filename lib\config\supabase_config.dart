import 'package:supabase_flutter/supabase_flutter.dart';

/// Configurazione centralizzata per Supabase
/// 
/// IMPORTANTE: Sostituisci questi valori con le tue chiavi reali da Supabase
/// Le trovi in: Supabase Dashboard > Settings > API
class SupabaseConfig {
  // 🔑 SOSTITUISCI QUESTI VALORI CON I TUOI REALI
  static const String supabaseUrl = 'https://rnunzfuibfjpritvcfmj.supabase.co' ;
  static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJudW56ZnVpYmZqcHJpdHZjZm1qIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxODQ5NTIsImV4cCI6MjA2Mzc2MDk1Mn0.A-6qca5E0J06f7tkM3AAyz7d36qEdImcaPEzxhjx0X0';
  
  /// Inizializza Supabase
  /// Chiamare questo metodo nel main() prima di runApp()
  static Future<void> initialize() async {
    print('🔗 Inizializzazione Supabase...');
    print('📍 URL: $supabaseUrl');
    print('🔑 Anon Key: ${supabaseAnonKey.substring(0, 20)}...');

    await Supabase.initialize(
      url: supabaseUrl,
      anonKey: supabaseAnonKey,
      debug: true, // Imposta false in produzione
    );

    print('✅ Supabase inizializzato con successo');
    print('🔗 Client URL: ${Supabase.instance.client.supabaseUrl}');
  }
  
  /// Client Supabase globale
  static SupabaseClient get client => Supabase.instance.client;
  
  /// Auth helper
  static GoTrueClient get auth => client.auth;
  
  /// Database helper
  static SupabaseQueryBuilder from(String table) => client.from(table);
  
  /// Storage helper
  static SupabaseStorageClient get storage => client.storage;
  
  /// Realtime helper
  static RealtimeClient get realtime => client.realtime;
}

/// Extension per accesso rapido
extension SupabaseExtension on SupabaseClient {
  /// Shortcut per ottenere l'utente corrente
  User? get currentUser => auth.currentUser;
  
  /// Shortcut per verificare se l'utente è loggato
  bool get isLoggedIn => currentUser != null;
  
  /// Shortcut per ottenere l'ID dell'utente corrente
  String? get currentUserId => currentUser?.id;
}
