import 'package:supabase_flutter/supabase_flutter.dart';

/// Configurazione centralizzata per Supabase
/// 
/// IMPORTANTE: Sostituisci questi valori con le tue chiavi reali da Supabase
/// Le trovi in: Supabase Dashboard > Settings > API
class SupabaseConfig {
  // 🔑 SOSTITUISCI QUESTI VALORI CON I TUOI REALI
  static const String supabaseUrl = 'https://YOUR_PROJECT_REF.supabase.co';
  static const String supabaseAnonKey = 'YOUR_ANON_KEY_HERE';
  
  /// Inizializza Supabase
  /// Chiamare questo metodo nel main() prima di runApp()
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: supabaseUrl,
      anonKey: supabaseAnonKey,
      debug: true, // Imposta false in produzione
    );
  }
  
  /// Client Supabase globale
  static SupabaseClient get client => Supabase.instance.client;
  
  /// Auth helper
  static GoTrueClient get auth => client.auth;
  
  /// Database helper
  static SupabaseQueryBuilder from(String table) => client.from(table);
  
  /// Storage helper
  static SupabaseStorageClient get storage => client.storage;
  
  /// Realtime helper
  static RealtimeClient get realtime => client.realtime;
}

/// Extension per accesso rapido
extension SupabaseExtension on SupabaseClient {
  /// Shortcut per ottenere l'utente corrente
  User? get currentUser => auth.currentUser;
  
  /// Shortcut per verificare se l'utente è loggato
  bool get isLoggedIn => currentUser != null;
  
  /// Shortcut per ottenere l'ID dell'utente corrente
  String? get currentUserId => currentUser?.id;
}
