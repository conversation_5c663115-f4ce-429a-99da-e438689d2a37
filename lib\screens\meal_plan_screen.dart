import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/meal.dart';
import '../models/meal_plan.dart';
import '../models/diet_plan.dart';
import '../services/storage_service.dart';
import '../services/diet_statistics_service.dart';
import '../theme/dr_staffilano_theme.dart';
import '../widgets/meal_card.dart';
import '../widgets/responsive_button.dart';
import '../widgets/responsive_layout.dart';
import '../utils/responsive_utils.dart';
import 'add_meal_screen.dart';
import 'meal_detail_screen.dart';
import 'ultra_advanced_diet_screen.dart';
import 'stats_screen.dart';

class MealPlanScreen extends StatefulWidget {
  const MealPlanScreen({super.key});

  @override
  State<MealPlanScreen> createState() => _MealPlanScreenState();
}

class _MealPlanScreenState extends State<MealPlanScreen> {
  StorageService? _storageService;
  final DietStatisticsService _statsService = DietStatisticsService();
  WeeklyMealPlan? _pianoPasti;
  WeeklyDietPlan? _dietPlan;
  bool _isLoading = true;
  bool _hasGeneratedDiet = false;
  String? _lastIntegratedDietPlanId; // Track which diet plan was last integrated
  int _selectedDayIndex = 0;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    try {
      _storageService = await StorageService.getInstance();
      await _caricaPianoPasti();
    } catch (e) {
      print('Errore nell\'inizializzazione dei servizi: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Ricarica i dati quando la schermata diventa visibile
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkForNewDietPlan();
    });
  }

  /// Controlla se c'è un nuovo piano dietetico da integrare
  Future<void> _checkForNewDietPlan() async {
    if (_storageService == null) return;

    try {
      final dietPlan = await _storageService!.caricaDietPlan();
      if (dietPlan != null && _lastIntegratedDietPlanId != dietPlan.id) {
        print('Rilevato nuovo piano dietetico, ricarico...');
        await _caricaPianoPasti();
      }
    } catch (e) {
      print('Errore nel controllo del piano dietetico: $e');
    }
  }

  Future<void> _caricaPianoPasti() async {
    if (_storageService == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Carica il piano dietetico generato dall'AI
      final dietPlan = await _storageService!.caricaDietPlan();

      // Carica l'ID dell'ultimo piano integrato
      _lastIntegratedDietPlanId = await _storageService!.caricaLastIntegratedDietPlanId();

      // Carica o crea il piano pasti dell'app
      final piano = await _storageService!.getPianoSettimanaleCorrente();

      // Se esiste un piano dietetico e non è stato ancora integrato, integralo
      if (dietPlan != null && _lastIntegratedDietPlanId != dietPlan.id) {
        print('Integrazione nuovo piano dietetico: ${dietPlan.id}');
        await _integraDietPlan(dietPlan, piano);
        _lastIntegratedDietPlanId = dietPlan.id;

        // Salva l'ID del piano integrato
        await _storageService!.salvaLastIntegratedDietPlanId(dietPlan.id);
      }

      // Imposta il giorno selezionato al giorno corrente
      final now = DateTime.now();
      final today = DateFormat('yyyy-MM-dd').format(now);

      int dayIndex = piano.dailyPlans.indexWhere((plan) => plan.date == today);
      if (dayIndex == -1) {
        dayIndex = 0; // Se non troviamo il giorno corrente, seleziona il primo giorno
      }

      setState(() {
        _pianoPasti = piano;
        _dietPlan = dietPlan;
        _hasGeneratedDiet = dietPlan != null;
        _selectedDayIndex = dayIndex;
        _isLoading = false;
      });
    } catch (e) {
      print('Errore nel caricamento del piano pasti: $e');
      setState(() {
        _isLoading = false;
      });

      // Mostra un messaggio di errore
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Errore nel caricamento del piano pasti'),
            backgroundColor: DrStaffilanoTheme.errorRed,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  /// Integra il piano dietetico AI nel piano pasti dell'app
  Future<void> _integraDietPlan(WeeklyDietPlan dietPlan, WeeklyMealPlan pianoPasti) async {
    print('Inizio integrazione piano dietetico con ${dietPlan.dailyPlans.length} giorni');

    WeeklyMealPlan updatedPiano = pianoPasti;

    for (var dailyDietPlan in dietPlan.dailyPlans) {
      print('Integrazione giorno ${dailyDietPlan.date} con ${dailyDietPlan.meals.length} pasti');

      // Converti i PlannedMeal in Meal con tutti i dettagli
      final meals = dailyDietPlan.meals.map((plannedMeal) {
        // Converti i FoodPortion in FoodItem per mantenere i dettagli degli alimenti
        final foodItems = plannedMeal.foods.map((foodPortion) {
          return FoodItem(
            food: foodPortion.food,
            quantity: foodPortion.grams.toDouble(),
          );
        }).toList();

        return Meal(
          nome: plannedMeal.name,
          orario: plannedMeal.time,
          calorie: plannedMeal.totalCalories,
          proteine: double.parse(plannedMeal.getMacroValue('proteins').toStringAsFixed(1)),
          carboidrati: double.parse(plannedMeal.getMacroValue('carbs').toStringAsFixed(1)),
          grassi: double.parse(plannedMeal.getMacroValue('fats').toStringAsFixed(1)),
          completato: plannedMeal.isCompleted,
          foods: foodItems, // Mantieni i dettagli degli alimenti
        );
      }).toList();

      // Crea o aggiorna il piano giornaliero
      final dailyPlan = DailyPlan(
        date: dailyDietPlan.date,
        meals: meals,
      );

      // Aggiorna il piano settimanale
      updatedPiano = updatedPiano.updateDailyPlan(dailyPlan);
      print('Giorno ${dailyDietPlan.date} integrato con successo');
    }

    // Aggiorna la variabile di stato
    _pianoPasti = updatedPiano;

    // Salva il piano aggiornato
    if (_storageService != null) {
      await _storageService!.salvaPianoPasti(_pianoPasti!);
      print('Piano integrato e salvato con successo');
    }
  }

  Future<void> _aggiornaPianoPasti() async {
    if (_pianoPasti == null || _storageService == null) return;

    await _storageService!.salvaPianoPasti(_pianoPasti!);

    // Aggiorna anche le statistiche quando un pasto viene completato
    await _aggiornaStatistiche();
  }

  /// Aggiorna le statistiche basate sui pasti completati
  Future<void> _aggiornaStatistiche() async {
    try {
      // Usa il nuovo servizio statistiche per aggiornare automaticamente
      await _statsService.updateStatsOnMealCompletion();
    } catch (e) {
      print('Errore nell\'aggiornamento delle statistiche: $e');
      // Fallback al metodo precedente se necessario
      await _aggiornaStatisticheFallback();
    }
  }

  /// Metodo fallback per aggiornare le statistiche
  Future<void> _aggiornaStatisticheFallback() async {
    if (_pianoPasti == null || _storageService == null) return;

    final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
    final dailyPlan = _pianoPasti!.dailyPlans.firstWhere(
      (plan) => plan.date == today,
      orElse: () => DailyPlan(date: today, meals: []),
    );

    // Calcola le calorie e macronutrienti consumati oggi
    int calorieConsumate = 0;
    double proteineConsumate = 0;
    double carboidratiConsumati = 0;
    double grassiConsumati = 0;

    for (var meal in dailyPlan.meals) {
      if (meal.completato) {
        calorieConsumate += meal.calorie;
        proteineConsumate += meal.proteine;
        carboidratiConsumati += meal.carboidrati;
        grassiConsumati += meal.grassi;
      }
    }

    // Salva le statistiche
    await _storageService!.salvaCalorieConsumate(calorieConsumate);
    await _storageService!.salvaMacroConsumati({
      'proteine': proteineConsumate,
      'carboidrati': carboidratiConsumati,
      'grassi': grassiConsumati,
    });
  }

  void _aggiungiPasto(Meal nuovoPasto) {
    if (_pianoPasti == null) return;

    setState(() {
      final selectedDay = _pianoPasti!.dailyPlans[_selectedDayIndex];
      final updatedMeals = List<Meal>.from(selectedDay.meals)..add(nuovoPasto);
      updatedMeals.sort((a, b) => a.orario.compareTo(b.orario));

      final updatedDay = selectedDay.copyWith(meals: updatedMeals);
      final updatedPlan = _pianoPasti!.updateDailyPlan(updatedDay);

      _pianoPasti = updatedPlan;
    });

    _aggiornaPianoPasti();
  }

  void _aggiornaPasto(int index, Meal pastoAggiornato) {
    if (_pianoPasti == null) return;

    setState(() {
      final selectedDay = _pianoPasti!.dailyPlans[_selectedDayIndex];
      final updatedMeals = List<Meal>.from(selectedDay.meals);
      updatedMeals[index] = pastoAggiornato;

      final updatedDay = selectedDay.copyWith(meals: updatedMeals);
      final updatedPlan = _pianoPasti!.updateDailyPlan(updatedDay);

      _pianoPasti = updatedPlan;
    });

    _aggiornaPianoPasti();
  }

  void _togglePastoCompletato(int index) {
    if (_pianoPasti == null) return;

    setState(() {
      final selectedDay = _pianoPasti!.dailyPlans[_selectedDayIndex];
      final updatedMeals = List<Meal>.from(selectedDay.meals);
      updatedMeals[index] = updatedMeals[index].copyWith(
        completato: !updatedMeals[index].completato,
      );

      final updatedDay = selectedDay.copyWith(meals: updatedMeals);
      final updatedPlan = _pianoPasti!.updateDailyPlan(updatedDay);

      _pianoPasti = updatedPlan;
    });

    _aggiornaPianoPasti();
  }

  void _copiaPastiDaGiornoPrecedente() {
    if (_pianoPasti == null || _selectedDayIndex <= 0) return;

    setState(() {
      final previousDay = _pianoPasti!.dailyPlans[_selectedDayIndex - 1];
      final selectedDay = _pianoPasti!.dailyPlans[_selectedDayIndex];

      // Copia i pasti dal giorno precedente
      final copiedMeals = previousDay.meals.map((meal) {
        // Crea una copia del pasto con completato = false
        return meal.copyWith(completato: false);
      }).toList();

      final updatedDay = selectedDay.copyWith(meals: copiedMeals);
      final updatedPlan = _pianoPasti!.updateDailyPlan(updatedDay);

      _pianoPasti = updatedPlan;
    });

    _aggiornaPianoPasti();

    // Mostra un messaggio di conferma
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Pasti copiati dal giorno precedente'),
        backgroundColor: DrStaffilanoTheme.primaryGreen,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  String _formatDate(String dateString) {
    final date = DateTime.parse(dateString);
    return DateFormat('EEEE, d MMMM').format(date);
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                FontAwesomeIcons.utensils,
                size: 48,
                color: DrStaffilanoTheme.primaryGreen,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Nessun Piano Alimentare',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: DrStaffilanoTheme.textPrimary,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Inizia generando un piano personalizzato con l\'AI del Dr. Staffilano o aggiungi pasti manualmente.',
              style: TextStyle(
                fontSize: 16,
                color: DrStaffilanoTheme.textSecondary,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () async {
                  final result = await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const UltraAdvancedDietScreen(),
                    ),
                  );
                  if (result == true) {
                    _caricaPianoPasti();
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: DrStaffilanoTheme.primaryGreen,
                  foregroundColor: DrStaffilanoTheme.textOnPrimary,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
                icon: const Icon(
                  FontAwesomeIcons.wandMagicSparkles,
                  size: 18,
                ),
                label: const Text(
                  'Genera Piano AI',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.5,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DrStaffilanoTheme.backgroundLight,
      appBar: AppBar(
        title: const Text('Piano Alimentare'),
        backgroundColor: DrStaffilanoTheme.primaryGreen,
        foregroundColor: DrStaffilanoTheme.textOnPrimary,
        elevation: 0,
        actions: [
          if (!_hasGeneratedDiet)
            IconButton(
              icon: const Icon(FontAwesomeIcons.wandMagicSparkles),
              tooltip: 'Genera Piano AI',
              onPressed: () async {
                final result = await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const UltraAdvancedDietScreen(),
                  ),
                );
                if (result == true) {
                  // Forza il ricaricamento del piano pasti quando torniamo dal generatore
                  print('Ritorno dal generatore AI, ricarico piano pasti...');
                  await _caricaPianoPasti();
                }
              },
            ),
          IconButton(
            icon: const Icon(FontAwesomeIcons.chartLine),
            tooltip: 'Statistiche Dieta',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const StatsScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(FontAwesomeIcons.rotate),
            tooltip: 'Aggiorna',
            onPressed: _caricaPianoPasti,
          ),
        ],
      ),
      body: _isLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: DrStaffilanoTheme.primaryGreen,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Caricamento piano alimentare...',
                    style: TextStyle(
                      color: DrStaffilanoTheme.textSecondary,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            )
          : _pianoPasti == null
              ? _buildEmptyState()
              : Stack(
                  children: [
                    Column(
                  children: [
                    // Selettore giorni
                    Container(
                      height: 110, // Aumentato ulteriormente l'altezza per evitare l'overflow
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: _pianoPasti!.dailyPlans.length,
                        itemBuilder: (context, index) {
                          final day = _pianoPasti!.dailyPlans[index];
                          final date = DateTime.parse(day.date);
                          final isSelected = index == _selectedDayIndex;

                          // Formatta il giorno della settimana in modo più breve
                          final dayName = DateFormat('E').format(date).substring(0, 3);
                          final monthName = DateFormat('MMM').format(date).substring(0, 3);

                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedDayIndex = index;
                              });
                            },
                            child: AnimatedContainer(
                              duration: const Duration(milliseconds: 200),
                              width: 70,
                              margin: const EdgeInsets.symmetric(horizontal: 4),
                              decoration: BoxDecoration(
                                color: isSelected
                                    ? DrStaffilanoTheme.primaryGreen
                                    : DrStaffilanoTheme.backgroundWhite,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: isSelected
                                      ? DrStaffilanoTheme.primaryGreen
                                      : DrStaffilanoTheme.primaryGreen.withOpacity(0.2),
                                  width: 2,
                                ),
                                boxShadow: isSelected
                                    ? [
                                        BoxShadow(
                                          color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
                                          blurRadius: 8,
                                          offset: const Offset(0, 2),
                                        ),
                                      ]
                                    : [
                                        BoxShadow(
                                          color: DrStaffilanoTheme.shadowLight,
                                          blurRadius: 4,
                                          offset: const Offset(0, 1),
                                        ),
                                      ],
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    // Giorno della settimana
                                    Container(
                                      margin: const EdgeInsets.only(bottom: 4),
                                      child: Text(
                                        dayName,
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: isSelected ? DrStaffilanoTheme.textOnPrimary : DrStaffilanoTheme.textPrimary,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                    // Numero del giorno
                                    Container(
                                      margin: const EdgeInsets.only(bottom: 4),
                                      child: Text(
                                        date.day.toString(),
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                          color: isSelected ? DrStaffilanoTheme.textOnPrimary : DrStaffilanoTheme.textPrimary,
                                        ),
                                      ),
                                    ),
                                    // Mese
                                    Text(
                                      monthName,
                                      style: TextStyle(
                                        fontSize: 11,
                                        color: isSelected
                                            ? DrStaffilanoTheme.textOnPrimary.withOpacity(0.8)
                                            : DrStaffilanoTheme.textSecondary,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),

                    // Intestazione giorno selezionato
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        _formatDate(_pianoPasti!.dailyPlans[_selectedDayIndex].date),
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                    // Lista pasti
                    Expanded(
                      child: _pianoPasti!.dailyPlans[_selectedDayIndex].meals.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    FontAwesomeIcons.utensils,
                                    size: 48,
                                    color: Colors.grey.shade400,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'Nessun pasto pianificato',
                                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                      color: DrStaffilanoTheme.textSecondary,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    _hasGeneratedDiet
                                        ? 'I tuoi pasti generati dall\'AI appariranno qui'
                                        : 'Genera un piano con l\'AI o aggiungi pasti manualmente',
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: DrStaffilanoTheme.textSecondary,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            )
                          : ListView.builder(
                              padding: const EdgeInsets.only(bottom: 100), // Increased padding for floating button
                              itemCount: _pianoPasti!.dailyPlans[_selectedDayIndex].meals.length,
                              itemBuilder: (context, index) {
                                final pasto = _pianoPasti!.dailyPlans[_selectedDayIndex].meals[index];
                                return MealCard(
                                  meal: pasto,
                                  onTap: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => MealDetailScreen(
                                          pasto: pasto,
                                          onSave: (pastoAggiornato) {
                                            _aggiornaPasto(index, pastoAggiornato);
                                          },
                                        ),
                                      ),
                                    );
                                  },
                                  onCompletedChanged: (value) {
                                    _togglePastoCompletato(index);
                                  },
                                );
                              },
                            ),
                    ),


                    ],
                  ),
                  // Floating Action Button
                  Positioned(
                    bottom: 20,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: SizedBox(
                        width: 280,
                        height: 56,
                        child: ElevatedButton.icon(
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => AddMealScreen(
                                  onAdd: _aggiungiPasto,
                                  initialDate: _pianoPasti?.dailyPlans[_selectedDayIndex].date,
                                ),
                              ),
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: DrStaffilanoTheme.primaryGreen,
                            foregroundColor: DrStaffilanoTheme.textOnPrimary,
                            elevation: 8,
                            shadowColor: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(28),
                            ),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 16,
                            ),
                          ),
                          icon: Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: DrStaffilanoTheme.textOnPrimary.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: const Icon(
                              FontAwesomeIcons.plus,
                              size: 16,
                            ),
                          ),
                          label: const Text(
                            'Aggiungi Nuovo Pasto',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 0.3,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  // Secondary Action Button - Copia da ieri (Responsive)
                  if (_selectedDayIndex > 0)
                    Positioned(
                      top: ResponsiveUtils.isMobile(context) ? 20 : 24,
                      right: ResponsiveUtils.isMobile(context) ? 20 : 24,
                      child: ResponsiveButton(
                        text: 'Copia ieri',
                        icon: FontAwesomeIcons.copy,
                        onPressed: _copiaPastiDaGiornoPrecedente,
                        type: ButtonType.primary,
                        size: ResponsiveUtils.isMobile(context)
                            ? ButtonSize.medium
                            : ButtonSize.large,
                        backgroundColor: DrStaffilanoTheme.primaryGreen,
                        foregroundColor: DrStaffilanoTheme.textOnPrimary,
                        width: ResponsiveUtils.getResponsiveWidth(context,
                          mobileRatio: 0.35,
                          tabletRatio: 0.25,
                          desktopRatio: 0.2,
                        ),
                      ),
                    ),
                ],
              ),
    );
  }
}
