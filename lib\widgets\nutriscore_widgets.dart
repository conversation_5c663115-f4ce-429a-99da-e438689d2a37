import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;
import '../services/nutriscore_service.dart';
import '../controllers/welljourney_controller.dart';
import '../theme/dr_staffilano_theme.dart';

/// Widget per il punteggio NutriScore principale con animazione circolare
class NutriScoreCircularWidget extends StatefulWidget {
  final NutriScoreResult result;
  final bool animate;

  const NutriScoreCircularWidget({
    Key? key,
    required this.result,
    this.animate = true,
  }) : super(key: key);

  @override
  State<NutriScoreCircularWidget> createState() => _NutriScoreCircularWidgetState();
}

class _NutriScoreCircularWidgetState extends State<NutriScoreCircularWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scoreAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    if (widget.animate) {
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _scoreAnimation = Tween<double>(
      begin: 0,
      end: widget.result.totalScore,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        final animatedScore = widget.animate ? _scoreAnimation.value : widget.result.totalScore;
        final pulseScale = widget.animate ? _pulseAnimation.value : 1.0;

        return Transform.scale(
          scale: pulseScale,
          child: Container(
            width: 200,
            height: 200,
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Cerchio di sfondo
                Container(
                  width: 180,
                  height: 180,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.grey.shade100,
                    boxShadow: [
                      BoxShadow(
                        color: DrStaffilanoTheme.primaryGreen.withOpacity(0.2),
                        blurRadius: 20,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                ),
                // Indicatore di progresso circolare
                SizedBox(
                  width: 180,
                  height: 180,
                  child: CircularProgressIndicator(
                    value: animatedScore / 100,
                    strokeWidth: 12,
                    backgroundColor: Colors.grey.shade200,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _getScoreColor(animatedScore),
                    ),
                  ),
                ),
                // Contenuto centrale
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${animatedScore.round()}',
                      style: TextStyle(
                        fontSize: 48,
                        fontWeight: FontWeight.bold,
                        color: _getScoreColor(animatedScore),
                      ),
                    ),
                    Text(
                      '/100',
                      style: TextStyle(
                        fontSize: 16,
                        color: DrStaffilanoTheme.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getScoreColor(animatedScore).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        widget.result.level.displayName,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: _getScoreColor(animatedScore),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Color _getScoreColor(double score) {
    if (score >= 90) return const Color(0xFF2E7D32); // Verde scuro
    if (score >= 75) return DrStaffilanoTheme.primaryGreen;
    if (score >= 60) return DrStaffilanoTheme.accentGold;
    if (score >= 40) return Colors.orange;
    return Colors.red;
  }
}

/// Widget per la breakdown dei punteggi per categoria
class NutriScoreBreakdownWidget extends StatelessWidget {
  final NutriScoreResult result;

  const NutriScoreBreakdownWidget({
    Key? key,
    required this.result,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.chartPie,
                color: DrStaffilanoTheme.primaryGreen,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'Analisi Dettagliata',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: DrStaffilanoTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildScoreBar(
            'Conoscenza Educativa',
            result.educationalScore,
            FontAwesomeIcons.graduationCap,
            DrStaffilanoTheme.primaryGreen,
          ),
          const SizedBox(height: 16),
          _buildScoreBar(
            'Consistenza',
            result.consistencyScore,
            FontAwesomeIcons.calendar,
            DrStaffilanoTheme.secondaryBlue,
          ),
          const SizedBox(height: 16),
          _buildScoreBar(
            'Applicazione Pratica',
            result.applicationScore,
            FontAwesomeIcons.handHoldingHeart,
            DrStaffilanoTheme.accentGold,
          ),
          const SizedBox(height: 16),
          _buildScoreBar(
            'Expertise Avanzata',
            result.expertiseScore,
            FontAwesomeIcons.award,
            Colors.purple,
          ),
        ],
      ),
    );
  }

  Widget _buildScoreBar(String title, double score, IconData icon, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: DrStaffilanoTheme.textPrimary,
                ),
              ),
            ),
            Text(
              '${score.round()}%',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          height: 8,
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(4),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: score / 100,
            child: Container(
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// Widget per le raccomandazioni personalizzate
class NutriScoreRecommendationsWidget extends StatelessWidget {
  final NutriScoreResult result;

  const NutriScoreRecommendationsWidget({
    Key? key,
    required this.result,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            DrStaffilanoTheme.secondaryBlue.withOpacity(0.05),
            DrStaffilanoTheme.secondaryBlue.withOpacity(0.02),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: DrStaffilanoTheme.secondaryBlue.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.userDoctor,
                color: DrStaffilanoTheme.secondaryBlue,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'Consigli del Dr. Staffilano',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: DrStaffilanoTheme.secondaryBlue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...result.recommendations.map((recommendation) => Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  FontAwesomeIcons.quoteLeft,
                  size: 12,
                  color: DrStaffilanoTheme.secondaryBlue.withOpacity(0.6),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    recommendation,
                    style: TextStyle(
                      fontSize: 14,
                      color: DrStaffilanoTheme.textPrimary,
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          )).toList(),
        ],
      ),
    );
  }
}

/// Widget per il prossimo traguardo
class NutriScoreMilestoneWidget extends StatelessWidget {
  final NutriScoreResult result;

  const NutriScoreMilestoneWidget({
    Key? key,
    required this.result,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final milestone = result.nextMilestone;
    final progress = result.totalScore / milestone.targetScore;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.accentGold.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.trophy,
                color: DrStaffilanoTheme.accentGold,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'Prossimo Traguardo',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: DrStaffilanoTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            milestone.title,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: DrStaffilanoTheme.accentGold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            milestone.description,
            style: TextStyle(
              fontSize: 14,
              color: DrStaffilanoTheme.textSecondary,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 8,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade200,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: progress.clamp(0.0, 1.0),
                    child: Container(
                      decoration: BoxDecoration(
                        color: DrStaffilanoTheme.accentGold,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Text(
                '${milestone.pointsNeeded} punti',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: DrStaffilanoTheme.accentGold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// Widget per la barra di progresso dei punti WellJourney animata
class WellJourneyPointsProgressWidget extends StatefulWidget {
  final bool animate;
  final double height;
  final EdgeInsets margin;

  const WellJourneyPointsProgressWidget({
    Key? key,
    this.animate = true,
    this.height = 60.0,
    this.margin = const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
  }) : super(key: key);

  @override
  State<WellJourneyPointsProgressWidget> createState() => _WellJourneyPointsProgressWidgetState();
}

class _WellJourneyPointsProgressWidgetState extends State<WellJourneyPointsProgressWidget>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _pulseController;
  late Animation<double> _progressAnimation;
  late Animation<double> _pulseAnimation;

  int _previousPoints = 0;
  int _currentPoints = 0;
  bool _showPointsGain = false;

  @override
  void initState() {
    super.initState();

    _progressController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeOutCubic,
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.elasticOut,
    ));

    if (widget.animate) {
      _progressController.forward();
    } else {
      _progressController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _progressController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _animatePointsGain(int newPoints) {
    if (newPoints > _currentPoints) {
      setState(() {
        _previousPoints = _currentPoints;
        _currentPoints = newPoints;
        _showPointsGain = true;
      });

      _pulseController.forward().then((_) {
        _pulseController.reverse();
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            setState(() {
              _showPointsGain = false;
            });
          }
        });
      });
    } else {
      setState(() {
        _currentPoints = newPoints;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<WellJourneyController>(
      builder: (context, controller, child) {
        final userProgress = controller.userProgress;
        final totalPoints = userProgress?.totalPoints ?? 0;

        // Anima i punti se sono cambiati
        if (totalPoints != _currentPoints) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _animatePointsGain(totalPoints);
          });
        }

        // Calcola il progresso verso il prossimo livello (ogni 1000 punti)
        const pointsPerLevel = 1000;
        final currentLevel = (totalPoints / pointsPerLevel).floor() + 1;
        final pointsInCurrentLevel = totalPoints % pointsPerLevel;
        final progressToNextLevel = pointsInCurrentLevel / pointsPerLevel;
        final pointsToNextLevel = pointsPerLevel - pointsInCurrentLevel;

        return Container(
          margin: widget.margin,
          child: AnimatedBuilder(
            animation: _progressAnimation,
            builder: (context, child) {
              return AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _showPointsGain ? _pulseAnimation.value : 1.0,
                    child: Container(
                      height: widget.height,
                      padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 6.0),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                            DrStaffilanoTheme.professionalBlue.withOpacity(0.1),
                          ],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
                          width: 1,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          // Sezione sinistra: Icona e titolo
                          Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: DrStaffilanoTheme.accentGold.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: DrStaffilanoTheme.accentGold.withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.stars_rounded,
                                  color: DrStaffilanoTheme.accentGold,
                                  size: 18,
                                ),
                                const SizedBox(height: 1),
                                Text(
                                  'WellJourney™',
                                  style: TextStyle(
                                    fontSize: 8,
                                    fontWeight: FontWeight.w600,
                                    color: DrStaffilanoTheme.textPrimary,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(width: 12),

                          // Sezione centrale: Progresso e punti
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Header con punti
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      '$totalPoints punti',
                                      style: TextStyle(
                                        fontSize: 15,
                                        fontWeight: FontWeight.bold,
                                        color: DrStaffilanoTheme.primaryGreen,
                                      ),
                                    ),
                                    // Animazione punti guadagnati
                                    if (_showPointsGain && totalPoints > _previousPoints)
                                      Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                        decoration: BoxDecoration(
                                          color: DrStaffilanoTheme.accentGold,
                                          borderRadius: BorderRadius.circular(10),
                                          boxShadow: [
                                            BoxShadow(
                                              color: DrStaffilanoTheme.accentGold.withOpacity(0.3),
                                              blurRadius: 3,
                                              offset: const Offset(0, 1),
                                            ),
                                          ],
                                        ),
                                        child: Text(
                                          '+${totalPoints - _previousPoints}',
                                          style: const TextStyle(
                                            fontSize: 10,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),

                                const SizedBox(height: 6),

                                // Barra di progresso elegante
                                Stack(
                                  children: [
                                    // Background della barra
                                    Container(
                                      height: 6,
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade200,
                                        borderRadius: BorderRadius.circular(3),
                                      ),
                                    ),
                                    // Progresso animato
                                    FractionallySizedBox(
                                      alignment: Alignment.centerLeft,
                                      widthFactor: (progressToNextLevel * _progressAnimation.value).clamp(0.0, 1.0),
                                      child: Container(
                                        height: 6,
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                            colors: [
                                              DrStaffilanoTheme.primaryGreen,
                                              DrStaffilanoTheme.accentGold,
                                            ],
                                            stops: const [0.0, 1.0],
                                          ),
                                          borderRadius: BorderRadius.circular(3),
                                          boxShadow: [
                                            BoxShadow(
                                              color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
                                              blurRadius: 4,
                                              offset: const Offset(0, 1),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),

                                const SizedBox(height: 4),

                                // Info livello e progresso
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Livello $currentLevel',
                                      style: TextStyle(
                                        fontSize: 10,
                                        fontWeight: FontWeight.w600,
                                        color: DrStaffilanoTheme.professionalBlue,
                                      ),
                                    ),
                                    Text(
                                      '$pointsToNextLevel al livello ${currentLevel + 1}',
                                      style: TextStyle(
                                        fontSize: 9,
                                        fontWeight: FontWeight.w500,
                                        color: DrStaffilanoTheme.textSecondary,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          ),
        );
      },
    );
  }
}

/// Widget per le aree di forza e miglioramento
class NutriScoreInsightsWidget extends StatelessWidget {
  final NutriScoreResult result;

  const NutriScoreInsightsWidget({
    Key? key,
    required this.result,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Analisi Personalizzata',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: DrStaffilanoTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 20),
          if (result.strengthAreas.isNotEmpty) ...[
            _buildInsightSection(
              'Punti di Forza',
              result.strengthAreas,
              FontAwesomeIcons.check,
              Colors.green,
            ),
            const SizedBox(height: 16),
          ],
          if (result.improvementAreas.isNotEmpty) ...[
            _buildInsightSection(
              'Aree di Miglioramento',
              result.improvementAreas,
              FontAwesomeIcons.exclamationTriangle,
              Colors.orange,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInsightSection(String title, List<String> items, IconData icon, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ...items.map((item) => Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('• ', style: TextStyle(color: color)),
              Expanded(
                child: Text(
                  item,
                  style: TextStyle(
                    fontSize: 14,
                    color: DrStaffilanoTheme.textPrimary,
                  ),
                ),
              ),
            ],
          ),
        )).toList(),
      ],
    );
  }
}
