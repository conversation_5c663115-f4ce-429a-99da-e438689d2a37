import os
import requests
import tensorflow as tf
import tensorflow_hub as hub
from tensorflow.lite.python.interpreter import Interpreter

# URL del modello MobileNet pre-addestrato
MODEL_URL = "https://tfhub.dev/google/lite-model/aiy/vision/classifier/food_V1/1"

# Percorso di output
OUTPUT_DIR = "../assets/ml_models"
OUTPUT_FILE = "food_classifier.tflite"

def download_model():
    """Scarica il modello TensorFlow Lite pre-addestrato."""
    print(f"Scaricamento del modello da {MODEL_URL}...")
    
    # Crea la directory di output se non esiste
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # Percorso completo del file di output
    output_path = os.path.join(OUTPUT_DIR, OUTPUT_FILE)
    
    try:
        # Scarica il modello
        model = hub.load(MODEL_URL)
        
        # Salva il modello in formato TensorFlow Lite
        converter = tf.lite.TFLiteConverter.from_keras_model(model)
        tflite_model = converter.convert()
        
        # Salva il modello
        with open(output_path, 'wb') as f:
            f.write(tflite_model)
        
        print(f"Modello salvato in {output_path}")
        
        # Verifica il modello
        interpreter = Interpreter(model_path=output_path)
        interpreter.allocate_tensors()
        
        # Ottieni informazioni sul modello
        input_details = interpreter.get_input_details()
        output_details = interpreter.get_output_details()
        
        print(f"Dettagli di input: {input_details}")
        print(f"Dettagli di output: {output_details}")
        
        print("Modello verificato con successo!")
    except Exception as e:
        print(f"Errore durante il download del modello: {e}")
        
        # Fallback: scarica direttamente il file
        try:
            print("Tentativo di download diretto...")
            response = requests.get(MODEL_URL)
            
            if response.status_code == 200:
                with open(output_path, 'wb') as f:
                    f.write(response.content)
                print(f"Modello salvato in {output_path}")
            else:
                print(f"Errore nel download diretto: {response.status_code}")
        except Exception as e:
            print(f"Errore nel download diretto: {e}")

if __name__ == "__main__":
    download_model()
