import 'package:flutter/material.dart';
import '../models/ultra_detailed_profile.dart';
import '../models/diet_plan.dart';
import '../models/food.dart';
import '../models/user_profile.dart';
import '../services/ultra_advanced_diet_generator.dart';
import '../services/ultra_profile_service.dart';
import '../widgets/planned_meal_card.dart';
import '../widgets/portion_analysis_widget.dart';
import '../theme/app_theme.dart';

/// SCHERMATA TEST PER SISTEMA ULTRA-AVANZATO
/// Testa tutte le funzionalità del nuovo generatore di diete
class UltraDietTestScreen extends StatefulWidget {
  const UltraDietTestScreen({Key? key}) : super(key: key);

  @override
  State<UltraDietTestScreen> createState() => _UltraDietTestScreenState();
}

class _UltraDietTestScreenState extends State<UltraDietTestScreen> {
  UltraDetailedProfile? _profile;
  DailyDietPlan? _dailyPlan;
  bool _isLoading = true;
  String _errorMessage = '';
  bool _showAnalysis = false;
  bool _enableRefeedDay = false;

  @override
  void initState() {
    super.initState();
    _initializeProfile();
  }

  Future<void> _initializeProfile() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      final profileService = UltraProfileService();

      // Prova a caricare profilo esistente o crea uno demo
      UltraDetailedProfile profile;
      final existing = await profileService.loadUltraProfile();

      if (existing != null) {
        profile = existing;
        print('📱 Profilo esistente caricato');
      } else {
        // Crea profilo demo ultra-dettagliato
        profile = await _createDemoProfile(profileService);
        print('🆕 Profilo demo creato');
      }

      setState(() {
        _profile = profile;
        _isLoading = false;
      });

      // Genera piano automaticamente
      await _generateUltraPlan();

    } catch (e) {
      setState(() {
        _errorMessage = 'Errore inizializzazione: $e';
        _isLoading = false;
      });
    }
  }

  Future<UltraDetailedProfile> _createDemoProfile(UltraProfileService service) async {
    return await service.createUltraProfile(
      name: 'Dr. Staffilano Demo',
      age: 35,
      gender: Gender.male,
      weight: 75.0,
      height: 175.0,
      goal: Goal.maintenance,

      // Dati avanzati demo
      bodyFatPercentage: 15.0,
      bodyCompositionMethod: BodyCompositionMethod.bioelectricalImpedance,

      // Attività fisica
      workActivity: WorkActivity.light,
      plannedExercises: [
        PlannedExercise(
          name: 'Corsa',
          type: ExerciseType.cardio,
          durationMinutes: 45,
          frequencyPerWeek: 3,
          intensity: ExerciseIntensity.moderate,
          metValue: 8.0,
        ),
        PlannedExercise(
          name: 'Pesi',
          type: ExerciseType.strength,
          durationMinutes: 60,
          frequencyPerWeek: 2,
          intensity: ExerciseIntensity.vigorous,
          metValue: 6.0,
        ),
      ],
      neatLevel: NEATLevel.high,

      // Obiettivi
      primaryGoal: PrimaryGoal.bodyRecomposition,
      secondaryGoals: [SecondaryGoal.improveHealth, SecondaryGoal.increaseEnergy],

      // Preferenze alimentari
      dietaryRegimen: DietaryRegimen.mediterranean,
      preferredFoods: ['Salmone', 'Avocado', 'Quinoa', 'Spinaci'],
      dislikedFoods: ['Fegato'],
      budgetLevel: BudgetLevel.high,
      cookingTime: CookingTime.medium,
      mealsPerDay: 4,
      cookingSkillLevel: CookingSkillLevel.intermediate,

      // Preferenze avanzate
      allowCheatMeals: true,
      cheatMealsPerWeek: 1,
      preferOrganicFoods: true,
      enableCalorieCycling: true,
      enableMealTiming: true,
      hasConsultedDoctor: true,
    );
  }

  Future<void> _generateUltraPlan() async {
    if (_profile == null) return;

    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      final generator = await UltraAdvancedDietGenerator.getInstance();
      final plan = await generator.generateUltraPersonalizedDiet(
        profile: _profile!,
        targetDate: DateTime.now(),
        enableRefeedDay: _enableRefeedDay,
      );

      setState(() {
        _dailyPlan = plan;
        _isLoading = false;
      });

    } catch (e) {
      setState(() {
        _errorMessage = 'Errore generazione piano: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Sistema Ultra-Avanzato'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(_showAnalysis ? Icons.visibility_off : Icons.analytics),
            tooltip: _showAnalysis ? 'Nascondi Analisi' : 'Mostra Analisi Avanzata',
            onPressed: () {
              setState(() {
                _showAnalysis = !_showAnalysis;
              });
            },
          ),
          IconButton(
            icon: Icon(_enableRefeedDay ? Icons.restaurant : Icons.restaurant_outlined),
            tooltip: _enableRefeedDay ? 'Disabilita Refeed Day' : 'Abilita Refeed Day',
            onPressed: () {
              setState(() {
                _enableRefeedDay = !_enableRefeedDay;
              });
              _generateUltraPlan();
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _generateUltraPlan,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Generazione piano ultra-avanzato...'),
          ],
        ),
      );
    }

    if (_errorMessage.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              'Errore Sistema Ultra-Avanzato',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _errorMessage,
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.grey[600]),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _initializeProfile,
              child: const Text('Riprova'),
            ),
          ],
        ),
      );
    }

    if (_profile == null || _dailyPlan == null) {
      return const Center(
        child: Text('Nessun dato disponibile'),
      );
    }

    return Column(
      children: [
        // Header con informazioni profilo e piano
        _buildProfileHeader(),

        // Statistiche piano
        _buildPlanStatistics(),

        // Lista pasti con analisi
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(vertical: 8),
            itemCount: _dailyPlan!.meals.length,
            itemBuilder: (context, index) {
              final meal = _dailyPlan!.meals[index];
              return Column(
                children: [
                  PlannedMealCard(
                    meal: meal,
                    onCompletedChanged: (isCompleted) => _onMealCompletedChanged(meal.id, isCompleted),
                    onFoodReplaced: _onFoodReplaced,
                  ),

                  // Analisi avanzata (se abilitata)
                  if (_showAnalysis) ...[
                    PortionAnalysisWidget(
                      meal: meal,
                      targetCalories: _calculateMealTargetCalories(index),
                      targetMacros: _calculateMealTargetMacros(index),
                    ),
                    _buildAdvancedMealAnalysis(meal, index),
                  ],
                ],
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildProfileHeader() {
    final validation = _profile!.validateNutritionalSafety();

    return Container(
      padding: const EdgeInsets.all(16),
      color: AppTheme.primaryColor.withOpacity(0.1),
      child: Column(
        children: [
          Row(
            children: [
              CircleAvatar(
                backgroundColor: AppTheme.primaryColor,
                child: Text(
                  _profile!.baseProfile.name[0].toUpperCase(),
                  style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _profile!.baseProfile.name,
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    Text(
                      '${_profile!.baseProfile.age} anni • ${_profile!.baseProfile.weight}kg • ${_profile!.primaryGoal.toString().split('.').last}',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
              if (_enableRefeedDay)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.accentColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'REFEED DAY',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),

          const SizedBox(height: 12),

          // Indicatori validazione
          Row(
            children: [
              _buildValidationIndicator(
                'Sicurezza',
                validation['isValid'],
                validation['isValid'] ? Icons.check_circle : Icons.warning,
              ),
              const SizedBox(width: 16),
              _buildValidationIndicator(
                'Supervisione Medica',
                !validation['requiresMedicalSupervision'],
                validation['requiresMedicalSupervision'] ? Icons.local_hospital : Icons.check,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildValidationIndicator(String label, bool isGood, IconData icon) {
    final color = isGood ? Colors.green : Colors.orange;

    return Row(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 4),
        Text(
          label,
          style: TextStyle(
            color: color,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildPlanStatistics() {
    final totalCalories = _dailyPlan!.meals.fold(0, (sum, meal) =>
      sum + meal.foods.fold(0, (mealSum, portion) => mealSum + portion.calories));

    final bmr = _profile!.calculateBMR();
    final tdee = _profile!.calculateTDEE();

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildStatChip('BMR', '${bmr.round()} kcal', AppTheme.secondaryColor),
          _buildStatChip('TDEE', '${tdee.round()} kcal', AppTheme.primaryColor),
          _buildStatChip('Target', '${_dailyPlan!.calorieTarget} kcal', AppTheme.accentColor),
          _buildStatChip('Attuale', '${totalCalories} kcal',
            totalCalories <= _dailyPlan!.calorieTarget * 1.05 ? Colors.green : Colors.orange),
        ],
      ),
    );
  }

  Widget _buildStatChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancedMealAnalysis(PlannedMeal meal, int index) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Analisi Avanzata ${meal.name}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 8),

            // Timing ottimale
            Row(
              children: [
                Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  'Timing: ${meal.time} (ottimale per ${meal.type.toString().split('.').last})',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),

            // Densità nutrizionale
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.star, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  'Densità nutrizionale: ${_calculateNutritionalDensity(meal).toStringAsFixed(1)}/10',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),

            // Compatibilità regime
            if (_profile!.dietaryRegimen != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(Icons.check_circle, size: 16, color: Colors.green),
                  const SizedBox(width: 4),
                  Text(
                    'Compatibile con ${_profile!.dietaryRegimen.toString().split('.').last}',
                    style: const TextStyle(fontSize: 12, color: Colors.green),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  double _calculateNutritionalDensity(PlannedMeal meal) {
    // Calcolo semplificato della densità nutrizionale
    final totalCalories = meal.foods.fold(0, (sum, portion) => sum + portion.calories);
    final totalProteins = meal.foods.fold(0.0, (sum, portion) => sum + portion.proteins);
    final totalFiber = meal.foods.fold(0.0, (sum, portion) => sum + portion.fiber);

    if (totalCalories == 0) return 0;

    final proteinDensity = (totalProteins / totalCalories) * 100;
    final fiberDensity = (totalFiber / totalCalories) * 100;

    return ((proteinDensity + fiberDensity) * 2).clamp(0, 10);
  }

  int _calculateMealTargetCalories(int mealIndex) {
    if (_dailyPlan == null) return 500;

    final totalCalories = _dailyPlan!.calorieTarget;
    final mealsCount = _dailyPlan!.meals.length;

    // Distribuzione semplificata
    switch (mealsCount) {
      case 3:
        final percentages = [0.25, 0.40, 0.35];
        return (totalCalories * percentages[mealIndex]).round();
      case 4:
        final percentages = [0.20, 0.10, 0.35, 0.35];
        return (totalCalories * percentages[mealIndex]).round();
      default:
        return (totalCalories / mealsCount).round();
    }
  }

  Map<String, int> _calculateMealTargetMacros(int mealIndex) {
    final targetCalories = _calculateMealTargetCalories(mealIndex);

    // Distribuzione macronutrienti basata sul profilo
    final macros = _profile!.calculateMacroDistribution();
    final totalCalories = _profile!.calculateCalorieTarget();

    final mealFactor = targetCalories / totalCalories;

    return {
      'proteins': (macros['proteins']! * mealFactor).round(),
      'carbs': (macros['carbs']! * mealFactor).round(),
      'fats': (macros['fats']! * mealFactor).round(),
    };
  }

  void _onMealCompletedChanged(String mealId, bool isCompleted) {
    // Implementazione completamento pasto
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isCompleted
            ? 'Pasto completato! Sistema ultra-avanzato aggiornato 🎉'
            : 'Pasto rimosso dai completati',
        ),
        backgroundColor: isCompleted ? AppTheme.primaryColor : Colors.grey,
      ),
    );
  }

  void _onFoodReplaced(String mealId, Food oldFood, Food newFood) {
    // Implementazione sostituzione alimento
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${oldFood.name} sostituito con ${newFood.name}'),
        backgroundColor: AppTheme.accentColor,
      ),
    );
  }
}
