import 'package:flutter/material.dart';
import 'media_service.dart';

/// Stub per WebCameraService su piattaforme non-web
class WebCameraService {
  static final WebCameraService _instance = WebCameraService._internal();
  factory WebCameraService() => _instance;
  WebCameraService._internal();

  bool get isSupported => false;

  Future<bool> initialize() async => false;

  Future<MediaFile?> capturePhoto(BuildContext context) async => null;

  void dispose() {}
}
