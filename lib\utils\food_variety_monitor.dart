import '../services/food_variety_manager.dart';
import '../services/food_database_analyzer.dart';
import '../models/food.dart';

/// Utility per monitorare e visualizzare i miglioramenti nella varietà degli alimenti
class FoodVarietyMonitor {
  final FoodVarietyManager _varietyManager;
  final FoodDatabaseAnalyzer _analyzer;

  FoodVarietyMonitor(this._varietyManager, this._analyzer);

  /// Factory method per creare un'istanza con dipendenze
  static Future<FoodVarietyMonitor> create() async {
    final varietyManager = await FoodVarietyManager.getInstance();
    final analyzer = await FoodDatabaseAnalyzer.create();
    return FoodVarietyMonitor(varietyManager, analyzer);
  }

  /// Genera un report completo sulla varietà degli alimenti
  Future<FoodVarietyReport> generateVarietyReport() async {
    final usageStats = _varietyManager.getUsageStatistics();
    final utilizationReport = await _analyzer.analyzeDatabaseUtilization();
    final suggestions = await _analyzer.suggestFoodsToPromote(maxSuggestions: 10);

    return FoodVarietyReport(
      totalTrackedFoods: usageStats['totalTrackedFoods'] as int,
      totalUsages: usageStats['totalUsages'] as int,
      recentUsages: usageStats['recentUsages'] as int,
      averageUsagePerFood: usageStats['averageUsagePerFood'] as double,
      databaseUtilizationRate: utilizationReport.overallUtilizationRate,
      neverUsedFoodsCount: utilizationReport.neverUsedFoods.length,
      suggestedFoods: suggestions,
      categoryUtilization: utilizationReport.categoryUtilization,
    );
  }

  /// Genera consigli personalizzati per migliorare la varietà
  Future<List<String>> generateVarietyTips() async {
    final report = await generateVarietyReport();
    final tips = <String>[];

    // Consigli basati sul tasso di utilizzo del database
    if (report.databaseUtilizationRate < 0.3) {
      tips.add('🔄 Il tuo piano dietetico utilizza solo il ${(report.databaseUtilizationRate * 100).toStringAsFixed(0)}% degli alimenti disponibili. Prova a rigenerare i piani per scoprire nuovi sapori!');
    } else if (report.databaseUtilizationRate < 0.6) {
      tips.add('📈 Buona varietà! Stai utilizzando il ${(report.databaseUtilizationRate * 100).toStringAsFixed(0)}% degli alimenti disponibili. Continua così!');
    } else {
      tips.add('🌟 Eccellente varietà! Stai sfruttando al meglio il database degli alimenti italiani.');
    }

    // Consigli basati sugli alimenti mai utilizzati
    if (report.neverUsedFoodsCount > 50) {
      tips.add('🍽️ Ci sono ancora ${report.neverUsedFoodsCount} alimenti che non hai mai provato. Rigenera i piani per scoprire nuove ricette!');
    }

    // Consigli stagionali
    final currentMonth = DateTime.now().month;
    final seasonalSuggestions = report.suggestedFoods.where((food) =>
      food.isSeasonal && food.seasonalMonths.contains(currentMonth)
    ).toList();

    if (seasonalSuggestions.isNotEmpty) {
      final seasonalNames = seasonalSuggestions.take(3).map((f) => f.name).join(', ');
      tips.add('🌱 Alimenti di stagione da provare: $seasonalNames');
    }

    // Consigli per categorie sottoutilizzate
    final underutilizedCategories = report.categoryUtilization.entries
        .where((entry) => entry.value.utilizationRate < 0.4)
        .map((entry) => entry.key)
        .toList();

    if (underutilizedCategories.isNotEmpty) {
      final categoryNames = underutilizedCategories
          .take(2)
          .map((cat) => _getCategoryDisplayName(cat))
          .join(' e ');
      tips.add('🎯 Prova ad aggiungere più varietà in: $categoryNames');
    }

    // Consigli per alimenti tradizionali italiani
    final traditionalSuggestions = report.suggestedFoods.where((food) =>
      food.isTraditionalItalian
    ).toList();

    if (traditionalSuggestions.isNotEmpty) {
      final traditionalNames = traditionalSuggestions.take(2).map((f) => f.name).join(', ');
      tips.add('🇮🇹 Scopri la tradizione italiana: $traditionalNames');
    }

    // Consigli generali se non ci sono suggerimenti specifici
    if (tips.length == 1) {
      tips.add('💡 Continua a rigenerare i piani dietetici per mantenere alta la varietà!');
      tips.add('📚 Esplora diverse combinazioni di alimenti per pasti sempre nuovi.');
    }

    return tips;
  }

  /// Genera un report di progresso settimanale sulla varietà
  Future<WeeklyVarietyProgress> generateWeeklyProgress() async {
    final now = DateTime.now();
    final weekAgo = now.subtract(Duration(days: 7));
    
    // Simula il calcolo del progresso (in un'implementazione reale,
    // dovremmo tracciare questi dati nel tempo)
    final currentReport = await generateVarietyReport();
    
    return WeeklyVarietyProgress(
      weekStartDate: weekAgo,
      weekEndDate: now,
      foodsTriedThisWeek: currentReport.recentUsages,
      newFoodsDiscovered: (currentReport.recentUsages * 0.3).round(), // Stima
      varietyScoreImprovement: 0.05, // Stima del miglioramento
      topCategoriesExplored: _getTopExploredCategories(currentReport),
    );
  }

  /// Ottiene le categorie più esplorate
  List<FoodCategory> _getTopExploredCategories(FoodVarietyReport report) {
    final sortedCategories = report.categoryUtilization.entries.toList()
      ..sort((a, b) => b.value.utilizationRate.compareTo(a.value.utilizationRate));
    
    return sortedCategories.take(3).map((entry) => entry.key).toList();
  }

  /// Ottiene il nome visualizzabile di una categoria
  String _getCategoryDisplayName(FoodCategory category) {
    switch (category) {
      case FoodCategory.fruit:
        return 'frutta';
      case FoodCategory.vegetable:
        return 'verdure';
      case FoodCategory.grain:
        return 'cereali';
      case FoodCategory.protein:
        return 'proteine';
      case FoodCategory.dairy:
        return 'latticini';
      case FoodCategory.fat:
        return 'grassi';
      case FoodCategory.sweet:
        return 'dolci';
      case FoodCategory.beverage:
        return 'bevande';
      case FoodCategory.mixed:
        return 'piatti misti';
      case FoodCategory.condiment:
        return 'condimenti';
      case FoodCategory.other:
        return 'altro';
    }
  }

  /// Resetta le statistiche di varietà (per test o reset completo)
  Future<void> resetVarietyTracking() async {
    await _varietyManager.resetUsageHistory();
  }
}

/// Report sulla varietà degli alimenti
class FoodVarietyReport {
  final int totalTrackedFoods;
  final int totalUsages;
  final int recentUsages;
  final double averageUsagePerFood;
  final double databaseUtilizationRate;
  final int neverUsedFoodsCount;
  final List<Food> suggestedFoods;
  final Map<FoodCategory, CategoryUtilization> categoryUtilization;

  FoodVarietyReport({
    required this.totalTrackedFoods,
    required this.totalUsages,
    required this.recentUsages,
    required this.averageUsagePerFood,
    required this.databaseUtilizationRate,
    required this.neverUsedFoodsCount,
    required this.suggestedFoods,
    required this.categoryUtilization,
  });

  /// Calcola un punteggio di varietà generale (0-100)
  double get varietyScore {
    double score = 0;
    
    // 40% basato sul tasso di utilizzo del database
    score += databaseUtilizationRate * 40;
    
    // 30% basato sulla distribuzione tra categorie
    final categoryScores = categoryUtilization.values
        .map((util) => util.utilizationRate)
        .toList();
    if (categoryScores.isNotEmpty) {
      final avgCategoryUtilization = categoryScores.reduce((a, b) => a + b) / categoryScores.length;
      score += avgCategoryUtilization * 30;
    }
    
    // 20% basato sulla frequenza di utilizzo recente
    if (totalTrackedFoods > 0) {
      final recentUsageRate = recentUsages / totalTrackedFoods;
      score += (recentUsageRate * 20).clamp(0, 20);
    }
    
    // 10% bonus per varietà eccezionale
    if (databaseUtilizationRate > 0.7) {
      score += 10;
    }
    
    return score.clamp(0, 100);
  }
}

/// Progresso settimanale della varietà
class WeeklyVarietyProgress {
  final DateTime weekStartDate;
  final DateTime weekEndDate;
  final int foodsTriedThisWeek;
  final int newFoodsDiscovered;
  final double varietyScoreImprovement;
  final List<FoodCategory> topCategoriesExplored;

  WeeklyVarietyProgress({
    required this.weekStartDate,
    required this.weekEndDate,
    required this.foodsTriedThisWeek,
    required this.newFoodsDiscovered,
    required this.varietyScoreImprovement,
    required this.topCategoriesExplored,
  });
}
