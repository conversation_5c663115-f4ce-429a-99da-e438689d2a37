import 'dart:math' as math;
import '../models/advanced_user_profile.dart';
import '../models/food.dart';
import '../models/diet_plan.dart';
import '../utils/advanced_nutrition_properties.dart';

/// Servizio specializzato nella selezione avanzata degli alimenti per condizioni speciali
class AdvancedFoodSelector {
  // Tolleranze per la precisione
  static const double _calorieTolerancePercent = 0.03; // 3% di tolleranza per le calorie
  static const int _calorieToleranceAbsolute = 15; // ±15 kcal di tolleranza assoluta
  static const int _macroToleranceGrams = 2; // ±2g di tolleranza per i macronutrienti

  // Punteggi per la valutazione degli alimenti
  static const int _scoreBaseValue = 100;
  static const int _scoreNutrientDensity = 30;
  static const int _scoreVariety = 25;
  static const int _scoreSeasonal = 15;
  static const int _scoreSpecialCondition = 40;
  static const int _scoreMacroMatch = 35;
  static const int _scorePreference = 20;

  /// Seleziona gli alimenti per un pasto con grammature precise
  static List<FoodPortion> selectFoodsForMeal({
    required AdvancedUserProfile profile,
    required String mealType,
    required int targetCalories,
    required Map<String, int> targetMacros,
    required List<Food> availableFoods,
    required Map<FoodCategory, List<Food>> foodsByCategory,
    required List<FoodCategory> categoriesToInclude,
    Map<String, int>? usedFoodsCount,
  }) {
    print('Selezione alimenti avanzata per pasto di tipo $mealType');
    print('Obiettivo calorico: $targetCalories kcal');
    print('Obiettivi macronutrienti: $targetMacros');

    // Lista per i risultati
    final selectedFoods = <FoodPortion>[];

    // Calorie e macronutrienti correnti
    int currentCalories = 0;
    int currentProteins = 0;
    int currentCarbs = 0;
    int currentFats = 0;

    // Target macronutrienti
    final targetProtein = targetMacros['proteins'] ?? 0;
    final targetCarbs = targetMacros['carbs'] ?? 0;
    final targetFats = targetMacros['fats'] ?? 0;

    // Calcola il numero di alimenti da selezionare per categoria
    final foodsPerCategory = _calculateFoodsPerCategory(categoriesToInclude, mealType);

    // Mappa per tenere traccia delle categorie già utilizzate
    final usedCategories = <FoodCategory, int>{};

    // Assegna punteggi agli alimenti in base alle condizioni speciali
    final scoredFoods = _scoreFoodsForConditions(availableFoods, profile, mealType, usedFoodsCount);

    // Ordina gli alimenti per punteggio (dal più alto al più basso)
    scoredFoods.sort((a, b) => b.score.compareTo(a.score));

    // Seleziona gli alimenti in base al punteggio e alle categorie
    for (final category in categoriesToInclude) {
      // Ottieni gli alimenti di questa categoria
      final categoryFoods = foodsByCategory[category] ?? [];

      // Filtra e ordina gli alimenti per punteggio
      final filteredCategoryFoods = scoredFoods
          .where((scoredFood) => categoryFoods.contains(scoredFood.food))
          .toList();

      // Numero di alimenti da selezionare per questa categoria
      final numToSelect = foodsPerCategory[category] ?? 1;

      // Seleziona gli alimenti
      for (int i = 0; i < numToSelect; i++) {
        if (filteredCategoryFoods.isEmpty) break;

        // Seleziona l'alimento con il punteggio più alto
        final selectedScoredFood = filteredCategoryFoods.removeAt(0);
        final selectedFood = selectedScoredFood.food;

        // Calcola la porzione ottimale
        final portion = _calculateOptimalPortion(
          selectedFood,
          targetCalories - currentCalories,
          {
            'proteins': targetProtein - currentProteins,
            'carbs': targetCarbs - currentCarbs,
            'fats': targetFats - currentFats,
          },
          mealType,
          profile,
        );

        // Aggiungi l'alimento selezionato
        if (portion.grams > 0) {
          selectedFoods.add(portion);

          // Aggiorna i totali
          currentCalories += portion.calories;
          currentProteins += portion.proteins.round();
          currentCarbs += portion.carbs.round();
          currentFats += portion.fats.round();

          // Aggiorna il conteggio delle categorie utilizzate
          usedCategories[category] = (usedCategories[category] ?? 0) + 1;
        }

        // Verifica se abbiamo raggiunto gli obiettivi
        if (_isWithinTolerance(currentCalories, targetCalories) &&
            _isWithinTolerance(currentProteins, targetProtein, _macroToleranceGrams) &&
            _isWithinTolerance(currentCarbs, targetCarbs, _macroToleranceGrams) &&
            _isWithinTolerance(currentFats, targetFats, _macroToleranceGrams)) {
          break;
        }
      }

      // Verifica se abbiamo raggiunto gli obiettivi
      if (_isWithinTolerance(currentCalories, targetCalories) &&
          _isWithinTolerance(currentProteins, targetProtein, _macroToleranceGrams) &&
          _isWithinTolerance(currentCarbs, targetCarbs, _macroToleranceGrams) &&
          _isWithinTolerance(currentFats, targetFats, _macroToleranceGrams)) {
        break;
      }
    }

    // Se non abbiamo raggiunto gli obiettivi, aggiungi alimenti aggiuntivi
    if (!_isWithinTolerance(currentCalories, targetCalories) ||
        !_isWithinTolerance(currentProteins, targetProtein, _macroToleranceGrams) ||
        !_isWithinTolerance(currentCarbs, targetCarbs, _macroToleranceGrams) ||
        !_isWithinTolerance(currentFats, targetFats, _macroToleranceGrams)) {

      // Calcola i macronutrienti mancanti
      final remainingCalories = targetCalories - currentCalories;
      final remainingProteins = targetProtein - currentProteins;
      final remainingCarbs = targetCarbs - currentCarbs;
      final remainingFats = targetFats - currentFats;

      // Determina quale macronutriente è più carente
      final macroDeficit = {
        'proteins': remainingProteins / targetProtein,
        'carbs': remainingCarbs / targetCarbs,
        'fats': remainingFats / targetFats,
      };

      // Trova il macronutriente più carente
      String mostDeficientMacro = 'proteins';
      double maxDeficit = macroDeficit['proteins']!;

      if (macroDeficit['carbs']! > maxDeficit) {
        mostDeficientMacro = 'carbs';
        maxDeficit = macroDeficit['carbs']!;
      }

      if (macroDeficit['fats']! > maxDeficit) {
        mostDeficientMacro = 'fats';
        maxDeficit = macroDeficit['fats']!;
      }

      // Seleziona alimenti ricchi del macronutriente più carente
      final additionalFoods = _selectAdditionalFoods(
        scoredFoods,
        mostDeficientMacro,
        remainingCalories,
        {
          'proteins': remainingProteins,
          'carbs': remainingCarbs,
          'fats': remainingFats,
        },
        selectedFoods.map((portion) => portion.food.id).toList(),
      );

      // Aggiungi gli alimenti aggiuntivi
      selectedFoods.addAll(additionalFoods);

      // Aggiorna i totali
      for (final portion in additionalFoods) {
        currentCalories += portion.calories;
        currentProteins += portion.proteins.round();
        currentCarbs += portion.carbs.round();
        currentFats += portion.fats.round();
      }
    }

    print('Alimenti selezionati: ${selectedFoods.length}');
    print('Calorie totali: $currentCalories / $targetCalories');
    print('Proteine totali: $currentProteins / $targetProtein');
    print('Carboidrati totali: $currentCarbs / $targetCarbs');
    print('Grassi totali: $currentFats / $targetFats');

    return selectedFoods;
  }

  /// Calcola il numero di alimenti da selezionare per categoria
  static Map<FoodCategory, int> _calculateFoodsPerCategory(
    List<FoodCategory> categories,
    String mealType,
  ) {
    final result = <FoodCategory, int>{};

    // Imposta valori predefiniti
    for (final category in categories) {
      result[category] = 1;
    }

    // Personalizza in base al tipo di pasto
    if (mealType == 'breakfast') {
      if (categories.contains(FoodCategory.fruit)) {
        result[FoodCategory.fruit] = 1;
      }
      if (categories.contains(FoodCategory.grain)) {
        result[FoodCategory.grain] = 1;
      }
      if (categories.contains(FoodCategory.dairy)) {
        result[FoodCategory.dairy] = 1;
      }
    } else if (mealType == 'lunch' || mealType == 'dinner') {
      if (categories.contains(FoodCategory.protein)) {
        result[FoodCategory.protein] = 1;
      }
      if (categories.contains(FoodCategory.vegetable)) {
        result[FoodCategory.vegetable] = 2;
      }
      if (categories.contains(FoodCategory.grain)) {
        result[FoodCategory.grain] = 1;
      }
      if (categories.contains(FoodCategory.fat)) {
        result[FoodCategory.fat] = 1;
      }
    } else if (mealType == 'snack') {
      if (categories.contains(FoodCategory.fruit)) {
        result[FoodCategory.fruit] = 1;
      }
      if (categories.contains(FoodCategory.protein)) {
        result[FoodCategory.protein] = 1;
      }
    }

    return result;
  }

  /// Assegna punteggi agli alimenti in base alle condizioni speciali
  static List<ScoredFood> _scoreFoodsForConditions(
    List<Food> foods,
    AdvancedUserProfile profile,
    String mealType,
    Map<String, int>? usedFoodsCount,
  ) {
    final scoredFoods = <ScoredFood>[];

    for (final food in foods) {
      int score = _scoreBaseValue;

      // Punteggio per densità nutrizionale
      if (food.advancedProperties.containsKey(AdvancedNutritionProperties.NUTRIENT_DENSITY)) {
        final density = food.advancedProperties[AdvancedNutritionProperties.NUTRIENT_DENSITY] as double;
        score += (density * _scoreNutrientDensity / 10).round();
      }

      // Punteggio per stagionalità
      if (food.isSeasonal && food.isCurrentlySeasonal()) {
        score += _scoreSeasonal;
      }

      // Punteggio per varietà (penalizza alimenti già utilizzati)
      if (usedFoodsCount != null && usedFoodsCount.containsKey(food.id)) {
        final usageCount = usedFoodsCount[food.id] ?? 0;
        score -= usageCount * _scoreVariety;
      }

      // Punteggio per condizioni mediche
      score += _scoreForMedicalConditions(food, profile.medicalConditions);

      // Punteggio per obiettivi di fitness
      score += _scoreForFitnessGoal(food, profile.fitnessGoal);

      // Punteggio per tipo di sport
      score += _scoreForSportType(food, profile.primarySport);

      // Punteggio per adeguatezza al tipo di pasto
      if (food.suitableForMeals.contains(mealType.toLowerCase())) {
        score += 20;
      }

      scoredFoods.add(ScoredFood(food: food, score: score));
    }

    return scoredFoods;
  }

  /// Calcola il punteggio per condizioni mediche
  static int _scoreForMedicalConditions(Food food, List<MedicalCondition> conditions) {
    int score = 0;

    for (final condition in conditions) {
      switch (condition) {
        case MedicalCondition.diabetes:
          // Premia alimenti a basso indice glicemico
          if (food.glycemicIndex > 0 && food.glycemicIndex < 55) {
            score += _scoreSpecialCondition;
          } else if (food.glycemicIndex >= 55 && food.glycemicIndex < 70) {
            score += _scoreSpecialCondition ~/ 2;
          }
          break;

        case MedicalCondition.hypertension:
          // Premia alimenti a basso contenuto di sodio
          final sodium = food.micronutrients['sodium'] ?? 0.0;
          if (sodium < 120.0) {
            score += _scoreSpecialCondition;
          } else if (sodium < 300.0) {
            score += _scoreSpecialCondition ~/ 2;
          }
          break;

        case MedicalCondition.heartDisease:
          // Premia alimenti ricchi di omega-3 e antiossidanti
          if (food.tags.contains('omega3') ||
              food.tags.contains('antiossidante') ||
              food.tags.contains('antioxidant')) {
            score += _scoreSpecialCondition;
          }
          break;

        case MedicalCondition.ibs:
          // Premia alimenti a basso contenuto di FODMAP
          if (food.advancedProperties.containsKey(AdvancedNutritionProperties.FODMAP_LEVEL)) {
            final fodmapLevel = food.advancedProperties[AdvancedNutritionProperties.FODMAP_LEVEL];
            if (fodmapLevel == 'Basso') {
              score += _scoreSpecialCondition;
            } else if (fodmapLevel == 'Moderato') {
              score += _scoreSpecialCondition ~/ 2;
            }
          }
          break;

        default:
          break;
      }
    }

    return score;
  }

  /// Calcola il punteggio per obiettivi di fitness
  static int _scoreForFitnessGoal(Food food, FitnessGoal goal) {
    int score = 0;

    switch (goal) {
      case FitnessGoal.muscleGain:
        // Premia alimenti ricchi di proteine
        if (food.proteins > 20.0) {
          score += _scoreSpecialCondition;
        } else if (food.proteins > 10.0) {
          score += _scoreSpecialCondition ~/ 2;
        }
        break;

      case FitnessGoal.endurance:
        // Premia alimenti ricchi di carboidrati complessi
        if (food.carbs > 30.0 && food.sugar < 10.0) {
          score += _scoreSpecialCondition;
        } else if (food.carbs > 15.0 && food.sugar < 5.0) {
          score += _scoreSpecialCondition ~/ 2;
        }
        break;

      case FitnessGoal.weightLoss:
        // Premia alimenti a bassa densità calorica e alto potere saziante
        if (food.calories < 100 && food.fiber > 3.0) {
          score += _scoreSpecialCondition;
        } else if (food.calories < 150 && food.fiber > 2.0) {
          score += _scoreSpecialCondition ~/ 2;
        }
        break;

      default:
        break;
    }

    return score;
  }

  /// Calcola il punteggio per tipo di sport
  static int _scoreForSportType(Food food, SportType sport) {
    int score = 0;

    switch (sport) {
      case SportType.running:
      case SportType.cycling:
      case SportType.swimming:
        // Premia alimenti ricchi di carboidrati e antiossidanti
        if (food.carbs > 20.0 &&
            food.advancedProperties.containsKey(AdvancedNutritionProperties.ANTIOXIDANT_SCORE)) {
          score += _scoreSpecialCondition ~/ 2;
        }
        break;

      case SportType.weightlifting:
      case SportType.crossfit:
        // Premia alimenti ricchi di proteine e creatina
        if (food.proteins > 15.0 && food.tags.contains('creatine')) {
          score += _scoreSpecialCondition;
        } else if (food.proteins > 10.0) {
          score += _scoreSpecialCondition ~/ 2;
        }
        break;

      default:
        break;
    }

    return score;
  }

  /// Calcola la porzione ottimale di un alimento
  static FoodPortion _calculateOptimalPortion(
    Food food,
    int targetCalories,
    Map<String, int> targetMacros,
    String mealType,
    AdvancedUserProfile profile,
  ) {
    // Calcola la porzione base in base alle calorie
    int baseGrams = (targetCalories * 100 / food.calories).round();

    // Limita la porzione in base al tipo di alimento e al tipo di pasto
    baseGrams = _limitPortionSize(food, baseGrams, mealType);

    // Aggiusta la porzione in base ai macronutrienti target
    final proteinGrams = (targetMacros['proteins'] ?? 0) * 100 / food.proteins;
    final carbGrams = (targetMacros['carbs'] ?? 0) * 100 / food.carbs;
    final fatGrams = (targetMacros['fats'] ?? 0) * 100 / food.fats;

    // Usa il valore più basso per evitare di superare i target
    List<double> validGrams = [];

    if (food.proteins > 0) validGrams.add(proteinGrams);
    if (food.carbs > 0) validGrams.add(carbGrams);
    if (food.fats > 0) validGrams.add(fatGrams);

    if (validGrams.isNotEmpty) {
      final minGrams = validGrams.reduce(math.min);
      baseGrams = math.min(baseGrams, minGrams.round());
    }

    // Assicurati che la porzione sia almeno 5g
    baseGrams = math.max(baseGrams, 5);

    // Crea la porzione
    return FoodPortion(
      food: food,
      grams: baseGrams,
    );
  }

  /// Limita la dimensione della porzione in base al tipo di alimento e al tipo di pasto
  static int _limitPortionSize(Food food, int grams, String mealType) {
    // Limiti massimi per categoria
    final maxGrams = <FoodCategory, int>{
      FoodCategory.protein: 150,
      FoodCategory.grain: 100,
      FoodCategory.vegetable: 200,
      FoodCategory.fruit: 150,
      FoodCategory.dairy: 200,
      FoodCategory.fat: 30,
      FoodCategory.sweet: 50,
      FoodCategory.beverage: 250,
      FoodCategory.mixed: 300,
    };

    // Limiti minimi per categoria
    final minGrams = <FoodCategory, int>{
      FoodCategory.protein: 30,
      FoodCategory.grain: 30,
      FoodCategory.vegetable: 50,
      FoodCategory.fruit: 50,
      FoodCategory.dairy: 30,
      FoodCategory.fat: 5,
      FoodCategory.sweet: 10,
      FoodCategory.beverage: 100,
      FoodCategory.mixed: 50,
    };

    // Applica i limiti per ogni categoria dell'alimento
    int limitedGrams = grams;

    for (final category in food.categories) {
      final max = maxGrams[category] ?? 100;
      final min = minGrams[category] ?? 10;

      limitedGrams = math.min(limitedGrams, max);
      limitedGrams = math.max(limitedGrams, min);
    }

    // Aggiusta in base al tipo di pasto
    if (mealType == 'breakfast') {
      limitedGrams = math.min(limitedGrams, 100);
    } else if (mealType == 'lunch' || mealType == 'dinner') {
      // Nessun aggiustamento aggiuntivo
    } else if (mealType == 'snack') {
      limitedGrams = math.min(limitedGrams, 80);
    }

    return limitedGrams;
  }

  /// Seleziona alimenti aggiuntivi per completare i target nutrizionali
  static List<FoodPortion> _selectAdditionalFoods(
    List<ScoredFood> scoredFoods,
    String primaryMacro,
    int remainingCalories,
    Map<String, int> remainingMacros,
    List<String> excludedFoodIds,
  ) {
    final result = <FoodPortion>[];

    // Filtra gli alimenti già selezionati
    final availableFoods = scoredFoods
        .where((scoredFood) => !excludedFoodIds.contains(scoredFood.food.id))
        .toList();

    // Ordina gli alimenti in base al contenuto del macronutriente primario
    availableFoods.sort((a, b) {
      double aValue = 0;
      double bValue = 0;

      switch (primaryMacro) {
        case 'proteins':
          aValue = a.food.proteins;
          bValue = b.food.proteins;
          break;
        case 'carbs':
          aValue = a.food.carbs;
          bValue = b.food.carbs;
          break;
        case 'fats':
          aValue = a.food.fats;
          bValue = b.food.fats;
          break;
      }

      return bValue.compareTo(aValue);
    });

    // Seleziona alimenti fino a raggiungere i target
    int currentCalories = 0;
    int currentProteins = 0;
    int currentCarbs = 0;
    int currentFats = 0;

    for (final scoredFood in availableFoods) {
      // Verifica se abbiamo raggiunto i target
      if (currentCalories >= remainingCalories &&
          currentProteins >= remainingMacros['proteins']! &&
          currentCarbs >= remainingMacros['carbs']! &&
          currentFats >= remainingMacros['fats']!) {
        break;
      }

      // Calcola la porzione ottimale
      final portion = FoodPortion(
        food: scoredFood.food,
        grams: math.min(50, (remainingCalories * 100 / scoredFood.food.calories).round()),
      );

      // Aggiungi la porzione
      if (portion.grams > 0) {
        result.add(portion);

        // Aggiorna i totali
        currentCalories += portion.calories;
        currentProteins += portion.proteins.round();
        currentCarbs += portion.carbs.round();
        currentFats += portion.fats.round();
      }
    }

    return result;
  }

  /// Verifica se un valore è entro la tolleranza
  static bool _isWithinTolerance(int current, int target, [int absoluteTolerance = _calorieToleranceAbsolute]) {
    final relativeTolerance = target * _calorieTolerancePercent;
    final tolerance = math.max(absoluteTolerance, relativeTolerance);

    return (current - target).abs() <= tolerance;
  }
}

/// Classe per rappresentare un alimento con un punteggio
class ScoredFood {
  final Food food;
  final int score;

  ScoredFood({
    required this.food,
    required this.score,
  });
}
