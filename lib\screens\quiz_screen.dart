import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/quiz_models.dart';
import '../services/quiz_service.dart';
import '../controllers/welljourney_controller.dart';
import '../theme/dr_staffilano_theme.dart';
import '../widgets/dr_staffilano_logo.dart';
import 'quiz_result_screen.dart';

/// Schermata del quiz interattivo
class QuizScreen extends StatefulWidget {
  final ModuleQuiz quiz;
  final String moduleId;

  const QuizScreen({
    Key? key,
    required this.quiz,
    required this.moduleId,
  }) : super(key: key);

  @override
  State<QuizScreen> createState() => _QuizScreenState();
}

class _QuizScreenState extends State<QuizScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;

  QuizAttempt? _currentAttempt;
  List<int?> _userAnswers = [];
  int _currentQuestionIndex = 0;
  bool _isLoading = false;
  bool _showFeedback = false;
  String _currentFeedback = '';

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));

    _userAnswers = List.filled(widget.quiz.questions.length, null);
    _startQuiz();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  Future<void> _startQuiz() async {
    setState(() => _isLoading = true);

    try {
      _currentAttempt = await QuizService.instance.startQuizAttempt(
        widget.quiz.id,
        'user_001', // TODO: ID utente reale
      );
      _updateProgress();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Errore nell\'avvio del quiz: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }

    setState(() => _isLoading = false);
  }

  void _updateProgress() {
    final progress = (_currentQuestionIndex + 1) / widget.quiz.questions.length;
    _progressController.animateTo(progress);
  }

  void _selectAnswer(int answerIndex) {
    setState(() {
      _userAnswers[_currentQuestionIndex] = answerIndex;
      _showFeedback = true;

      final question = widget.quiz.questions[_currentQuestionIndex];
      final isCorrect = question.isCorrectAnswer(answerIndex);

      if (isCorrect) {
        _currentFeedback = 'Corretto! ${question.explanation}';
      } else {
        _currentFeedback = 'Risposta errata. ${question.explanation}';
      }
    });

    // Mostra il feedback per 2 secondi, poi passa alla prossima domanda
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        _nextQuestion();
      }
    });
  }

  void _nextQuestion() {
    setState(() {
      _showFeedback = false;
      _currentFeedback = '';
    });

    if (_currentQuestionIndex < widget.quiz.questions.length - 1) {
      setState(() {
        _currentQuestionIndex++;
      });
      _updateProgress();
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeQuiz();
    }
  }

  void _previousQuestion() {
    if (_currentQuestionIndex > 0) {
      setState(() {
        _currentQuestionIndex--;
        _showFeedback = false;
        _currentFeedback = '';
      });
      _updateProgress();
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _completeQuiz() async {
    if (_currentAttempt == null) return;

    setState(() => _isLoading = true);

    try {
      // Rimuovi i null dalla lista delle risposte
      final answers = _userAnswers.map((answer) => answer ?? 0).toList();

      final result = await QuizService.instance.completeQuizAttempt(
        _currentAttempt!,
        answers,
      );

      if (mounted) {
        // Aggiorna il controller WellJourney con integrazione completa
        final controller = context.read<WellJourneyController>();

        // Determina il pathway ID dal moduleId
        String pathwayId = _determinePathwayId(widget.moduleId);

        // Completa il quiz con integrazione NutriScore
        await controller.completeQuiz(
          result.quiz.id,
          widget.moduleId,
          result.attempt.score,
          100, // maxScore è sempre 100 per percentuali
          bonusPoints: result.bonusPoints,
        );

        // Se il quiz è passato, completa anche il modulo
        if (result.attempt.isPassed) {
          await controller.completeModule(widget.moduleId, pathwayId);
        }

        // Naviga alla schermata dei risultati con animazione
        Navigator.pushReplacement(
          context,
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) =>
                QuizResultScreen(result: result),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              const begin = Offset(1.0, 0.0);
              const end = Offset.zero;
              const curve = Curves.easeInOut;

              var tween = Tween(begin: begin, end: end).chain(
                CurveTween(curve: curve),
              );

              return SlideTransition(
                position: animation.drive(tween),
                child: child,
              );
            },
            transitionDuration: const Duration(milliseconds: 300),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Errore nel completamento del quiz: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }

    setState(() => _isLoading = false);
  }

  /// Determina il pathway ID dal module ID
  String _determinePathwayId(String moduleId) {
    if (moduleId.startsWith('heart_')) {
      return 'heart_health_pathway';
    } else if (moduleId.startsWith('weight_')) {
      return 'weight_management_pathway';
    } else if (moduleId.startsWith('sports_')) {
      return 'sports_nutrition_pathway';
    } else if (moduleId.startsWith('mediterranean_')) {
      return 'mediterranean_diet_pathway';
    } else if (moduleId.startsWith('prevention_')) {
      return 'prevention_care_pathway';
    } else {
      return 'heart_health_pathway'; // Default fallback
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: DrStaffilanoTheme.backgroundLight,
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                color: DrStaffilanoTheme.primaryGreen,
              ),
              SizedBox(height: 16),
              Text(
                'Preparazione del quiz...',
                style: TextStyle(
                  fontSize: 16,
                  color: DrStaffilanoTheme.textSecondary,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: DrStaffilanoTheme.backgroundLight,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildProgressHeader(),
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: widget.quiz.questions.length,
              itemBuilder: (context, index) {
                return _buildQuestionPage(widget.quiz.questions[index]);
              },
            ),
          ),
          _buildNavigationButtons(),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Row(
        children: [
          const DrStaffilanoLogo.small(),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  widget.quiz.title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  'Domanda ${_currentQuestionIndex + 1} di ${widget.quiz.questions.length}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      backgroundColor: DrStaffilanoTheme.primaryGreen,
      elevation: 0,
    );
  }

  Widget _buildProgressHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Progresso Quiz',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: DrStaffilanoTheme.textPrimary,
                ),
              ),
              Text(
                '${((_currentQuestionIndex + 1) / widget.quiz.questions.length * 100).round()}%',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: DrStaffilanoTheme.primaryGreen,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          AnimatedBuilder(
            animation: _progressAnimation,
            builder: (context, child) {
              return LinearProgressIndicator(
                value: _progressAnimation.value,
                backgroundColor: Colors.grey.shade200,
                valueColor: AlwaysStoppedAnimation<Color>(DrStaffilanoTheme.primaryGreen),
                minHeight: 8,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionPage(QuizQuestion question) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildQuestionCard(question),
          const SizedBox(height: 20),
          _buildAnswerOptions(question),
          if (_showFeedback) ...[
            const SizedBox(height: 20),
            _buildFeedbackCard(),
          ],
        ],
      ),
    );
  }

  Widget _buildQuestionCard(QuizQuestion question) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: question.difficulty.color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  question.difficulty.displayName,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: question.difficulty.color,
                  ),
                ),
              ),
              const Spacer(),
              Icon(
                Icons.quiz,
                color: DrStaffilanoTheme.primaryGreen,
                size: 24,
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            question.question,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: DrStaffilanoTheme.textPrimary,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnswerOptions(QuizQuestion question) {
    return Column(
      children: question.options.asMap().entries.map((entry) {
        final index = entry.key;
        final option = entry.value;
        final isSelected = _userAnswers[_currentQuestionIndex] == index;
        final isCorrect = question.correctAnswerIndex == index;

        Color backgroundColor = Colors.white;
        Color borderColor = Colors.grey.shade300;
        Color textColor = DrStaffilanoTheme.textPrimary;

        if (_showFeedback) {
          if (isSelected && isCorrect) {
            backgroundColor = Colors.green.shade50;
            borderColor = Colors.green;
            textColor = Colors.green.shade700;
          } else if (isSelected && !isCorrect) {
            backgroundColor = Colors.red.shade50;
            borderColor = Colors.red;
            textColor = Colors.red.shade700;
          } else if (!isSelected && isCorrect) {
            backgroundColor = Colors.green.shade50;
            borderColor = Colors.green;
            textColor = Colors.green.shade700;
          }
        } else if (isSelected) {
          backgroundColor = DrStaffilanoTheme.primaryGreen.withOpacity(0.1);
          borderColor = DrStaffilanoTheme.primaryGreen;
          textColor = DrStaffilanoTheme.primaryGreen;
        }

        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: borderColor, width: 2),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(12),
              onTap: _showFeedback ? null : () => _selectAnswer(index),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isSelected ? borderColor : Colors.transparent,
                        border: Border.all(color: borderColor, width: 2),
                      ),
                      child: isSelected
                          ? Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 16,
                            )
                          : null,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        option.text,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: textColor,
                        ),
                      ),
                    ),
                    if (_showFeedback && isCorrect)
                      Icon(
                        Icons.check_circle,
                        color: Colors.green,
                        size: 24,
                      ),
                  ],
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildFeedbackCard() {
    final question = widget.quiz.questions[_currentQuestionIndex];
    final isCorrect = _userAnswers[_currentQuestionIndex] == question.correctAnswerIndex;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isCorrect ? Colors.green.shade50 : Colors.orange.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isCorrect ? Colors.green : Colors.orange,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isCorrect ? Icons.check_circle : Icons.info,
                color: isCorrect ? Colors.green : Colors.orange,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                isCorrect ? 'Risposta Corretta!' : 'Risposta Errata',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isCorrect ? Colors.green.shade700 : Colors.orange.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            _currentFeedback,
            style: TextStyle(
              fontSize: 14,
              color: isCorrect ? Colors.green.shade700 : Colors.orange.shade700,
              height: 1.4,
            ),
          ),
          if (question.drStaffilanoInsight.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: DrStaffilanoTheme.secondaryBlue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.format_quote,
                    color: DrStaffilanoTheme.secondaryBlue,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      question.drStaffilanoInsight,
                      style: TextStyle(
                        fontSize: 13,
                        fontStyle: FontStyle.italic,
                        color: DrStaffilanoTheme.secondaryBlue,
                        height: 1.3,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (_currentQuestionIndex > 0)
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _showFeedback ? null : _previousQuestion,
                icon: const Icon(Icons.arrow_back),
                label: const Text('Precedente'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: DrStaffilanoTheme.primaryGreen,
                  side: BorderSide(color: DrStaffilanoTheme.primaryGreen),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          if (_currentQuestionIndex > 0) const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _userAnswers[_currentQuestionIndex] != null && !_showFeedback
                  ? (_currentQuestionIndex == widget.quiz.questions.length - 1
                      ? _completeQuiz
                      : _nextQuestion)
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: DrStaffilanoTheme.primaryGreen,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(
                _currentQuestionIndex == widget.quiz.questions.length - 1
                    ? 'Completa Quiz'
                    : 'Prossima Domanda',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
