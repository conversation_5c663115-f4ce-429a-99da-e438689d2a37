import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:uuid/uuid.dart';
import '../models/food.dart';

/// Service per interagire con l'API USDA FoodData Central
/// Documentazione: https://fdc.nal.usda.gov/api-guide.html
class UsdaApiService {
  // Chiave API per USDA FoodData Central
  // Nota: In un'app reale, questa dovrebbe essere protetta e non hardcoded
  // Ottieni una chiave API gratuita da: https://fdc.nal.usda.gov/api-key-signup.html
  static const String _apiKey = 'DEMO_KEY'; // Sostituisci con la tua chiave API
  static const String _baseUrl = 'https://api.nal.usda.gov/fdc/v1';

  // Endpoint API
  static const String _searchEndpoint = '/foods/search';
  static const String _foodDetailsEndpoint = '/food';

  // Client HTTP
  final http.Client _client;

  UsdaApiService({http.Client? client}) : _client = client ?? http.Client();

  /// Cerca alimenti nel database USDA
  /// [query] - Termine di ricerca
  /// [pageSize] - Numero di risultati per pagina (default: 25, max: 200)
  /// [pageNumber] - Numero di pagina (default: 1)
  /// [dataType] - Tipo di dati (Foundation, SR Legacy, Survey (FNDDS), Branded)
  Future<List<Map<String, dynamic>>> searchFoods({
    required String query,
    int pageSize = 25,
    int pageNumber = 1,
    List<String> dataType = const [],
  }) async {
    try {
      final queryParams = {
        'api_key': _apiKey,
        'query': query,
        'pageSize': pageSize.toString(),
        'pageNumber': pageNumber.toString(),
      };

      if (dataType.isNotEmpty) {
        queryParams['dataType'] = dataType.join(',');
      }

      final uri = Uri.parse('$_baseUrl$_searchEndpoint').replace(
        queryParameters: queryParams,
      );

      final response = await _client.get(uri);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return List<Map<String, dynamic>>.from(data['foods']);
      } else {
        throw Exception('Errore nella ricerca USDA: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Errore nella ricerca USDA: $e');
    }
  }

  /// Ottieni dettagli di un alimento specifico dal database USDA
  /// [fdcId] - ID FDC dell'alimento
  /// [format] - Formato dei dati (full, abridged)
  Future<Map<String, dynamic>> getFoodDetails({
    required String fdcId,
    String format = 'full',
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl$_foodDetailsEndpoint/$fdcId').replace(
        queryParameters: {
          'api_key': _apiKey,
          'format': format,
        },
      );

      final response = await _client.get(uri);

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Errore nel recupero dettagli USDA: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Errore nel recupero dettagli USDA: $e');
    }
  }

  /// Converte un alimento USDA in un oggetto Food
  /// [usdaFood] - Dati dell'alimento dal database USDA
  /// [isDetailed] - Indica se i dati provengono dall'endpoint di dettaglio o di ricerca
  Future<Food> convertUsdaFoodToFood(Map<String, dynamic> usdaFood, {bool isDetailed = false}) async {
    try {
      // Estrai l'ID FDC
      final String fdcId = usdaFood['fdcId'].toString();

      // Se non abbiamo dati dettagliati, ottienili
      Map<String, dynamic> detailedFood = usdaFood;
      if (!isDetailed) {
        try {
          detailedFood = await getFoodDetails(fdcId: fdcId);
        } catch (e) {
          print('Impossibile ottenere dettagli per FDC ID $fdcId: $e');
          // Continua con i dati limitati che abbiamo
        }
      }

      // Estrai i nutrienti
      final nutrients = _extractNutrients(detailedFood);

      // Determina le categorie di alimenti
      final categories = _determineFoodCategories(detailedFood);

      // Determina i tipi di pasto adatti
      final mealTypes = _determineSuitableMealTypes(detailedFood, categories);

      // Estrai informazioni sugli allergeni
      final allergens = _extractAllergens(detailedFood);

      // Determina se è adatto a diete specifiche
      final dietaryInfo = _determineDietarySuitability(detailedFood, allergens);

      // Crea l'oggetto Food
      return Food(
        id: const Uuid().v4(), // Genera un nuovo UUID
        name: detailedFood['description'] ?? 'Alimento sconosciuto',
        description: _generateDescription(detailedFood),
        calories: nutrients['calories'] ?? 0,
        proteins: nutrients['proteins'] ?? 0.0,
        carbs: nutrients['carbs'] ?? 0.0,
        fats: nutrients['fats'] ?? 0.0,
        fiber: nutrients['fiber'] ?? 0.0,
        sugar: nutrients['sugar'] ?? 0.0,
        suitableForMeals: mealTypes,
        categories: categories,
        isVegetarian: dietaryInfo['isVegetarian'] ?? false,
        isVegan: dietaryInfo['isVegan'] ?? false,
        isGlutenFree: dietaryInfo['isGlutenFree'] ?? false,
        isDairyFree: dietaryInfo['isDairyFree'] ?? false,
        allergens: allergens,
        dataSource: DataSource.usda,
        sourceId: fdcId,
        sourceDescription: 'USDA FoodData Central',
        brandName: detailedFood['brandName'] ?? '',
        validationStatus: ValidationStatus.validated, // I dati USDA sono considerati validati
        lastValidatedAt: DateTime.now(),

        tags: _generateTags(detailedFood, categories, dietaryInfo),
        micronutrients: _extractMicronutrients(detailedFood),
      );
    } catch (e) {
      throw Exception('Errore nella conversione dell\'alimento USDA: $e');
    }
  }

  /// Estrae i valori nutrizionali principali dai dati USDA
  Map<String, dynamic> _extractNutrients(Map<String, dynamic> usdaFood) {
    final result = {
      'calories': 0,
      'proteins': 0.0,
      'carbs': 0.0,
      'fats': 0.0,
      'fiber': 0.0,
      'sugar': 0.0,
    };

    // Mappa degli ID dei nutrienti USDA ai nostri campi
    const nutrientIdMap = {
      '1008': 'calories',    // Energia (kcal)
      '1003': 'proteins',    // Proteine
      '1005': 'carbs',       // Carboidrati
      '1004': 'fats',        // Grassi totali
      '1079': 'fiber',       // Fibre
      '2000': 'sugar',       // Zuccheri totali
    };

    // Estrai i nutrienti
    if (usdaFood.containsKey('foodNutrients')) {
      final nutrients = usdaFood['foodNutrients'];
      if (nutrients is List) {
        for (final nutrient in nutrients) {
          String? nutrientId;

          // L'API USDA ha formati diversi per nutrienti in base all'endpoint
          if (nutrient.containsKey('nutrientId')) {
            nutrientId = nutrient['nutrientId'].toString();
          } else if (nutrient.containsKey('nutrient') &&
                    nutrient['nutrient'].containsKey('id')) {
            nutrientId = nutrient['nutrient']['id'].toString();
          }

          if (nutrientId != null && nutrientIdMap.containsKey(nutrientId)) {
            final field = nutrientIdMap[nutrientId];
            double value = 0.0;

            // Estrai il valore (anche qui l'API ha formati diversi)
            if (nutrient.containsKey('value')) {
              value = double.tryParse(nutrient['value'].toString()) ?? 0.0;
            } else if (nutrient.containsKey('amount')) {
              value = double.tryParse(nutrient['amount'].toString()) ?? 0.0;
            }

            // Assegna il valore al campo appropriato
            if (field != null) {
              if (field == 'calories') {
                result[field] = value.round();
              } else {
                result[field] = value;
              }
            }
          }
        }
      }
    }

    return result;
  }

  /// Estrae i micronutrienti dai dati USDA
  Map<String, double> _extractMicronutrients(Map<String, dynamic> usdaFood) {
    final result = <String, double>{};

    // Mappa degli ID dei micronutrienti USDA ai nomi
    const micronutrientIdMap = {
      '1087': 'calcium',      // Calcio
      '1089': 'iron',         // Ferro
      '1090': 'magnesium',    // Magnesio
      '1091': 'phosphorus',   // Fosforo
      '1092': 'potassium',    // Potassio
      '1093': 'sodium',       // Sodio
      '1095': 'zinc',         // Zinco
      '1098': 'copper',       // Rame
      '1103': 'selenium',     // Selenio
      '1162': 'vitamin_c',    // Vitamina C
      '1165': 'vitamin_a',    // Vitamina A
      '1166': 'vitamin_e',    // Vitamina E
      '1175': 'vitamin_b6',   // Vitamina B6
      '1177': 'folate',       // Folati
      '1178': 'vitamin_b12',  // Vitamina B12
      '1180': 'choline',      // Colina
      '1185': 'vitamin_k',    // Vitamina K
    };

    // Estrai i micronutrienti
    if (usdaFood.containsKey('foodNutrients')) {
      final nutrients = usdaFood['foodNutrients'];
      if (nutrients is List) {
        for (final nutrient in nutrients) {
          String? nutrientId;

          if (nutrient.containsKey('nutrientId')) {
            nutrientId = nutrient['nutrientId'].toString();
          } else if (nutrient.containsKey('nutrient') &&
                    nutrient['nutrient'].containsKey('id')) {
            nutrientId = nutrient['nutrient']['id'].toString();
          }

          if (nutrientId != null && micronutrientIdMap.containsKey(nutrientId)) {
            final name = micronutrientIdMap[nutrientId];
            double value = 0.0;

            if (nutrient.containsKey('value')) {
              value = double.tryParse(nutrient['value'].toString()) ?? 0.0;
            } else if (nutrient.containsKey('amount')) {
              value = double.tryParse(nutrient['amount'].toString()) ?? 0.0;
            }

            if (name != null && value > 0) {
              result[name] = value;
            }
          }
        }
      }
    }

    return result;
  }

  /// Determina le categorie di alimenti in base ai dati USDA
  List<FoodCategory> _determineFoodCategories(Map<String, dynamic> usdaFood) {
    final categories = <FoodCategory>{};

    // Estrai informazioni sulla categoria
    final description = (usdaFood['description'] ?? '').toLowerCase();
    final foodClass = (usdaFood['foodClass'] ?? '').toLowerCase();
    final foodGroup = (usdaFood['foodGroup'] ?? '').toLowerCase();
    final foodCategory = (usdaFood['foodCategory'] ?? '').toLowerCase();

    // Verifica le categorie in base a parole chiave
    if (_containsAny(description, ['fruit', 'apple', 'banana', 'orange', 'berry', 'frutta', 'mela', 'banana', 'arancia'])) {
      categories.add(FoodCategory.fruit);
    }

    if (_containsAny(description, ['vegetable', 'carrot', 'broccoli', 'spinach', 'verdura', 'carota', 'broccoli', 'spinaci'])) {
      categories.add(FoodCategory.vegetable);
    }

    if (_containsAny(description, ['grain', 'bread', 'pasta', 'rice', 'cereal', 'pane', 'pasta', 'riso', 'cereale'])) {
      categories.add(FoodCategory.grain);
    }

    if (_containsAny(description, ['meat', 'chicken', 'beef', 'pork', 'fish', 'carne', 'pollo', 'manzo', 'maiale', 'pesce'])) {
      categories.add(FoodCategory.protein);
    }

    if (_containsAny(description, ['milk', 'cheese', 'yogurt', 'dairy', 'latte', 'formaggio', 'yogurt', 'latticini'])) {
      categories.add(FoodCategory.dairy);
    }

    if (_containsAny(description, ['oil', 'butter', 'margarine', 'olio', 'burro', 'margarina'])) {
      categories.add(FoodCategory.fat);
    }

    if (_containsAny(description, ['sweet', 'candy', 'chocolate', 'dessert', 'dolce', 'caramella', 'cioccolato'])) {
      categories.add(FoodCategory.sweet);
    }

    if (_containsAny(description, ['beverage', 'drink', 'juice', 'water', 'bevanda', 'succo', 'acqua'])) {
      categories.add(FoodCategory.beverage);
    }

    // Se non è stata trovata alcuna categoria, aggiungi "mixed"
    if (categories.isEmpty) {
      categories.add(FoodCategory.mixed);
    }

    return categories.toList();
  }

  /// Verifica se una stringa contiene una delle parole chiave
  bool _containsAny(String text, List<String> keywords) {
    for (final keyword in keywords) {
      if (text.contains(keyword)) {
        return true;
      }
    }
    return false;
  }

  /// Determina i tipi di pasto adatti in base alle categorie e ai dati USDA
  List<MealType> _determineSuitableMealTypes(Map<String, dynamic> usdaFood, List<FoodCategory> categories) {
    final mealTypes = <MealType>{};

    // Logica semplificata per determinare i tipi di pasto adatti
    // In un'implementazione reale, questa logica sarebbe più sofisticata

    // Colazione: frutta, cereali, latticini
    if (categories.contains(FoodCategory.fruit) ||
        categories.contains(FoodCategory.grain) ||
        categories.contains(FoodCategory.dairy)) {
      mealTypes.add(MealType.breakfast);
    }

    // Pranzo e cena: proteine, cereali, verdure
    if (categories.contains(FoodCategory.protein) ||
        categories.contains(FoodCategory.grain) ||
        categories.contains(FoodCategory.vegetable)) {
      mealTypes.add(MealType.lunch);
      mealTypes.add(MealType.dinner);
    }

    // Spuntino: frutta, latticini, dolci
    if (categories.contains(FoodCategory.fruit) ||
        categories.contains(FoodCategory.dairy) ||
        categories.contains(FoodCategory.sweet)) {
      mealTypes.add(MealType.snack);
    }

    // Se non è stato trovato alcun tipo di pasto, aggiungi tutti
    if (mealTypes.isEmpty) {
      mealTypes.addAll([MealType.breakfast, MealType.lunch, MealType.dinner, MealType.snack]);
    }

    return mealTypes.toList();
  }

  /// Estrae informazioni sugli allergeni dai dati USDA
  List<String> _extractAllergens(Map<String, dynamic> usdaFood) {
    final allergens = <String>{};
    final description = (usdaFood['description'] ?? '').toLowerCase();

    // Verifica la presenza di allergeni comuni
    if (_containsAny(description, ['milk', 'dairy', 'cheese', 'yogurt', 'latte', 'formaggio', 'yogurt'])) {
      allergens.add('latticini');
    }

    if (_containsAny(description, ['wheat', 'gluten', 'frumento', 'glutine'])) {
      allergens.add('glutine');
    }

    if (_containsAny(description, ['nut', 'peanut', 'almond', 'hazelnut', 'arachidi', 'mandorle', 'nocciole'])) {
      allergens.add('frutta a guscio');
    }

    if (_containsAny(description, ['egg', 'uovo', 'uova'])) {
      allergens.add('uova');
    }

    if (_containsAny(description, ['fish', 'pesce'])) {
      allergens.add('pesce');
    }

    if (_containsAny(description, ['shellfish', 'crostacei'])) {
      allergens.add('crostacei');
    }

    if (_containsAny(description, ['soy', 'soia'])) {
      allergens.add('soia');
    }

    return allergens.toList();
  }

  /// Determina l'idoneità a diete specifiche
  Map<String, bool> _determineDietarySuitability(Map<String, dynamic> usdaFood, List<String> allergens) {
    final result = {
      'isVegetarian': true,
      'isVegan': true,
      'isGlutenFree': true,
      'isDairyFree': true,
    };

    final description = (usdaFood['description'] ?? '').toLowerCase();

    // Verifica se è vegetariano
    if (_containsAny(description, ['meat', 'chicken', 'beef', 'pork', 'fish', 'carne', 'pollo', 'manzo', 'maiale', 'pesce'])) {
      result['isVegetarian'] = false;
      result['isVegan'] = false;
    }

    // Verifica se è vegano
    if (result['isVegetarian'] == true &&
        _containsAny(description, ['milk', 'cheese', 'yogurt', 'egg', 'honey', 'latte', 'formaggio', 'yogurt', 'uovo', 'miele'])) {
      result['isVegan'] = false;
    }

    // Verifica se è senza glutine
    if (_containsAny(description, ['wheat', 'gluten', 'frumento', 'glutine']) ||
        allergens.contains('glutine')) {
      result['isGlutenFree'] = false;
    }

    // Verifica se è senza latticini
    if (_containsAny(description, ['milk', 'cheese', 'yogurt', 'dairy', 'latte', 'formaggio', 'yogurt']) ||
        allergens.contains('latticini')) {
      result['isDairyFree'] = false;
    }

    return result;
  }

  /// Genera una descrizione per l'alimento
  String _generateDescription(Map<String, dynamic> usdaFood) {
    final description = usdaFood['description'] ?? '';
    final brandName = usdaFood['brandName'] ?? '';
    final additionalDescriptions = <String>[];

    if (usdaFood.containsKey('foodCategory')) {
      additionalDescriptions.add('Categoria: ${usdaFood['foodCategory']}');
    }

    if (brandName.isNotEmpty) {
      additionalDescriptions.add('Marca: $brandName');
    }

    if (additionalDescriptions.isEmpty) {
      return description;
    } else {
      return '$description (${additionalDescriptions.join(', ')})';
    }
  }

  /// Genera tag per l'alimento
  List<String> _generateTags(Map<String, dynamic> usdaFood, List<FoodCategory> categories, Map<String, bool> dietaryInfo) {
    final tags = <String>{};

    // Aggiungi tag per le categorie
    for (final category in categories) {
      tags.add(category.toString().split('.').last);
    }

    // Aggiungi tag per le diete
    if (dietaryInfo['isVegetarian'] == true) {
      tags.add('vegetariano');
    }

    if (dietaryInfo['isVegan'] == true) {
      tags.add('vegano');
    }

    if (dietaryInfo['isGlutenFree'] == true) {
      tags.add('senza glutine');
    }

    if (dietaryInfo['isDairyFree'] == true) {
      tags.add('senza latticini');
    }

    // Aggiungi tag per il tipo di alimento
    final description = (usdaFood['description'] ?? '').toLowerCase();

    if (_containsAny(description, ['raw', 'fresh', 'crudo', 'fresco'])) {
      tags.add('crudo');
    }

    if (_containsAny(description, ['cooked', 'baked', 'fried', 'boiled', 'cotto', 'al forno', 'fritto', 'bollito'])) {
      tags.add('cotto');
    }

    if (_containsAny(description, ['organic', 'biologico'])) {
      tags.add('biologico');
    }

    return tags.toList();
  }
}
