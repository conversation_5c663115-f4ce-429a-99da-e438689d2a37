// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'community_notification.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CommunityNotification _$CommunityNotificationFromJson(
        Map<String, dynamic> json) =>
    CommunityNotification(
      id: json['id'] as String,
      userId: json['userId'] as String,
      type: $enumDecode(_$NotificationTypeEnumMap, json['type']),
      postId: json['postId'] as String?,
      actorUserId: json['actorUserId'] as String,
      actorUserName: json['actorUserName'] as String,
      actorUserAvatar: json['actorUserAvatar'] as String?,
      message: json['message'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      isRead: json['isRead'] as bool? ?? false,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$CommunityNotificationToJson(
        CommunityNotification instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'type': _$NotificationTypeEnumMap[instance.type]!,
      'postId': instance.postId,
      'actorUserId': instance.actorUserId,
      'actorUserName': instance.actorUserName,
      'actorUserAvatar': instance.actorUserAvatar,
      'message': instance.message,
      'timestamp': instance.timestamp.toIso8601String(),
      'isRead': instance.isRead,
      'metadata': instance.metadata,
    };

const _$NotificationTypeEnumMap = {
  NotificationType.likePost: 'LIKE_POST',
  NotificationType.commentPost: 'COMMENT_POST',
  NotificationType.sharePost: 'SHARE_POST',
  NotificationType.tagUser: 'TAG_USER',
  NotificationType.friendRequest: 'FRIEND_REQUEST',
};

T $enumDecode<T>(
  Map<T, Object> enumValues,
  Object? source, {
  T? unknownValue,
}) {
  if (source == null) {
    throw ArgumentError(
      'A value must be provided. Supported values: '
      '${enumValues.values.join(', ')}',
    );
  }

  return enumValues.entries.singleWhere(
    (e) => e.value == source,
    orElse: () {
      if (unknownValue == null) {
        throw ArgumentError(
          '`$source` is not one of the supported values: '
          '${enumValues.values.join(', ')}',
        );
      }
      return MapEntry(unknownValue, enumValues.values.first);
    },
  ).key;
}
