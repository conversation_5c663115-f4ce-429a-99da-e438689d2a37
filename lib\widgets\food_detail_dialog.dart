import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/food.dart';
import '../utils/ui_utils.dart';
import '../ai/services/ai_service.dart';
import 'similar_foods_widget.dart';
import 'food_feedback_widget.dart';
import '../models/user_profile.dart';
import '../services/user_service.dart';

/// Widget che mostra i dettagli di un alimento
class FoodDetailDialog extends StatefulWidget {
  final Food food;
  final bool showSimilarFoods;
  final Function(Food)? onFoodSelected;

  const FoodDetailDialog({
    Key? key,
    required this.food,
    this.showSimilarFoods = true,
    this.onFoodSelected,
  }) : super(key: key);

  @override
  _FoodDetailDialogState createState() => _FoodDetailDialogState();
}

class _FoodDetailDialogState extends State<FoodDetailDialog> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  UserProfile? _userProfile;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadUserProfile();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadUserProfile() async {
    try {
      final userService = await UserService.getInstance();
      final profile = await userService.getCurrentUserProfile();
      
      setState(() {
        _userProfile = profile;
        _isLoading = false;
      });
    } catch (e) {
      print('Errore nel caricamento del profilo: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Header con nome dell'alimento
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(12),
              topRight: Radius.circular(12),
            ),
          ),
          child: Row(
            children: [
              Icon(
                UIUtils.getCategoryIcon(
                  widget.food.categories.isNotEmpty ? widget.food.categories.first : null,
                ),
                color: Colors.white,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  widget.food.name,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close, color: Colors.white),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
        ),

        // Tabs
        TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Nutrienti'),
            Tab(text: 'Dettagli'),
          ],
        ),

        // Tab content
        SizedBox(
          height: 300,
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildNutrientsTab(),
              _buildDetailsTab(),
            ],
          ),
        ),

        // Similar foods
        if (widget.showSimilarFoods && widget.onFoodSelected != null)
          Column(
            children: [
              const Divider(),
              SimilarFoodsWidget(
                food: widget.food,
                onFoodSelected: widget.onFoodSelected!,
                title: 'Alimenti simili',
                icon: Icons.restaurant,
                color: Theme.of(context).primaryColor,
                fetchFunction: (food, limit) async {
                  final aiService = await AIService.getInstance();
                  return aiService.getSimilarFoods(food, limit);
                },
              ),
              SimilarFoodsWidget(
                food: widget.food,
                onFoodSelected: widget.onFoodSelected!,
                title: 'Abbinamenti consigliati',
                icon: Icons.restaurant_menu,
                color: Colors.green,
                fetchFunction: (food, limit) async {
                  final aiService = await AIService.getInstance();
                  return aiService.getComplementaryFoods(food, limit);
                },
              ),
              SimilarFoodsWidget(
                food: widget.food,
                onFoodSelected: widget.onFoodSelected!,
                title: 'Alternative',
                icon: Icons.swap_horiz,
                color: Colors.orange,
                fetchFunction: (food, limit) async {
                  final aiService = await AIService.getInstance();
                  return aiService.getAlternativeFoods(food, limit);
                },
              ),
            ],
          ),

        // Feedback
        if (_userProfile != null)
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: FoodFeedbackWidget(
              food: widget.food,
              userProfile: _userProfile!,
            ),
          ),
      ],
    );
  }

  Widget _buildNutrientsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Macronutrienti principali
          _buildMacronutrientsSummary(),
          const SizedBox(height: 16),

          // Tabella dettagliata dei nutrienti
          _buildNutrientsTable(),
        ],
      ),
    );
  }

  Widget _buildMacronutrientsSummary() {
    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Valori nutrizionali per 100g',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildMacronutrientCircle('Calorie', '${widget.food.calories}', 'kcal', Colors.orange),
                _buildMacronutrientCircle('Proteine', '${widget.food.proteins}', 'g', Colors.red),
                _buildMacronutrientCircle('Carboidrati', '${widget.food.carbs}', 'g', Colors.blue),
                _buildMacronutrientCircle('Grassi', '${widget.food.fats}', 'g', Colors.green),
              ],
            ),
            if (widget.food.fiber > 0)
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildMacronutrientCircle('Fibre', '${widget.food.fiber}', 'g', Colors.brown),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildMacronutrientCircle(String label, String value, String unit, Color color) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: color.withOpacity(0.1),
            border: Border.all(color: color, width: 2),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  value,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: color,
                  ),
                ),
                Text(
                  unit,
                  style: TextStyle(
                    fontSize: 12,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildNutrientsTable() {
    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Dettaglio nutrienti',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Table(
              columnWidths: const {
                0: FlexColumnWidth(3),
                1: FlexColumnWidth(2),
                2: FlexColumnWidth(1),
              },
              children: [
                _buildTableRow('Nutriente', 'Valore', 'Unità', isHeader: true),
                _buildTableRow('Calorie', '${widget.food.calories}', 'kcal'),
                _buildTableRow('Proteine', '${widget.food.proteins}', 'g'),
                _buildTableRow('Carboidrati', '${widget.food.carbs}', 'g'),
                if (widget.food.sugars != null)
                  _buildTableRow('- di cui zuccheri', '${widget.food.sugars}', 'g'),
                _buildTableRow('Grassi', '${widget.food.fats}', 'g'),
                if (widget.food.saturatedFats != null)
                  _buildTableRow('- di cui saturi', '${widget.food.saturatedFats}', 'g'),
                if (widget.food.fiber > 0)
                  _buildTableRow('Fibre', '${widget.food.fiber}', 'g'),
                if (widget.food.sodium != null)
                  _buildTableRow('Sodio', '${widget.food.sodium}', 'mg'),
                if (widget.food.potassium != null)
                  _buildTableRow('Potassio', '${widget.food.potassium}', 'mg'),
                if (widget.food.calcium != null)
                  _buildTableRow('Calcio', '${widget.food.calcium}', 'mg'),
                if (widget.food.magnesium != null)
                  _buildTableRow('Magnesio', '${widget.food.magnesium}', 'mg'),
                if (widget.food.phosphorus != null)
                  _buildTableRow('Fosforo', '${widget.food.phosphorus}', 'mg'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  TableRow _buildTableRow(String label, String value, String unit, {bool isHeader = false}) {
    final style = isHeader
        ? const TextStyle(fontWeight: FontWeight.bold)
        : const TextStyle();
    
    return TableRow(
      decoration: BoxDecoration(
        color: isHeader ? Colors.grey[200] : null,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey[300]!,
            width: 1,
          ),
        ),
      ),
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
          child: Text(label, style: style),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
          child: Text(value, style: style, textAlign: TextAlign.right),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
          child: Text(unit, style: style),
        ),
      ],
    );
  }

  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Categorie
          _buildCategoriesSection(),
          const SizedBox(height: 16),

          // Proprietà
          _buildPropertiesSection(),
          const SizedBox(height: 16),

          // Allergeni
          if (widget.food.allergens.isNotEmpty)
            _buildAllergensSection(),
        ],
      ),
    );
  }

  Widget _buildCategoriesSection() {
    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Categorie',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: widget.food.categories.map((category) {
                return Chip(
                  avatar: Icon(
                    UIUtils.getCategoryIcon(category),
                    size: 16,
                    color: UIUtils.getCategoryColor(category),
                  ),
                  label: Text(UIUtils.getFoodCategoryName(category)),
                  backgroundColor: UIUtils.getCategoryColor(category).withOpacity(0.1),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPropertiesSection() {
    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Proprietà',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            _buildPropertyRow('Vegetariano', widget.food.isVegetarian),
            _buildPropertyRow('Vegano', widget.food.isVegan),
            _buildPropertyRow('Senza glutine', widget.food.isGlutenFree),
            _buildPropertyRow('Senza lattosio', widget.food.isDairyFree),
          ],
        ),
      ),
    );
  }

  Widget _buildPropertyRow(String label, bool value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Icon(
            value ? Icons.check_circle : Icons.cancel,
            color: value ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(label),
        ],
      ),
    );
  }

  Widget _buildAllergensSection() {
    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Allergeni',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: widget.food.allergens.map((allergen) {
                return Chip(
                  avatar: const Icon(
                    FontAwesomeIcons.triangleExclamation,
                    size: 16,
                    color: Colors.orange,
                  ),
                  label: Text(allergen),
                  backgroundColor: Colors.orange.withOpacity(0.1),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }
}
