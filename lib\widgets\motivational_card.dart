import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../constants/image_constants.dart';
import '../theme/app_theme.dart';

class MotivationalCard extends StatelessWidget {
  final String tip;
  final String? imageUrl;

  const MotivationalCard({
    super.key,
    required this.tip,
    this.imageUrl,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final finalImageUrl = imageUrl ?? ImageConstants.motivationalImage1;
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      clipBehavior: Clip.antiAlias,
      child: Stack(
        children: [
          // Immagine di sfondo
          SizedBox(
            height: 120,
            width: double.infinity,
            child: CachedNetworkImage(
              imageUrl: finalImageUrl,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: Colors.grey.shade200,
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
              errorWidget: (context, url, error) => Container(
                color: Colors.grey.shade200,
                child: const Icon(Icons.image, size: 40),
              ),
            ),
          ),
          
          // Overlay scuro
          Container(
            height: 120,
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withOpacity(0.3),
                  Colors.black.withOpacity(0.7),
                ],
              ),
            ),
          ),
          
          // Testo motivazionale
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        color: AppTheme.accentColor,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Consiglio del giorno',
                        style: textTheme.titleSmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    tip,
                    style: textTheme.bodyMedium?.copyWith(
                      color: Colors.white,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  // Lista di suggerimenti predefiniti
  static List<String> getTips() {
    return [
      'Bevi un bicchiere d\'acqua prima di ogni pasto per sentirti più sazio.',
      'Aggiungi più verdure colorate al tuo piatto per aumentare i nutrienti.',
      'Prova a fare una passeggiata di 10 minuti dopo i pasti principali.',
      'Pianifica i pasti in anticipo per evitare scelte impulsive.',
      'Mangia lentamente e assapora ogni boccone per migliorare la digestione.',
      'Scegli snack proteici per mantenerti sazio più a lungo.',
      'Limita il consumo di zuccheri aggiunti per mantenere stabili i livelli di energia.',
      'Prova a sostituire bevande zuccherate con acqua o tè non zuccherato.',
      'Aggiungi spezie alle tue ricette per ridurre il bisogno di sale.',
      'Ricorda che piccoli cambiamenti costanti portano a grandi risultati nel tempo.',
    ];
  }
  
  // Ottieni un suggerimento casuale
  static String getRandomTip() {
    final tips = getTips();
    final now = DateTime.now();
    final index = now.day % tips.length;
    return tips[index];
  }
}
