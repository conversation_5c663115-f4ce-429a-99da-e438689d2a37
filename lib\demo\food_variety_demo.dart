import '../services/food_variety_manager.dart';
import '../services/enhanced_food_selector.dart';
import '../services/food_database_analyzer.dart';
import '../utils/food_variety_monitor.dart';
import '../models/user_profile.dart';
import '../models/food.dart';

/// Demo per mostrare i miglioramenti nella varietà degli alimenti
class FoodVarietyDemo {
  static Future<void> runDemo() async {
    print('🍽️ DEMO: Miglioramenti nella Varietà degli Alimenti\n');
    print('=' * 60);

    try {
      // Inizializza i servizi
      print('📋 Inizializzazione servizi...');
      final varietyManager = await FoodVarietyManager.getInstance();
      final enhancedSelector = await EnhancedFoodSelector.create();
      final analyzer = await FoodDatabaseAnalyzer.create();
      final monitor = await FoodVarietyMonitor.create();

      // Reset per demo pulita
      await varietyManager.resetUsageHistory();

      // Crea profilo utente di test
      final testProfile = UserProfile(
        id: 'demo_user',
        name: 'Utente Demo',
        age: 35,
        gender: Gender.female,
        height: 165,
        weight: 60,
        activityLevel: ActivityLevel.moderate,
        goal: Goal.maintain,
        dietType: DietType.omnivore,
        allergies: [],
        dislikedFoods: [],
        mealsPerDay: 3,
      );

      print('✅ Servizi inizializzati\n');

      // 1. Analisi iniziale del database
      print('📊 ANALISI INIZIALE DEL DATABASE');
      print('-' * 40);
      final initialReport = await analyzer.generateUtilizationReport();
      print(initialReport);

      // 2. Genera piani dietetici con varietà migliorata
      print('\n🍽️ GENERAZIONE PIANI DIETETICI CON VARIETÀ');
      print('-' * 50);
      
      final mealTypes = ['breakfast', 'lunch', 'dinner'];
      final selectedFoodsPerDay = <int, List<String>>{};

      for (int day = 1; day <= 7; day++) {
        print('\n📅 Giorno $day:');
        final dayFoods = <String>[];

        for (final mealType in mealTypes) {
          try {
            final selectedFoods = await enhancedSelector.selectFoodsForMeal(
              userProfile: testProfile,
              mealType: mealType,
              targetCalories: _getCaloriesForMeal(mealType),
              targetMacros: _getMacrosForMeal(mealType),
              forDate: DateTime.now().add(Duration(days: day - 1)),
            );

            if (selectedFoods.isNotEmpty) {
              final foodNames = selectedFoods.map((fp) => fp.food.name).toList();
              dayFoods.addAll(foodNames);
              print('  ${_getMealDisplayName(mealType)}: ${foodNames.join(', ')}');
            } else {
              print('  ${_getMealDisplayName(mealType)}: Nessun alimento selezionato');
            }
          } catch (e) {
            print('  ${_getMealDisplayName(mealType)}: Errore nella selezione ($e)');
          }
        }

        selectedFoodsPerDay[day] = dayFoods;
      }

      // 3. Analisi della varietà ottenuta
      print('\n📈 ANALISI DELLA VARIETÀ OTTENUTA');
      print('-' * 40);
      
      final allSelectedFoods = selectedFoodsPerDay.values
          .expand((foods) => foods)
          .toList();
      
      final uniqueFoods = allSelectedFoods.toSet();
      final varietyRatio = uniqueFoods.length / allSelectedFoods.length;

      print('• Alimenti totali selezionati: ${allSelectedFoods.length}');
      print('• Alimenti unici: ${uniqueFoods.length}');
      print('• Rapporto di varietà: ${(varietyRatio * 100).toStringAsFixed(1)}%');

      // Analizza la varietà per giorno
      print('\n📊 Varietà per giorno:');
      for (int day = 1; day <= 7; day++) {
        final dayFoods = selectedFoodsPerDay[day] ?? [];
        final uniqueDayFoods = dayFoods.toSet();
        final dayVariety = dayFoods.isNotEmpty ? uniqueDayFoods.length / dayFoods.length : 0;
        print('  Giorno $day: ${uniqueDayFoods.length}/${dayFoods.length} alimenti unici (${(dayVariety * 100).toStringAsFixed(0)}%)');
      }

      // 4. Report di monitoraggio
      print('\n🔍 REPORT DI MONITORAGGIO VARIETÀ');
      print('-' * 40);
      
      final varietyReport = await monitor.generateVarietyReport();
      print('• Punteggio varietà: ${varietyReport.varietyScore.toStringAsFixed(1)}/100');
      print('• Tasso utilizzo database: ${(varietyReport.databaseUtilizationRate * 100).toStringAsFixed(1)}%');
      print('• Alimenti tracciati: ${varietyReport.totalTrackedFoods}');
      print('• Utilizzi recenti: ${varietyReport.recentUsages}');

      // 5. Consigli per migliorare la varietà
      print('\n💡 CONSIGLI PER MIGLIORARE LA VARIETÀ');
      print('-' * 40);
      
      final tips = await monitor.generateVarietyTips();
      for (int i = 0; i < tips.length; i++) {
        print('${i + 1}. ${tips[i]}');
      }

      // 6. Alimenti suggeriti da provare
      print('\n🌟 ALIMENTI SUGGERITI DA PROVARE');
      print('-' * 35);
      
      final suggestions = await analyzer.suggestFoodsToPromote(maxSuggestions: 5);
      if (suggestions.isNotEmpty) {
        for (int i = 0; i < suggestions.length; i++) {
          final food = suggestions[i];
          final categories = food.categories.map((c) => c.toString().split('.').last).join(', ');
          print('${i + 1}. ${food.name} ($categories)');
        }
      } else {
        print('Nessun suggerimento disponibile (database non inizializzato)');
      }

      print('\n' + '=' * 60);
      print('✅ Demo completata con successo!');
      print('🎯 Il sistema di varietà migliorata è ora attivo e funzionante.');

    } catch (e) {
      print('\n❌ Errore durante la demo: $e');
      print('💡 Questo è normale se il database degli alimenti non è ancora inizializzato.');
    }
  }

  static int _getCaloriesForMeal(String mealType) {
    switch (mealType) {
      case 'breakfast':
        return 400;
      case 'lunch':
        return 600;
      case 'dinner':
        return 500;
      default:
        return 300;
    }
  }

  static Map<String, int> _getMacrosForMeal(String mealType) {
    switch (mealType) {
      case 'breakfast':
        return {'proteins': 20, 'carbs': 50, 'fats': 15};
      case 'lunch':
        return {'proteins': 35, 'carbs': 60, 'fats': 20};
      case 'dinner':
        return {'proteins': 30, 'carbs': 45, 'fats': 18};
      default:
        return {'proteins': 15, 'carbs': 30, 'fats': 10};
    }
  }

  static String _getMealDisplayName(String mealType) {
    switch (mealType) {
      case 'breakfast':
        return 'Colazione';
      case 'lunch':
        return 'Pranzo';
      case 'dinner':
        return 'Cena';
      case 'snack':
        return 'Spuntino';
      default:
        return mealType;
    }
  }
}

/// Funzione principale per eseguire la demo
void main() async {
  await FoodVarietyDemo.runDemo();
}
