import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// Widget per mostrare un indicatore di caricamento con un messaggio
class LoadingIndicator extends StatelessWidget {
  /// Messaggio da mostrare
  final String message;
  
  /// Colore dell'indicatore
  final Color? color;
  
  /// Dimensione dell'indicatore
  final double size;

  const LoadingIndicator({
    Key? key,
    required this.message,
    this.color,
    this.size = 40.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CircularProgressIndicator(
          color: color ?? AppTheme.primaryColor,
          strokeWidth: 3.0,
        ),
        const SizedBox(height: 16.0),
        Text(
          message,
          style: TextStyle(
            fontSize: 16.0,
            fontWeight: FontWeight.w500,
            color: color ?? AppTheme.primaryColor,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
