import 'package:flutter_test/flutter_test.dart';
import 'package:nutriplan_staffilano/services/nutriscore_service.dart';

void main() {
  group('NutriScore Service Tests', () {
    late NutriScoreService nutriScoreService;

    setUp(() {
      nutriScoreService = NutriScoreService.instance;
    });

    test('NutriScore should handle null controller gracefully', () {
      final result = nutriScoreService.calculateNutriScore(null);

      expect(result.totalScore, equals(45.0)); // Punteggio di default sicuro
      expect(result.level.displayName, equals('Principiante'));
      expect(result.recommendations, isNotEmpty);

      print('✅ Test Null Safety:');
      print('   - Punteggio con controller null: ${result.totalScore}');
      print('   - Livello: ${result.level.displayName}');
      print('   - Raccomandazioni: ${result.recommendations.length}');
    });

    test('NutriScore levels should be calculated correctly', () {
      // Test dei livelli NutriScore
      final testCases = [
        {'score': 0.0, 'expectedLevel': 'Novizio'},
        {'score': 25.0, 'expectedLevel': 'Novizio'},
        {'score': 40.0, 'expectedLevel': 'Principiante'},
        {'score': 60.0, 'expectedLevel': 'Intermedio'},
        {'score': 75.0, 'expectedLevel': 'Avanzato'},
        {'score': 90.0, 'expectedLevel': 'Esperto'},
      ];

      for (final testCase in testCases) {
        final score = testCase['score'] as double;
        final expectedLevel = testCase['expectedLevel'] as String;

        // Simula un controller con punteggio specifico
        final result = nutriScoreService.calculateNutriScore(null);

        print('✅ Test Livello $expectedLevel:');
        print('   - Punteggio: $score → Livello atteso: $expectedLevel');
      }
    });

    test('NutriScore should provide meaningful recommendations', () {
      final result = nutriScoreService.calculateNutriScore(null);

      expect(result.recommendations, isNotEmpty);
      expect(result.recommendations.length, greaterThanOrEqualTo(3));
      expect(result.nextMilestone, isNotNull);
      expect(result.nextMilestone.title, isNotEmpty);

      print('✅ Test Raccomandazioni:');
      print('   - Numero raccomandazioni: ${result.recommendations.length}');
      print('   - Prossimo traguardo: ${result.nextMilestone.title}');
      print('   - Punti necessari: ${result.nextMilestone.pointsNeeded}');

      for (int i = 0; i < result.recommendations.length; i++) {
        print('   - Raccomandazione ${i + 1}: ${result.recommendations[i]}');
      }
    });

    test('NutriScore should have valid score components', () {
      final result = nutriScoreService.calculateNutriScore(null);

      // Verifica che tutti i componenti siano validi
      expect(result.educationalScore, greaterThanOrEqualTo(0));
      expect(result.consistencyScore, greaterThanOrEqualTo(0));
      expect(result.applicationScore, greaterThanOrEqualTo(0));
      expect(result.expertiseScore, greaterThanOrEqualTo(0));

      expect(result.educationalScore, lessThanOrEqualTo(25));
      expect(result.consistencyScore, lessThanOrEqualTo(25));
      expect(result.applicationScore, lessThanOrEqualTo(25));
      expect(result.expertiseScore, lessThanOrEqualTo(25));

      final calculatedTotal = result.educationalScore +
                            result.consistencyScore +
                            result.applicationScore +
                            result.expertiseScore;

      expect(result.totalScore, equals(calculatedTotal));

      print('✅ Test Componenti Punteggio:');
      print('   - Educativo: ${result.educationalScore.toStringAsFixed(1)}');
      print('   - Consistenza: ${result.consistencyScore.toStringAsFixed(1)}');
      print('   - Applicazione: ${result.applicationScore.toStringAsFixed(1)}');
      print('   - Expertise: ${result.expertiseScore.toStringAsFixed(1)}');
      print('   - Totale: ${result.totalScore.toStringAsFixed(1)}');
      print('   - Totale calcolato: ${calculatedTotal.toStringAsFixed(1)}');
    });

    test('NutriScore should have proper areas identification', () {
      final result = nutriScoreService.calculateNutriScore(null);

      expect(result.strengthAreas, isNotNull);
      expect(result.improvementAreas, isNotNull);
      expect(result.improvementAreas, isNotEmpty); // Con punteggio 0, dovrebbe avere aree di miglioramento

      print('✅ Test Aree di Forza e Miglioramento:');
      print('   - Aree di forza: ${result.strengthAreas.length}');
      for (final strength in result.strengthAreas) {
        print('     • $strength');
      }
      print('   - Aree di miglioramento: ${result.improvementAreas.length}');
      for (final improvement in result.improvementAreas) {
        print('     • $improvement');
      }
    });
  });
}
