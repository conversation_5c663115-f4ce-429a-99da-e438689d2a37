import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'dart:io';
import '../models/food_recognition_result.dart';
import '../theme/dr_staffilano_theme.dart';
import 'food_recognition_feedback_widget.dart';

/// Widget per visualizzare i risultati del riconoscimento alimentare
class FoodRecognitionResultCard extends StatefulWidget {
  final FoodRecognitionResult result;
  final VoidCallback? onSave;
  final VoidCallback? onRetake;
  final Function(FoodRecognitionResult)? onResultCorrected;

  const FoodRecognitionResultCard({
    Key? key,
    required this.result,
    this.onSave,
    this.onRetake,
    this.onResultCorrected,
  }) : super(key: key);

  @override
  State<FoodRecognitionResultCard> createState() => _FoodRecognitionResultCardState();
}

class _FoodRecognitionResultCardState extends State<FoodRecognitionResultCard> {
  late FoodRecognitionResult _currentResult;

  @override
  void initState() {
    super.initState();
    _currentResult = widget.result;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Immagine analizzata
          _buildImageSection(),

          const SizedBox(height: 20),

          // Informazioni generali
          _buildGeneralInfo(),

          const SizedBox(height: 20),

          // Alimenti riconosciuti
          _buildRecognizedFoods(),

          const SizedBox(height: 20),

          // Riepilogo nutrizionale
          _buildNutritionalSummary(),

          const SizedBox(height: 20),

          // Suggerimenti
          _buildSuggestions(),

          const SizedBox(height: 30),

          // Pulsanti di azione
          _buildActionButtons(),
        ],
      ),
    );
  }

  /// Costruisce la sezione dell'immagine
  Widget _buildImageSection() {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: File(_currentResult.imagePath).existsSync()
            ? Image.file(
                File(_currentResult.imagePath),
                fit: BoxFit.cover,
              )
            : Container(
                color: DrStaffilanoTheme.backgroundLight,
                child: const Icon(
                  FontAwesomeIcons.image,
                  size: 50,
                  color: DrStaffilanoTheme.textSecondary,
                ),
              ),
      ),
    );
  }

  /// Costruisce le informazioni generali
  Widget _buildGeneralInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.backgroundWhite,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: DrStaffilanoTheme.borderLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.utensils,
                color: DrStaffilanoTheme.primaryGreen,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                _currentResult.mealTypeDisplayName,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: DrStaffilanoTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(
                FontAwesomeIcons.clock,
                color: DrStaffilanoTheme.textSecondary,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'Analizzato il ${_formatDateTime(_currentResult.timestamp)}',
                style: const TextStyle(
                  fontSize: 14,
                  color: DrStaffilanoTheme.textSecondary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                FontAwesomeIcons.chartLine,
                color: _getConfidenceColor(),
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'Confidenza: ${_currentResult.confidenceLevel} (${(_currentResult.confidenceScore * 100).toStringAsFixed(1)}%)',
                style: TextStyle(
                  fontSize: 14,
                  color: _getConfidenceColor(),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Costruisce la lista degli alimenti riconosciuti
  Widget _buildRecognizedFoods() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.backgroundWhite,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: DrStaffilanoTheme.borderLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.magnifyingGlass,
                color: DrStaffilanoTheme.primaryGreen,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Alimenti Riconosciuti (${_currentResult.recognizedFoods.length})',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: DrStaffilanoTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (_currentResult.recognizedFoods.isEmpty)
            const Text(
              'Nessun alimento riconosciuto nell\'immagine.',
              style: TextStyle(
                fontSize: 14,
                color: DrStaffilanoTheme.textSecondary,
                fontStyle: FontStyle.italic,
              ),
            )
          else
            ..._currentResult.recognizedFoods.map((recognizedFood) =>
              _buildFoodItem(recognizedFood)
            ).toList(),
        ],
      ),
    );
  }

  /// Costruisce un singolo elemento alimentare
  Widget _buildFoodItem(RecognizedFood recognizedFood) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.backgroundLight,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: DrStaffilanoTheme.borderLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  recognizedFood.food.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: DrStaffilanoTheme.textPrimary,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getFoodConfidenceColor(recognizedFood.confidence),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  recognizedFood.confidenceLevel,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            recognizedFood.food.description,
            style: const TextStyle(
              fontSize: 14,
              color: DrStaffilanoTheme.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                FontAwesomeIcons.scaleBalanced,
                color: DrStaffilanoTheme.primaryGreen,
                size: 14,
              ),
              const SizedBox(width: 6),
              Text(
                '${recognizedFood.estimatedGrams}g',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: DrStaffilanoTheme.primaryGreen,
                ),
              ),
              const SizedBox(width: 16),
              Icon(
                FontAwesomeIcons.fire,
                color: DrStaffilanoTheme.accentGold,
                size: 14,
              ),
              const SizedBox(width: 6),
              Text(
                '${recognizedFood.estimatedCalories} kcal',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: DrStaffilanoTheme.accentGold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Costruisce il riepilogo nutrizionale
  Widget _buildNutritionalSummary() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.backgroundWhite,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: DrStaffilanoTheme.borderLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.chartPie,
                color: DrStaffilanoTheme.primaryGreen,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'Riepilogo Nutrizionale',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: DrStaffilanoTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildNutrientCard(
                  'Calorie',
                  '${_currentResult.nutritionalSummary.calories}',
                  'kcal',
                  FontAwesomeIcons.fire,
                  DrStaffilanoTheme.accentGold,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildNutrientCard(
                  'Proteine',
                  _currentResult.nutritionalSummary.proteins.toStringAsFixed(1),
                  'g',
                  FontAwesomeIcons.dumbbell,
                  DrStaffilanoTheme.primaryGreen,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildNutrientCard(
                  'Carboidrati',
                  _currentResult.nutritionalSummary.carbs.toStringAsFixed(1),
                  'g',
                  FontAwesomeIcons.wheatAwn,
                  DrStaffilanoTheme.professionalBlue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildNutrientCard(
                  'Grassi',
                  _currentResult.nutritionalSummary.fats.toStringAsFixed(1),
                  'g',
                  FontAwesomeIcons.droplet,
                  Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Costruisce una card per un nutriente
  Widget _buildNutrientCard(String label, String value, String unit, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            '$value $unit',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// Costruisce la sezione dei suggerimenti
  Widget _buildSuggestions() {
    if (_currentResult.suggestions.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.backgroundWhite,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: DrStaffilanoTheme.borderLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.lightbulb,
                color: DrStaffilanoTheme.accentGold,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'Suggerimenti del Dr. Staffilano',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: DrStaffilanoTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ..._currentResult.suggestions.map((suggestion) =>
            Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('• ', style: TextStyle(
                    fontSize: 16,
                    color: DrStaffilanoTheme.accentGold,
                    fontWeight: FontWeight.bold,
                  )),
                  Expanded(
                    child: Text(
                      suggestion,
                      style: const TextStyle(
                        fontSize: 14,
                        color: DrStaffilanoTheme.textPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ).toList(),
        ],
      ),
    );
  }

  /// Costruisce i pulsanti di azione
  Widget _buildActionButtons() {
    return Column(
      children: [
        // Prima riga: Correggi risultati
        if (widget.onResultCorrected != null)
          Container(
            width: double.infinity,
            margin: const EdgeInsets.only(bottom: 12),
            child: OutlinedButton.icon(
              onPressed: () => _showFeedbackDialog(),
              icon: const Icon(FontAwesomeIcons.userPen),
              label: const Text('Correggi Risultati'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                side: const BorderSide(color: DrStaffilanoTheme.accentGold),
                foregroundColor: DrStaffilanoTheme.accentGold,
              ),
            ),
          ),

        // Seconda riga: Nuova foto e Salva
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: widget.onRetake,
                icon: const Icon(FontAwesomeIcons.camera),
                label: const Text('Nuova Foto'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: const BorderSide(color: DrStaffilanoTheme.primaryGreen),
                  foregroundColor: DrStaffilanoTheme.primaryGreen,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: widget.onSave,
                icon: const Icon(FontAwesomeIcons.floppyDisk),
                label: const Text('Salva'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  backgroundColor: DrStaffilanoTheme.primaryGreen,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Mostra il dialog di feedback per correggere i risultati
  void _showFeedbackDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FoodRecognitionFeedbackWidget(
        result: _currentResult,
        onResultCorrected: (correctedResult) {
          setState(() {
            _currentResult = correctedResult;
          });
          widget.onResultCorrected?.call(correctedResult);
        },
        onClose: () => Navigator.of(context).pop(),
      ),
    );
  }

  /// Formatta la data e ora
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} alle ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// Ottiene il colore per il livello di confidenza
  Color _getConfidenceColor() {
    if (_currentResult.confidenceScore >= 0.8) return DrStaffilanoTheme.primaryGreen;
    if (_currentResult.confidenceScore >= 0.6) return Colors.orange;
    return Colors.red;
  }

  /// Ottiene il colore per la confidenza di un singolo alimento
  Color _getFoodConfidenceColor(double confidence) {
    if (confidence >= 0.8) return DrStaffilanoTheme.primaryGreen;
    if (confidence >= 0.6) return Colors.orange;
    return Colors.red;
  }
}
