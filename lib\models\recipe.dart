import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'food.dart';

class RecipeIngredient {
  final String id;
  final String foodId; // ID dell'alimento nel database
  final String name; // Nome dell'ingrediente (può essere diverso dal nome dell'alimento)
  final int quantity; // Quantità in grammi o millilitri
  final String unit; // Unità di misura (g, ml, cucchiaio, ecc.)
  final String notes; // Note aggiuntive (es. "tagliato a cubetti")
  final bool isOptional; // Indica se l'ingrediente è opzionale

  // Valori nutrizionali calcolati per la quantità specificata
  final int calories;
  final double proteins;
  final double carbs;
  final double fats;
  final double fiber;
  final double sugar;

  RecipeIngredient({
    String? id,
    required this.foodId,
    required this.name,
    required this.quantity,
    required this.unit,
    this.notes = '',
    this.isOptional = false,
    required this.calories,
    required this.proteins,
    required this.carbs,
    required this.fats,
    this.fiber = 0,
    this.sugar = 0,
  }) : id = id ?? const Uuid().v4();

  // Crea un ingrediente a partire da un alimento e una quantità
  factory RecipeIngredient.fromFood({
    required Food food,
    required int quantity,
    required String unit,
    String notes = '',
    bool isOptional = false,
  }) {
    // Calcola i valori nutrizionali in base alla quantità
    final double factor = quantity / 100.0; // Fattore di conversione (i valori nutrizionali sono per 100g)

    return RecipeIngredient(
      foodId: food.id,
      name: food.name,
      quantity: quantity,
      unit: unit,
      notes: notes,
      isOptional: isOptional,
      calories: (food.calories * factor).round(),
      proteins: food.proteins * factor,
      carbs: food.carbs * factor,
      fats: food.fats * factor,
      fiber: food.fiber * factor,
      sugar: food.sugar * factor,
    );
  }

  // Converti da RecipeIngredient a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'foodId': foodId,
      'name': name,
      'quantity': quantity,
      'unit': unit,
      'notes': notes,
      'isOptional': isOptional,
      'calories': calories,
      'proteins': proteins,
      'carbs': carbs,
      'fats': fats,
      'fiber': fiber,
      'sugar': sugar,
    };
  }

  // Converti da Map a RecipeIngredient
  factory RecipeIngredient.fromMap(Map<String, dynamic> map) {
    return RecipeIngredient(
      id: map['id'] as String,
      foodId: map['foodId'] as String,
      name: map['name'] as String,
      quantity: map['quantity'] as int,
      unit: map['unit'] as String,
      notes: map['notes'] as String? ?? '',
      isOptional: map['isOptional'] as bool? ?? false,
      calories: map['calories'] as int,
      proteins: map['proteins'] as double,
      carbs: map['carbs'] as double,
      fats: map['fats'] as double,
      fiber: map['fiber'] as double? ?? 0,
      sugar: map['sugar'] as double? ?? 0,
    );
  }

  // Converti da RecipeIngredient a JSON
  String toJson() => json.encode(toMap());

  // Converti da JSON a RecipeIngredient
  factory RecipeIngredient.fromJson(String source) =>
      RecipeIngredient.fromMap(json.decode(source) as Map<String, dynamic>);
}

class Recipe {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final List<RecipeIngredient> ingredients;
  final String instructions;
  final int preparationTimeMinutes;
  final int cookingTimeMinutes;
  final int servings;
  final List<MealType> suitableForMeals;
  final List<FoodCategory> categories;
  final bool isVegetarian;
  final bool isVegan;
  final bool isGlutenFree;
  final bool isDairyFree;
  final List<String> allergens;
  final int complexity; // complessità da 1 (semplice) a 5 (complesso)
  final bool isSeasonal;
  final List<int> seasonalMonths; // mesi in cui è disponibile (1-12)
  final List<String> tags;

  // Valori nutrizionali totali della ricetta
  final int totalCalories;
  final double totalProteins;
  final double totalCarbs;
  final double totalFats;
  final double totalFiber;
  final double totalSugar;

  // Valori nutrizionali per porzione
  final int caloriesPerServing;
  final double proteinsPerServing;
  final double carbsPerServing;
  final double fatsPerServing;
  final double fiberPerServing;
  final double sugarPerServing;

  // Campi per la gestione delle fonti e validazione
  final String source; // fonte della ricetta
  final ValidationStatus validationStatus;
  final DateTime lastValidatedAt;
  final String validatedBy;

  Recipe({
    String? id,
    required this.name,
    this.description = '',
    this.imageUrl = '',
    required this.ingredients,
    required this.instructions,
    required this.preparationTimeMinutes,
    required this.cookingTimeMinutes,
    required this.servings,
    required this.suitableForMeals,
    required this.categories,
    this.isVegetarian = false,
    this.isVegan = false,
    this.isGlutenFree = false,
    this.isDairyFree = false,
    this.allergens = const [],
    this.complexity = 1,
    this.isSeasonal = false,
    this.seasonalMonths = const [],
    this.tags = const [],
    int? totalCalories,
    double? totalProteins,
    double? totalCarbs,
    double? totalFats,
    double? totalFiber,
    double? totalSugar,
    int? caloriesPerServing,
    double? proteinsPerServing,
    double? carbsPerServing,
    double? fatsPerServing,
    double? fiberPerServing,
    double? sugarPerServing,
    this.source = '',
    this.validationStatus = ValidationStatus.unverified,
    DateTime? lastValidatedAt,
    this.validatedBy = '',
  }) :
    id = id ?? const Uuid().v4(),
    totalCalories = totalCalories ?? _calculateTotalCalories(ingredients),
    totalProteins = totalProteins ?? _calculateTotalProteins(ingredients),
    totalCarbs = totalCarbs ?? _calculateTotalCarbs(ingredients),
    totalFats = totalFats ?? _calculateTotalFats(ingredients),
    totalFiber = totalFiber ?? _calculateTotalFiber(ingredients),
    totalSugar = totalSugar ?? _calculateTotalSugar(ingredients),
    caloriesPerServing = caloriesPerServing ??
      (_calculateTotalCalories(ingredients) / (servings > 0 ? servings : 1)).round(),
    proteinsPerServing = proteinsPerServing ??
      _calculateTotalProteins(ingredients) / (servings > 0 ? servings : 1),
    carbsPerServing = carbsPerServing ??
      _calculateTotalCarbs(ingredients) / (servings > 0 ? servings : 1),
    fatsPerServing = fatsPerServing ??
      _calculateTotalFats(ingredients) / (servings > 0 ? servings : 1),
    fiberPerServing = fiberPerServing ??
      _calculateTotalFiber(ingredients) / (servings > 0 ? servings : 1),
    sugarPerServing = sugarPerServing ??
      _calculateTotalSugar(ingredients) / (servings > 0 ? servings : 1),
    this.lastValidatedAt = lastValidatedAt ?? DateTime.fromMillisecondsSinceEpoch(0);

  // Metodi statici per calcolare i totali
  static int _calculateTotalCalories(List<RecipeIngredient> ingredients) {
    return ingredients.fold(0, (sum, ingredient) => sum + ingredient.calories);
  }

  static double _calculateTotalProteins(List<RecipeIngredient> ingredients) {
    return ingredients.fold(0.0, (sum, ingredient) => sum + ingredient.proteins);
  }

  static double _calculateTotalCarbs(List<RecipeIngredient> ingredients) {
    return ingredients.fold(0.0, (sum, ingredient) => sum + ingredient.carbs);
  }

  static double _calculateTotalFats(List<RecipeIngredient> ingredients) {
    return ingredients.fold(0.0, (sum, ingredient) => sum + ingredient.fats);
  }

  static double _calculateTotalFiber(List<RecipeIngredient> ingredients) {
    return ingredients.fold(0.0, (sum, ingredient) => sum + ingredient.fiber);
  }

  static double _calculateTotalSugar(List<RecipeIngredient> ingredients) {
    return ingredients.fold(0.0, (sum, ingredient) => sum + ingredient.sugar);
  }

  // Converti da Recipe a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'ingredients': ingredients.map((ingredient) => ingredient.toMap()).toList(),
      'instructions': instructions,
      'preparationTimeMinutes': preparationTimeMinutes,
      'cookingTimeMinutes': cookingTimeMinutes,
      'servings': servings,
      'suitableForMeals': suitableForMeals.map((e) => e.toString().split('.').last).toList(),
      'categories': categories.map((e) => e.toString().split('.').last).toList(),
      'isVegetarian': isVegetarian,
      'isVegan': isVegan,
      'isGlutenFree': isGlutenFree,
      'isDairyFree': isDairyFree,
      'allergens': allergens,
      'complexity': complexity,
      'isSeasonal': isSeasonal,
      'seasonalMonths': seasonalMonths,
      'tags': tags,
      'totalCalories': totalCalories,
      'totalProteins': totalProteins,
      'totalCarbs': totalCarbs,
      'totalFats': totalFats,
      'totalFiber': totalFiber,
      'totalSugar': totalSugar,
      'caloriesPerServing': caloriesPerServing,
      'proteinsPerServing': proteinsPerServing,
      'carbsPerServing': carbsPerServing,
      'fatsPerServing': fatsPerServing,
      'fiberPerServing': fiberPerServing,
      'sugarPerServing': sugarPerServing,
      'source': source,
      'validationStatus': validationStatus.toString().split('.').last,
      'lastValidatedAt': lastValidatedAt.millisecondsSinceEpoch,
      'validatedBy': validatedBy,
    };
  }

  // Converti da Map a Recipe
  factory Recipe.fromMap(Map<String, dynamic> map) {
    return Recipe(
      id: map['id'] as String,
      name: map['name'] as String,
      description: map['description'] as String? ?? '',
      imageUrl: map['imageUrl'] as String? ?? '',
      ingredients: (map['ingredients'] as List)
          .map((ingredientMap) => RecipeIngredient.fromMap(ingredientMap as Map<String, dynamic>))
          .toList(),
      instructions: map['instructions'] as String,
      preparationTimeMinutes: map['preparationTimeMinutes'] as int,
      cookingTimeMinutes: map['cookingTimeMinutes'] as int,
      servings: map['servings'] as int,
      suitableForMeals: (map['suitableForMeals'] as List).map((e) =>
          MealType.values.firstWhere((type) => type.toString().split('.').last == e)).toList(),
      categories: (map['categories'] as List).map((e) =>
          FoodCategory.values.firstWhere((cat) => cat.toString().split('.').last == e)).toList(),
      isVegetarian: map['isVegetarian'] as bool? ?? false,
      isVegan: map['isVegan'] as bool? ?? false,
      isGlutenFree: map['isGlutenFree'] as bool? ?? false,
      isDairyFree: map['isDairyFree'] as bool? ?? false,
      allergens: List<String>.from(map['allergens'] ?? []),
      complexity: map['complexity'] as int? ?? 1,
      isSeasonal: map['isSeasonal'] as bool? ?? false,
      seasonalMonths: List<int>.from(map['seasonalMonths'] ?? []),
      tags: List<String>.from(map['tags'] ?? []),
      totalCalories: map['totalCalories'] as int,
      totalProteins: map['totalProteins'] as double,
      totalCarbs: map['totalCarbs'] as double,
      totalFats: map['totalFats'] as double,
      totalFiber: map['totalFiber'] as double? ?? 0,
      totalSugar: map['totalSugar'] as double? ?? 0,
      caloriesPerServing: map['caloriesPerServing'] as int,
      proteinsPerServing: map['proteinsPerServing'] as double,
      carbsPerServing: map['carbsPerServing'] as double,
      fatsPerServing: map['fatsPerServing'] as double,
      fiberPerServing: map['fiberPerServing'] as double? ?? 0,
      sugarPerServing: map['sugarPerServing'] as double? ?? 0,
      source: map['source'] as String? ?? '',
      validationStatus: map['validationStatus'] != null
          ? ValidationStatus.values.firstWhere(
              (status) => status.toString().split('.').last == map['validationStatus'],
              orElse: () => ValidationStatus.unverified)
          : ValidationStatus.unverified,
      lastValidatedAt: map['lastValidatedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['lastValidatedAt'] as int)
          : DateTime.fromMillisecondsSinceEpoch(0),
      validatedBy: map['validatedBy'] as String? ?? '',
    );
  }

  // Converti da Recipe a JSON
  String toJson() => json.encode(toMap());

  // Converti da JSON a Recipe
  factory Recipe.fromJson(String source) =>
      Recipe.fromMap(json.decode(source) as Map<String, dynamic>);

  // Verifica se la ricetta è validata
  bool isValidated() {
    return validationStatus == ValidationStatus.validated;
  }

  // Crea una copia della ricetta con stato di validazione aggiornato
  Recipe withValidation({
    required ValidationStatus status,
    required String validator,
  }) {
    return copyWith(
      validationStatus: status,
      lastValidatedAt: DateTime.now(),
      validatedBy: validator,
    );
  }

  // Crea una copia della ricetta con possibilità di sovrascrivere alcuni campi
  Recipe copyWith({
    String? name,
    String? description,
    String? imageUrl,
    List<RecipeIngredient>? ingredients,
    String? instructions,
    int? preparationTimeMinutes,
    int? cookingTimeMinutes,
    int? servings,
    List<MealType>? suitableForMeals,
    List<FoodCategory>? categories,
    bool? isVegetarian,
    bool? isVegan,
    bool? isGlutenFree,
    bool? isDairyFree,
    List<String>? allergens,
    int? complexity,
    bool? isSeasonal,
    List<int>? seasonalMonths,
    List<String>? tags,
    int? totalCalories,
    double? totalProteins,
    double? totalCarbs,
    double? totalFats,
    double? totalFiber,
    double? totalSugar,
    int? caloriesPerServing,
    double? proteinsPerServing,
    double? carbsPerServing,
    double? fatsPerServing,
    double? fiberPerServing,
    double? sugarPerServing,
    String? source,
    ValidationStatus? validationStatus,
    DateTime? lastValidatedAt,
    String? validatedBy,
  }) {
    return Recipe(
      id: this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      ingredients: ingredients ?? this.ingredients,
      instructions: instructions ?? this.instructions,
      preparationTimeMinutes: preparationTimeMinutes ?? this.preparationTimeMinutes,
      cookingTimeMinutes: cookingTimeMinutes ?? this.cookingTimeMinutes,
      servings: servings ?? this.servings,
      suitableForMeals: suitableForMeals ?? this.suitableForMeals,
      categories: categories ?? this.categories,
      isVegetarian: isVegetarian ?? this.isVegetarian,
      isVegan: isVegan ?? this.isVegan,
      isGlutenFree: isGlutenFree ?? this.isGlutenFree,
      isDairyFree: isDairyFree ?? this.isDairyFree,
      allergens: allergens ?? this.allergens,
      complexity: complexity ?? this.complexity,
      isSeasonal: isSeasonal ?? this.isSeasonal,
      seasonalMonths: seasonalMonths ?? this.seasonalMonths,
      tags: tags ?? this.tags,
      totalCalories: totalCalories ?? this.totalCalories,
      totalProteins: totalProteins ?? this.totalProteins,
      totalCarbs: totalCarbs ?? this.totalCarbs,
      totalFats: totalFats ?? this.totalFats,
      totalFiber: totalFiber ?? this.totalFiber,
      totalSugar: totalSugar ?? this.totalSugar,
      caloriesPerServing: caloriesPerServing ?? this.caloriesPerServing,
      proteinsPerServing: proteinsPerServing ?? this.proteinsPerServing,
      carbsPerServing: carbsPerServing ?? this.carbsPerServing,
      fatsPerServing: fatsPerServing ?? this.fatsPerServing,
      fiberPerServing: fiberPerServing ?? this.fiberPerServing,
      sugarPerServing: sugarPerServing ?? this.sugarPerServing,
      source: source ?? this.source,
      validationStatus: validationStatus ?? this.validationStatus,
      lastValidatedAt: lastValidatedAt ?? this.lastValidatedAt,
      validatedBy: validatedBy ?? this.validatedBy,
    );
  }
}
