import '../models/food.dart';

/// Servizio per validare l'appropriatezza degli alimenti per tipo di pasto
/// Basato sulle tradizioni culinarie italiane e buone pratiche nutrizionali
class MealAppropriatenessValidator {
  
  /// Valida se un alimento è appropriato per un tipo di pasto specifico
  static bool isAppropriateForMeal(Food food, MealType mealType) {
    // Prima verifica se l'alimento è già marcato come adatto
    if (!food.suitableForMeals.contains(mealType)) {
      return false;
    }
    
    // Applica regole specifiche per tradizioni italiane
    switch (mealType) {
      case MealType.breakfast:
        return _isAppropriateForBreakfast(food);
      case MealType.lunch:
        return _isAppropriateForLunch(food);
      case MealType.dinner:
        return _isAppropriateForDinner(food);
      case MealType.snack:
        return _isAppropriateForSnack(food);
    }
  }

  /// Verifica appropriatezza per colazione italiana
  static bool _isAppropriateForBreakfast(Food food) {
    // Alimenti tradizionalmente consumati a colazione in Italia
    final breakfastKeywords = [
      'cornetto', 'brioche', 'biscotti', 'fette biscottate', 'marmellata',
      'miele', 'yogurt', 'latte', 'caffè', 'cappuccino', 'cereali',
      'frutta', 'succo', 'pane tostato', 'burro', 'nutella'
    ];
    
    final foodName = food.name.toLowerCase();
    final foodDescription = food.description.toLowerCase();
    
    // Verifica parole chiave
    if (breakfastKeywords.any((keyword) => 
        foodName.contains(keyword) || foodDescription.contains(keyword))) {
      return true;
    }
    
    // Categorie appropriate per colazione
    if (food.categories.contains(FoodCategory.fruit) ||
        food.categories.contains(FoodCategory.dairy) ||
        food.categories.contains(FoodCategory.beverage)) {
      return true;
    }
    
    // Esclusioni specifiche per colazione
    if (_isHeavyMeal(food) || _isMainCourseDish(food)) {
      return false;
    }
    
    return true;
  }

  /// Verifica appropriatezza per pranzo italiano
  static bool _isAppropriateForLunch(Food food) {
    // Il pranzo italiano può includere quasi tutto tranne dolci pesanti
    if (_isDessertOnly(food)) {
      return false;
    }
    
    // Esclude alimenti tipicamente da colazione
    if (_isBreakfastOnly(food)) {
      return false;
    }
    
    return true;
  }

  /// Verifica appropriatezza per cena italiana
  static bool _isAppropriateForDinner(Food food) {
    // La cena italiana è simile al pranzo ma più leggera
    if (_isDessertOnly(food)) {
      return false;
    }
    
    // Esclude alimenti tipicamente da colazione
    if (_isBreakfastOnly(food)) {
      return false;
    }
    
    // Preferisce alimenti più leggeri per la sera
    return true;
  }

  /// Verifica appropriatezza per spuntino italiano
  static bool _isAppropriateForSnack(Food food) {
    // Spuntini devono essere leggeri e facilmente digeribili
    
    // ESCLUSIONI CRITICHE per spuntini
    if (_isInappropriateForSnack(food)) {
      return false;
    }
    
    // Alimenti appropriati per spuntini italiani
    final snackKeywords = [
      'frutta', 'yogurt', 'crackers', 'grissini', 'taralli',
      'mandorle', 'noci', 'nocciole', 'biscotti secchi',
      'parmigiano', 'grana', 'bresaola', 'prosciutto crudo'
    ];
    
    final foodName = food.name.toLowerCase();
    final foodDescription = food.description.toLowerCase();
    
    // Verifica parole chiave appropriate
    if (snackKeywords.any((keyword) => 
        foodName.contains(keyword) || foodDescription.contains(keyword))) {
      return true;
    }
    
    // Categorie appropriate per spuntini
    if (food.categories.contains(FoodCategory.fruit)) {
      return true;
    }
    
    // Porzioni piccole di formaggi stagionati (non freschi)
    if (food.categories.contains(FoodCategory.dairy) && 
        _isAgedCheese(food) && 
        food.servingSizeGrams <= 50) {
      return true;
    }
    
    // Verifica dimensione porzione (spuntini devono essere piccoli)
    if (food.servingSizeGrams > 150) {
      return false;
    }
    
    // Verifica calorie (spuntini non devono essere troppo calorici)
    final caloriesPerServing = (food.calories * food.servingSizeGrams / 100).round();
    if (caloriesPerServing > 200) {
      return false;
    }
    
    return true;
  }

  /// Identifica alimenti inappropriati per spuntini
  static bool _isInappropriateForSnack(Food food) {
    final foodName = food.name.toLowerCase();
    final foodDescription = food.description.toLowerCase();
    
    // Formaggi freschi e molli NON sono appropriati per spuntini
    final inappropriateForSnack = [
      'mozzarella', 'ricotta', 'mascarpone', 'stracchino', 'robiola',
      'burrata', 'crescenza', 'squacquerone', 'primo sale',
      'pasta', 'risotto', 'pizza', 'lasagne', 'cannelloni',
      'ragù', 'sugo', 'minestrone', 'zuppa', 'brodo',
      'arrosto', 'brasato', 'spezzatino', 'scaloppine',
      'gelato', 'tiramisù', 'panna cotta', 'semifreddo'
    ];
    
    // Verifica esclusioni specifiche
    if (inappropriateForSnack.any((keyword) => 
        foodName.contains(keyword) || foodDescription.contains(keyword))) {
      return true;
    }
    
    // Piatti complessi non sono spuntini
    if (food.isRecipe && food.complexity > 2) {
      return true;
    }
    
    // Alimenti che richiedono cottura non sono spuntini
    if (food.preparationTimeMinutes > 15) {
      return true;
    }
    
    return false;
  }

  /// Verifica se è un formaggio stagionato
  static bool _isAgedCheese(Food food) {
    final agedCheeseKeywords = [
      'parmigiano', 'grana', 'pecorino', 'gorgonzola', 'taleggio',
      'asiago', 'provolone', 'caciocavallo', 'stagionato'
    ];
    
    final foodName = food.name.toLowerCase();
    final foodDescription = food.description.toLowerCase();
    
    return agedCheeseKeywords.any((keyword) => 
        foodName.contains(keyword) || foodDescription.contains(keyword));
  }

  /// Verifica se è un pasto pesante
  static bool _isHeavyMeal(Food food) {
    return food.calories > 300 || 
           food.fats > 15 || 
           (food.isRecipe && food.complexity > 3);
  }

  /// Verifica se è un piatto principale
  static bool _isMainCourseDish(Food food) {
    final mainCourseKeywords = [
      'pasta', 'risotto', 'pizza', 'lasagne', 'cannelloni',
      'arrosto', 'brasato', 'spezzatino', 'scaloppine',
      'cotoletta', 'bistecca', 'pesce al forno'
    ];
    
    final foodName = food.name.toLowerCase();
    return mainCourseKeywords.any((keyword) => foodName.contains(keyword));
  }

  /// Verifica se è solo un dolce
  static bool _isDessertOnly(Food food) {
    final dessertKeywords = [
      'gelato', 'tiramisù', 'panna cotta', 'semifreddo',
      'cannoli', 'cassata', 'babà', 'sfogliatelle'
    ];
    
    final foodName = food.name.toLowerCase();
    return dessertKeywords.any((keyword) => foodName.contains(keyword)) ||
           food.categories.contains(FoodCategory.sweet);
  }

  /// Verifica se è solo da colazione
  static bool _isBreakfastOnly(Food food) {
    final breakfastOnlyKeywords = [
      'cornetto', 'brioche', 'cereali', 'muesli',
      'fette biscottate', 'nutella'
    ];
    
    final foodName = food.name.toLowerCase();
    return breakfastOnlyKeywords.any((keyword) => foodName.contains(keyword));
  }

  /// Ottieni suggerimenti per migliorare l'appropriatezza
  static List<String> getSuggestions(Food food, MealType mealType) {
    final suggestions = <String>[];
    
    if (!isAppropriateForMeal(food, mealType)) {
      switch (mealType) {
        case MealType.snack:
          if (_isInappropriateForSnack(food)) {
            suggestions.add('Questo alimento è troppo pesante per uno spuntino');
            suggestions.add('Considera porzioni più piccole o alimenti più leggeri');
          }
          break;
        case MealType.breakfast:
          if (_isMainCourseDish(food)) {
            suggestions.add('Questo piatto è più adatto per pranzo o cena');
          }
          break;
        case MealType.lunch:
        case MealType.dinner:
          if (_isBreakfastOnly(food)) {
            suggestions.add('Questo alimento è tipicamente consumato a colazione');
          }
          break;
      }
    }
    
    return suggestions;
  }

  /// Filtra una lista di alimenti per appropriatezza
  static List<Food> filterAppropriateForMeal(List<Food> foods, MealType mealType) {
    return foods.where((food) => isAppropriateForMeal(food, mealType)).toList();
  }

  /// Assegna automaticamente i tipi di pasto appropriati per un alimento
  static List<MealType> suggestAppropriateMenuals(Food food) {
    final appropriateMeals = <MealType>[];
    
    for (final mealType in MealType.values) {
      if (isAppropriateForMeal(food, mealType)) {
        appropriateMeals.add(mealType);
      }
    }
    
    return appropriateMeals;
  }
}
