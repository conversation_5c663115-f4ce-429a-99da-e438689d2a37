import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/food.dart';
import '../models/recipe.dart';
import '../models/food_categories_extended.dart';
import '../utils/advanced_nutrition_properties.dart';
import 'usda_api_service.dart';
import 'open_food_facts_api_service.dart';
import 'advanced_nutrition_service.dart';
import 'extended_categories_service.dart';

/// Service per la gestione del database alimentare
/// Coordina l'accesso ai dati alimentari da diverse fonti e gestisce la persistenza locale
class FoodDatabaseService {
  // Chiavi per SharedPreferences
  static const String _foodsKey = 'foods';
  static const String _recipesKey = 'recipes';
  static const String _lastSyncKey = 'last_sync';

  // Servizi API
  final UsdaApiService _usdaApiService;
  final OpenFoodFactsApiService _openFoodFactsApiService;

  // Cache locale
  List<Food> _foodsCache = [];
  List<Recipe> _recipesCache = [];
  DateTime _lastSync = DateTime.fromMillisecondsSinceEpoch(0);

  // Singleton
  static FoodDatabaseService? _instance;

  // Costruttore privato
  FoodDatabaseService._({
    UsdaApiService? usdaApiService,
    OpenFoodFactsApiService? openFoodFactsApiService,
  }) :
    _usdaApiService = usdaApiService ?? UsdaApiService(),
    _openFoodFactsApiService = openFoodFactsApiService ?? OpenFoodFactsApiService();

  /// Ottieni l'istanza singleton del service
  static Future<FoodDatabaseService> getInstance() async {
    if (_instance == null) {
      _instance = FoodDatabaseService._();
      await _instance!._init();
    }
    return _instance!;
  }

  /// Inizializza il service caricando i dati da SharedPreferences
  Future<void> _init() async {
    await _loadFromLocalStorage();
  }

  /// Carica i dati da SharedPreferences
  Future<void> _loadFromLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Carica gli alimenti
      final foodsJson = prefs.getStringList(_foodsKey) ?? [];
      _foodsCache = foodsJson.map((json) => Food.fromJson(json)).toList();

      // Carica le ricette
      final recipesJson = prefs.getStringList(_recipesKey) ?? [];
      _recipesCache = recipesJson.map((json) => Recipe.fromJson(json)).toList();

      // Carica la data dell'ultima sincronizzazione
      final lastSyncMillis = prefs.getInt(_lastSyncKey) ?? 0;
      _lastSync = DateTime.fromMillisecondsSinceEpoch(lastSyncMillis);

      print('Caricati ${_foodsCache.length} alimenti e ${_recipesCache.length} ricette dal database locale');
    } catch (e) {
      print('Errore nel caricamento dei dati da SharedPreferences: $e');
      // In caso di errore, inizializza con liste vuote
      _foodsCache = [];
      _recipesCache = [];
    }
  }

  /// Salva i dati in SharedPreferences
  Future<void> _saveToLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Salva gli alimenti
      final foodsJson = _foodsCache.map((food) => food.toJson()).toList();
      await prefs.setStringList(_foodsKey, foodsJson);

      // Salva le ricette
      final recipesJson = _recipesCache.map((recipe) => recipe.toJson()).toList();
      await prefs.setStringList(_recipesKey, recipesJson);

      // Salva la data dell'ultima sincronizzazione
      await prefs.setInt(_lastSyncKey, DateTime.now().millisecondsSinceEpoch);

      print('Salvati ${_foodsCache.length} alimenti e ${_recipesCache.length} ricette nel database locale');
    } catch (e) {
      print('Errore nel salvataggio dei dati in SharedPreferences: $e');
    }
  }

  /// Ottieni tutti gli alimenti
  List<Food> getAllFoods() {
    return List.unmodifiable(_foodsCache);
  }

  /// Ottieni tutti gli alimenti validati
  List<Food> getValidatedFoods() {
    return _foodsCache.where((food) => food.isValidated()).toList();
  }

  /// Ottieni un alimento per ID
  Food? getFoodById(String id) {
    try {
      return _foodsCache.firstWhere((food) => food.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Aggiungi o aggiorna un alimento
  Future<Food> saveFood(Food food) async {
    // Verifica se l'alimento esiste già
    final index = _foodsCache.indexWhere((f) => f.id == food.id);

    if (index >= 0) {
      // Aggiorna l'alimento esistente
      _foodsCache[index] = food;
    } else {
      // Aggiungi un nuovo alimento
      _foodsCache.add(food);
    }

    // Salva i dati
    await _saveToLocalStorage();

    return food;
  }

  /// Elimina un alimento
  Future<bool> deleteFood(String id) async {
    final initialLength = _foodsCache.length;
    _foodsCache.removeWhere((food) => food.id == id);

    // Verifica se l'alimento è stato rimosso
    if (_foodsCache.length < initialLength) {
      await _saveToLocalStorage();
      return true;
    }

    return false;
  }

  /// Ottieni tutte le ricette
  List<Recipe> getAllRecipes() {
    return List.unmodifiable(_recipesCache);
  }

  /// Ottieni tutte le ricette validate
  List<Recipe> getValidatedRecipes() {
    return _recipesCache.where((recipe) => recipe.isValidated()).toList();
  }

  /// Ottieni una ricetta per ID
  Recipe? getRecipeById(String id) {
    try {
      return _recipesCache.firstWhere((recipe) => recipe.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Aggiungi o aggiorna una ricetta
  Future<Recipe> saveRecipe(Recipe recipe) async {
    // Verifica se la ricetta esiste già
    final index = _recipesCache.indexWhere((r) => r.id == recipe.id);

    if (index >= 0) {
      // Aggiorna la ricetta esistente
      _recipesCache[index] = recipe;
    } else {
      // Aggiungi una nuova ricetta
      _recipesCache.add(recipe);
    }

    // Salva i dati
    await _saveToLocalStorage();

    return recipe;
  }

  /// Elimina una ricetta
  Future<bool> deleteRecipe(String id) async {
    final initialLength = _recipesCache.length;
    _recipesCache.removeWhere((recipe) => recipe.id == id);

    // Verifica se la ricetta è stata rimossa
    if (_recipesCache.length < initialLength) {
      await _saveToLocalStorage();
      return true;
    }

    return false;
  }

  /// Cerca alimenti nel database USDA
  Future<List<Food>> searchUsdaFoods(String query) async {
    try {
      final results = await _usdaApiService.searchFoods(query: query);

      // Converti i risultati in oggetti Food
      final foods = <Food>[];
      for (final result in results) {
        try {
          final food = await _usdaApiService.convertUsdaFoodToFood(result);
          foods.add(food);
        } catch (e) {
          print('Errore nella conversione di un alimento USDA: $e');
        }
      }

      return foods;
    } catch (e) {
      print('Errore nella ricerca USDA: $e');
      return [];
    }
  }

  /// Cerca alimenti nel database Open Food Facts
  Future<List<Food>> searchOpenFoodFactsFoods(String query) async {
    try {
      final results = await _openFoodFactsApiService.searchFoods(query: query);

      // Converti i risultati in oggetti Food
      final foods = <Food>[];
      for (final result in results) {
        try {
          final food = await _openFoodFactsApiService.convertOffProductToFood(result);
          foods.add(food);
        } catch (e) {
          print('Errore nella conversione di un prodotto Open Food Facts: $e');
        }
      }

      return foods;
    } catch (e) {
      print('Errore nella ricerca Open Food Facts: $e');
      return [];
    }
  }

  /// Cerca alimenti in tutte le fonti disponibili
  Future<List<Food>> searchFoods(String query) async {
    // Cerca prima nel database locale
    final localResults = _foodsCache
        .where((food) =>
            food.name.toLowerCase().contains(query.toLowerCase()) ||
            food.description.toLowerCase().contains(query.toLowerCase()) ||
            food.tags.any((tag) => tag.toLowerCase().contains(query.toLowerCase())))
        .toList();

    // Cerca nelle API esterne
    final usdaResults = await searchUsdaFoods(query);
    final offResults = await searchOpenFoodFactsFoods(query);

    // Combina i risultati
    final allResults = [...localResults, ...usdaResults, ...offResults];

    // Rimuovi i duplicati (basati sul nome e sulla fonte)
    final uniqueResults = <Food>[];
    final seenKeys = <String>{};

    for (final food in allResults) {
      final key = '${food.name}_${food.dataSource}_${food.sourceId}';
      if (!seenKeys.contains(key)) {
        uniqueResults.add(food);
        seenKeys.add(key);
      }
    }

    return uniqueResults;
  }

  /// Importa un alimento da una fonte esterna e lo salva nel database locale
  Future<Food> importFood(Food food) async {
    // Genera un nuovo ID per evitare conflitti
    var newFood = food.copyWith(
      id: const Uuid().v4(),
    );

    // Arricchisci l'alimento con categorie estese
    newFood = ExtendedCategoriesService.enrichFoodWithExtendedCategories(newFood);

    // Arricchisci l'alimento con proprietà nutrizionali avanzate
    newFood = AdvancedNutritionService.enrichFoodWithAdvancedProperties(newFood);

    // Salva l'alimento nel database locale
    return await saveFood(newFood);
  }

  /// Valida un alimento
  Future<Food> validateFood({
    required String foodId,
    required ValidationStatus status,
    required String validator,
  }) async {
    final food = getFoodById(foodId);

    if (food == null) {
      throw Exception('Alimento non trovato: $foodId');
    }

    // Aggiorna lo stato di validazione
    final updatedFood = food.withValidation(
      status: status,
      validator: validator,
    );

    // Salva l'alimento aggiornato
    return await saveFood(updatedFood);
  }

  /// Crea una nuova ricetta
  Future<Recipe> createRecipe({
    required String name,
    required String description,
    required List<RecipeIngredient> ingredients,
    required String instructions,
    required int preparationTimeMinutes,
    required int cookingTimeMinutes,
    required int servings,
    required List<MealType> suitableForMeals,
    required List<FoodCategory> categories,
  }) async {
    // Crea una nuova ricetta
    final recipe = Recipe(
      name: name,
      description: description,
      ingredients: ingredients,
      instructions: instructions,
      preparationTimeMinutes: preparationTimeMinutes,
      cookingTimeMinutes: cookingTimeMinutes,
      servings: servings,
      suitableForMeals: suitableForMeals,
      categories: categories,
    );

    // Salva la ricetta nel database locale
    return await saveRecipe(recipe);
  }

  /// Valida una ricetta
  Future<Recipe> validateRecipe({
    required String recipeId,
    required ValidationStatus status,
    required String validator,
  }) async {
    final recipe = getRecipeById(recipeId);

    if (recipe == null) {
      throw Exception('Ricetta non trovata: $recipeId');
    }

    // Aggiorna lo stato di validazione
    final updatedRecipe = recipe.withValidation(
      status: status,
      validator: validator,
    );

    // Salva la ricetta aggiornata
    return await saveRecipe(updatedRecipe);
  }

  /// Filtra gli alimenti in base a criteri specifici
  List<Food> filterFoods({
    List<FoodCategory>? categories,
    List<String>? extendedCategories,
    List<MealType>? suitableForMeals,
    bool? isVegetarian,
    bool? isVegan,
    bool? isGlutenFree,
    bool? isDairyFree,
    List<String>? allergens,
    Map<String, dynamic>? advancedProperties,
    bool onlyValidated = true,
  }) {
    // Inizia con tutti gli alimenti o solo quelli validati
    List<Food> filteredFoods = onlyValidated ? getValidatedFoods() : getAllFoods();

    // Filtra per categorie
    if (categories != null && categories.isNotEmpty) {
      filteredFoods = filteredFoods.where((food) {
        return food.categories.any((category) => categories.contains(category));
      }).toList();
    }

    // Filtra per categorie estese
    if (extendedCategories != null && extendedCategories.isNotEmpty) {
      filteredFoods = filteredFoods.where((food) {
        return extendedCategories.any((extCat) => food.extendedCategories.contains(extCat));
      }).toList();
    }

    // Filtra per tipi di pasto
    if (suitableForMeals != null && suitableForMeals.isNotEmpty) {
      filteredFoods = filteredFoods.where((food) {
        return food.suitableForMeals.any((mealType) => suitableForMeals.contains(mealType));
      }).toList();
    }

    // Filtra per dieta
    if (isVegetarian == true) {
      filteredFoods = filteredFoods.where((food) => food.isVegetarian).toList();
    }

    if (isVegan == true) {
      filteredFoods = filteredFoods.where((food) => food.isVegan).toList();
    }

    if (isGlutenFree == true) {
      filteredFoods = filteredFoods.where((food) => food.isGlutenFree).toList();
    }

    if (isDairyFree == true) {
      filteredFoods = filteredFoods.where((food) => food.isDairyFree).toList();
    }

    // Filtra per allergeni (esclude alimenti che contengono gli allergeni specificati)
    if (allergens != null && allergens.isNotEmpty) {
      filteredFoods = filteredFoods.where((food) {
        return !food.allergens.any((allergen) => allergens.contains(allergen));
      }).toList();
    }

    // Filtra per proprietà nutrizionali avanzate
    if (advancedProperties != null && advancedProperties.isNotEmpty) {
      filteredFoods = filteredFoods.where((food) {
        bool matches = true;

        advancedProperties.forEach((property, value) {
          if (food.advancedProperties.containsKey(property)) {
            var foodValue = food.advancedProperties[property];

            if (value is Map<String, dynamic> && foodValue != null) {
              // Gestione di intervalli (min, max)
              if (value.containsKey('min') && foodValue < value['min']) {
                matches = false;
              }
              if (value.containsKey('max') && foodValue > value['max']) {
                matches = false;
              }
            } else if (value is List && foodValue != null) {
              // Gestione di liste di valori accettabili
              if (!value.contains(foodValue)) {
                matches = false;
              }
            } else if (foodValue != value) {
              // Confronto diretto
              matches = false;
            }
          } else {
            // La proprietà non esiste nell'alimento
            matches = false;
          }
        });

        return matches;
      }).toList();
    }

    return filteredFoods;
  }

  /// Ottieni alimenti per categoria estesa
  List<Food> getFoodsByExtendedCategory(FoodCategoryExtended category) {
    String categoryString = category.toString().split('.').last;
    return _foodsCache.where((food) => food.extendedCategories.contains(categoryString)).toList();
  }

  /// Ottieni alimenti con una specifica proprietà nutrizionale avanzata
  List<Food> getFoodsWithAdvancedProperty(String property) {
    return _foodsCache.where((food) =>
      food.advancedProperties.containsKey(property) &&
      food.advancedProperties[property] != null
    ).toList();
  }

  // Metodi per la ricerca di alimenti per nutrienti, categoria e micronutrienti
  // Questi metodi sono stati rimossi temporaneamente per risolvere problemi di compilazione

  /// Cerca alimenti per nutriente (implementazione temporanea)
  Future<List<Food>> searchFoodsByNutrient(String nutrient, {double? minValue, double? maxValue, int limit = 10}) async {
    // Restituisce alcuni alimenti casuali dal database
    return _foodsCache.take(limit).toList();
  }

  /// Cerca alimenti per categoria (implementazione temporanea)
  Future<List<Food>> searchFoodsByCategory(FoodCategory category, {int limit = 10}) async {
    // Restituisce alcuni alimenti casuali dal database
    return _foodsCache.take(limit).toList();
  }

  /// Cerca alimenti per micronutriente (implementazione temporanea)
  Future<List<Food>> searchFoodsByMicronutrient(String micronutrient, {double? minValue, double? maxValue, int limit = 10}) async {
    // Restituisce alcuni alimenti casuali dal database
    return _foodsCache.take(limit).toList();
  }

  /// Ottieni alimenti con un valore di una proprietà nutrizionale avanzata in un intervallo
  List<Food> getFoodsByAdvancedPropertyRange(String property, double min, double max) {
    return _foodsCache.where((food) {
      if (!food.advancedProperties.containsKey(property)) return false;

      var foodValue = food.advancedProperties[property];
      if (foodValue == null || foodValue is! num) return false;

      return foodValue >= min && foodValue <= max;
    }).toList();
  }

  /// Arricchisci tutti gli alimenti nel database con proprietà nutrizionali avanzate
  Future<void> enrichAllFoodsWithAdvancedProperties() async {
    for (int i = 0; i < _foodsCache.length; i++) {
      _foodsCache[i] = AdvancedNutritionService.enrichFoodWithAdvancedProperties(_foodsCache[i]);
    }

    await _saveToLocalStorage();
  }

  /// Arricchisci tutti gli alimenti nel database con categorie estese
  Future<void> enrichAllFoodsWithExtendedCategories() async {
    for (int i = 0; i < _foodsCache.length; i++) {
      _foodsCache[i] = ExtendedCategoriesService.enrichFoodWithExtendedCategories(_foodsCache[i]);
    }

    await _saveToLocalStorage();
  }

  /// Filtra le ricette in base a criteri specifici
  List<Recipe> filterRecipes({
    List<FoodCategory>? categories,
    List<MealType>? suitableForMeals,
    bool? isVegetarian,
    bool? isVegan,
    bool? isGlutenFree,
    bool? isDairyFree,
    List<String>? allergens,
    int? maxPrepTimeMinutes,
    int? maxTotalTimeMinutes,
    int? maxComplexity,
    bool onlyValidated = true,
  }) {
    // Inizia con tutte le ricette o solo quelle validate
    List<Recipe> filteredRecipes = onlyValidated ? getValidatedRecipes() : getAllRecipes();

    // Filtra per categorie
    if (categories != null && categories.isNotEmpty) {
      filteredRecipes = filteredRecipes.where((recipe) {
        return recipe.categories.any((category) => categories.contains(category));
      }).toList();
    }

    // Filtra per tipi di pasto
    if (suitableForMeals != null && suitableForMeals.isNotEmpty) {
      filteredRecipes = filteredRecipes.where((recipe) {
        return recipe.suitableForMeals.any((mealType) => suitableForMeals.contains(mealType));
      }).toList();
    }

    // Filtra per dieta
    if (isVegetarian == true) {
      filteredRecipes = filteredRecipes.where((recipe) => recipe.isVegetarian).toList();
    }

    if (isVegan == true) {
      filteredRecipes = filteredRecipes.where((recipe) => recipe.isVegan).toList();
    }

    if (isGlutenFree == true) {
      filteredRecipes = filteredRecipes.where((recipe) => recipe.isGlutenFree).toList();
    }

    if (isDairyFree == true) {
      filteredRecipes = filteredRecipes.where((recipe) => recipe.isDairyFree).toList();
    }

    // Filtra per allergeni (esclude ricette che contengono gli allergeni specificati)
    if (allergens != null && allergens.isNotEmpty) {
      filteredRecipes = filteredRecipes.where((recipe) {
        return !recipe.allergens.any((allergen) => allergens.contains(allergen));
      }).toList();
    }

    // Filtra per tempo di preparazione
    if (maxPrepTimeMinutes != null) {
      filteredRecipes = filteredRecipes.where((recipe) =>
        recipe.preparationTimeMinutes <= maxPrepTimeMinutes
      ).toList();
    }

    // Filtra per tempo totale (preparazione + cottura)
    if (maxTotalTimeMinutes != null) {
      filteredRecipes = filteredRecipes.where((recipe) =>
        (recipe.preparationTimeMinutes + recipe.cookingTimeMinutes) <= maxTotalTimeMinutes
      ).toList();
    }

    // Filtra per complessità
    if (maxComplexity != null) {
      filteredRecipes = filteredRecipes.where((recipe) =>
        recipe.complexity <= maxComplexity
      ).toList();
    }

    return filteredRecipes;
  }
}
