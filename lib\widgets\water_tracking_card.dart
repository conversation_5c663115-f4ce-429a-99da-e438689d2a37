import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../theme/app_theme.dart';

class WaterTrackingCard extends StatelessWidget {
  final int bicchieriAcqua;
  final int obiettivoBicchieri;
  final Function(int) onBicchieriChanged;

  const WaterTrackingCard({
    super.key,
    required this.bicchieriAcqua,
    required this.obiettivoBicchieri,
    required this.onBicchieriChanged,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final percentuale = obiettivoBicchieri > 0 
        ? (bicchieriAcqua / obiettivoBicchieri).clamp(0.0, 1.0) 
        : 0.0;
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Intestazione
            Row(
              children: [
                const Icon(
                  FontAwesomeIcons.droplet,
                  color: Colors.blue,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Monitoraggio Acqua',
                  style: textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Progresso
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Testo progresso
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '$bicchieriAcqua di $obiettivoBicchieri bicchieri',
                      style: textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade700,
                      ),
                    ),
                    Text(
                      '${(percentuale * 100).round()}% dell\'obiettivo',
                      style: textTheme.bodyMedium?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
                
                // Pulsanti
                Row(
                  children: [
                    IconButton(
                      onPressed: bicchieriAcqua > 0 
                          ? () => onBicchieriChanged(bicchieriAcqua - 1)
                          : null,
                      icon: const Icon(Icons.remove_circle_outline),
                      color: Colors.blue.shade700,
                      tooltip: 'Rimuovi un bicchiere',
                    ),
                    IconButton(
                      onPressed: bicchieriAcqua < obiettivoBicchieri
                          ? () => onBicchieriChanged(bicchieriAcqua + 1)
                          : null,
                      icon: const Icon(Icons.add_circle_outline),
                      color: Colors.blue.shade700,
                      tooltip: 'Aggiungi un bicchiere',
                    ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Visualizzazione bicchieri
            SizedBox(
              height: 60,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: List.generate(obiettivoBicchieri, (index) {
                  final isFilled = index < bicchieriAcqua;
                  return _buildWaterGlass(isFilled, index);
                }),
              ),
            ),
            
            const SizedBox(height: 8),
            
            // Barra di progresso
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: LinearProgressIndicator(
                value: percentuale,
                backgroundColor: Colors.blue.shade100,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade500),
                minHeight: 10,
              ),
            ).animate().fadeIn(duration: 300.ms).slideX(
              begin: -1,
              end: 0,
              duration: 500.ms,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildWaterGlass(bool isFilled, int index) {
    return Container(
      width: 24,
      height: 40,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: Icon(
        FontAwesomeIcons.glassWater,
        color: isFilled ? Colors.blue.shade500 : Colors.blue.shade100,
        size: 24,
      ),
    ).animate(
      target: isFilled ? 1 : 0,
    ).scaleXY(
      begin: 0.8,
      end: 1.0,
      duration: 300.ms,
    ).shake(
      hz: 2,
      rotation: 0.05,
      duration: 300.ms,
    );
  }
}
