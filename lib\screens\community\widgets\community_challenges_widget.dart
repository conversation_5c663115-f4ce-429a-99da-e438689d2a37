import 'package:flutter/material.dart';
import '../../../models/community_challenge.dart';
import '../../../utils/theme_colors.dart';

/// Widget per le sfide della community
class CommunityChallengesWidget extends StatefulWidget {
  final List<CommunityChallenge> challenges;

  const CommunityChallengesWidget({
    super.key,
    required this.challenges,
  });

  @override
  State<CommunityChallengesWidget> createState() => _CommunityChallengesWidgetState();
}

class _CommunityChallengesWidgetState extends State<CommunityChallengesWidget> {
  String _selectedFilter = 'active';
  final ScrollController _scrollController = ScrollController();

  /// Filtra le sfide per stato
  List<CommunityChallenge> get _filteredChallenges {
    switch (_selectedFilter) {
      case 'active':
        return widget.challenges.where((c) => c.isOngoing).toList();
      case 'upcoming':
        return widget.challenges.where((c) => c.isUpcoming).toList();
      case 'completed':
        return widget.challenges.where((c) => c.isCompleted).toList();
      default:
        return widget.challenges;
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Filtri per stato
        _buildStatusFilters(),
        
        // Lista sfide
        Expanded(
          child: _filteredChallenges.isEmpty
              ? _buildEmptyState()
              : _buildChallengesList(),
        ),
      ],
    );
  }

  /// Costruisce i filtri per stato
  Widget _buildStatusFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildFilterChip('active', 'Attive', Icons.play_circle),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildFilterChip('upcoming', 'Prossime', Icons.schedule),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildFilterChip('completed', 'Completate', Icons.check_circle),
          ),
        ],
      ),
    );
  }

  /// Costruisce un chip di filtro
  Widget _buildFilterChip(String filterId, String label, IconData icon) {
    final isSelected = _selectedFilter == filterId;
    
    return FilterChip(
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedFilter = filterId;
        });
      },
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: isSelected ? ThemeColors.primaryGreen : Colors.grey[600],
          ),
          const SizedBox(width: 4),
          Flexible(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ),
        ],
      ),
      backgroundColor: Colors.grey[100],
      selectedColor: ThemeColors.primaryGreen.withOpacity(0.2),
      checkmarkColor: ThemeColors.primaryGreen,
      side: BorderSide(
        color: isSelected ? ThemeColors.primaryGreen : Colors.grey[300]!,
      ),
    );
  }

  /// Costruisce lo stato vuoto
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.emoji_events_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Nessuna sfida trovata',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Prova a cambiare filtro o\ncrea una nuova sfida',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _createNewChallenge(),
            icon: const Icon(Icons.add),
            label: const Text('Crea Sfida'),
            style: ElevatedButton.styleFrom(
              backgroundColor: ThemeColors.primaryGreen,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  /// Costruisce la lista delle sfide
  Widget _buildChallengesList() {
    return RefreshIndicator(
      onRefresh: () async {
        // Implementa il refresh delle sfide
        await Future.delayed(const Duration(seconds: 1));
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        itemCount: _filteredChallenges.length,
        itemBuilder: (context, index) {
          final challenge = _filteredChallenges[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: _buildChallengeCard(challenge),
          );
        },
      ),
    );
  }

  /// Costruisce la card di una sfida
  Widget _buildChallengeCard(CommunityChallenge challenge) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _openChallenge(challenge),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header con immagine e stato
            _buildChallengeHeader(challenge),
            
            // Contenuto della sfida
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Titolo e difficoltà
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          challenge.title,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      _buildDifficultyBadge(challenge.difficulty),
                    ],
                  ),
                  const SizedBox(height: 8),
                  
                  // Categoria e tipo
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Color(int.parse('0xFF${challenge.colorHex.substring(1)}')).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '${challenge.emoji} ${challenge.category.displayName}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Color(int.parse('0xFF${challenge.colorHex.substring(1)}')),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '${challenge.type.emoji} ${challenge.type.displayName}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  
                  // Descrizione
                  Text(
                    challenge.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                      height: 1.4,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 16),
                  
                  // Progresso e statistiche
                  _buildChallengeProgress(challenge),
                  const SizedBox(height: 16),
                  
                  // Footer con azioni
                  _buildChallengeFooter(challenge),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Costruisce l'header della sfida
  Widget _buildChallengeHeader(CommunityChallenge challenge) {
    return Container(
      height: 100,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
        gradient: LinearGradient(
          colors: [
            Color(int.parse('0xFF${challenge.colorHex.substring(1)}')).withOpacity(0.8),
            Color(int.parse('0xFF${challenge.colorHex.substring(1)}')),
          ],
        ),
        image: challenge.imageUrl != null
            ? DecorationImage(
                image: NetworkImage(challenge.imageUrl!),
                fit: BoxFit.cover,
                colorFilter: ColorFilter.mode(
                  Color(int.parse('0xFF${challenge.colorHex.substring(1)}')).withOpacity(0.7),
                  BlendMode.overlay,
                ),
              )
            : null,
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status badge
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                challenge.isOngoing
                    ? '🔥 Attiva'
                    : challenge.isUpcoming
                        ? '⏰ Prossima'
                        : '✅ Completata',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Color(int.parse('0xFF${challenge.colorHex.substring(1)}')),
                ),
              ),
            ),
            const Spacer(),
            
            // Giorni rimanenti
            if (challenge.isOngoing)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.9),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${challenge.daysRemaining} giorni',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.orange,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Costruisce il badge di difficoltà
  Widget _buildDifficultyBadge(ChallengeDifficulty difficulty) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getDifficultyColor(difficulty).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        '${difficulty.emoji} ${difficulty.displayName}',
        style: TextStyle(
          fontSize: 12,
          color: _getDifficultyColor(difficulty),
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// Ottiene il colore della difficoltà
  Color _getDifficultyColor(ChallengeDifficulty difficulty) {
    switch (difficulty) {
      case ChallengeDifficulty.easy:
        return Colors.green;
      case ChallengeDifficulty.medium:
        return Colors.orange;
      case ChallengeDifficulty.hard:
        return Colors.red;
      case ChallengeDifficulty.expert:
        return Colors.purple;
    }
  }

  /// Costruisce il progresso della sfida
  Widget _buildChallengeProgress(CommunityChallenge challenge) {
    final completionRate = challenge.completionRate;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progresso',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            Text(
              '${(completionRate * 100).toStringAsFixed(0)}% completato',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: completionRate,
          backgroundColor: Colors.grey[200],
          valueColor: AlwaysStoppedAnimation<Color>(
            Color(int.parse('0xFF${challenge.colorHex.substring(1)}')),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            _buildChallengeStat(
              icon: Icons.people,
              value: challenge.currentParticipants.toString(),
              label: 'partecipanti',
            ),
            const SizedBox(width: 16),
            _buildChallengeStat(
              icon: Icons.check_circle,
              value: challenge.completedCount.toString(),
              label: 'completati',
            ),
            const SizedBox(width: 16),
            _buildChallengeStat(
              icon: Icons.star,
              value: '${challenge.difficulty.points}',
              label: 'punti',
            ),
          ],
        ),
      ],
    );
  }

  /// Costruisce una statistica della sfida
  Widget _buildChallengeStat({
    required IconData icon,
    required String value,
    required String label,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(width: 2),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// Costruisce il footer della sfida
  Widget _buildChallengeFooter(CommunityChallenge challenge) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: challenge.hasAvailableSlots
                ? () => _joinChallenge(challenge)
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: ThemeColors.primaryGreen,
              foregroundColor: Colors.white,
              disabledBackgroundColor: Colors.grey[300],
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
            child: Text(
              challenge.hasAvailableSlots ? 'Partecipa' : 'Completo',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ),
        const SizedBox(width: 12),
        OutlinedButton(
          onPressed: () => _shareChallenge(challenge),
          style: OutlinedButton.styleFrom(
            foregroundColor: ThemeColors.primaryGreen,
            side: BorderSide(color: ThemeColors.primaryGreen),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          child: const Icon(Icons.share, size: 20),
        ),
      ],
    );
  }

  /// Apre una sfida
  void _openChallenge(CommunityChallenge challenge) {
    // Implementa la navigazione alla sfida
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Apertura sfida: ${challenge.title}'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  /// Partecipa a una sfida
  void _joinChallenge(CommunityChallenge challenge) {
    // Implementa la partecipazione alla sfida
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Hai partecipato a: ${challenge.title}'),
        backgroundColor: ThemeColors.primaryGreen,
      ),
    );
  }

  /// Condivide una sfida
  void _shareChallenge(CommunityChallenge challenge) {
    // Implementa la condivisione della sfida
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Condivisione: ${challenge.title}'),
      ),
    );
  }

  /// Crea una nuova sfida
  void _createNewChallenge() {
    // Implementa la creazione di una nuova sfida
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Funzionalità di creazione sfida in arrivo'),
      ),
    );
  }
}
