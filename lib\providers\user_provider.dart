import 'package:flutter/foundation.dart';
import '../models/user_profile.dart';

/// Provider per gestire il profilo utente
class UserProvider extends ChangeNotifier {
  UserProfile? _userProfile;

  /// Ottiene il profilo utente corrente
  UserProfile? get userProfile => _userProfile;

  /// Imposta il profilo utente
  set userProfile(UserProfile? profile) {
    _userProfile = profile;
    notifyListeners();
  }

  /// Costruttore che inizializza con un profilo utente di esempio
  UserProvider() {
    // Crea un profilo utente di esempio
    _userProfile = UserProfile(
      id: 'user123',
      name: 'Utente di Esempio',
      age: 30,
      gender: Gender.male,
      height: 175,
      weight: 70,
      goal: Goal.maintenance,
      activityLevel: ActivityLevel.moderatelyActive,
      // dietPreferences: [DietType.mediterranean],
      allergies: [],
      // medicalConditions: [],
    );
  }

  /// Aggiorna il profilo utente
  void updateProfile({
    String? name,
    int? age,
    Gender? gender,
    int? height,
    int? weight,
    Goal? goal,
    ActivityLevel? activityLevel,
    List<DietType>? dietPreferences,
    List<String>? allergies,
    List<String>? medicalConditions,
  }) {
    if (_userProfile == null) return;

    _userProfile = UserProfile(
      id: _userProfile!.id,
      name: name ?? _userProfile!.name,
      age: age ?? _userProfile!.age,
      gender: gender ?? _userProfile!.gender,
      height: height ?? _userProfile!.height,
      weight: weight ?? _userProfile!.weight,
      goal: goal ?? _userProfile!.goal,
      activityLevel: activityLevel ?? _userProfile!.activityLevel,
      // dietPreferences: dietPreferences ?? _userProfile!.dietPreferences,
      allergies: allergies ?? _userProfile!.allergies,
      // medicalConditions: medicalConditions ?? _userProfile!.medicalConditions,
    );

    notifyListeners();
  }
}
