import 'services/specific_diet_generator_service.dart';
import 'services/food_variety_manager.dart';
import 'models/user_profile.dart';

/// Dimostrazione dei miglioramenti nella varietà degli alimenti
Future<void> main() async {
  print('🍽️ DIMOSTRAZIONE MIGLIORAMENTI VARIETÀ ALIMENTI');
  print('=' * 60);
  print('Questo test dimostra come il nuovo sistema di varietà');
  print('migliora la diversità degli alimenti nei piani dietetici.\n');

  try {
    // Inizializza i servizi
    final varietyManager = await FoodVarietyManager.getInstance();
    await varietyManager.resetUsageHistory();
    final specificGenerator = await SpecificDietGeneratorService.getInstance();
    
    // Crea profilo utente di esempio
    final userProfile = UserProfile(
      id: 'demo_user',
      name: 'Utente Demo',
      age: 35,
      gender: Gender.female,
      height: 165,
      weight: 60,
      activityLevel: ActivityLevel.moderate,
      goal: Goal.maintain,
      dietType: DietType.omnivore,
      allergies: [],
      dislikedFoods: [],
      mealsPerDay: 3,
    );
    
    print('👤 PROFILO UTENTE:');
    print('   Nome: ${userProfile.name}');
    print('   Età: ${userProfile.age} anni');
    print('   Obiettivo calorico: ${userProfile.calculateCalorieTarget()} kcal/giorno');
    print('   Pasti al giorno: ${userProfile.mealsPerDay}');
    
    // Genera piani per una settimana
    print('\n📅 GENERAZIONE PIANI SETTIMANALI CON VARIETÀ MIGLIORATA');
    print('-' * 55);
    
    final weeklyFoodsByMeal = <String, List<String>>{
      'Colazione': [],
      'Pranzo': [],
      'Cena': [],
    };
    
    for (int day = 1; day <= 7; day++) {
      print('\n🗓️ GIORNO $day:');
      
      final weeklyPlan = await specificGenerator.generateWeeklyDietPlan(
        userProfile,
        weeks: 1,
      );
      
      if (weeklyPlan.dailyPlans.isNotEmpty) {
        final dailyPlan = weeklyPlan.dailyPlans.first;
        
        for (final meal in dailyPlan.meals) {
          final mealName = _getMealDisplayName(meal.type);
          final foodNames = meal.foods.map((fp) => fp.food.name).toList();
          
          // Aggiungi alla collezione settimanale
          if (weeklyFoodsByMeal.containsKey(mealName)) {
            weeklyFoodsByMeal[mealName]!.addAll(foodNames);
          }
          
          print('   $mealName: ${foodNames.join(', ')}');
        }
      } else {
        print('   ❌ Nessun piano generato');
      }
    }
    
    // Analisi della varietà
    print('\n📊 ANALISI VARIETÀ SETTIMANALE');
    print('-' * 35);
    
    for (final mealEntry in weeklyFoodsByMeal.entries) {
      final mealType = mealEntry.key;
      final allFoods = mealEntry.value;
      final uniqueFoods = allFoods.toSet();
      
      if (allFoods.isNotEmpty) {
        final varietyRatio = uniqueFoods.length / allFoods.length;
        final varietyPercentage = (varietyRatio * 100).toStringAsFixed(1);
        
        print('\n🍽️ $mealType:');
        print('   📈 Varietà: $varietyPercentage% (${uniqueFoods.length}/${allFoods.length})');
        print('   🥘 Alimenti unici utilizzati:');
        
        final sortedFoods = uniqueFoods.toList()..sort();
        for (int i = 0; i < sortedFoods.length; i++) {
          final food = sortedFoods[i];
          final count = allFoods.where((f) => f == food).length;
          print('      ${i + 1}. $food (utilizzato $count volte)');
        }
        
        // Valutazione varietà
        if (varietyRatio >= 0.7) {
          print('   ✅ Eccellente varietà!');
        } else if (varietyRatio >= 0.5) {
          print('   ✅ Buona varietà');
        } else if (varietyRatio >= 0.3) {
          print('   ⚠️ Varietà moderata');
        } else {
          print('   ❌ Varietà limitata');
        }
      }
    }
    
    // Statistiche del sistema di varietà
    print('\n🔍 STATISTICHE SISTEMA VARIETÀ');
    print('-' * 35);
    
    final stats = varietyManager.getUsageStatistics();
    print('📊 FoodVarietyManager:');
    print('   - Alimenti tracciati: ${stats['totalTrackedFoods']}');
    print('   - Utilizzi totali: ${stats['totalUsages']}');
    print('   - Utilizzi recenti (7 giorni): ${stats['recentUsages']}');
    print('   - Media utilizzi per alimento: ${(stats['averageUsagePerFood'] as double).toStringAsFixed(1)}');
    
    final recentFoods = varietyManager.getRecentlyUsedFoods();
    if (recentFoods.isNotEmpty) {
      print('   - Alimenti utilizzati di recente: ${recentFoods.length}');
    }
    
    // Confronto con sistema tradizionale (simulato)
    print('\n⚖️ CONFRONTO CON SISTEMA TRADIZIONALE');
    print('-' * 40);
    
    print('🔴 PRIMA (Sistema tradizionale):');
    print('   - Selezione casuale semplice');
    print('   - Nessun tracking degli utilizzi');
    print('   - Ripetizione frequente degli stessi alimenti');
    print('   - Varietà tipica: 20-30%');
    
    print('\n🟢 DOPO (Sistema con varietà migliorata):');
    print('   - Selezione intelligente con punteggi di varietà');
    print('   - Tracking utilizzi per 14 giorni');
    print('   - Promozione alimenti stagionali e tradizionali');
    print('   - Cooldown di 3 giorni per alimenti utilizzati');
    
    // Calcola varietà media
    final totalFoods = weeklyFoodsByMeal.values.expand((foods) => foods).length;
    final totalUniqueFoods = weeklyFoodsByMeal.values
        .expand((foods) => foods)
        .toSet()
        .length;
    final overallVariety = totalUniqueFoods / totalFoods;
    
    print('   - Varietà ottenuta: ${(overallVariety * 100).toStringAsFixed(1)}%');
    
    // Valutazione finale
    print('\n🎯 VALUTAZIONE FINALE');
    print('-' * 25);
    
    if (stats['totalTrackedFoods'] as int > 0) {
      print('✅ Sistema di tracking attivo e funzionante');
    } else {
      print('⚠️ Sistema di tracking non attivo');
    }
    
    if (overallVariety >= 0.5) {
      print('✅ Varietà significativamente migliorata');
      print('🎉 Gli utenti vedranno piani dietetici più vari e interessanti!');
    } else if (overallVariety >= 0.3) {
      print('✅ Varietà moderatamente migliorata');
      print('💡 Il sistema sta funzionando, ma può essere ottimizzato');
    } else {
      print('⚠️ Varietà ancora limitata');
      print('🔧 Potrebbero essere necessari ulteriori aggiustamenti');
    }
    
    // Benefici per l'utente
    print('\n🌟 BENEFICI PER L\'UTENTE:');
    print('   ✅ Maggiore varietà di sapori e texture');
    print('   ✅ Scoperta di nuovi alimenti italiani');
    print('   ✅ Piani dietetici meno ripetitivi');
    print('   ✅ Migliore aderenza alla dieta a lungo termine');
    print('   ✅ Educazione alimentare attraverso la varietà');
    
    print('\n' + '=' * 60);
    print('🎊 DIMOSTRAZIONE COMPLETATA CON SUCCESSO!');
    
  } catch (e) {
    print('\n❌ ERRORE DURANTE LA DIMOSTRAZIONE: $e');
  }
}

String _getMealDisplayName(String mealType) {
  switch (mealType) {
    case 'breakfast':
      return 'Colazione';
    case 'lunch':
      return 'Pranzo';
    case 'dinner':
      return 'Cena';
    case 'snack':
      return 'Spuntino';
    default:
      return mealType;
  }
}
