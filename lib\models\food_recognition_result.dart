import 'food.dart';

/// Risultato del riconoscimento alimentare tramite Food Oracle
class FoodRecognitionResult {
  final String id;
  final DateTime timestamp;
  final String imagePath;
  final String mealType;
  final List<RecognizedFood> recognizedFoods;
  final NutritionalSummary nutritionalSummary;
  final List<String> suggestions;
  final double confidenceScore;
  final String? userId;

  const FoodRecognitionResult({
    required this.id,
    required this.timestamp,
    required this.imagePath,
    required this.mealType,
    required this.recognizedFoods,
    required this.nutritionalSummary,
    required this.suggestions,
    required this.confidenceScore,
    this.userId,
  });

  /// Crea una copia con modifiche
  FoodRecognitionResult copyWith({
    String? id,
    DateTime? timestamp,
    String? imagePath,
    String? mealType,
    List<RecognizedFood>? recognizedFoods,
    NutritionalSummary? nutritionalSummary,
    List<String>? suggestions,
    double? confidenceScore,
    String? userId,
  }) {
    return FoodRecognitionResult(
      id: id ?? this.id,
      timestamp: timestamp ?? this.timestamp,
      imagePath: imagePath ?? this.imagePath,
      mealType: mealType ?? this.mealType,
      recognizedFoods: recognizedFoods ?? this.recognizedFoods,
      nutritionalSummary: nutritionalSummary ?? this.nutritionalSummary,
      suggestions: suggestions ?? this.suggestions,
      confidenceScore: confidenceScore ?? this.confidenceScore,
      userId: userId ?? this.userId,
    );
  }

  /// Converte in Map per la serializzazione
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'timestamp': timestamp.toIso8601String(),
      'imagePath': imagePath,
      'mealType': mealType,
      'recognizedFoods': recognizedFoods.map((f) => f.toJson()).toList(),
      'nutritionalSummary': nutritionalSummary.toJson(),
      'suggestions': suggestions,
      'confidenceScore': confidenceScore,
      'userId': userId,
    };
  }

  /// Crea da Map per la deserializzazione
  factory FoodRecognitionResult.fromJson(Map<String, dynamic> json) {
    return FoodRecognitionResult(
      id: json['id'],
      timestamp: DateTime.parse(json['timestamp']),
      imagePath: json['imagePath'],
      mealType: json['mealType'],
      recognizedFoods: (json['recognizedFoods'] as List)
          .map((f) => RecognizedFood.fromJson(f))
          .toList(),
      nutritionalSummary: NutritionalSummary.fromJson(json['nutritionalSummary']),
      suggestions: List<String>.from(json['suggestions']),
      confidenceScore: json['confidenceScore'].toDouble(),
      userId: json['userId'],
    );
  }

  /// Ottiene il nome del tipo di pasto in italiano
  String get mealTypeDisplayName {
    switch (mealType) {
      case 'breakfast':
        return 'Colazione';
      case 'lunch':
        return 'Pranzo';
      case 'dinner':
        return 'Cena';
      case 'snack':
        return 'Spuntino';
      default:
        return 'Pasto';
    }
  }

  /// Ottiene il livello di confidenza come testo
  String get confidenceLevel {
    if (confidenceScore >= 0.8) return 'Alta';
    if (confidenceScore >= 0.6) return 'Media';
    if (confidenceScore >= 0.4) return 'Bassa';
    return 'Molto Bassa';
  }

  /// Ottiene il colore per il livello di confidenza
  String get confidenceColor {
    if (confidenceScore >= 0.8) return 'green';
    if (confidenceScore >= 0.6) return 'orange';
    return 'red';
  }
}

/// Alimento riconosciuto con informazioni aggiuntive
class RecognizedFood {
  final Food food;
  final double confidence;
  final int estimatedGrams;
  final BoundingBox boundingBox;

  const RecognizedFood({
    required this.food,
    required this.confidence,
    required this.estimatedGrams,
    required this.boundingBox,
  });

  /// Calcola le calorie per la porzione stimata
  int get estimatedCalories {
    return ((food.calories * estimatedGrams) / 100).round();
  }

  /// Calcola le proteine per la porzione stimata
  double get estimatedProteins {
    return (food.proteins * estimatedGrams) / 100;
  }

  /// Calcola i carboidrati per la porzione stimata
  double get estimatedCarbs {
    return (food.carbs * estimatedGrams) / 100;
  }

  /// Calcola i grassi per la porzione stimata
  double get estimatedFats {
    return (food.fats * estimatedGrams) / 100;
  }

  /// Ottiene il livello di confidenza come testo
  String get confidenceLevel {
    if (confidence >= 0.8) return 'Alta';
    if (confidence >= 0.6) return 'Media';
    if (confidence >= 0.4) return 'Bassa';
    return 'Molto Bassa';
  }

  /// Converte in Map per la serializzazione
  Map<String, dynamic> toJson() {
    return {
      'food': food.toJson(),
      'confidence': confidence,
      'estimatedGrams': estimatedGrams,
      'boundingBox': boundingBox.toJson(),
    };
  }

  /// Crea da Map per la deserializzazione
  factory RecognizedFood.fromJson(Map<String, dynamic> json) {
    return RecognizedFood(
      food: Food.fromJson(json['food']),
      confidence: json['confidence'].toDouble(),
      estimatedGrams: json['estimatedGrams'],
      boundingBox: BoundingBox.fromJson(json['boundingBox']),
    );
  }
}

/// Bounding box per la localizzazione dell'alimento nell'immagine
class BoundingBox {
  final double x;
  final double y;
  final double width;
  final double height;

  const BoundingBox({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
  });

  /// Converte in Map per la serializzazione
  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'y': y,
      'width': width,
      'height': height,
    };
  }

  /// Crea da Map per la deserializzazione
  factory BoundingBox.fromJson(Map<String, dynamic> json) {
    return BoundingBox(
      x: json['x'].toDouble(),
      y: json['y'].toDouble(),
      width: json['width'].toDouble(),
      height: json['height'].toDouble(),
    );
  }
}

/// Riepilogo nutrizionale del pasto riconosciuto
class NutritionalSummary {
  final int calories;
  final double proteins;
  final double carbs;
  final double fats;
  final double fiber;

  const NutritionalSummary({
    required this.calories,
    required this.proteins,
    required this.carbs,
    required this.fats,
    required this.fiber,
  });

  /// Ottiene la distribuzione percentuale dei macronutrienti
  Map<String, double> get macroPercentages {
    final totalMacroCalories = (proteins * 4) + (carbs * 4) + (fats * 9);
    
    if (totalMacroCalories == 0) {
      return {'proteins': 0, 'carbs': 0, 'fats': 0};
    }
    
    return {
      'proteins': ((proteins * 4) / totalMacroCalories) * 100,
      'carbs': ((carbs * 4) / totalMacroCalories) * 100,
      'fats': ((fats * 9) / totalMacroCalories) * 100,
    };
  }

  /// Verifica se il pasto è bilanciato
  bool get isBalanced {
    final percentages = macroPercentages;
    final proteinPercent = percentages['proteins']!;
    final carbPercent = percentages['carbs']!;
    final fatPercent = percentages['fats']!;
    
    // Considera bilanciato se:
    // - Proteine: 15-35%
    // - Carboidrati: 45-65%
    // - Grassi: 20-35%
    return proteinPercent >= 15 && proteinPercent <= 35 &&
           carbPercent >= 45 && carbPercent <= 65 &&
           fatPercent >= 20 && fatPercent <= 35;
  }

  /// Ottiene suggerimenti per migliorare l'equilibrio
  List<String> get balanceRecommendations {
    final recommendations = <String>[];
    final percentages = macroPercentages;
    
    if (percentages['proteins']! < 15) {
      recommendations.add('Aggiungi più proteine al pasto');
    } else if (percentages['proteins']! > 35) {
      recommendations.add('Riduci leggermente le proteine');
    }
    
    if (percentages['carbs']! < 45) {
      recommendations.add('Includi più carboidrati complessi');
    } else if (percentages['carbs']! > 65) {
      recommendations.add('Riduci i carboidrati');
    }
    
    if (percentages['fats']! < 20) {
      recommendations.add('Aggiungi grassi sani (olio d\'oliva, noci)');
    } else if (percentages['fats']! > 35) {
      recommendations.add('Riduci i grassi');
    }
    
    return recommendations;
  }

  /// Converte in Map per la serializzazione
  Map<String, dynamic> toJson() {
    return {
      'calories': calories,
      'proteins': proteins,
      'carbs': carbs,
      'fats': fats,
      'fiber': fiber,
    };
  }

  /// Crea da Map per la deserializzazione
  factory NutritionalSummary.fromJson(Map<String, dynamic> json) {
    return NutritionalSummary(
      calories: json['calories'],
      proteins: json['proteins'].toDouble(),
      carbs: json['carbs'].toDouble(),
      fats: json['fats'].toDouble(),
      fiber: json['fiber'].toDouble(),
    );
  }
}
