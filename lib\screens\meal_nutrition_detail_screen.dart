import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/meal.dart';
import '../models/food.dart';
import '../utils/micronutrients_helper.dart';
import 'food_detail_screen.dart';

class MealNutritionDetailScreen extends StatefulWidget {
  final Meal meal;
  final String mealTitle;

  const MealNutritionDetailScreen({
    Key? key,
    required this.meal,
    required this.mealTitle,
  }) : super(key: key);

  @override
  _MealNutritionDetailScreenState createState() => _MealNutritionDetailScreenState();
}

class _MealNutritionDetailScreenState extends State<MealNutritionDetailScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Calcola i totali nutrizionali del pasto
  late int totalCalories;
  late double totalProteins;
  late double totalCarbs;
  late double totalFats;
  late double totalFiber;
  late double totalSugar;
  late Map<String, double> totalMicronutrients;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _calculateTotals();
  }

  void _calculateTotals() {
    totalCalories = 0;
    totalProteins = 0;
    totalCarbs = 0;
    totalFats = 0;
    totalFiber = 0;
    totalSugar = 0;
    totalMicronutrients = {};

    for (var foodItem in widget.meal.foods) {
      final food = foodItem.food;
      final servingRatio = foodItem.quantity / food.servingSizeGrams;

      totalCalories += (food.calories * servingRatio).round();
      totalProteins += food.proteins * servingRatio;
      totalCarbs += food.carbs * servingRatio;
      totalFats += food.fats * servingRatio;
      totalFiber += food.fiber * servingRatio;
      totalSugar += food.sugar * servingRatio;

      // Calcola i micronutrienti totali
      food.micronutrients.forEach((key, value) {
        if (totalMicronutrients.containsKey(key)) {
          totalMicronutrients[key] = totalMicronutrients[key]! + (value * servingRatio);
        } else {
          totalMicronutrients[key] = value * servingRatio;
        }
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.mealTitle} - Dettagli nutrizionali'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Panoramica'),
            Tab(text: 'Nutrienti'),
            Tab(text: 'Alimenti'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildNutrientsTab(),
          _buildFoodsTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Riepilogo calorico
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Riepilogo calorico',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '$totalCalories kcal',
                        style: const TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Distribuzione dei macronutrienti
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Distribuzione dei macronutrienti',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    height: 200,
                    child: _buildMacronutrientsPieChart(),
                  ),
                  const SizedBox(height: 16),
                  // Tabella dettagliata dei macronutrienti
                  Table(
                    border: TableBorder.all(
                      color: Colors.grey.shade300,
                      width: 1,
                    ),
                    columnWidths: const {
                      0: FlexColumnWidth(3),
                      1: FlexColumnWidth(2),
                      2: FlexColumnWidth(2),
                      3: FlexColumnWidth(2),
                    },
                    children: [
                      TableRow(
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                        ),
                        children: const [
                          Padding(
                            padding: EdgeInsets.all(8.0),
                            child: Text('Macronutriente', style: TextStyle(fontWeight: FontWeight.bold)),
                          ),
                          Padding(
                            padding: EdgeInsets.all(8.0),
                            child: Text('Grammi', style: TextStyle(fontWeight: FontWeight.bold)),
                          ),
                          Padding(
                            padding: EdgeInsets.all(8.0),
                            child: Text('Calorie', style: TextStyle(fontWeight: FontWeight.bold)),
                          ),
                          Padding(
                            padding: EdgeInsets.all(8.0),
                            child: Text('% del totale', style: TextStyle(fontWeight: FontWeight.bold)),
                          ),
                        ],
                      ),
                      _buildMacronutrientRow(
                        'Proteine',
                        totalProteins.toStringAsFixed(1),
                        (totalProteins * 4).toStringAsFixed(0),
                        Colors.red,
                        (totalProteins + totalCarbs + totalFats) > 0
                          ? (totalProteins / (totalProteins + totalCarbs + totalFats) * 100).toStringAsFixed(1)
                          : '0',
                      ),
                      _buildMacronutrientRow(
                        'Carboidrati',
                        totalCarbs.toStringAsFixed(1),
                        (totalCarbs * 4).toStringAsFixed(0),
                        Colors.blue,
                        (totalProteins + totalCarbs + totalFats) > 0
                          ? (totalCarbs / (totalProteins + totalCarbs + totalFats) * 100).toStringAsFixed(1)
                          : '0',
                      ),
                      _buildMacronutrientRow(
                        'Grassi',
                        totalFats.toStringAsFixed(1),
                        (totalFats * 9).toStringAsFixed(0),
                        Colors.yellow,
                        (totalProteins + totalCarbs + totalFats) > 0
                          ? (totalFats / (totalProteins + totalCarbs + totalFats) * 100).toStringAsFixed(1)
                          : '0',
                      ),
                      TableRow(
                        decoration: BoxDecoration(
                          color: Colors.grey.shade200,
                        ),
                        children: [
                          const Padding(
                            padding: EdgeInsets.all(8.0),
                            child: Text('TOTALE', style: TextStyle(fontWeight: FontWeight.bold)),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Text(
                              '${(totalProteins + totalCarbs + totalFats).toStringAsFixed(1)}g',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Text(
                              '$totalCalories kcal',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ),
                          const Padding(
                            padding: EdgeInsets.all(8.0),
                            child: Text('100%', style: TextStyle(fontWeight: FontWeight.bold)),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildMacronutrientLegend('Proteine', totalProteins, Colors.red),
                      _buildMacronutrientLegend('Carboidrati', totalCarbs, Colors.blue),
                      _buildMacronutrientLegend('Grassi', totalFats, Colors.yellow),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Percentuali caloriche
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Percentuali caloriche',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  _buildCaloriePercentagesTable(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMacronutrientLegend(String label, double value, Color color) {
    return Column(
      children: [
        Row(
          children: [
            Container(
              width: 16,
              height: 16,
              color: color,
            ),
            const SizedBox(width: 8),
            Text(label),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          '${value.toStringAsFixed(1)}g',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  TableRow _buildMacronutrientRow(String name, String grams, String calories, Color color, String percentage) {
    return TableRow(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            children: [
              Container(
                width: 12,
                height: 12,
                color: color,
                margin: const EdgeInsets.only(right: 8),
              ),
              Text(name),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text('${grams}g'),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text('$calories kcal'),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text('$percentage%'),
        ),
      ],
    );
  }

  Widget _buildMacronutrientsPieChart() {
    // Calcola i grammi totali di macronutrienti
    final double totalGrams = totalProteins + totalCarbs + totalFats;

    // Se non ci sono macronutrienti, mostra un messaggio
    if (totalGrams == 0) {
      return const Center(child: Text('Nessun dato sui macronutrienti disponibile'));
    }

    // Calcola le percentuali
    final proteinPercentage = (totalProteins / totalGrams * 100).toStringAsFixed(1);
    final carbsPercentage = (totalCarbs / totalGrams * 100).toStringAsFixed(1);
    final fatsPercentage = (totalFats / totalGrams * 100).toStringAsFixed(1);

    return PieChart(
      PieChartData(
        sections: [
          PieChartSectionData(
            color: Colors.red,
            value: totalProteins,
            title: 'P: $proteinPercentage%',
            radius: 60,
            titleStyle: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
          PieChartSectionData(
            color: Colors.blue,
            value: totalCarbs,
            title: 'C: $carbsPercentage%',
            radius: 60,
            titleStyle: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
          PieChartSectionData(
            color: Colors.yellow,
            value: totalFats,
            title: 'G: $fatsPercentage%',
            radius: 60,
            titleStyle: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ],
        sectionsSpace: 0,
        centerSpaceRadius: 40,
        startDegreeOffset: 180,
      ),
    );
  }

  Widget _buildCaloriePercentagesTable() {
    // Calcola le calorie da proteine, carboidrati e grassi
    final double proteinCalories = totalProteins * 4;
    final double carbCalories = totalCarbs * 4;
    final double fatCalories = totalFats * 9;
    final double totalCaloriesFromMacros = proteinCalories + carbCalories + fatCalories;

    return Table(
      columnWidths: const {
        0: FlexColumnWidth(3),
        1: FlexColumnWidth(2),
        2: FlexColumnWidth(2),
      },
      border: TableBorder.all(
        color: Colors.grey.shade300,
        width: 1,
      ),
      children: [
        const TableRow(
          decoration: BoxDecoration(color: Colors.grey),
          children: [
            Padding(
              padding: EdgeInsets.all(8.0),
              child: Text('Nutriente', style: TextStyle(fontWeight: FontWeight.bold)),
            ),
            Padding(
              padding: EdgeInsets.all(8.0),
              child: Text('kcal', style: TextStyle(fontWeight: FontWeight.bold)),
            ),
            Padding(
              padding: EdgeInsets.all(8.0),
              child: Text('%', style: TextStyle(fontWeight: FontWeight.bold)),
            ),
          ],
        ),
        _buildCalorieRow('Proteine', proteinCalories.round(), totalCaloriesFromMacros),
        _buildCalorieRow('Carboidrati', carbCalories.round(), totalCaloriesFromMacros),
        _buildCalorieRow('Grassi', fatCalories.round(), totalCaloriesFromMacros),
      ],
    );
  }

  TableRow _buildCalorieRow(String nutrient, int calories, double totalCalories) {
    final percentage = totalCalories > 0 ? (calories / totalCalories * 100).toStringAsFixed(1) : '0';

    return TableRow(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(nutrient),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text('$calories'),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text('$percentage%'),
        ),
      ],
    );
  }

  Widget _buildNutrientsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Tabella dei macronutrienti
          const Text(
            'Macronutrienti',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          _buildNutrientsTable(),
          const SizedBox(height: 24),

          // Grafico a barre per i macronutrienti
          const Text(
            'Distribuzione calorica',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 200,
            child: _buildCaloriesBarChart(),
          ),
          const SizedBox(height: 24),

          // Micronutrienti
          if (totalMicronutrients.isNotEmpty) ...[
            const Text(
              'Micronutrienti',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            _buildMicronutrientsTable(),
          ],
        ],
      ),
    );
  }

  Widget _buildNutrientsTable() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Table(
          columnWidths: const {
            0: FlexColumnWidth(3),
            1: FlexColumnWidth(2),
            2: FlexColumnWidth(2),
          },
          border: TableBorder.all(
            color: Colors.grey.shade300,
            width: 1,
          ),
          children: [
            const TableRow(
              decoration: BoxDecoration(color: Colors.grey),
              children: [
                Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text('Nutriente', style: TextStyle(fontWeight: FontWeight.bold)),
                ),
                Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text('Quantità', style: TextStyle(fontWeight: FontWeight.bold)),
                ),
                Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text('% Calorie', style: TextStyle(fontWeight: FontWeight.bold)),
                ),
              ],
            ),
            _buildNutrientRow('Calorie', '$totalCalories kcal', '100%'),
            _buildNutrientRow('Proteine', '${totalProteins.toStringAsFixed(1)} g',
              '${_calculatePercentage(totalProteins * 4, totalCalories)}%'),
            _buildNutrientRow('Carboidrati', '${totalCarbs.toStringAsFixed(1)} g',
              '${_calculatePercentage(totalCarbs * 4, totalCalories)}%'),
            if (totalSugar > 0)
              _buildNutrientRow('di cui zuccheri', '${totalSugar.toStringAsFixed(1)} g',
                '${_calculatePercentage(totalSugar * 4, totalCalories)}%'),
            _buildNutrientRow('Grassi', '${totalFats.toStringAsFixed(1)} g',
              '${_calculatePercentage(totalFats * 9, totalCalories)}%'),
            if (totalFiber > 0)
              _buildNutrientRow('Fibre', '${totalFiber.toStringAsFixed(1)} g', '-'),
          ],
        ),
      ),
    );
  }

  TableRow _buildNutrientRow(String nutrient, String amount, String percentage) {
    return TableRow(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(nutrient),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(amount),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(percentage),
        ),
      ],
    );
  }

  String _calculatePercentage(double value, int total) {
    if (total == 0) return '0';
    return ((value / total) * 100).toStringAsFixed(1);
  }

  Widget _buildCaloriesBarChart() {
    // Calcola le calorie da proteine, carboidrati e grassi
    final double proteinCalories = totalProteins * 4;
    final double carbCalories = totalCarbs * 4;
    final double fatCalories = totalFats * 9;

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: totalCalories.toDouble(),
        barTouchData: BarTouchData(enabled: false),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                String text = '';
                if (value == 0) text = 'Proteine';
                else if (value == 1) text = 'Carboidrati';
                else if (value == 2) text = 'Grassi';

                return Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(text, style: const TextStyle(fontSize: 12)),
                );
              },
              reservedSize: 40,
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text('${value.toInt()} kcal',
                  style: const TextStyle(fontSize: 10),
                );
              },
            ),
          ),
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false),
        barGroups: [
          BarChartGroupData(
            x: 0,
            barRods: [
              BarChartRodData(
                toY: proteinCalories,
                color: Colors.red,
                width: 20,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(6),
                  topRight: Radius.circular(6),
                ),
              ),
            ],
          ),
          BarChartGroupData(
            x: 1,
            barRods: [
              BarChartRodData(
                toY: carbCalories,
                color: Colors.blue,
                width: 20,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(6),
                  topRight: Radius.circular(6),
                ),
              ),
            ],
          ),
          BarChartGroupData(
            x: 2,
            barRods: [
              BarChartRodData(
                toY: fatCalories,
                color: Colors.yellow,
                width: 20,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(6),
                  topRight: Radius.circular(6),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMicronutrientsTable() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Table(
          columnWidths: const {
            0: FlexColumnWidth(3),
            1: FlexColumnWidth(2),
            2: FlexColumnWidth(2),
          },
          border: TableBorder.all(
            color: Colors.grey.shade300,
            width: 1,
          ),
          children: [
            const TableRow(
              decoration: BoxDecoration(color: Colors.grey),
              children: [
                Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text('Micronutriente', style: TextStyle(fontWeight: FontWeight.bold)),
                ),
                Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text('Quantità', style: TextStyle(fontWeight: FontWeight.bold)),
                ),
                Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text('% VNR', style: TextStyle(fontWeight: FontWeight.bold)),
                ),
              ],
            ),
            ...totalMicronutrients.entries.map((entry) {
              final String nutrientKey = entry.key;
              final double value = entry.value;
              final String displayName = MicronutrientsHelper.getDisplayName(nutrientKey);
              final String unit = MicronutrientsHelper.UNITS[nutrientKey] ?? '';
              final double percentage = MicronutrientsHelper.calculateDailyValuePercentage(nutrientKey, value);

              return TableRow(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(displayName),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text('${value.toStringAsFixed(1)} $unit'),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text('${percentage.toStringAsFixed(1)}%'),
                  ),
                ],
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildFoodsTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: widget.meal.foods.length,
      itemBuilder: (context, index) {
        final foodItem = widget.meal.foods[index];
        final food = foodItem.food;
        final servingRatio = foodItem.quantity / food.servingSizeGrams;

        return Card(
          margin: const EdgeInsets.only(bottom: 16.0),
          child: InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => FoodDetailScreen(food: food),
                ),
              );
            },
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      if (food.imageUrl.isNotEmpty)
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8.0),
                          child: Image.network(
                            food.imageUrl,
                            width: 60,
                            height: 60,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) => Container(
                              width: 60,
                              height: 60,
                              color: Colors.grey[300],
                              child: const Icon(Icons.restaurant, size: 30, color: Colors.grey),
                            ),
                          ),
                        )
                      else
                        Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          child: const Icon(Icons.restaurant, size: 30, color: Colors.grey),
                        ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              food.name,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              'Porzione: ${foodItem.quantity}g',
                              style: TextStyle(
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildNutrientInfo(
                        'Calorie',
                        '${(food.calories * servingRatio).round()} kcal',
                        Icons.local_fire_department,
                        Colors.red,
                      ),
                      _buildNutrientInfo(
                        'Proteine',
                        '${(food.proteins * servingRatio).toStringAsFixed(1)}g',
                        Icons.fitness_center,
                        Colors.blue,
                      ),
                      _buildNutrientInfo(
                        'Carboidrati',
                        '${(food.carbs * servingRatio).toStringAsFixed(1)}g',
                        Icons.grain,
                        Colors.orange,
                      ),
                      _buildNutrientInfo(
                        'Grassi',
                        '${(food.fats * servingRatio).toStringAsFixed(1)}g',
                        Icons.opacity,
                        Colors.yellow[700]!,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildNutrientInfo(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
      ],
    );
  }
}
