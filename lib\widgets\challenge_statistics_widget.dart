import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../controllers/advanced_challenges_controller.dart';
import '../models/welljourney_models.dart';
import '../theme/dr_staffilano_theme.dart';

/// Widget per visualizzare le statistiche delle sfide
class ChallengeStatisticsWidget extends StatefulWidget {
  final ChallengeStatistics statistics;
  final bool isDarkMode;

  const ChallengeStatisticsWidget({
    Key? key,
    required this.statistics,
    this.isDarkMode = false,
  }) : super(key: key);

  @override
  State<ChallengeStatisticsWidget> createState() => _ChallengeStatisticsWidgetState();
}

class _ChallengeStatisticsWidgetState extends State<ChallengeStatisticsWidget>
    with TickerProviderStateMixin {

  late AnimationController _animationController;
  late List<AnimationController> _counterControllers;
  late List<Animation<int>> _counterAnimations;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Animazioni per i contatori
    _counterControllers = List.generate(4, (index) {
      return AnimationController(
        duration: Duration(milliseconds: 1000 + (index * 200)),
        vsync: this,
      );
    });

    _counterAnimations = [
      IntTween(begin: 0, end: widget.statistics.totalCompleted).animate(
        CurvedAnimation(parent: _counterControllers[0], curve: Curves.easeOut),
      ),
      IntTween(begin: 0, end: widget.statistics.totalActive).animate(
        CurvedAnimation(parent: _counterControllers[1], curve: Curves.easeOut),
      ),
      IntTween(begin: 0, end: widget.statistics.totalPoints).animate(
        CurvedAnimation(parent: _counterControllers[2], curve: Curves.easeOut),
      ),
      IntTween(begin: 0, end: widget.statistics.currentStreak).animate(
        CurvedAnimation(parent: _counterControllers[3], curve: Curves.easeOut),
      ),
    ];

    _startAnimations();
  }

  @override
  void dispose() {
    _animationController.dispose();
    for (final controller in _counterControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _startAnimations() {
    _animationController.forward();
    for (int i = 0; i < _counterControllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 100), () {
        if (mounted) {
          _counterControllers[i].forward();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(8), // Ridotto ulteriormente da 12 a 8
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            widget.isDarkMode
                ? DrStaffilanoTheme.backgroundDark
                : Colors.white,
            widget.isDarkMode
                ? DrStaffilanoTheme.backgroundDark.withOpacity(0.8)
                : Colors.white.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12), // Ridotto da 16 a 12
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 6, // Ridotto da 8 a 6
            offset: const Offset(0, 1), // Ridotto da 2 a 1
          ),
        ],
        border: Border.all(
          color: DrStaffilanoTheme.primaryGreen.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: IntrinsicHeight( // Aggiunto per adattare l'altezza al contenuto
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildCompactHeader(),
            _buildCompactStatisticsGrid(), // Rimosso SizedBox per ridurre altezza
          ],
        ),
      ),
    );
  }

  /// Header compatto delle statistiche
  Widget _buildCompactHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(4), // Ridotto ulteriormente
          decoration: BoxDecoration(
            color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Icon(
            FontAwesomeIcons.chartLine,
            color: DrStaffilanoTheme.primaryGreen,
            size: 12, // Ridotto ulteriormente
          ),
        ),
        const SizedBox(width: 6),
        Expanded(
          child: Text(
            'Statistiche Sfide',
            style: TextStyle(
              fontSize: 12, // Ridotto ulteriormente
              fontWeight: FontWeight.bold,
              color: widget.isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
            ),
          ),
        ),
        _buildCompactAverageCompletionBadge(),
      ],
    ).animate().fadeIn().slideX(begin: -0.3, end: 0);
  }

  /// Header delle statistiche (versione originale)
  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6), // Ridotto da 8 a 6
          decoration: BoxDecoration(
            color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
            borderRadius: BorderRadius.circular(6), // Ridotto da 8 a 6
          ),
          child: Icon(
            FontAwesomeIcons.chartLine,
            color: DrStaffilanoTheme.primaryGreen,
            size: 16, // Ridotto da 20 a 16
          ),
        ),
        const SizedBox(width: 8), // Ridotto da 12 a 8
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min, // Aggiunto per ridurre l'altezza
            children: [
              Text(
                'Statistiche Sfide',
                style: TextStyle(
                  fontSize: 14, // Ridotto da 16 a 14
                  fontWeight: FontWeight.bold,
                  color: widget.isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                ),
              ),
              Text(
                'I tuoi progressi nel tempo',
                style: TextStyle(
                  fontSize: 11, // Ridotto da 12 a 11
                  color: widget.isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
                ),
              ),
            ],
          ),
        ),
        _buildAverageCompletionBadge(),
      ],
    ).animate().fadeIn().slideX(begin: -0.3, end: 0);
  }

  /// Badge percentuale completamento media
  Widget _buildAverageCompletionBadge() {
    final percentage = (widget.statistics.averageCompletion * 100).round();
    final color = percentage >= 80
        ? DrStaffilanoTheme.primaryGreen
        : percentage >= 60
            ? DrStaffilanoTheme.accentGold
            : Colors.orange;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.trending_up,
            size: 12,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            '$percentage%',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// Badge compatto percentuale completamento media
  Widget _buildCompactAverageCompletionBadge() {
    final percentage = (widget.statistics.averageCompletion * 100).round();
    final color = percentage >= 80
        ? DrStaffilanoTheme.primaryGreen
        : percentage >= 60
            ? DrStaffilanoTheme.accentGold
            : Colors.orange;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Text(
        '$percentage%',
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.bold,
          color: color,
        ),
      ),
    );
  }

  /// Griglia compatta delle statistiche
  Widget _buildCompactStatisticsGrid() {
    return Row(
      children: [
        Expanded(
          child: _buildCompactStatCard(
            'Completate',
            _counterAnimations[0],
            FontAwesomeIcons.trophy,
            DrStaffilanoTheme.accentGold,
            0,
          ),
        ),
        const SizedBox(width: 6),
        Expanded(
          child: _buildCompactStatCard(
            'Attive',
            _counterAnimations[1],
            FontAwesomeIcons.play,
            DrStaffilanoTheme.primaryGreen,
            1,
          ),
        ),
        const SizedBox(width: 6),
        Expanded(
          child: _buildCompactStatCard(
            'Punti',
            _counterAnimations[2],
            FontAwesomeIcons.star,
            DrStaffilanoTheme.secondaryBlue,
            2,
          ),
        ),
        const SizedBox(width: 6),
        Expanded(
          child: _buildCompactStatCard(
            'Striscia',
            _counterAnimations[3],
            FontAwesomeIcons.fire,
            Colors.orange,
            3,
          ),
        ),
      ],
    );
  }

  /// Griglia delle statistiche (versione originale)
  Widget _buildStatisticsGrid() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Completate',
            _counterAnimations[0],
            FontAwesomeIcons.trophy,
            DrStaffilanoTheme.accentGold,
            0,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Attive',
            _counterAnimations[1],
            FontAwesomeIcons.play,
            DrStaffilanoTheme.primaryGreen,
            1,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Punti',
            _counterAnimations[2],
            FontAwesomeIcons.star,
            DrStaffilanoTheme.secondaryBlue,
            2,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Striscia',
            _counterAnimations[3],
            FontAwesomeIcons.fire,
            Colors.orange,
            3,
          ),
        ),
      ],
    );
  }

  /// Singola card statistica
  Widget _buildStatCard(
    String label,
    Animation<int> animation,
    IconData icon,
    Color color,
    int index,
  ) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        return Container(
          padding: const EdgeInsets.all(8), // Ridotto da 12 a 8
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(10), // Ridotto da 12 a 10
            border: Border.all(
              color: color.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min, // Aggiunto per ridurre l'altezza
            children: [
              Icon(
                icon,
                color: color,
                size: 16, // Ridotto da 20 a 16
              ),
              const SizedBox(height: 4), // Ridotto da 8 a 4
              Text(
                '${animation.value}',
                style: TextStyle(
                  fontSize: 16, // Ridotto da 18 a 16
                  fontWeight: FontWeight.bold,
                  color: widget.isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                ),
              ),
              Text(
                label,
                style: TextStyle(
                  fontSize: 9, // Ridotto da 10 a 9
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      },
    ).animate(delay: (index * 150).ms)
        .slideY(begin: 0.5, end: 0)
        .fadeIn();
  }

  /// Card compatta statistica
  Widget _buildCompactStatCard(
    String label,
    Animation<int> animation,
    IconData icon,
    Color color,
    int index,
  ) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        return Container(
          padding: const EdgeInsets.all(2), // Ridotto ulteriormente da 4 a 2
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(4), // Ridotto da 6 a 4
            border: Border.all(
              color: color.withOpacity(0.3),
              width: 0.5,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: color,
                size: 10, // Ridotto ulteriormente da 12 a 10
              ),
              const SizedBox(height: 1), // Ridotto ulteriormente da 2 a 1
              Text(
                '${animation.value}',
                style: TextStyle(
                  fontSize: 10, // Ridotto ulteriormente da 12 a 10
                  fontWeight: FontWeight.bold,
                  color: widget.isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                ),
              ),
              Text(
                label,
                style: TextStyle(
                  fontSize: 7, // Ridotto ulteriormente da 8 a 7
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      },
    ).animate(delay: (index * 150).ms)
        .slideY(begin: 0.5, end: 0)
        .fadeIn();
  }
}
