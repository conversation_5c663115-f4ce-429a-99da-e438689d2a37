import '../models/food.dart';

/// Database di alimenti italiani ad alto contenuto proteico specificamente
/// selezionati per atleti e persone molto attive
class AthleticItalianProteins {
  static List<Food> getAthleticProteins() {
    return [
      // CARNI MAGRE ITALIANE - METODI TRADIZIONALI
      Food(
        id: 'athletic_protein_1',
        name: 'Bresaola della Valtellina IGP',
        description: 'Carne bovina stagionata delle Alpi lombarde, ricca di proteine nobili',
        calories: 151,
        proteins: 32.0,
        carbs: 0.6,
        fats: 2.6,
        fiber: 0.0,
        sugar: 0.0,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.breakfast, MealType.lunch, MealType.dinner, MealType.snack],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '80g',
        servingSizeGrams: 80,
        foodState: FoodState.processed,
        calcium: 15.0,
        potassium: 505.0,
        magnesium: 28.0,
        phosphorus: 280.0,
        sodium: 1200.0,
        micronutrients: {
          'ferro': 3.8,
          'zinco': 4.2,
          'vitamina_b12': 2.8,
        },
        tags: ['bresaola', 'valtellina', 'igp', 'proteica', 'magra'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
      ),

      Food(
        id: 'athletic_protein_2',
        name: 'Petto di pollo al limone',
        description: 'Petto di pollo grigliato con limone e rosmarino, preparazione italiana',
        calories: 165,
        proteins: 31.0,
        carbs: 0.0,
        fats: 3.6,
        fiber: 0.0,
        sugar: 0.0,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '150g',
        servingSizeGrams: 150,
        foodState: FoodState.cooked,
        calcium: 15.0,
        potassium: 256.0,
        magnesium: 28.0,
        phosphorus: 228.0,
        sodium: 74.0,
        micronutrients: {
          'niacina': 13.7,
          'vitamina_b6': 0.6,
          'selenio': 27.6,
        },
        tags: ['pollo', 'limone', 'rosmarino', 'grigliato', 'proteico'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
        recipe: 'Marinare il petto di pollo con limone, rosmarino e olio. Grigliare fino a cottura.',
        ingredients: ['petto di pollo', 'limone', 'rosmarino', 'olio extravergine', 'sale'],
      ),

      // PESCE ITALIANO AD ALTO CONTENUTO PROTEICO
      Food(
        id: 'athletic_protein_3',
        name: 'Tonno siciliano alla griglia',
        description: 'Tonno fresco siciliano grigliato, ricco di proteine e omega-3',
        calories: 184,
        proteins: 30.0,
        carbs: 0.0,
        fats: 6.3,
        fiber: 0.0,
        sugar: 0.0,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '120g',
        servingSizeGrams: 120,
        foodState: FoodState.cooked,
        calcium: 8.0,
        potassium: 407.0,
        magnesium: 50.0,
        phosphorus: 278.0,
        sodium: 50.0,
        micronutrients: {
          'omega_3': 1280.0,
          'vitamina_d': 5.7,
          'vitamina_b12': 9.4,
          'selenio': 108.0,
        },
        tags: ['tonno', 'siciliano', 'omega3', 'proteico', 'pesce'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
      ),

      Food(
        id: 'athletic_protein_4',
        name: 'Branzino in crosta di sale',
        description: 'Branzino del Mediterraneo cotto in crosta di sale, metodo tradizionale',
        calories: 97,
        proteins: 20.0,
        carbs: 0.0,
        fats: 1.5,
        fiber: 0.0,
        sugar: 0.0,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '150g',
        servingSizeGrams: 150,
        foodState: FoodState.cooked,
        calcium: 15.0,
        potassium: 256.0,
        magnesium: 45.0,
        phosphorus: 250.0,
        sodium: 120.0,
        micronutrients: {
          'omega_3': 595.0,
          'vitamina_b12': 1.2,
          'selenio': 36.5,
        },
        tags: ['branzino', 'mediterraneo', 'crosta di sale', 'proteico'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
      ),

      // LATTICINI ITALIANI PROTEICI
      Food(
        id: 'athletic_protein_5',
        name: 'Ricotta di pecora siciliana',
        description: 'Ricotta fresca di pecora siciliana, ricca di proteine del siero',
        calories: 146,
        proteins: 11.0,
        carbs: 4.0,
        fats: 10.0,
        fiber: 0.0,
        sugar: 4.0,
        categories: [FoodCategory.dairy],
        suitableForMeals: [MealType.breakfast, MealType.snack],
        isVegetarian: true,
        isGlutenFree: true,
        allergens: ['latticini'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        calcium: 272.0,
        potassium: 105.0,
        magnesium: 11.0,
        phosphorus: 158.0,
        sodium: 84.0,
        micronutrients: {
          'vitamina_a': 162.0,
          'vitamina_b12': 0.34,
          'riboflavina': 0.27,
        },
        tags: ['ricotta', 'pecora', 'siciliana', 'siero', 'proteica'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
      ),

      Food(
        id: 'athletic_protein_6',
        name: 'Parmigiano Reggiano DOP 24 mesi',
        description: 'Parmigiano Reggiano stagionato 24 mesi, concentrato di proteine nobili',
        calories: 392,
        proteins: 33.0,
        carbs: 0.0,
        fats: 28.0,
        fiber: 0.0,
        sugar: 0.0,
        categories: [FoodCategory.dairy],
        suitableForMeals: [MealType.breakfast, MealType.lunch, MealType.dinner, MealType.snack],
        isVegetarian: true,
        isGlutenFree: true,
        allergens: ['latticini'],
        servingSize: '30g',
        servingSizeGrams: 30,
        foodState: FoodState.processed,
        calcium: 1184.0,
        potassium: 102.0,
        magnesium: 43.0,
        phosphorus: 694.0,
        sodium: 1529.0,
        micronutrients: {
          'vitamina_a': 270.0,
          'vitamina_b12': 1.4,
          'zinco': 2.75,
        },
        tags: ['parmigiano', 'reggiano', 'dop', '24mesi', 'stagionato'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
      ),

      // LEGUMI ITALIANI PROTEICI
      Food(
        id: 'athletic_protein_7',
        name: 'Lenticchie di Castelluccio IGP',
        description: 'Lenticchie di Castelluccio cotte, proteine vegetali complete',
        calories: 116,
        proteins: 9.0,
        carbs: 20.0,
        fats: 0.4,
        fiber: 8.0,
        sugar: 1.8,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '150g cotte',
        servingSizeGrams: 150,
        foodState: FoodState.cooked,
        calcium: 19.0,
        potassium: 369.0,
        magnesium: 36.0,
        phosphorus: 180.0,
        sodium: 2.0,
        micronutrients: {
          'ferro': 3.3,
          'zinco': 1.3,
          'folati': 181.0,
        },
        tags: ['lenticchie', 'castelluccio', 'igp', 'proteiche', 'vegetali'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
      ),

      // UOVA ITALIANE
      Food(
        id: 'athletic_protein_8',
        name: 'Frittata alle erbe italiane',
        description: 'Frittata con uova fresche e erbe aromatiche italiane',
        calories: 154,
        proteins: 11.0,
        carbs: 1.0,
        fats: 11.5,
        fiber: 0.0,
        sugar: 1.0,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.breakfast, MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isGlutenFree: true,
        allergens: ['uova'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        calcium: 56.0,
        potassium: 138.0,
        magnesium: 12.0,
        phosphorus: 198.0,
        sodium: 124.0,
        micronutrients: {
          'vitamina_a': 160.0,
          'vitamina_d': 2.0,
          'vitamina_b12': 0.89,
          'colina': 294.0,
        },
        tags: ['frittata', 'erbe', 'uova', 'proteica', 'italiana'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
        recipe: 'Sbattere le uova con erbe fresche. Cuocere in padella antiaderente.',
        ingredients: ['uova fresche', 'basilico', 'prezzemolo', 'origano', 'olio extravergine'],
      ),
    ];
  }

  /// Ottieni alimenti proteici per tipo di pasto specifico per atleti
  static List<Food> getProteinsByMealType(MealType mealType) {
    final allProteins = getAthleticProteins();
    return allProteins
        .where((food) => food.suitableForMeals.contains(mealType))
        .toList()
        ..sort((a, b) => b.proteins.compareTo(a.proteins));
  }

  /// Ottieni i migliori alimenti proteici per il recupero post-allenamento
  static List<Food> getPostWorkoutProteins() {
    final allProteins = getAthleticProteins();
    return allProteins
        .where((food) => 
            food.proteins >= 20.0 && // Almeno 20g di proteine
            food.suitableForMeals.contains(MealType.snack))
        .toList()
        ..sort((a, b) => b.proteins.compareTo(a.proteins));
  }
}
