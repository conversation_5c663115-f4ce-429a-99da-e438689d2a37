import 'package:flutter/material.dart';
import '../../../models/community_group.dart';
import '../../../utils/theme_colors.dart';

/// Widget per i gruppi della community
class CommunityGroupsWidget extends StatefulWidget {
  final List<CommunityGroup> groups;

  const CommunityGroupsWidget({
    super.key,
    required this.groups,
  });

  @override
  State<CommunityGroupsWidget> createState() => _CommunityGroupsWidgetState();
}

class _CommunityGroupsWidgetState extends State<CommunityGroupsWidget> {
  String _selectedCategory = 'all';
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// Filtra i gruppi per categoria
  List<CommunityGroup> get _filteredGroups {
    if (_selectedCategory == 'all') {
      return widget.groups;
    }
    return widget.groups.where((group) => group.category.id == _selectedCategory).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Filtri per categoria
        _buildCategoryFilters(),
        
        // Lista gruppi
        Expanded(
          child: _filteredGroups.isEmpty
              ? _buildEmptyState()
              : _buildGroupsList(),
        ),
      ],
    );
  }

  /// Costruisce i filtri per categoria
  Widget _buildCategoryFilters() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          _buildCategoryChip('all', 'Tutti', '🌟'),
          ...GroupCategory.values.map((category) =>
              _buildCategoryChip(category.id, category.displayName, category.emoji)),
        ],
      ),
    );
  }

  /// Costruisce un chip di categoria
  Widget _buildCategoryChip(String categoryId, String label, String emoji) {
    final isSelected = _selectedCategory == categoryId;
    
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedCategory = categoryId;
          });
        },
        label: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(emoji),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
        backgroundColor: Colors.grey[100],
        selectedColor: ThemeColors.primaryGreen.withOpacity(0.2),
        checkmarkColor: ThemeColors.primaryGreen,
        side: BorderSide(
          color: isSelected ? ThemeColors.primaryGreen : Colors.grey[300]!,
        ),
      ),
    );
  }

  /// Costruisce lo stato vuoto
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.group_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Nessun gruppo trovato',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Prova a cambiare categoria o\ncrea un nuovo gruppo',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _createNewGroup(),
            icon: const Icon(Icons.add),
            label: const Text('Crea Gruppo'),
            style: ElevatedButton.styleFrom(
              backgroundColor: ThemeColors.primaryGreen,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  /// Costruisce la lista dei gruppi
  Widget _buildGroupsList() {
    return RefreshIndicator(
      onRefresh: () async {
        // Implementa il refresh dei gruppi
        await Future.delayed(const Duration(seconds: 1));
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        itemCount: _filteredGroups.length,
        itemBuilder: (context, index) {
          final group = _filteredGroups[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: _buildGroupCard(group),
          );
        },
      ),
    );
  }

  /// Costruisce la card di un gruppo
  Widget _buildGroupCard(CommunityGroup group) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _openGroup(group),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header con immagine
            if (group.imageUrl != null) _buildGroupImage(group),
            
            // Contenuto del gruppo
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Nome e badge
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          group.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      _buildGroupBadges(group),
                    ],
                  ),
                  const SizedBox(height: 8),
                  
                  // Categoria
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Color(int.parse('0xFF${group.colorHex.substring(1)}')).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${group.emoji} ${group.category.displayName}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(int.parse('0xFF${group.colorHex.substring(1)}')),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  
                  // Descrizione
                  Text(
                    group.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                      height: 1.4,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 16),
                  
                  // Statistiche
                  Row(
                    children: [
                      _buildGroupStat(
                        icon: Icons.people,
                        value: _formatNumber(group.membersCount),
                        label: 'membri',
                      ),
                      const SizedBox(width: 16),
                      _buildGroupStat(
                        icon: Icons.article,
                        value: _formatNumber(group.postsCount),
                        label: 'post',
                      ),
                      const SizedBox(width: 16),
                      _buildGroupStat(
                        icon: Icons.schedule,
                        value: group.isRecentlyActive ? 'Attivo' : 'Inattivo',
                        label: '',
                        color: group.isRecentlyActive ? Colors.green : Colors.orange,
                      ),
                      const Spacer(),
                      
                      // Pulsante azione
                      _buildJoinButton(group),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Costruisce l'immagine del gruppo
  Widget _buildGroupImage(CommunityGroup group) {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
        image: DecorationImage(
          image: NetworkImage(group.imageUrl!),
          fit: BoxFit.cover,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withOpacity(0.3),
            ],
          ),
        ),
      ),
    );
  }

  /// Costruisce i badge del gruppo
  Widget _buildGroupBadges(CommunityGroup group) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (group.isFeatured)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: ThemeColors.accentGold.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '⭐ In evidenza',
              style: TextStyle(
                fontSize: 10,
                color: ThemeColors.accentGold,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        if (group.type == GroupType.official) ...[
          if (group.isFeatured) const SizedBox(width: 4),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: ThemeColors.primaryBlue.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '✅ Ufficiale',
              style: TextStyle(
                fontSize: 10,
                color: ThemeColors.primaryBlue,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// Costruisce una statistica del gruppo
  Widget _buildGroupStat({
    required IconData icon,
    required String value,
    required String label,
    Color? color,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16,
          color: color ?? Colors.grey[600],
        ),
        const SizedBox(width: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: color ?? Colors.grey[800],
          ),
        ),
        if (label.isNotEmpty) ...[
          const SizedBox(width: 2),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ],
    );
  }

  /// Costruisce il pulsante per unirsi al gruppo
  Widget _buildJoinButton(CommunityGroup group) {
    return ElevatedButton(
      onPressed: () => _joinGroup(group),
      style: ElevatedButton.styleFrom(
        backgroundColor: ThemeColors.primaryGreen,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        minimumSize: Size.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child: const Text(
        'Unisciti',
        style: TextStyle(fontSize: 12),
      ),
    );
  }

  /// Formatta un numero per la visualizzazione
  String _formatNumber(int number) {
    if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    }
    return number.toString();
  }

  /// Apre un gruppo
  void _openGroup(CommunityGroup group) {
    // Implementa la navigazione al gruppo
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Apertura gruppo: ${group.name}'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  /// Unisce l'utente a un gruppo
  void _joinGroup(CommunityGroup group) {
    // Implementa l'unione al gruppo
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Ti sei unito a: ${group.name}'),
        backgroundColor: ThemeColors.primaryGreen,
      ),
    );
  }

  /// Crea un nuovo gruppo
  void _createNewGroup() {
    // Implementa la creazione di un nuovo gruppo
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Funzionalità di creazione gruppo in arrivo'),
      ),
    );
  }
}
