import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/diet_plan.dart';
import '../models/meal.dart';
import '../models/food.dart';
import '../theme/app_theme.dart';
import '../theme/dr_staffilano_theme.dart';

/// Widget per visualizzazione dettagliata degli alimenti in formato tabellare
/// Supporta sia PlannedMeal che Meal per compatibilità
class DetailedFoodTable extends StatefulWidget {
  final dynamic meal; // Può essere PlannedMeal o Meal
  final Function(Food)? onFoodTapped;

  const DetailedFoodTable({
    Key? key,
    required this.meal,
    this.onFoodTapped,
  }) : super(key: key);

  @override
  State<DetailedFoodTable> createState() => _DetailedFoodTableState();
}

class _DetailedFoodTableState extends State<DetailedFoodTable> {
  Food? _selectedFood;

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header del pasto
            _buildMealHeader(),
            const SizedBox(height: 16),

            // Tabella alimenti
            _buildFoodTable(),
            const SizedBox(height: 16),

            // Totali del pasto
            _buildMealTotals(),
            const SizedBox(height: 16),

            // Dettagli alimento selezionato
            if (_selectedFood != null) ...[
              _buildSelectedFoodDetails(),
              const SizedBox(height: 16),
            ],

            // Grafici nutrizionali
            _buildNutritionalCharts(),
          ],
        ),
      ),
    );
  }

  Widget _buildMealHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getMealIcon(),
            color: AppTheme.primaryColor,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _getMealName(),
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Orario: ${_getMealTime()}',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: AppTheme.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            '${_calculateTotalCalories()} kcal',
            style: TextStyle(
              color: AppTheme.accentColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFoodTable() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Header tabella
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                const Expanded(
                  flex: 3,
                  child: Text(
                    'Alimento',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                const Expanded(
                  flex: 1,
                  child: Text(
                    'Quantità',
                    style: TextStyle(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
                const Expanded(
                  flex: 1,
                  child: Text(
                    'Kcal',
                    style: TextStyle(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
                const Expanded(
                  flex: 1,
                  child: Text(
                    'P',
                    style: TextStyle(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
                const Expanded(
                  flex: 1,
                  child: Text(
                    'C',
                    style: TextStyle(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
                const Expanded(
                  flex: 1,
                  child: Text(
                    'G',
                    style: TextStyle(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),

          // Righe alimenti
          ..._getMealFoods().asMap().entries.map((entry) {
            final index = entry.key;
            final foodData = entry.value;
            final food = _getFoodFromData(foodData);
            final isSelected = _selectedFood == food;

            return InkWell(
              onTap: () {
                setState(() {
                  _selectedFood = isSelected ? null : food;
                });
                if (widget.onFoodTapped != null) {
                  widget.onFoodTapped!(food);
                }
              },
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isSelected
                    ? AppTheme.primaryColor.withOpacity(0.1)
                    : index.isEven
                      ? Colors.grey[50]
                      : Colors.white,
                  border: Border(
                    bottom: BorderSide(
                      color: Colors.grey[200]!,
                      width: 0.5,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      flex: 3,
                      child: Row(
                        children: [
                          if (isSelected)
                            Icon(
                              Icons.arrow_right,
                              color: AppTheme.primaryColor,
                              size: 20,
                            ),
                          Expanded(
                            child: Text(
                              food.name,
                              style: TextStyle(
                                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                color: isSelected ? AppTheme.primaryColor : null,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        '${_getQuantityFromData(foodData).round()}g',
                        textAlign: TextAlign.center,
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        '${_getCaloriesFromData(foodData)}',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: AppTheme.accentColor,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        '${_getProteinsFromData(foodData).toStringAsFixed(1)}',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: AppTheme.proteinColor,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        '${_getCarbsFromData(foodData).toStringAsFixed(1)}',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: AppTheme.carbColor,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        '${_getFatsFromData(foodData).toStringAsFixed(1)}',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: AppTheme.fatColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildMealTotals() {
    final totalCalories = _calculateTotalCalories();
    final totalProteins = _calculateTotalProteins();
    final totalCarbs = _calculateTotalCarbs();
    final totalFats = _calculateTotalFats();
    final totalFiber = _calculateTotalFiber();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor.withOpacity(0.1),
            AppTheme.primaryColor.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.primaryColor.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Totali Pasto',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              _buildTotalChip('Calorie', '$totalCalories kcal', AppTheme.accentColor),
              const SizedBox(width: 8),
              _buildTotalChip('Proteine', '${totalProteins.toStringAsFixed(1)}g', AppTheme.proteinColor),
              const SizedBox(width: 8),
              _buildTotalChip('Carboidrati', '${totalCarbs.toStringAsFixed(1)}g', AppTheme.carbColor),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              _buildTotalChip('Grassi', '${totalFats.toStringAsFixed(1)}g', AppTheme.fatColor),
              const SizedBox(width: 8),
              _buildTotalChip('Fibre', '${totalFiber.toStringAsFixed(1)}g', Colors.green),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTotalChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedFoodDetails() {
    if (_selectedFood == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.primaryColor.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppTheme.primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Dettagli: ${_selectedFood!.name}',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Valori nutrizionali per 100g:',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          _buildNutritionalGrid(_selectedFood!),
        ],
      ),
    );
  }

  Widget _buildNutritionalGrid(Food food) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 3,
      childAspectRatio: 2.5,
      crossAxisSpacing: 8,
      mainAxisSpacing: 8,
      children: [
        _buildNutritionalItem('Calorie', '${food.calories} kcal', AppTheme.accentColor),
        _buildNutritionalItem('Proteine', '${food.proteins.toStringAsFixed(1)}g', AppTheme.proteinColor),
        _buildNutritionalItem('Carboidrati', '${food.carbs.toStringAsFixed(1)}g', AppTheme.carbColor),
        _buildNutritionalItem('Grassi', '${food.fats.toStringAsFixed(1)}g', AppTheme.fatColor),
        _buildNutritionalItem('Fibre', '${food.fiber.toStringAsFixed(1)}g', Colors.green),
        _buildNutritionalItem('Sodio', '${(food.sodium ?? 0).toStringAsFixed(0)}mg', Colors.orange),
      ],
    );
  }

  Widget _buildNutritionalItem(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNutritionalCharts() {
    return Row(
      children: [
        // Grafico a torta macronutrienti
        Expanded(
          child: _buildMacronutrientPieChart(),
        ),
        const SizedBox(width: 16),
        // Grafico a barre micronutrienti
        Expanded(
          child: _buildMicronutrientBarChart(),
        ),
      ],
    );
  }

  Widget _buildMacronutrientPieChart() {
    final totalProteins = _calculateTotalProteins();
    final totalCarbs = _calculateTotalCarbs();
    final totalFats = _calculateTotalFats();

    final proteinCalories = totalProteins * 4;
    final carbCalories = totalCarbs * 4;
    final fatCalories = totalFats * 9;
    final totalCalories = proteinCalories + carbCalories + fatCalories;

    if (totalCalories == 0) return const SizedBox.shrink();

    return Column(
      children: [
        Text(
          'Distribuzione Macronutrienti',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 120,
          child: PieChart(
            PieChartData(
              sections: [
                PieChartSectionData(
                  value: proteinCalories,
                  title: '${(proteinCalories / totalCalories * 100).round()}%',
                  color: AppTheme.proteinColor,
                  radius: 50,
                  titleStyle: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                PieChartSectionData(
                  value: carbCalories,
                  title: '${(carbCalories / totalCalories * 100).round()}%',
                  color: AppTheme.carbColor,
                  radius: 50,
                  titleStyle: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                PieChartSectionData(
                  value: fatCalories,
                  title: '${(fatCalories / totalCalories * 100).round()}%',
                  color: AppTheme.fatColor,
                  radius: 50,
                  titleStyle: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
              sectionsSpace: 2,
              centerSpaceRadius: 20,
            ),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildLegendItem('P', AppTheme.proteinColor),
            _buildLegendItem('C', AppTheme.carbColor),
            _buildLegendItem('G', AppTheme.fatColor),
          ],
        ),
      ],
    );
  }

  Widget _buildMicronutrientBarChart() {
    final totalFiber = _calculateTotalFiber();
    final totalSodium = _calculateTotalSodium();

    return Column(
      children: [
        Text(
          'Micronutrienti',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 120,
          child: BarChart(
            BarChartData(
              alignment: BarChartAlignment.spaceAround,
              maxY: 50,
              barTouchData: BarTouchData(enabled: false),
              titlesData: FlTitlesData(
                show: true,
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    getTitlesWidget: (value, meta) {
                      switch (value.toInt()) {
                        case 0:
                          return const Text('Fibre', style: TextStyle(fontSize: 10));
                        case 1:
                          return const Text('Sodio', style: TextStyle(fontSize: 10));
                        default:
                          return const Text('');
                      }
                    },
                  ),
                ),
                leftTitles: AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
                topTitles: AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
                rightTitles: AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
              ),
              borderData: FlBorderData(show: false),
              barGroups: [
                BarChartGroupData(
                  x: 0,
                  barRods: [
                    BarChartRodData(
                      toY: totalFiber.clamp(0, 50),
                      color: Colors.green,
                      width: 20,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ],
                ),
                BarChartGroupData(
                  x: 1,
                  barRods: [
                    BarChartRodData(
                      toY: (totalSodium / 100).clamp(0, 50), // Scala per visualizzazione
                      color: Colors.orange,
                      width: 20,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 10),
        ),
      ],
    );
  }

  // Metodi di calcolo
  int _calculateTotalCalories() {
    return _getMealFoods().fold(0, (sum, foodData) => sum + _getCaloriesFromData(foodData));
  }

  double _calculateTotalProteins() {
    return _getMealFoods().fold(0.0, (sum, foodData) => sum + _getProteinsFromData(foodData));
  }

  double _calculateTotalCarbs() {
    return _getMealFoods().fold(0.0, (sum, foodData) => sum + _getCarbsFromData(foodData));
  }

  double _calculateTotalFats() {
    return _getMealFoods().fold(0.0, (sum, foodData) => sum + _getFatsFromData(foodData));
  }

  double _calculateTotalFiber() {
    return _getMealFoods().fold(0.0, (sum, foodData) {
      final food = _getFoodFromData(foodData);
      final quantity = _getQuantityFromData(foodData);
      return sum + (food.fiber * quantity / 100);
    });
  }

  double _calculateTotalSodium() {
    return _getMealFoods().fold(0.0, (sum, foodData) {
      final food = _getFoodFromData(foodData);
      final quantity = _getQuantityFromData(foodData);
      return sum + ((food.sodium ?? 0) * quantity / 100);
    });
  }

  IconData _getMealIcon() {
    final type = _getMealType();
    if (type == null) {
      // Per il modello Meal, proviamo a dedurre dal nome
      final name = _getMealName().toLowerCase();
      if (name.contains('colazione') || name.contains('breakfast')) {
        return Icons.free_breakfast;
      } else if (name.contains('pranzo') || name.contains('lunch')) {
        return Icons.lunch_dining;
      } else if (name.contains('cena') || name.contains('dinner')) {
        return Icons.dinner_dining;
      } else if (name.contains('spuntino') || name.contains('snack') || name.contains('merenda')) {
        return Icons.cookie;
      } else {
        return Icons.restaurant;
      }
    }

    switch (type) {
      case MealType.breakfast:
        return Icons.free_breakfast;
      case MealType.lunch:
        return Icons.lunch_dining;
      case MealType.dinner:
        return Icons.dinner_dining;
      case MealType.snack:
        return Icons.cookie;
      default:
        return Icons.restaurant;
    }
  }

  // Metodi helper per gestire sia PlannedMeal che Meal
  String _getMealName() {
    if (widget.meal is PlannedMeal) {
      return (widget.meal as PlannedMeal).name;
    } else if (widget.meal is Meal) {
      return (widget.meal as Meal).nome;
    }
    return 'Pasto';
  }

  String _getMealTime() {
    if (widget.meal is PlannedMeal) {
      return (widget.meal as PlannedMeal).time;
    } else if (widget.meal is Meal) {
      return (widget.meal as Meal).orario;
    }
    return '';
  }

  MealType? _getMealType() {
    if (widget.meal is PlannedMeal) {
      return (widget.meal as PlannedMeal).type;
    } else if (widget.meal is Meal) {
      // Il modello Meal non ha type, quindi restituiamo null e gestiamo nel _getMealIcon
      return null;
    }
    return MealType.lunch;
  }

  List<dynamic> _getMealFoods() {
    if (widget.meal is PlannedMeal) {
      return (widget.meal as PlannedMeal).foods;
    } else if (widget.meal is Meal) {
      return (widget.meal as Meal).foods;
    }
    return [];
  }

  Food _getFoodFromData(dynamic foodData) {
    if (foodData is FoodPortion) {
      return foodData.food;
    } else if (foodData is FoodItem) {
      return foodData.food;
    }
    throw Exception('Tipo di dato alimento non supportato');
  }

  double _getQuantityFromData(dynamic foodData) {
    if (foodData is FoodPortion) {
      return foodData.grams.toDouble();
    } else if (foodData is FoodItem) {
      return foodData.quantity;
    }
    return 0.0;
  }

  int _getCaloriesFromData(dynamic foodData) {
    if (foodData is FoodPortion) {
      return foodData.calories;
    } else if (foodData is FoodItem) {
      final ratio = foodData.quantity / 100;
      return (foodData.food.calories * ratio).round();
    }
    return 0;
  }

  double _getProteinsFromData(dynamic foodData) {
    if (foodData is FoodPortion) {
      return foodData.proteins;
    } else if (foodData is FoodItem) {
      final ratio = foodData.quantity / 100;
      return foodData.food.proteins * ratio;
    }
    return 0.0;
  }

  double _getCarbsFromData(dynamic foodData) {
    if (foodData is FoodPortion) {
      return foodData.carbs;
    } else if (foodData is FoodItem) {
      final ratio = foodData.quantity / 100;
      return foodData.food.carbs * ratio;
    }
    return 0.0;
  }

  double _getFatsFromData(dynamic foodData) {
    if (foodData is FoodPortion) {
      return foodData.fats;
    } else if (foodData is FoodItem) {
      final ratio = foodData.quantity / 100;
      return foodData.food.fats * ratio;
    }
    return 0.0;
  }
}
