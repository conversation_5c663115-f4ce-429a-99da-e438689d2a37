import '../models/food.dart';
import '../utils/micronutrients_helper.dart';

/// Database di alimenti specifici per la generazione della dieta
/// Contiene solo gli alimenti forniti dall'utente con valori nutrizionali esatti
class SpecificDietFoods {
  static List<Food> getAllFoods() {
    return [
      ...getFruits(),
      ...getVegetables(),
      ...getGrains(),
      ...getProteins(),
      ...getDairy(),
      ...getFats(),
      ...getSweets(),
      ...getBeverages(),
      ...getBreakfastItems(),
    ];
  }

  static List<Food> getFruits() {
    return [
      Food(
        id: 'specific_fruit_1',
        name: '<PERSON><PERSON>',
        description: 'Mela Fuji cruda con buccia',
        imageUrl: 'https://images.unsplash.com/photo-1570913149827-d2ac84ab3f9a?q=80&w=500',
        calories: 65,
        proteins: 0.15,
        carbs: 15.65,
        fats: 0.16,
        fiber: 2.08,
        sugar: 13.33,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'fresco', 'mela', 'stagionale'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 5.0,
          phosphorus: 11.0,
          magnesium: 5.0,
          potassium: 107.0,
          vitaminC: 4.6,
        ),
      ),
      Food(
        id: 'specific_fruit_2',
        name: 'Frutti di bosco misti',
        description: 'Mix di mirtilli, more e lamponi freschi',
        imageUrl: 'https://images.unsplash.com/photo-1498557850523-fd3d118b962e?q=80&w=500',
        calories: 57,
        proteins: 0.7,
        carbs: 14.0,
        fats: 0.3,
        fiber: 6.5,
        sugar: 10.0,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'fresco', 'stagionale', 'antiossidante', 'bacche'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 25.0,
          phosphorus: 22.0,
          magnesium: 13.0,
          potassium: 162.0,
          vitaminC: 30.0,
        ),
      ),
      Food(
        id: 'specific_fruit_3',
        name: 'Banana',
        description: 'Banana matura',
        imageUrl: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?q=80&w=500',
        calories: 89,
        proteins: 1.1,
        carbs: 22.8,
        fats: 0.3,
        fiber: 2.6,
        sugar: 12.2,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '1 banana media (100g)',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'energetica', 'potassio'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 5.0,
          phosphorus: 22.0,
          magnesium: 27.0,
          potassium: 358.0,
          vitaminC: 8.7,
          vitaminB6: 0.4,
        ),
      ),
      Food(
        id: 'specific_fruit_4',
        name: 'Arancia',
        description: 'Arancia fresca',
        imageUrl: 'https://images.unsplash.com/photo-1547036967-23d11aacaee0?q=80&w=500',
        calories: 47,
        proteins: 0.9,
        carbs: 11.8,
        fats: 0.1,
        fiber: 2.4,
        sugar: 9.4,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '1 arancia media (100g)',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'vitamina C', 'agrumi', 'stagionale'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 40.0,
          phosphorus: 14.0,
          magnesium: 10.0,
          potassium: 181.0,
          vitaminC: 53.2,
        ),
      ),
      Food(
        id: 'specific_fruit_5',
        name: 'Pera',
        description: 'Pera matura con buccia',
        imageUrl: 'https://images.unsplash.com/photo-1568702846914-96b305d2aaeb?q=80&w=500',
        calories: 57,
        proteins: 0.4,
        carbs: 15.2,
        fats: 0.1,
        fiber: 3.1,
        sugar: 9.8,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '1 pera media (100g)',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'fibre', 'stagionale'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 9.0,
          phosphorus: 12.0,
          magnesium: 7.0,
          potassium: 116.0,
          vitaminC: 4.3,
        ),
      ),
      Food(
        id: 'specific_fruit_6',
        name: 'Kiwi',
        description: 'Kiwi maturo',
        imageUrl: 'https://images.unsplash.com/photo-1585059895524-72359e06133a?q=80&w=500',
        calories: 61,
        proteins: 1.1,
        carbs: 14.7,
        fats: 0.5,
        fiber: 3.0,
        sugar: 9.0,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '1 kiwi medio (100g)',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'vitamina C', 'esotica'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 34.0,
          phosphorus: 34.0,
          magnesium: 17.0,
          potassium: 312.0,
          vitaminC: 92.7,
          vitaminE: 1.5,
        ),
      ),
      Food(
        id: 'specific_fruit_7',
        name: 'Fragole',
        description: 'Fragole fresche',
        imageUrl: 'https://images.unsplash.com/photo-1464965911861-746a04b4bca6?q=80&w=500',
        calories: 32,
        proteins: 0.7,
        carbs: 7.7,
        fats: 0.3,
        fiber: 2.0,
        sugar: 4.9,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'fresco', 'stagionale', 'antiossidante'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 16.0,
          phosphorus: 24.0,
          magnesium: 13.0,
          potassium: 153.0,
          vitaminC: 58.8,
          vitaminB9: 24.0,
        ),
      ),
      Food(
        id: 'specific_fruit_8',
        name: 'Pesche',
        description: 'Pesche mature',
        imageUrl: 'https://images.unsplash.com/photo-1629828874514-d8e6d0ca4f8c?q=80&w=500',
        calories: 39,
        proteins: 0.9,
        carbs: 9.5,
        fats: 0.3,
        fiber: 1.5,
        sugar: 8.4,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '1 pesca media (100g)',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'estiva', 'stagionale', 'dolce'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 6.0,
          phosphorus: 20.0,
          magnesium: 9.0,
          potassium: 190.0,
          vitaminC: 6.6,
          vitaminA: 16.0,
        ),
      ),
      Food(
        id: 'specific_fruit_9',
        name: 'Uva',
        description: 'Uva da tavola',
        imageUrl: 'https://images.unsplash.com/photo-1537640538966-79f369143f8f?q=80&w=500',
        calories: 69,
        proteins: 0.7,
        carbs: 18.1,
        fats: 0.2,
        fiber: 0.9,
        sugar: 16.3,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'autunnale', 'stagionale', 'energetica'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 10.0,
          phosphorus: 20.0,
          magnesium: 7.0,
          potassium: 191.0,
          vitaminC: 10.8,
        ),
      ),
      Food(
        id: 'specific_fruit_10',
        name: 'Ananas',
        description: 'Ananas fresco',
        imageUrl: 'https://images.unsplash.com/photo-1550258987-190a2d41a8ba?q=80&w=500',
        calories: 50,
        proteins: 0.5,
        carbs: 13.1,
        fats: 0.1,
        fiber: 1.4,
        sugar: 9.9,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'esotica', 'digestiva', 'enzimi'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 13.0,
          phosphorus: 8.0,
          magnesium: 12.0,
          potassium: 109.0,
          vitaminC: 47.8,
          vitaminB1: 0.08,
        ),
      ),
    ];
  }

  static List<Food> getVegetables() {
    return [
      Food(
        id: 'specific_veg_1_safe',
        name: 'Spinaci saltati',
        description: 'Spinaci freschi saltati in padella con aglio e olio',
        imageUrl: 'https://images.unsplash.com/photo-1576064535185-c2526884001c?q=80&w=500',
        calories: 23,
        proteins: 2.9,
        carbs: 3.6,
        fats: 0.4,
        fiber: 2.2,
        sugar: 0.4,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotti',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 0.5,
        isSeasonal: true,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'saltati', 'sicuro', 'foglia verde', 'stagionale'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 99.0,
          phosphorus: 49.0,
          magnesium: 79.0,
          sodium: 79.0,
          potassium: 558.0,
          iron: 2.7,
          vitaminA: 469.0,
          vitaminC: 28.0,
        ),
      ),
      Food(
        id: 'specific_veg_2_safe',
        name: 'Broccoli al vapore',
        description: 'Broccoli cotti al vapore',
        imageUrl: 'https://images.unsplash.com/photo-1459411621453-7b03977f4bfc?q=80&w=500',
        calories: 34,
        proteins: 2.8,
        carbs: 7.0,
        fats: 0.4,
        fiber: 2.6,
        sugar: 1.5,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotti',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 0.7,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'vapore', 'sicuro', 'crucifere'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 47.0,
          phosphorus: 66.0,
          magnesium: 21.0,
          potassium: 316.0,
          vitaminC: 89.2,
          vitaminK: 101.6,
        ),
      ),
      Food(
        id: 'specific_veg_3_safe',
        name: 'Zucchine grigliate',
        description: 'Zucchine grigliate con olio e erbe',
        imageUrl: 'https://images.unsplash.com/photo-1566385101042-1a0aa0c1268c?q=80&w=500',
        calories: 20,
        proteins: 1.2,
        carbs: 4.1,
        fats: 0.3,
        fiber: 1.0,
        sugar: 2.5,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotte',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 0.8,
        isSeasonal: true,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'grigliate', 'sicuro', 'estive', 'stagionale'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 16.0,
          phosphorus: 38.0,
          magnesium: 18.0,
          potassium: 261.0,
          vitaminC: 17.9,
        ),
      ),
      Food(
        id: 'specific_veg_4_safe',
        name: 'Carote cotte',
        description: 'Carote lessate',
        imageUrl: 'https://images.unsplash.com/photo-1598170845058-32b9d6a5da37?q=80&w=500',
        calories: 35,
        proteins: 0.8,
        carbs: 8.2,
        fats: 0.2,
        fiber: 2.8,
        sugar: 3.4,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotte',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 0.9,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'lessate', 'sicuro', 'dolci'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 30.0,
          phosphorus: 30.0,
          magnesium: 10.0,
          potassium: 235.0,
          vitaminA: 852.0,
          vitaminC: 3.6,
        ),
      ),
      Food(
        id: 'specific_veg_5_safe',
        name: 'Peperoni arrostiti',
        description: 'Peperoni rossi e gialli arrostiti',
        imageUrl: 'https://images.unsplash.com/photo-1563565375-f3fdfdbefa83?q=80&w=500',
        calories: 31,
        proteins: 1.0,
        carbs: 7.3,
        fats: 0.3,
        fiber: 2.5,
        sugar: 4.2,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotti',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 0.8,
        isSeasonal: true,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'arrostiti', 'sicuro', 'colorati', 'stagionale'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 7.0,
          phosphorus: 26.0,
          magnesium: 12.0,
          potassium: 211.0,
          vitaminC: 127.7,
          vitaminA: 157.0,
        ),
      ),
      Food(
        id: 'specific_veg_6_safe',
        name: 'Melanzane grigliate',
        description: 'Melanzane grigliate con olio e erbe',
        imageUrl: 'https://images.unsplash.com/photo-1564737007-0e8e8b0c8b0c?q=80&w=500',
        calories: 25,
        proteins: 1.0,
        carbs: 6.0,
        fats: 0.2,
        fiber: 3.0,
        sugar: 3.5,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotte',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 0.8,
        isSeasonal: true,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'grigliate', 'sicuro', 'mediterranee', 'stagionale'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 9.0,
          phosphorus: 24.0,
          magnesium: 14.0,
          potassium: 229.0,
          vitaminC: 2.2,
        ),
      ),
      Food(
        id: 'specific_veg_7_safe',
        name: 'Asparagi al vapore',
        description: 'Asparagi cotti al vapore',
        imageUrl: 'https://images.unsplash.com/photo-1551754655-cd27e38d2076?q=80&w=500',
        calories: 20,
        proteins: 2.2,
        carbs: 3.9,
        fats: 0.1,
        fiber: 2.1,
        sugar: 1.9,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotti',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 0.9,
        isSeasonal: true,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'vapore', 'sicuro', 'primaverili', 'stagionale'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 24.0,
          phosphorus: 52.0,
          magnesium: 14.0,
          potassium: 202.0,
          vitaminC: 5.6,
          vitaminK: 41.6,
        ),
      ),
      Food(
        id: 'specific_veg_8_safe',
        name: 'Cavolfiore al vapore',
        description: 'Cavolfiore cotto al vapore',
        imageUrl: 'https://images.unsplash.com/photo-1568584711271-946d4d2e4b8a?q=80&w=500',
        calories: 25,
        proteins: 1.9,
        carbs: 5.0,
        fats: 0.3,
        fiber: 2.0,
        sugar: 1.9,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotti',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 0.8,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'vapore', 'sicuro', 'crucifere'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 22.0,
          phosphorus: 44.0,
          magnesium: 15.0,
          potassium: 299.0,
          vitaminC: 48.2,
          vitaminK: 15.5,
        ),
      ),
      Food(
        id: 'specific_veg_9_safe',
        name: 'Fagiolini lessati',
        description: 'Fagiolini verdi lessati',
        imageUrl: 'https://images.unsplash.com/photo-1506084868230-bb9d95c24759?q=80&w=500',
        calories: 35,
        proteins: 1.8,
        carbs: 7.9,
        fats: 0.2,
        fiber: 2.7,
        sugar: 3.3,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotti',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 0.9,
        isSeasonal: true,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'lessati', 'sicuro', 'estivi', 'stagionale'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 37.0,
          phosphorus: 38.0,
          magnesium: 25.0,
          potassium: 211.0,
          vitaminC: 12.2,
          vitaminA: 35.0,
        ),
      ),
      Food(
        id: 'specific_veg_10_safe',
        name: 'Pomodori cotti',
        description: 'Pomodori pelati cotti',
        imageUrl: 'https://images.unsplash.com/photo-1546470427-e5ac89c8ba37?q=80&w=500',
        calories: 18,
        proteins: 0.9,
        carbs: 3.9,
        fats: 0.2,
        fiber: 1.2,
        sugar: 2.6,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotti',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 0.9,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'cotti', 'sicuro', 'pomodori'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 10.0,
          phosphorus: 24.0,
          magnesium: 11.0,
          potassium: 237.0,
          vitaminC: 13.7,
          vitaminA: 42.0,
        ),
      ),
      Food(
        id: 'specific_veg_11_safe',
        name: 'Cavolo cappuccio saltato',
        description: 'Cavolo cappuccio saltato in padella',
        imageUrl: 'https://images.unsplash.com/photo-1594282486552-05b4d80fbb9f?q=80&w=500',
        calories: 25,
        proteins: 1.3,
        carbs: 5.8,
        fats: 0.1,
        fiber: 2.5,
        sugar: 3.2,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotti',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 0.7,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'saltati', 'sicuro', 'crucifere'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 40.0,
          phosphorus: 26.0,
          magnesium: 12.0,
          potassium: 170.0,
          vitaminC: 36.6,
          vitaminK: 76.0,
        ),
      ),
      Food(
        id: 'specific_veg_12_safe',
        name: 'Bietole cotte',
        description: 'Bietole lessate',
        imageUrl: 'https://images.unsplash.com/photo-1576064535185-c2526884001c?q=80&w=500',
        calories: 19,
        proteins: 1.8,
        carbs: 3.7,
        fats: 0.2,
        fiber: 1.6,
        sugar: 1.1,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotte',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 0.6,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'lessate', 'sicuro', 'foglia verde'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 51.0,
          phosphorus: 46.0,
          magnesium: 81.0,
          potassium: 379.0,
          iron: 1.8,
          vitaminA: 214.0,
        ),
      ),
      Food(
        id: 'specific_veg_13_safe',
        name: 'Finocchi brasati',
        description: 'Finocchi brasati in padella',
        imageUrl: 'https://images.unsplash.com/photo-1570197788417-0e82375c9371?q=80&w=500',
        calories: 31,
        proteins: 1.2,
        carbs: 7.3,
        fats: 0.2,
        fiber: 3.1,
        sugar: 3.9,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotti',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 0.8,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'brasati', 'sicuro', 'digestivi'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 49.0,
          phosphorus: 50.0,
          magnesium: 15.0,
          potassium: 414.0,
          vitaminC: 12.0,
        ),
      ),
    ];
  }

  static List<Food> getGrains() {
    return [
      Food(
        id: 'specific_grain_1_safe',
        name: 'Riso Basmati cotto',
        description: 'Riso Basmati bianco cotto',
        imageUrl: 'https://images.unsplash.com/photo-1586201375761-83865001e8ac?q=80&w=500',
        calories: 130,
        proteins: 2.7,
        carbs: 28.0,
        fats: 0.3,
        fiber: 0.4,
        sugar: 0.1,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        glycemicIndex: 58,
        glycemicLoad: 16,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['cereale', 'base', 'carboidrato', 'sicuro'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          magnesium: 12.0,
          phosphorus: 43.0,
          potassium: 35.0,
          zinc: 0.5,
        ),
      ),
      Food(
        id: 'specific_grain_2_safe',
        name: 'Pasta integrale cotta',
        description: 'Pasta di grano integrale cotta',
        imageUrl: 'https://images.unsplash.com/photo-1551892374-ecf8754cf8b0?q=80&w=500',
        calories: 124,
        proteins: 5.0,
        carbs: 25.0,
        fats: 1.1,
        fiber: 3.9,
        sugar: 0.8,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        isDairyFree: true,
        allergens: const ['glutine'],
        servingSize: '100g cotta',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        glycemicIndex: 42,
        glycemicLoad: 11,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['cereale', 'integrale', 'pasta', 'sicuro'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 15.0,
          phosphorus: 89.0,
          magnesium: 42.0,
          potassium: 112.0,
          iron: 1.3,
        ),
      ),
      Food(
        id: 'specific_grain_3_safe',
        name: 'Quinoa cotta',
        description: 'Quinoa cotta',
        imageUrl: 'https://images.unsplash.com/photo-1586444248902-2f64eddc13df?q=80&w=500',
        calories: 120,
        proteins: 4.4,
        carbs: 22.0,
        fats: 1.9,
        fiber: 2.8,
        sugar: 0.9,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotta',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        glycemicIndex: 53,
        glycemicLoad: 13,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['cereale', 'quinoa', 'proteico', 'sicuro'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 17.0,
          phosphorus: 152.0,
          magnesium: 64.0,
          potassium: 172.0,
          iron: 1.5,
        ),
      ),
      Food(
        id: 'specific_grain_4_safe',
        name: 'Riso integrale cotto',
        description: 'Riso integrale cotto',
        imageUrl: 'https://images.unsplash.com/photo-1586201375761-83865001e8ac?q=80&w=500',
        calories: 112,
        proteins: 2.6,
        carbs: 22.0,
        fats: 0.9,
        fiber: 1.8,
        sugar: 0.4,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        glycemicIndex: 50,
        glycemicLoad: 16,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['cereale', 'integrale', 'riso', 'sicuro'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 10.0,
          phosphorus: 83.0,
          magnesium: 44.0,
          potassium: 86.0,
          iron: 0.6,
        ),
      ),
      Food(
        id: 'specific_grain_5_safe',
        name: 'Orzo perlato cotto',
        description: 'Orzo perlato cotto',
        imageUrl: 'https://images.unsplash.com/photo-1586444248902-2f64eddc13df?q=80&w=500',
        calories: 123,
        proteins: 2.3,
        carbs: 28.0,
        fats: 0.4,
        fiber: 3.8,
        sugar: 0.8,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        isDairyFree: true,
        allergens: const ['glutine'],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        glycemicIndex: 35,
        glycemicLoad: 11,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['cereale', 'orzo', 'integrale', 'sicuro'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 11.0,
          phosphorus: 54.0,
          magnesium: 22.0,
          potassium: 93.0,
          iron: 1.3,
        ),
      ),
      Food(
        id: 'specific_grain_6_safe',
        name: 'Farro cotto',
        description: 'Farro decorticato cotto',
        imageUrl: 'https://images.unsplash.com/photo-1586444248902-2f64eddc13df?q=80&w=500',
        calories: 114,
        proteins: 4.2,
        carbs: 26.0,
        fats: 0.9,
        fiber: 3.8,
        sugar: 1.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        isDairyFree: true,
        allergens: const ['glutine'],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        glycemicIndex: 40,
        glycemicLoad: 12,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['cereale', 'farro', 'antico', 'sicuro'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 19.0,
          phosphorus: 119.0,
          magnesium: 67.0,
          potassium: 145.0,
          iron: 2.5,
        ),
      ),
      Food(
        id: 'specific_grain_7_safe',
        name: 'Avena cotta',
        description: 'Fiocchi d\'avena cotti',
        imageUrl: 'https://images.unsplash.com/photo-1517673132405-a56a62b18caf?q=80&w=500',
        calories: 68,
        proteins: 2.4,
        carbs: 12.0,
        fats: 1.4,
        fiber: 1.7,
        sugar: 0.3,
        suitableForMeals: const [MealType.breakfast],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotta',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        glycemicIndex: 55,
        glycemicLoad: 6,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['cereale', 'avena', 'colazione', 'sicuro'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 9.0,
          phosphorus: 77.0,
          magnesium: 18.0,
          potassium: 70.0,
          iron: 1.2,
        ),
      ),
      Food(
        id: 'specific_grain_8_safe',
        name: 'Grano saraceno cotto',
        description: 'Grano saraceno cotto',
        imageUrl: 'https://images.unsplash.com/photo-1586444248902-2f64eddc13df?q=80&w=500',
        calories: 92,
        proteins: 3.4,
        carbs: 20.0,
        fats: 0.6,
        fiber: 2.7,
        sugar: 0.9,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        glycemicIndex: 45,
        glycemicLoad: 9,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['cereale', 'grano saraceno', 'senza glutine', 'sicuro'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 7.0,
          phosphorus: 70.0,
          magnesium: 51.0,
          potassium: 88.0,
          iron: 0.8,
        ),
      ),
      Food(
        id: 'specific_grain_9_safe',
        name: 'Miglio cotto',
        description: 'Miglio decorticato cotto',
        imageUrl: 'https://images.unsplash.com/photo-1586444248902-2f64eddc13df?q=80&w=500',
        calories: 119,
        proteins: 3.5,
        carbs: 23.0,
        fats: 1.0,
        fiber: 1.3,
        sugar: 0.1,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        glycemicIndex: 71,
        glycemicLoad: 16,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['cereale', 'miglio', 'antico', 'sicuro'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 3.0,
          phosphorus: 100.0,
          magnesium: 44.0,
          potassium: 62.0,
          iron: 0.6,
        ),
      ),
      Food(
        id: 'specific_grain_10_safe',
        name: 'Polenta cotta',
        description: 'Polenta di mais cotta',
        imageUrl: 'https://images.unsplash.com/photo-1586444248902-2f64eddc13df?q=80&w=500',
        calories: 85,
        proteins: 1.7,
        carbs: 18.7,
        fats: 0.3,
        fiber: 1.2,
        sugar: 0.3,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotta',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        glycemicIndex: 68,
        glycemicLoad: 13,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['cereale', 'polenta', 'mais', 'sicuro'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 1.0,
          phosphorus: 13.0,
          magnesium: 6.0,
          potassium: 25.0,
          iron: 0.2,
        ),
      ),
    ];
  }

  static List<Food> getProteins() {
    return [
      Food(
        id: 'specific_protein_1_safe',
        name: 'Petto di pollo alla griglia',
        description: 'Petto di pollo grigliato, senza pelle, cotto',
        imageUrl: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?q=80&w=500',
        calories: 165,
        proteins: 31.0,
        carbs: 0.0,
        fats: 3.6,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        glycemicIndex: 0,
        glycemicLoad: 0,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['carne', 'pollo', 'griglia', 'sicuro', 'proteico'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          phosphorus: 210.0,
          potassium: 320.0,
          sodium: 70.0,
          vitaminB3: 10.7,
          vitaminB6: 0.5,
        ),
      ),
      Food(
        id: 'specific_protein_2_safe',
        name: 'Salmone alla griglia',
        description: 'Filetto di salmone grigliato',
        imageUrl: 'https://images.unsplash.com/photo-1467003909585-2f8a72700288?q=80&w=500',
        calories: 206,
        proteins: 22.0,
        carbs: 0.0,
        fats: 12.4,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['pesce'],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        glycemicIndex: 0,
        glycemicLoad: 0,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['pesce', 'salmone', 'griglia', 'sicuro', 'omega3'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          phosphorus: 252.0,
          potassium: 363.0,
          sodium: 59.0,
          vitaminD: 11.0,
          vitaminB12: 3.2,
        ),
      ),
      Food(
        id: 'specific_protein_3_safe',
        name: 'Tonno in scatola',
        description: 'Tonno in scatola al naturale, sgocciolato',
        imageUrl: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?q=80&w=500',
        calories: 116,
        proteins: 25.5,
        carbs: 0.0,
        fats: 0.8,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['pesce'],
        servingSize: '100g sgocciolato',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        glycemicIndex: 0,
        glycemicLoad: 0,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['pesce', 'tonno', 'conserva', 'sicuro', 'proteico'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          phosphorus: 200.0,
          potassium: 237.0,
          sodium: 320.0,
          vitaminB3: 8.5,
          vitaminB12: 2.5,
        ),
      ),
      Food(
        id: 'specific_protein_4_safe',
        name: 'Tacchino arrosto',
        description: 'Petto di tacchino arrosto, senza pelle',
        imageUrl: 'https://images.unsplash.com/photo-1574672280600-4accfa5b6f98?q=80&w=500',
        calories: 135,
        proteins: 30.0,
        carbs: 0.0,
        fats: 1.0,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        glycemicIndex: 0,
        glycemicLoad: 0,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['carne', 'tacchino', 'arrosto', 'sicuro', 'magro'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          phosphorus: 230.0,
          potassium: 298.0,
          sodium: 63.0,
          vitaminB3: 6.3,
          vitaminB6: 0.5,
        ),
      ),
      Food(
        id: 'specific_protein_5_safe',
        name: 'Lenticchie cotte',
        description: 'Lenticchie lessate',
        imageUrl: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?q=80&w=500',
        calories: 116,
        proteins: 9.0,
        carbs: 20.0,
        fats: 0.4,
        fiber: 7.9,
        sugar: 1.8,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotte',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        glycemicIndex: 29,
        glycemicLoad: 5,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['legumi', 'lenticchie', 'vegetale', 'sicuro', 'fibre'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 19.0,
          phosphorus: 180.0,
          magnesium: 36.0,
          potassium: 369.0,
          iron: 3.3,
          vitaminB9: 181.0,
        ),
      ),
      Food(
        id: 'specific_protein_6_safe',
        name: 'Ceci cotti',
        description: 'Ceci lessati',
        imageUrl: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?q=80&w=500',
        calories: 164,
        proteins: 8.9,
        carbs: 27.4,
        fats: 2.6,
        fiber: 7.6,
        sugar: 4.8,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotti',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        glycemicIndex: 33,
        glycemicLoad: 8,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['legumi', 'ceci', 'vegetale', 'sicuro', 'fibre'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 49.0,
          phosphorus: 168.0,
          magnesium: 48.0,
          potassium: 291.0,
          iron: 2.9,
          vitaminB9: 172.0,
        ),
      ),
      Food(
        id: 'specific_protein_7_safe',
        name: 'Fagioli cannellini cotti',
        description: 'Fagioli cannellini lessati',
        imageUrl: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?q=80&w=500',
        calories: 127,
        proteins: 8.2,
        carbs: 22.5,
        fats: 0.5,
        fiber: 6.2,
        sugar: 0.3,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotti',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        glycemicIndex: 31,
        glycemicLoad: 7,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['legumi', 'fagioli', 'vegetale', 'sicuro', 'fibre'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 90.0,
          phosphorus: 140.0,
          magnesium: 45.0,
          potassium: 561.0,
          iron: 3.7,
          vitaminB9: 130.0,
        ),
      ),
      Food(
        id: 'specific_protein_8_safe',
        name: 'Merluzzo al vapore',
        description: 'Filetto di merluzzo cotto al vapore',
        imageUrl: 'https://images.unsplash.com/photo-1467003909585-2f8a72700288?q=80&w=500',
        calories: 82,
        proteins: 17.8,
        carbs: 0.0,
        fats: 0.7,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['pesce'],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        glycemicIndex: 0,
        glycemicLoad: 0,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['pesce', 'merluzzo', 'vapore', 'sicuro', 'magro'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          phosphorus: 203.0,
          potassium: 413.0,
          sodium: 54.0,
          vitaminB3: 2.1,
          vitaminB12: 1.1,
        ),
      ),
      Food(
        id: 'specific_protein_9_safe',
        name: 'Bresaola',
        description: 'Bresaola della Valtellina',
        imageUrl: 'https://images.unsplash.com/photo-1574672280600-4accfa5b6f98?q=80&w=500',
        calories: 151,
        proteins: 32.0,
        carbs: 0.6,
        fats: 2.6,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner, MealType.snack],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        glycemicIndex: 0,
        glycemicLoad: 0,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['carne', 'bresaola', 'stagionata', 'sicuro', 'proteico'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          phosphorus: 280.0,
          potassium: 505.0,
          sodium: 1200.0,
          iron: 2.3,
          vitaminB12: 1.5,
        ),
      ),
      Food(
        id: 'specific_protein_10_safe',
        name: 'Uova sode',
        description: 'Uova di gallina sode',
        imageUrl: 'https://images.unsplash.com/photo-1582722872445-44dc5f7e3c8f?q=80&w=500',
        calories: 155,
        proteins: 13.0,
        carbs: 1.1,
        fats: 11.0,
        fiber: 0.0,
        sugar: 1.1,
        suitableForMeals: const [MealType.breakfast, MealType.lunch, MealType.snack],
        categories: const [FoodCategory.protein],
        isVegetarian: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['uova'],
        servingSize: '100g (circa 2 uova)',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        glycemicIndex: 0,
        glycemicLoad: 0,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['uova', 'sode', 'sicuro', 'proteico'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 56.0,
          phosphorus: 198.0,
          magnesium: 12.0,
          potassium: 138.0,
          iron: 1.8,
          vitaminA: 160.0,
          vitaminB12: 0.9,
        ),
      ),
      Food(
        id: 'specific_protein_11_safe',
        name: 'Tofu grigliato',
        description: 'Tofu grigliato',
        imageUrl: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?q=80&w=500',
        calories: 76,
        proteins: 8.1,
        carbs: 1.9,
        fats: 4.8,
        fiber: 0.3,
        sugar: 0.6,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['soia'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        glycemicIndex: 15,
        glycemicLoad: 0,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['tofu', 'grigliato', 'sicuro', 'vegetale'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 350.0,
          phosphorus: 97.0,
          magnesium: 30.0,
          potassium: 121.0,
          iron: 5.4,
        ),
      ),
      Food(
        id: 'specific_protein_12_safe',
        name: 'Gamberetti cotti',
        description: 'Gamberetti lessati',
        imageUrl: 'https://images.unsplash.com/photo-1565680018434-b513d5e5fd47?q=80&w=500',
        calories: 99,
        proteins: 18.0,
        carbs: 0.9,
        fats: 1.7,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['crostacei'],
        servingSize: '100g cotti',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        glycemicIndex: 0,
        glycemicLoad: 0,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['crostacei', 'gamberetti', 'sicuro', 'magri'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 70.0,
          phosphorus: 237.0,
          magnesium: 39.0,
          potassium: 259.0,
          sodium: 111.0,
          vitaminB12: 1.3,
        ),
      ),
      Food(
        id: 'specific_protein_13_safe',
        name: 'Prosciutto cotto',
        description: 'Prosciutto cotto magro',
        imageUrl: 'https://images.unsplash.com/photo-1574672280600-4accfa5b6f98?q=80&w=500',
        calories: 145,
        proteins: 19.8,
        carbs: 0.9,
        fats: 6.6,
        fiber: 0.0,
        sugar: 0.9,
        suitableForMeals: const [MealType.breakfast, MealType.lunch, MealType.snack],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        glycemicIndex: 0,
        glycemicLoad: 0,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['carne', 'prosciutto', 'cotto', 'sicuro'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          phosphorus: 239.0,
          potassium: 287.0,
          sodium: 758.0,
          iron: 0.7,
          vitaminB1: 0.6,
        ),
      ),
    ];
  }

  static List<Food> getDairy() {
    return [
      Food(
        id: 'specific_dairy_1',
        name: 'Latte parzialmente scremato',
        description: 'Latte vaccino parzialmente scremato (1,5% grassi)',
        imageUrl: 'https://images.unsplash.com/photo-1550583724-b2692b85b150?q=80&w=500',
        calories: 46,
        proteins: 3.3,
        carbs: 4.8,
        fats: 1.5,
        fiber: 0.0,
        sugar: 4.8,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.dairy, FoodCategory.beverage],
        isVegetarian: true,
        allergens: const ['latticini'],
        servingSize: '100ml',
        servingSizeGrams: 103,
        foodState: FoodState.prepared,
        volumeToWeightFactor: 1.03,
        glycemicIndex: 30,
        glycemicLoad: 1,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['latte', 'base', 'colazione'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 120.0,
          phosphorus: 92.0,
          potassium: 150.0,
          sodium: 50.0,
          vitaminB2: 0.18,
          vitaminB12: 0.4,
        ),
      ),
      Food(
        id: 'specific_dairy_2',
        name: 'Yogurt greco',
        description: 'Yogurt greco intero',
        imageUrl: 'https://images.unsplash.com/photo-1488477181946-6428a0291777?q=80&w=500',
        calories: 97,
        proteins: 9.0,
        carbs: 3.6,
        fats: 5.0,
        fiber: 0.0,
        sugar: 3.6,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.dairy],
        isVegetarian: true,
        isGlutenFree: true,
        allergens: const ['latticini'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['yogurt', 'colazione', 'proteico'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 115.0,
          phosphorus: 135.0,
          potassium: 141.0,
          sodium: 36.0,
        ),
      ),
      Food(
        id: 'specific_dairy_3',
        name: 'Ricotta fresca',
        description: 'Ricotta di mucca fresca',
        imageUrl: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?q=80&w=500',
        calories: 174,
        proteins: 11.0,
        carbs: 3.0,
        fats: 13.0,
        fiber: 0.0,
        sugar: 0.3,
        suitableForMeals: const [MealType.breakfast, MealType.lunch, MealType.snack],
        categories: const [FoodCategory.dairy],
        isVegetarian: true,
        isGlutenFree: true,
        allergens: const ['latticini'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['ricotta', 'fresca', 'proteico'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 207.0,
          phosphorus: 158.0,
          potassium: 105.0,
          sodium: 84.0,
          vitaminA: 198.0,
        ),
      ),
      Food(
        id: 'specific_dairy_4',
        name: 'Mozzarella',
        description: 'Mozzarella di mucca',
        imageUrl: 'https://images.unsplash.com/photo-1571068316344-75bc76f77890?q=80&w=500',
        calories: 280,
        proteins: 18.0,
        carbs: 2.2,
        fats: 22.0,
        fiber: 0.0,
        sugar: 1.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner], // Rimossa da snack - troppo pesante
        categories: const [FoodCategory.dairy],
        isVegetarian: true,
        isGlutenFree: true,
        allergens: const ['latticini'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['mozzarella', 'formaggio', 'proteico'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 505.0,
          phosphorus: 354.0,
          potassium: 76.0,
          sodium: 627.0,
          vitaminA: 179.0,
        ),
      ),
      Food(
        id: 'specific_dairy_5',
        name: 'Parmigiano Reggiano',
        description: 'Parmigiano Reggiano stagionato',
        imageUrl: 'https://images.unsplash.com/photo-1571068316344-75bc76f77890?q=80&w=500',
        calories: 392,
        proteins: 33.0,
        carbs: 0.0,
        fats: 28.0,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner, MealType.snack],
        categories: const [FoodCategory.dairy],
        isVegetarian: true,
        isGlutenFree: true,
        allergens: const ['latticini'],
        servingSize: '30g (1 porzione)',
        servingSizeGrams: 30,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['formaggio', 'stagionato', 'proteico'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 1159.0,
          phosphorus: 692.0,
          potassium: 102.0,
          sodium: 1529.0,
          vitaminA: 270.0,
        ),
      ),
      Food(
        id: 'specific_dairy_6',
        name: 'Fiocchi di latte',
        description: 'Fiocchi di latte magri',
        imageUrl: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?q=80&w=500',
        calories: 98,
        proteins: 11.0,
        carbs: 3.4,
        fats: 4.3,
        fiber: 0.0,
        sugar: 2.7,
        suitableForMeals: const [MealType.breakfast, MealType.lunch, MealType.snack],
        categories: const [FoodCategory.dairy],
        isVegetarian: true,
        isGlutenFree: true,
        allergens: const ['latticini'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['fiocchi di latte', 'magro', 'proteico'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 83.0,
          phosphorus: 159.0,
          potassium: 104.0,
          sodium: 364.0,
          vitaminB12: 0.4,
        ),
      ),
    ];
  }

  static List<Food> getFats() {
    return [
      Food(
        id: 'specific_fat_1',
        name: 'Olio extravergine di oliva',
        description: 'Olio extravergine di oliva italiano',
        imageUrl: 'https://images.unsplash.com/photo-1474979266404-7eaacbcd87c5?q=80&w=500',
        calories: 884,
        proteins: 0.0,
        carbs: 0.0,
        fats: 100.0,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.fat],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '1 cucchiaio (13g)',
        servingSizeGrams: 13,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['olio', 'condimento', 'grasso sano'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          vitaminE: 14.4,
        ),
      ),
      Food(
        id: 'specific_fat_2',
        name: 'Noci',
        description: 'Noci sgusciate',
        imageUrl: 'https://images.unsplash.com/photo-1553909489-cd47e0ef937f?q=80&w=500',
        calories: 654,
        proteins: 15.2,
        carbs: 13.7,
        fats: 65.2,
        fiber: 6.7,
        sugar: 2.6,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fat],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['frutta a guscio'],
        servingSize: '30g (circa 7-8 noci)',
        servingSizeGrams: 30,
        foodState: FoodState.raw,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta secca', 'omega3', 'proteico'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 98.0,
          phosphorus: 346.0,
          magnesium: 158.0,
          potassium: 441.0,
          iron: 2.9,
          vitaminE: 0.7,
        ),
      ),
      Food(
        id: 'specific_fat_3',
        name: 'Mandorle',
        description: 'Mandorle sgusciate',
        imageUrl: 'https://images.unsplash.com/photo-1508061253366-f7da158b6d46?q=80&w=500',
        calories: 579,
        proteins: 21.2,
        carbs: 21.6,
        fats: 49.9,
        fiber: 12.5,
        sugar: 4.3,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fat],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['frutta a guscio'],
        servingSize: '30g (circa 20-25 mandorle)',
        servingSizeGrams: 30,
        foodState: FoodState.raw,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta secca', 'vitamina E', 'proteico'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 269.0,
          phosphorus: 481.0,
          magnesium: 270.0,
          potassium: 733.0,
          iron: 3.7,
          vitaminE: 25.6,
        ),
      ),
    ];
  }

  static List<Food> getSweets() {
    return [
      Food(
        id: 'specific_sweet_1',
        name: 'Miele',
        description: 'Miele di fiori',
        imageUrl: 'https://images.unsplash.com/photo-1587049352851-8d4e89133924?q=80&w=500',
        calories: 304,
        proteins: 0.3,
        carbs: 82.0,
        fats: 0.0,
        fiber: 0.0,
        sugar: 82.0,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.sweet],
        isVegetarian: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '1 cucchiaio (21g)',
        servingSizeGrams: 21,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['miele', 'dolcificante', 'naturale', 'dolce'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          potassium: 52.0,
        ),
      ),
    ];
  }

  static List<Food> getBeverages() {
    return [
      Food(
        id: 'specific_beverage_1',
        name: 'Acqua',
        description: 'Acqua naturale',
        imageUrl: 'https://images.unsplash.com/photo-1548839140-29a749e1cf4d?q=80&w=500',
        calories: 0,
        proteins: 0.0,
        carbs: 0.0,
        fats: 0.0,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.breakfast, MealType.lunch, MealType.dinner, MealType.snack],
        categories: const [FoodCategory.beverage],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '1 bicchiere (250ml)',
        servingSizeGrams: 250,
        foodState: FoodState.prepared,
        volumeToWeightFactor: 1.0,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['acqua', 'idratazione', 'base'],
        micronutrients: const {},
      ),
    ];
  }

  static List<Food> getBreakfastItems() {
    return [
      Food(
        id: 'specific_breakfast_1',
        name: 'Pancake integrali',
        description: 'Pancake preparati con farina integrale',
        imageUrl: 'https://images.unsplash.com/photo-1528207776546-365bb710ee93?q=80&w=500',
        calories: 180,
        proteins: 5.0,
        carbs: 30.0,
        fats: 4.5,
        fiber: 3.0,
        sugar: 5.0,
        suitableForMeals: const [MealType.breakfast],
        categories: const [FoodCategory.grain, FoodCategory.mixed],
        isVegetarian: true,
        allergens: const ['glutine', 'uova', 'latticini'],
        servingSize: '2 pancake (100g)',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        isRecipe: true,
        ingredients: const ['farina integrale', 'uova', 'latte', 'lievito'],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['colazione', 'dolce', 'integrale'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 85.0,
          phosphorus: 180.0,
          magnesium: 40.0,
          iron: 1.5,
        ),
      ),
      Food(
        id: 'specific_breakfast_2',
        name: 'Pane integrale',
        description: 'Pane di grano integrale',
        imageUrl: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?q=80&w=500',
        calories: 247,
        proteins: 10.0,
        carbs: 45.0,
        fats: 3.2,
        fiber: 7.0,
        sugar: 2.0,
        suitableForMeals: const [MealType.breakfast, MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        isDairyFree: true,
        allergens: const ['glutine'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['pane', 'integrale', 'colazione', 'base'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 30.0,
          phosphorus: 200.0,
          magnesium: 80.0,
          potassium: 230.0,
          iron: 2.5,
        ),
      ),
      Food(
        id: 'specific_breakfast_3',
        name: 'Muesli',
        description: 'Muesli con fiocchi d\'avena, frutta secca e semi',
        imageUrl: 'https://images.unsplash.com/photo-1517673132405-a56a62b18caf?q=80&w=500',
        calories: 340,
        proteins: 10.0,
        carbs: 60.0,
        fats: 8.0,
        fiber: 9.0,
        sugar: 15.0,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.grain, FoodCategory.mixed],
        isVegetarian: true,
        isVegan: true,
        isDairyFree: true,
        allergens: const ['glutine', 'frutta a guscio'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['cereali', 'colazione', 'fibre'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 50.0,
          phosphorus: 350.0,
          magnesium: 120.0,
          potassium: 400.0,
          iron: 3.0,
        ),
      ),
      Food(
        id: 'specific_breakfast_4',
        name: 'Uova strapazzate',
        description: 'Uova strapazzate cotte',
        imageUrl: 'https://images.unsplash.com/photo-1525351484163-7529414344d8?q=80&w=500',
        calories: 143,
        proteins: 12.5,
        carbs: 1.0,
        fats: 10.0,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.breakfast, MealType.lunch],
        categories: const [FoodCategory.protein],
        isVegetarian: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['uova'],
        servingSize: '2 uova (100g)',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['uova', 'colazione', 'proteico'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 50.0,
          phosphorus: 172.0,
          potassium: 126.0,
          sodium: 142.0,
          vitaminA: 149.0,
          vitaminB12: 1.1,
        ),
      ),
      Food(
        id: 'specific_breakfast_5',
        name: 'Avocado',
        description: 'Avocado maturo',
        imageUrl: 'https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?q=80&w=500',
        calories: 160,
        proteins: 2.0,
        carbs: 8.5,
        fats: 14.7,
        fiber: 6.7,
        sugar: 0.7,
        suitableForMeals: const [MealType.breakfast, MealType.lunch, MealType.snack],
        categories: const [FoodCategory.fruit, FoodCategory.fat],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '1/2 avocado (100g)',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'grasso sano', 'colazione'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 12.0,
          phosphorus: 52.0,
          magnesium: 29.0,
          potassium: 485.0,
          vitaminC: 10.0,
          vitaminE: 2.1,
        ),
      ),
    ];
  }
}
