import 'package:flutter/material.dart';

/// Costanti per le immagini locali utilizzate nella nuova interfaccia
class LocalImageConstants {
  // Percorsi delle immagini di sfondo
  static const String yogaBackground = 'assets/images/yoga_background.jpg';
  static const String morningChallengeBackground = 'assets/images/morning_challenge.jpg';
  static const String transformLifeBackground = 'assets/images/transform_life.jpg';
  
  // Percorsi delle immagini per gli avatar
  static const String userAvatar = 'assets/images/user_avatar.jpg';
  static const String trainerAvatar = 'assets/images/trainer_avatar.jpg';
  static const String participant1Avatar = 'assets/images/participant1_avatar.jpg';
  static const String participant2Avatar = 'assets/images/participant2_avatar.jpg';
  static const String participant3Avatar = 'assets/images/participant3_avatar.jpg';
  
  // Percorsi delle immagini per gli esercizi
  static const String yogaPose1 = 'assets/images/yoga_pose1.jpg';
  static const String yogaPose2 = 'assets/images/yoga_pose2.jpg';
  
  // Funzione per ottenere un'immagine di fallback se l'immagine locale non è disponibile
  static Widget getImageWidget(String imagePath, {double? width, double? height, BoxFit fit = BoxFit.cover}) {
    return Image.asset(
      imagePath,
      width: width,
      height: height,
      fit: fit,
      errorBuilder: (context, error, stackTrace) {
        // Fallback a un placeholder o a un'icona se l'immagine non è disponibile
        return Container(
          width: width,
          height: height,
          color: Colors.grey[200],
          child: const Icon(
            Icons.image_not_supported,
            color: Colors.grey,
          ),
        );
      },
    );
  }
}
