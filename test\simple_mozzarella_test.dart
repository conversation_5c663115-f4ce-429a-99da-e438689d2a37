import 'package:flutter_test/flutter_test.dart';
import '../lib/models/food.dart';
import '../lib/services/meal_appropriateness_validator.dart';

void main() {
  group('Simple Mozzarella Validation Tests', () {
    test('MealAppropriatenessValidator correctly blocks mozzarella from snacks', () {
      print('🧪 Test: Validazione appropriatezza mozzarella per spuntini');
      
      // Crea alimenti mozzarella di test (simili a quelli nel database)
      final mozzarellaBuffala = Food(
        id: 'test_mozzarella_bufala',
        name: 'Mozza<PERSON> di Bufala',
        description: 'Mozzarella di bufala campana DOP',
        calories: 288,
        proteins: 16.7,
        carbs: 1.0,
        fats: 24.4,
        suitableForMeals: [MealType.lunch, MealType.dinner], // Già corretta - no snack
        categories: [FoodCategory.dairy],
        servingSizeGrams: 125,
      );

      final mozzarellaMucca = Food(
        id: 'test_mozzarella_mucca',
        name: '<PERSON><PERSON><PERSON>',
        description: 'Mozzarella di mucca',
        calories: 280,
        proteins: 18.0,
        carbs: 2.2,
        fats: 22.0,
        suitableForMeals: [MealType.lunch, MealType.dinner], // Già corretta - no snack
        categories: [FoodCategory.dairy],
        servingSizeGrams: 100,
      );

      final mozzarellaFresca = Food(
        id: 'test_mozzarella_fresca',
        name: 'Mozzarella di mucca',
        description: 'Mozzarella fresca vaccina',
        calories: 253,
        proteins: 18.7,
        carbs: 2.5,
        fats: 19.0,
        suitableForMeals: [MealType.lunch, MealType.dinner], // Già corretta - no snack
        categories: [FoodCategory.dairy],
        servingSizeGrams: 100,
      );

      final mozzarellaItems = [mozzarellaBuffala, mozzarellaMucca, mozzarellaFresca];

      print('Test su ${mozzarellaItems.length} alimenti mozzarella:');

      for (final mozzarella in mozzarellaItems) {
        print('  Testing: ${mozzarella.name}');
        
        // Test per ogni tipo di pasto
        for (final mealType in MealType.values) {
          final isAppropriate = MealAppropriatenessValidator.isAppropriateForMeal(
            mozzarella, 
            mealType
          );
          
          final mealName = _getMealTypeName(mealType);
          print('    $mealName: ${isAppropriate ? "✅ APPROPRIATO" : "❌ INAPPROPRIATO"}');
          
          // VERIFICA CRITICA: Mozzarella NON deve essere appropriata per spuntini
          if (mealType == MealType.snack) {
            expect(
              isAppropriate, 
              isFalse, 
              reason: '${mozzarella.name} non dovrebbe essere appropriata per spuntini'
            );
          }
          
          // Verifica che sia appropriata per pranzo e cena
          if (mealType == MealType.lunch || mealType == MealType.dinner) {
            expect(
              isAppropriate,
              isTrue,
              reason: '${mozzarella.name} dovrebbe essere appropriata per ${mealName.toLowerCase()}'
            );
          }
        }
        print('');
      }
      
      print('✅ Test validazione appropriatezza completato con successo!');
    });

    test('MealAppropriatenessValidator correctly identifies inappropriate snack foods', () {
      print('🧪 Test: Identificazione alimenti inappropriati per spuntini');
      
      // Crea alimenti che dovrebbero essere inappropriati per spuntini
      final inappropriateFoods = [
        Food(
          id: 'test_heavy_cheese',
          name: 'Formaggio pesante',
          description: 'Formaggio molto calorico e pesante',
          calories: 400,
          proteins: 25.0,
          carbs: 2.0,
          fats: 32.0,
          suitableForMeals: [MealType.lunch, MealType.dinner, MealType.snack], // Include snack erroneamente
          categories: [FoodCategory.dairy],
          servingSizeGrams: 200, // Porzione troppo grande per spuntino
        ),
        Food(
          id: 'test_complex_recipe',
          name: 'Piatto complesso',
          description: 'Ricetta complessa che richiede cottura',
          calories: 350,
          proteins: 20.0,
          carbs: 30.0,
          fats: 15.0,
          suitableForMeals: [MealType.lunch, MealType.dinner, MealType.snack], // Include snack erroneamente
          categories: [FoodCategory.mixed],
          servingSizeGrams: 300,
          isRecipe: true,
          complexity: 4, // Troppo complesso per spuntino
          preparationTimeMinutes: 30, // Troppo tempo per spuntino
        ),
      ];

      for (final food in inappropriateFoods) {
        print('  Testing: ${food.name}');
        
        final isAppropriateForSnack = MealAppropriatenessValidator.isAppropriateForMeal(
          food, 
          MealType.snack
        );
        
        print('    Spuntino: ${isAppropriateForSnack ? "✅ APPROPRIATO" : "❌ INAPPROPRIATO"}');
        
        // Questi alimenti NON dovrebbero essere appropriati per spuntini
        expect(
          isAppropriateForSnack,
          isFalse,
          reason: '${food.name} non dovrebbe essere appropriato per spuntini'
        );
        
        // Ottieni suggerimenti
        final suggestions = MealAppropriatenessValidator.getSuggestions(food, MealType.snack);
        if (suggestions.isNotEmpty) {
          print('    Suggerimenti:');
          for (final suggestion in suggestions) {
            print('      💡 $suggestion');
          }
        }
        print('');
      }
      
      print('✅ Test identificazione alimenti inappropriati completato!');
    });

    test('MealAppropriatenessValidator correctly identifies appropriate snack foods', () {
      print('🧪 Test: Identificazione alimenti appropriati per spuntini');
      
      // Crea alimenti che dovrebbero essere appropriati per spuntini
      final appropriateFoods = [
        Food(
          id: 'test_apple',
          name: 'Mela',
          description: 'Mela fresca',
          calories: 52,
          proteins: 0.3,
          carbs: 13.8,
          fats: 0.2,
          suitableForMeals: [MealType.breakfast, MealType.snack],
          categories: [FoodCategory.fruit],
          servingSizeGrams: 150, // Porzione appropriata
        ),
        Food(
          id: 'test_aged_cheese',
          name: 'Parmigiano Reggiano',
          description: 'Formaggio stagionato in piccole porzioni',
          calories: 392,
          proteins: 35.8,
          carbs: 0.0,
          fats: 26.0,
          suitableForMeals: [MealType.lunch, MealType.dinner, MealType.snack],
          categories: [FoodCategory.dairy],
          servingSizeGrams: 30, // Porzione piccola appropriata per spuntino
        ),
        Food(
          id: 'test_nuts',
          name: 'Mandorle',
          description: 'Mandorle tostate',
          calories: 579,
          proteins: 21.2,
          carbs: 21.6,
          fats: 49.9,
          suitableForMeals: [MealType.snack],
          categories: [FoodCategory.fat],
          servingSizeGrams: 30, // Porzione piccola
        ),
      ];

      for (final food in appropriateFoods) {
        print('  Testing: ${food.name}');
        
        final isAppropriateForSnack = MealAppropriatenessValidator.isAppropriateForMeal(
          food, 
          MealType.snack
        );
        
        print('    Spuntino: ${isAppropriateForSnack ? "✅ APPROPRIATO" : "❌ INAPPROPRIATO"}');
        
        // Questi alimenti DOVREBBERO essere appropriati per spuntini
        expect(
          isAppropriateForSnack,
          isTrue,
          reason: '${food.name} dovrebbe essere appropriato per spuntini'
        );
        print('');
      }
      
      print('✅ Test identificazione alimenti appropriati completato!');
    });

    test('filterAppropriateForMeal correctly filters food lists', () {
      print('🧪 Test: Filtro lista alimenti per appropriatezza');
      
      // Crea una lista mista di alimenti
      final mixedFoods = [
        // Appropriato per spuntino
        Food(
          id: 'appropriate_fruit',
          name: 'Banana',
          description: 'Banana fresca',
          calories: 89,
          proteins: 1.1,
          carbs: 22.8,
          fats: 0.3,
          suitableForMeals: [MealType.breakfast, MealType.snack],
          categories: [FoodCategory.fruit],
          servingSizeGrams: 120,
        ),
        // NON appropriato per spuntino (mozzarella)
        Food(
          id: 'inappropriate_mozzarella',
          name: 'Mozzarella',
          description: 'Mozzarella fresca',
          calories: 280,
          proteins: 18.0,
          carbs: 2.2,
          fats: 22.0,
          suitableForMeals: [MealType.lunch, MealType.dinner, MealType.snack], // Include snack erroneamente
          categories: [FoodCategory.dairy],
          servingSizeGrams: 125,
        ),
        // Appropriato per spuntino
        Food(
          id: 'appropriate_nuts',
          name: 'Noci',
          description: 'Noci sgusciate',
          calories: 654,
          proteins: 15.2,
          carbs: 13.7,
          fats: 65.2,
          suitableForMeals: [MealType.snack],
          categories: [FoodCategory.fat],
          servingSizeGrams: 30,
        ),
      ];

      print('Lista originale: ${mixedFoods.length} alimenti');
      for (final food in mixedFoods) {
        print('  - ${food.name}');
      }

      // Filtra per spuntini
      final filteredFoods = MealAppropriatenessValidator.filterAppropriateForMeal(
        mixedFoods,
        MealType.snack,
      );

      print('Lista filtrata per spuntini: ${filteredFoods.length} alimenti');
      for (final food in filteredFoods) {
        print('  ✅ ${food.name}');
      }

      // Verifica che la mozzarella sia stata esclusa
      final mozzarellaInFiltered = filteredFoods.where((food) => 
        food.name.toLowerCase().contains('mozzarella')).toList();
      
      expect(
        mozzarellaInFiltered.length,
        equals(0),
        reason: 'Mozzarella dovrebbe essere esclusa dalla lista filtrata per spuntini'
      );

      // Verifica che gli alimenti appropriati siano inclusi
      expect(
        filteredFoods.length,
        equals(2), // Banana e Noci
        reason: 'Dovrebbero rimanere solo 2 alimenti appropriati per spuntini'
      );

      print('✅ Test filtro lista alimenti completato!');
    });
  });
}

String _getMealTypeName(MealType mealType) {
  switch (mealType) {
    case MealType.breakfast: return 'Colazione';
    case MealType.lunch: return 'Pranzo';
    case MealType.dinner: return 'Cena';
    case MealType.snack: return 'Spuntino';
  }
}
