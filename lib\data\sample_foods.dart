import '../models/food.dart';
import '../constants/image_constants.dart';
import 'additional_foods.dart';

class SampleFoods {
  static List<Food> getSampleFoods() {
    // Combina gli alimenti di base con gli alimenti aggiuntivi
    final basefoods = _getBaseFoods();
    final additionalFoods = AdditionalFoods.getAdditionalFoods();

    // Unisci le due liste
    return [...basefoods, ...additionalFoods];
  }

  static List<Food> _getBaseFoods() {
    return [
      // COLAZIONE
      Food(
        id: 'breakfast_1',
        name: 'Yogurt greco con miele e noci',
        description: 'Yogurt greco intero con miele e noci tritate',
        imageUrl: 'https://images.unsplash.com/photo-1488477181946-6428a0291777?q=80&w=500',
        calories: 220,
        proteins: 15.0,
        carbs: 18.0,
        fats: 10.0,
        fiber: 2.0,
        sugar: 15.0,
        suitableForMeals: const [MealType.breakfast],
        categories: const [FoodCategory.dairy, FoodCategory.protein],
        isVegetarian: true,
        isGlutenFree: true,
        allergens: const ['latticini', 'frutta a guscio'],
        servingSize: '1 porzione (200g)',
        servingSizeGrams: 200,
      ),
      Food(
        id: 'breakfast_2',
        name: 'Avocado toast',
        description: 'Pane integrale tostato con avocado schiacciato, olio d\'oliva e semi di sesamo',
        imageUrl: 'https://images.unsplash.com/photo-1588137378633-dea1336ce1e2?q=80&w=500',
        calories: 320,
        proteins: 8.0,
        carbs: 30.0,
        fats: 18.0,
        fiber: 8.0,
        sugar: 2.0,
        suitableForMeals: const [MealType.breakfast],
        categories: const [FoodCategory.grain, FoodCategory.fat],
        isVegetarian: true,
        isVegan: true,
        allergens: const ['glutine', 'sesamo'],
        servingSize: '1 porzione (150g)',
        servingSizeGrams: 150,
      ),
      Food(
        id: 'breakfast_3',
        name: 'Porridge di avena con frutta',
        description: 'Fiocchi d\'avena cotti con latte, banana, mirtilli e cannella',
        imageUrl: 'https://images.unsplash.com/photo-1517673132405-a56a62b18caf?q=80&w=500',
        calories: 280,
        proteins: 10.0,
        carbs: 45.0,
        fats: 6.0,
        fiber: 7.0,
        sugar: 18.0,
        suitableForMeals: const [MealType.breakfast],
        categories: const [FoodCategory.grain, FoodCategory.fruit],
        isVegetarian: true,
        allergens: const ['latticini', 'glutine'],
        servingSize: '1 porzione (250g)',
        servingSizeGrams: 250,
      ),

      // PRANZO
      Food(
        id: 'lunch_1',
        name: 'Insalata di quinoa con verdure',
        description: 'Quinoa con pomodorini, cetrioli, avocado, cipolla rossa e condimento al limone',
        imageUrl: 'https://images.unsplash.com/photo-1505576399279-565b52d4ac71?q=80&w=500',
        calories: 350,
        proteins: 12.0,
        carbs: 40.0,
        fats: 15.0,
        fiber: 10.0,
        sugar: 5.0,
        suitableForMeals: const [MealType.lunch],
        categories: const [FoodCategory.grain, FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        servingSize: '1 porzione (300g)',
        servingSizeGrams: 300,
      ),
      Food(
        id: 'lunch_2',
        name: 'Petto di pollo alla griglia con verdure',
        description: 'Petto di pollo grigliato con zucchine, peperoni e patate al forno',
        imageUrl: 'https://images.unsplash.com/photo-1532550907401-a500c9a57435?q=80&w=500',
        calories: 420,
        proteins: 35.0,
        carbs: 25.0,
        fats: 18.0,
        fiber: 6.0,
        sugar: 4.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein, FoodCategory.vegetable],
        isGlutenFree: true,
        isDairyFree: true,
        servingSize: '1 porzione (350g)',
        servingSizeGrams: 350,
      ),
      Food(
        id: 'lunch_3',
        name: 'Pasta integrale al pomodoro',
        description: 'Pasta integrale con salsa di pomodoro fresco, basilico e olio d\'oliva',
        imageUrl: 'https://images.unsplash.com/photo-1608219992759-8d74ed8d76eb?q=80&w=500',
        calories: 380,
        proteins: 12.0,
        carbs: 65.0,
        fats: 8.0,
        fiber: 8.0,
        sugar: 6.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain, FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        allergens: const ['glutine'],
        servingSize: '1 porzione (300g)',
        servingSizeGrams: 300,
      ),

      // CENA
      Food(
        id: 'dinner_1',
        name: 'Salmone al forno con asparagi',
        description: 'Filetto di salmone al forno con asparagi e limone',
        imageUrl: 'https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?q=80&w=500',
        calories: 380,
        proteins: 30.0,
        carbs: 10.0,
        fats: 22.0,
        fiber: 4.0,
        sugar: 2.0,
        suitableForMeals: const [MealType.dinner],
        categories: const [FoodCategory.protein, FoodCategory.vegetable],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['pesce'],
        servingSize: '1 porzione (300g)',
        servingSizeGrams: 300,
      ),
      Food(
        id: 'dinner_2',
        name: 'Zuppa di lenticchie',
        description: 'Zuppa di lenticchie con carote, sedano, cipolla e spezie',
        imageUrl: 'https://images.unsplash.com/photo-1547592166-23ac45744acd?q=80&w=500',
        calories: 280,
        proteins: 18.0,
        carbs: 40.0,
        fats: 4.0,
        fiber: 15.0,
        sugar: 6.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein, FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        servingSize: '1 porzione (350g)',
        servingSizeGrams: 350,
      ),
      Food(
        id: 'dinner_3',
        name: 'Tofu saltato con verdure',
        description: 'Tofu saltato con broccoli, carote, peperoni e salsa di soia',
        imageUrl: 'https://images.unsplash.com/photo-1546069901-d5bfd2cbfb1f?q=80&w=500',
        calories: 320,
        proteins: 20.0,
        carbs: 25.0,
        fats: 15.0,
        fiber: 8.0,
        sugar: 6.0,
        suitableForMeals: const [MealType.dinner],
        categories: const [FoodCategory.protein, FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['soia'],
        servingSize: '1 porzione (300g)',
        servingSizeGrams: 300,
      ),

      // SPUNTINI
      Food(
        id: 'snack_1',
        name: 'Mix di frutta secca',
        description: 'Mix di mandorle, noci, nocciole e uvetta',
        imageUrl: 'https://images.unsplash.com/photo-1604928141064-207cea5062db?q=80&w=500',
        calories: 180,
        proteins: 6.0,
        carbs: 12.0,
        fats: 14.0,
        fiber: 3.0,
        sugar: 8.0,
        suitableForMeals: const [MealType.snack],
        categories: const [FoodCategory.fat],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['frutta a guscio'],
        servingSize: '1 porzione (30g)',
        servingSizeGrams: 30,
      ),
      Food(
        id: 'snack_2',
        name: 'Mela con burro di arachidi',
        description: 'Mela fresca tagliata a fette con burro di arachidi',
        imageUrl: 'https://images.unsplash.com/photo-1581400151483-511d98110f01?q=80&w=500',
        calories: 220,
        proteins: 5.0,
        carbs: 30.0,
        fats: 10.0,
        fiber: 5.0,
        sugar: 20.0,
        suitableForMeals: const [MealType.snack],
        categories: const [FoodCategory.fruit, FoodCategory.fat],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['arachidi'],
        servingSize: '1 porzione (150g)',
        servingSizeGrams: 150,
      ),
      Food(
        id: 'snack_3',
        name: 'Yogurt con frutti di bosco',
        description: 'Yogurt naturale con mirtilli, lamponi e more',
        imageUrl: 'https://images.unsplash.com/photo-1488477181946-6428a0291777?q=80&w=500',
        calories: 150,
        proteins: 8.0,
        carbs: 20.0,
        fats: 4.0,
        fiber: 3.0,
        sugar: 15.0,
        suitableForMeals: const [MealType.snack, MealType.breakfast],
        categories: const [FoodCategory.dairy, FoodCategory.fruit],
        isVegetarian: true,
        isGlutenFree: true,
        allergens: const ['latticini'],
        servingSize: '1 porzione (200g)',
        servingSizeGrams: 200,
      ),
    ];
  }
}
