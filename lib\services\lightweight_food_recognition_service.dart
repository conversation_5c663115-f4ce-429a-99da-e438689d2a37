import 'dart:io';
import 'dart:math';
import '../models/food.dart';
import '../models/food_recognition_result.dart';
import '../data/food_database.dart';
import '../services/food_safety_service.dart';

/// Servizio di riconoscimento alimentare leggero per dispositivi con risorse limitate
class LightweightFoodRecognitionService {
  static LightweightFoodRecognitionService? _instance;
  static LightweightFoodRecognitionService getInstance() {
    _instance ??= LightweightFoodRecognitionService._();
    return _instance!;
  }
  LightweightFoodRecognitionService._();

  FoodDatabase? _foodDatabase;
  final Random _random = Random();
  bool _isInitialized = false;

  /// Inizializza il servizio
  Future<void> _ensureInitialized() async {
    if (_isInitialized && _foodDatabase != null) return;

    try {
      _foodDatabase = FoodDatabase();
      _isInitialized = true;
      print('✅ LightweightFoodRecognitionService inizializzato');
    } catch (e) {
      print('❌ Errore inizializzazione LightweightFoodRecognitionService: $e');
      _isInitialized = false;
      rethrow;
    }
  }

  /// Analizza un'immagine con approccio leggero
  Future<FoodRecognitionResult> analyzeFood({
    required File imageFile,
    required String mealType,
    String? userId,
  }) async {
    try {
      print('🚀 Avvio analisi leggera per ${imageFile.path}');

      // Assicurati che il servizio sia inizializzato
      await _ensureInitialized();

      // Verifica che il file esista
      if (!await imageFile.exists()) {
        throw Exception('File immagine non trovato');
      }

      // Verifica dimensione file
      final fileSize = await imageFile.length();
      if (fileSize == 0) {
        throw Exception('File immagine vuoto');
      }

      print('📊 File valido: ${fileSize} bytes');

      // Simula il riconoscimento senza processamento pesante dell'immagine
      final recognizedFoods = await _performLightweightRecognition(mealType);

      // Calcola metriche
      final nutritionalSummary = _calculateNutritionalSummary(recognizedFoods);
      final suggestions = _generateSimpleSuggestions(recognizedFoods, mealType);

      final result = FoodRecognitionResult(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        timestamp: DateTime.now(),
        imagePath: imageFile.path,
        mealType: mealType,
        recognizedFoods: recognizedFoods,
        nutritionalSummary: nutritionalSummary,
        suggestions: suggestions,
        confidenceScore: 0.75, // Confidenza fissa per semplicità
        userId: userId,
      );

      print('✅ Analisi leggera completata: ${recognizedFoods.length} alimenti');
      return result;

    } catch (e, stackTrace) {
      print('❌ Errore nell\'analisi leggera: $e');
      print('Stack trace: $stackTrace');
      rethrow;
    }
  }

  /// Riconoscimento leggero basato sul tipo di pasto
  Future<List<RecognizedFood>> _performLightweightRecognition(String mealType) async {
    if (_foodDatabase == null) {
      throw Exception('Database degli alimenti non inizializzato');
    }

    final allFoods = await _foodDatabase!.getAllFoods();
    final safeFoods = FoodSafetyService.filterSafeFoods(allFoods);
    final recognizedFoods = <RecognizedFood>[];

    // Seleziona alimenti appropriati per il tipo di pasto
    final appropriateFoods = _selectFoodsForMealType(safeFoods, mealType);

    // Prendi 1-3 alimenti casuali
    final numFoods = 1 + _random.nextInt(3);
    final selectedFoods = appropriateFoods.take(numFoods).toList();

    for (final food in selectedFoods) {
      final confidence = 0.6 + _random.nextDouble() * 0.3; // 0.6-0.9
      final estimatedGrams = _estimatePortionForMealType(food, mealType);

      recognizedFoods.add(RecognizedFood(
        food: food,
        confidence: confidence,
        estimatedGrams: estimatedGrams,
        boundingBox: BoundingBox(x: 0.1, y: 0.1, width: 0.8, height: 0.8),
      ));
    }

    return recognizedFoods;
  }

  /// Seleziona alimenti appropriati per il tipo di pasto
  List<Food> _selectFoodsForMealType(List<Food> foods, String mealType) {
    final shuffled = List<Food>.from(foods)..shuffle(_random);

    switch (mealType) {
      case 'breakfast':
        return shuffled.where((food) =>
          food.name.toLowerCase().contains('latte') ||
          food.name.toLowerCase().contains('cereali') ||
          food.name.toLowerCase().contains('pane') ||
          food.name.toLowerCase().contains('mela') ||
          food.name.toLowerCase().contains('banana')
        ).take(5).toList();

      case 'lunch':
      case 'dinner':
        return shuffled.where((food) =>
          food.name.toLowerCase().contains('pollo') ||
          food.name.toLowerCase().contains('riso') ||
          food.name.toLowerCase().contains('pasta') ||
          food.name.toLowerCase().contains('spinaci') ||
          food.name.toLowerCase().contains('pomodoro')
        ).take(5).toList();

      case 'snack':
        return shuffled.where((food) =>
          food.name.toLowerCase().contains('mela') ||
          food.name.toLowerCase().contains('banana') ||
          food.name.toLowerCase().contains('yogurt')
        ).take(3).toList();

      default:
        return shuffled.take(5).toList();
    }
  }

  /// Stima porzione per tipo di pasto
  int _estimatePortionForMealType(Food food, String mealType) {
    final foodName = food.name.toLowerCase();

    if (foodName.contains('mela') || foodName.contains('banana')) {
      return 150;
    } else if (foodName.contains('latte')) {
      return 200;
    } else if (foodName.contains('pollo')) {
      return mealType == 'snack' ? 50 : 120;
    } else if (foodName.contains('riso') || foodName.contains('pasta')) {
      return 80;
    } else if (foodName.contains('spinaci') || foodName.contains('pomodoro')) {
      return 100;
    }

    return 100; // Default
  }

  /// Calcola il riepilogo nutrizionale
  NutritionalSummary _calculateNutritionalSummary(List<RecognizedFood> foods) {
    double totalCalories = 0;
    double totalProteins = 0;
    double totalCarbs = 0;
    double totalFats = 0;
    double totalFiber = 0;

    for (final recognizedFood in foods) {
      final factor = recognizedFood.estimatedGrams / 100.0;
      totalCalories += recognizedFood.food.calories * factor;
      totalProteins += recognizedFood.food.proteins * factor;
      totalCarbs += recognizedFood.food.carbs * factor;
      totalFats += recognizedFood.food.fats * factor;
      totalFiber += recognizedFood.food.fiber * factor;
    }

    return NutritionalSummary(
      calories: totalCalories.round(),
      proteins: totalProteins,
      carbs: totalCarbs,
      fats: totalFats,
      fiber: totalFiber,
    );
  }

  /// Genera suggerimenti semplici
  List<String> _generateSimpleSuggestions(List<RecognizedFood> foods, String mealType) {
    final suggestions = <String>[];

    if (foods.isEmpty) {
      suggestions.add('🔍 Non sono riuscito a riconoscere chiaramente gli alimenti.');
      suggestions.add('💡 Prova con una foto più ravvicinata e ben illuminata.');
      return suggestions;
    }

    suggestions.add('✅ Ho riconosciuto ${foods.length} alimento${foods.length > 1 ? 'i' : ''}.');

    final totalCalories = foods.fold(0.0, (sum, food) =>
      sum + (food.food.calories * food.estimatedGrams / 100.0));

    if (totalCalories > 500) {
      suggestions.add('⚡ Pasto ricco di energia: ${totalCalories.round()} calorie.');
    } else if (totalCalories < 200) {
      suggestions.add('🍃 Pasto leggero: ${totalCalories.round()} calorie.');
    }

    return suggestions;
  }
}
