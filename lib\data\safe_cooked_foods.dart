import '../models/food.dart';

/// Database di alimenti sicuri e cotti con metodi di cottura italiani tradizionali
class SafeCookedFoods {
  static List<Food> getSafeCookedFoods() {
    return [
      // VERDURE COTTE CON METODI ITALIANI
      Food(
        id: 'safe_veg_1',
        name: '<PERSON><PERSON><PERSON> saltati',
        description: 'Spinaci freschi saltati in padella con aglio e olio',
        calories: 23,
        proteins: 2.9,
        carbs: 3.6,
        fats: 0.4,
        fiber: 2.2,
        sugar: 0.4,
        categories: [FoodCategory.vegetable],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '100g cotti',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 0.5,
        calcium: 99.0,
        potassium: 558.0,
        magnesium: 79.0,
        phosphorus: 49.0,
        sodium: 79.0,
        micronutrients: {
          'vitamina_a': 542.0,
          'vitamina_c': 28.0,
          'vitamina_k': 483.0,
          'folati': 146.0,
          'ferro': 2.7,
        },
        tags: ['verdura', 'spinaci', 'saltati', 'sicuro', 'italiano'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
      ),

      Food(
        id: 'safe_veg_2',
        name: 'Bietole lessate',
        description: 'Bietole da coste lessate e condite',
        calories: 20,
        proteins: 1.8,
        carbs: 3.7,
        fats: 0.2,
        fiber: 1.6,
        sugar: 1.1,
        categories: [FoodCategory.vegetable],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '100g cotte',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 0.6,
        calcium: 51.0,
        potassium: 379.0,
        magnesium: 81.0,
        phosphorus: 46.0,
        sodium: 213.0,
        micronutrients: {
          'vitamina_k': 830.0,
          'vitamina_a': 306.0,
          'vitamina_c': 30.0,
          'ferro': 1.8,
        },
        tags: ['verdura', 'bietole', 'lessate', 'sicuro', 'italiano'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
      ),

      Food(
        id: 'safe_veg_3',
        name: 'Broccoli al vapore',
        description: 'Broccoli cotti al vapore, metodo delicato',
        calories: 34,
        proteins: 2.8,
        carbs: 7.0,
        fats: 0.4,
        fiber: 2.6,
        sugar: 1.5,
        categories: [FoodCategory.vegetable],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '100g cotti',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 0.7,
        calcium: 47.0,
        potassium: 316.0,
        magnesium: 21.0,
        phosphorus: 66.0,
        sodium: 33.0,
        micronutrients: {
          'vitamina_c': 89.2,
          'vitamina_k': 101.6,
          'folati': 63.0,
          'vitamina_a': 31.0,
        },
        tags: ['verdura', 'broccoli', 'vapore', 'sicuro', 'italiano'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
      ),

      Food(
        id: 'safe_veg_4',
        name: 'Melanzane grigliate',
        description: 'Melanzane grigliate con olio e basilico',
        calories: 35,
        proteins: 0.8,
        carbs: 8.7,
        fats: 0.2,
        fiber: 2.5,
        sugar: 3.2,
        categories: [FoodCategory.vegetable],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '100g cotte',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 0.8,
        calcium: 9.0,
        potassium: 229.0,
        magnesium: 14.0,
        phosphorus: 24.0,
        sodium: 2.0,
        micronutrients: {
          'vitamina_c': 2.2,
          'folati': 22.0,
          'vitamina_k': 3.5,
          'nasunina': 750.0,
        },
        tags: ['verdura', 'melanzane', 'grigliate', 'sicuro', 'italiano'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
      ),

      // CEREALI E LEGUMI COTTI
      Food(
        id: 'safe_grain_1',
        name: 'Riso Basmati bollito',
        description: 'Riso Basmati bollito, chicchi separati',
        calories: 121,
        proteins: 2.7,
        carbs: 25.0,
        fats: 0.4,
        fiber: 0.4,
        sugar: 0.1,
        categories: [FoodCategory.grain],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 2.5,
        calcium: 3.0,
        potassium: 35.0,
        magnesium: 12.0,
        phosphorus: 43.0,
        sodium: 1.0,
        micronutrients: {
          'vitamina_b1': 0.02,
          'vitamina_b3': 0.4,
          'ferro': 0.2,
        },
        tags: ['cereali', 'riso', 'basmati', 'bollito', 'sicuro'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
      ),

      Food(
        id: 'safe_grain_2',
        name: 'Pasta cotta',
        description: 'Pasta di grano duro cotta al dente',
        calories: 131,
        proteins: 5.0,
        carbs: 25.0,
        fats: 1.1,
        fiber: 1.8,
        sugar: 0.6,
        categories: [FoodCategory.grain],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: false,
        isDairyFree: true,
        allergens: ['glutine'],
        servingSize: '100g cotta',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 2.2,
        calcium: 7.0,
        potassium: 44.0,
        magnesium: 18.0,
        phosphorus: 58.0,
        sodium: 1.0,
        micronutrients: {
          'vitamina_b1': 0.09,
          'vitamina_b3': 1.7,
          'ferro': 0.9,
          'folati': 7.0,
        },
        tags: ['cereali', 'pasta', 'cotta', 'sicuro', 'italiano'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
      ),

      Food(
        id: 'safe_legume_1',
        name: 'Fagioli cannellini lessati',
        description: 'Fagioli cannellini lessati, pronti al consumo',
        calories: 127,
        proteins: 9.7,
        carbs: 22.8,
        fats: 0.5,
        fiber: 6.3,
        sugar: 0.3,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '100g cotti',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 2.5,
        calcium: 40.0,
        potassium: 561.0,
        magnesium: 45.0,
        phosphorus: 113.0,
        sodium: 2.0,
        micronutrients: {
          'ferro': 2.9,
          'zinco': 1.0,
          'folati': 102.0,
          'vitamina_b1': 0.1,
        },
        tags: ['legumi', 'fagioli', 'cannellini', 'lessati', 'sicuro'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
      ),
    ];
  }
}
