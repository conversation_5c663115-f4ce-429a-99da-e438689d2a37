import '../interfaces/ai_interface.dart';
import '../implementations/basic_ai_implementation.dart';
import '../implementations/staffilano_ai_implementation.dart';
import '../models/ai_models.dart';
import '../../models/food.dart';
import '../../models/user_profile.dart';
import '../../models/diet_plan.dart';
import '../../services/food_database_service.dart';
import '../../services/storage_service.dart';
import '../../constants/app_constants.dart';

/// Servizio che gestisce l'intelligenza artificiale dell'applicazione
class AIService {
  static AIService? _instance;
  late AIInterface _aiImplementation;
  bool _isInitialized = false;

  /// Ottiene l'istanza singleton del servizio AI
  static Future<AIService> getInstance() async {
    if (_instance == null) {
      _instance = AIService._();
      await _instance!._initialize();
    }
    return _instance!;
  }

  AIService._();

  /// Inizializza il servizio AI
  Future<void> _initialize() async {
    if (_isInitialized) return;

    print('Inizializzazione del servizio AI avanzato ${AppConstants.aiNutritionistName}...');

    // Ottieni le dipendenze necessarie
    final foodDatabaseService = await FoodDatabaseService.getInstance();
    final storageService = await StorageService.getInstance();

    // Crea l'implementazione dell'AI avanzata del Dr. Staffilano
    _aiImplementation = StaffilanoAIImplementation(
      foodDatabaseService: foodDatabaseService,
      storageService: storageService,
    );

    // Inizializza l'AI
    await _aiImplementation.initialize();

    _isInitialized = true;
    print('Servizio AI avanzato inizializzato con successo');
  }

  /// Ottiene raccomandazioni alimentari personalizzate
  Future<AIRecommendationResponse> getRecommendations(AIRecommendationRequest request) async {
    _ensureInitialized();
    return _aiImplementation.getRecommendations(request);
  }

  /// Registra un feedback dell'utente
  Future<void> recordFeedback(UserFeedback feedback) async {
    _ensureInitialized();
    await _aiImplementation.recordFeedback(feedback);
  }

  /// Analizza un piano dietetico e fornisce suggerimenti di miglioramento
  Future<List<String>> analyzeDietPlan(WeeklyDietPlan dietPlan, UserProfile userProfile) async {
    _ensureInitialized();
    return _aiImplementation.analyzeDietPlan(dietPlan, userProfile);
  }

  /// Genera un piano dietetico personalizzato basato sull'apprendimento
  Future<WeeklyDietPlan> generatePersonalizedDietPlan(
    UserProfile userProfile,
    {int weeks = 1}
  ) async {
    _ensureInitialized();
    return _aiImplementation.generatePersonalizedDietPlan(userProfile, weeks: weeks);
  }

  /// Ottieni alimenti simili a un alimento dato
  Future<List<FoodRecommendation>> getSimilarFoods(Food food, int limit) async {
    _ensureInitialized();
    return _aiImplementation.getSimilarFoods(food, limit);
  }

  /// Ottieni alimenti complementari a un alimento dato (buone combinazioni)
  Future<List<FoodRecommendation>> getComplementaryFoods(Food food, int limit) async {
    _ensureInitialized();
    return _aiImplementation.getComplementaryFoods(food, limit);
  }

  /// Ottieni alimenti alternativi a un alimento dato (sostituzioni)
  Future<List<FoodRecommendation>> getAlternativeFoods(Food food, int limit) async {
    _ensureInitialized();
    return _aiImplementation.getAlternativeFoods(food, limit);
  }

  /// Ottieni le preferenze apprese per un utente
  Future<List<LearnedPreference>> getLearnedPreferences(String userId) async {
    _ensureInitialized();
    return _aiImplementation.getLearnedPreferences(userId);
  }

  /// Ottieni statistiche sull'apprendimento dell'AI
  Future<Map<String, dynamic>> getAIStats() async {
    _ensureInitialized();
    return _aiImplementation.getAIStats();
  }

  /// Addestra il modello AI con nuovi dati
  Future<void> trainModel(AILearningContext context) async {
    _ensureInitialized();
    await _aiImplementation.train(context);
  }

  /// Verifica che il servizio sia inizializzato
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw Exception('Il servizio AI non è stato inizializzato. Chiamare getInstance() prima di utilizzare il servizio.');
    }
  }
}
