import 'package:flutter/material.dart';
import '../models/meal.dart';

class MealDetailScreen extends StatefulWidget {
  final Meal pasto;
  final Function(Meal) onSave;

  const MealDetailScreen({
    super.key,
    required this.pasto,
    required this.onSave,
  });

  @override
  State<MealDetailScreen> createState() => _MealDetailScreenState();
}

class _MealDetailScreenState extends State<MealDetailScreen> {
  late TextEditingController _nomeController;
  late TextEditingController _orarioController;
  late TextEditingController _calorieController;
  late TextEditingController _proteineController;
  late TextEditingController _carboidratiController;
  late TextEditingController _grassiController;
  late bool _completato;
  bool _calcolaAutomaticamente = false;

  @override
  void initState() {
    super.initState();

    // Se il pasto ha alimenti, calcola i valori nutrizionali da quelli
    // altrimenti usa i valori esistenti del pasto
    if (widget.pasto.foods.isNotEmpty) {
      // Calcola i valori nutrizionali dagli alimenti
      widget.pasto.calcolaNutrientiDaAlimenti();
    }

    _nomeController = TextEditingController(text: widget.pasto.nome);
    _orarioController = TextEditingController(text: widget.pasto.orario);
    _calorieController = TextEditingController(text: widget.pasto.calorie.toString());
    _proteineController = TextEditingController(text: widget.pasto.proteine.toStringAsFixed(1));
    _carboidratiController = TextEditingController(text: widget.pasto.carboidrati.toStringAsFixed(1));
    _grassiController = TextEditingController(text: widget.pasto.grassi.toStringAsFixed(1));
    _completato = widget.pasto.completato;

    // Se i macronutrienti sono già impostati, verifichiamo se le calorie sono calcolate automaticamente
    if (widget.pasto.proteine > 0 || widget.pasto.carboidrati > 0 || widget.pasto.grassi > 0) {
      final calorieCalcolate = widget.pasto.calcolaCalorieDaMacro();
      _calcolaAutomaticamente = (calorieCalcolate == widget.pasto.calorie);
    }
  }

  @override
  void dispose() {
    _nomeController.dispose();
    _orarioController.dispose();
    _calorieController.dispose();
    _proteineController.dispose();
    _carboidratiController.dispose();
    _grassiController.dispose();
    super.dispose();
  }

  // Calcola le calorie dai macronutrienti
  void _calcolaCaloireDaMacro() {
    if (!_calcolaAutomaticamente) return;

    try {
      final proteine = double.parse(_proteineController.text);
      final carboidrati = double.parse(_carboidratiController.text);
      final grassi = double.parse(_grassiController.text);

      // 1g proteine = 4 kcal, 1g carboidrati = 4 kcal, 1g grassi = 9 kcal
      final calorie = (proteine * 4 + carboidrati * 4 + grassi * 9).round();

      setState(() {
        _calorieController.text = calorie.toString();
      });
    } catch (e) {
      // Ignora errori di parsing
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Dettaglio ${widget.pasto.nome}'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _salvaPasto,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextField(
              controller: _nomeController,
              decoration: const InputDecoration(
                labelText: 'Nome pasto',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _orarioController,
              decoration: const InputDecoration(
                labelText: 'Orario',
                border: OutlineInputBorder(),
                hintText: 'es. 8:00',
              ),
            ),
            const SizedBox(height: 16),

            // Sezione calorie e macronutrienti
            const Text(
              'Informazioni nutrizionali',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),

            // Switch per calcolo automatico calorie
            SwitchListTile(
              title: const Text('Calcola calorie automaticamente'),
              subtitle: const Text('In base ai macronutrienti inseriti'),
              value: _calcolaAutomaticamente,
              onChanged: (value) {
                setState(() {
                  _calcolaAutomaticamente = value;
                  if (value) {
                    _calcolaCaloireDaMacro();
                  }
                });
              },
              activeColor: Colors.green,
            ),

            const SizedBox(height: 8),
            TextField(
              controller: _calorieController,
              decoration: const InputDecoration(
                labelText: 'Calorie (kcal)',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              enabled: !_calcolaAutomaticamente,
            ),
            const SizedBox(height: 16),

            // Macronutrienti
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _proteineController,
                    decoration: const InputDecoration(
                      labelText: 'Proteine (g)',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (_) => _calcolaCaloireDaMacro(),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextField(
                    controller: _carboidratiController,
                    decoration: const InputDecoration(
                      labelText: 'Carboidrati (g)',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (_) => _calcolaCaloireDaMacro(),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextField(
                    controller: _grassiController,
                    decoration: const InputDecoration(
                      labelText: 'Grassi (g)',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (_) => _calcolaCaloireDaMacro(),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),
            Row(
              children: [
                Checkbox(
                  value: _completato,
                  onChanged: (value) {
                    setState(() {
                      _completato = value ?? false;
                    });
                  },
                  activeColor: Colors.green,
                ),
                const Text('Pasto completato'),
              ],
            ),
            const SizedBox(height: 32),
            Center(
              child: ElevatedButton(
                onPressed: _salvaPasto,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 16,
                  ),
                ),
                child: const Text(
                  'Salva modifiche',
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _salvaPasto() {
    // Validazione input
    if (_nomeController.text.isEmpty ||
        _orarioController.text.isEmpty ||
        _calorieController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Compila tutti i campi obbligatori'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Conversione calorie a intero
    int calorie;
    try {
      calorie = int.parse(_calorieController.text);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Inserisci un valore numerico valido per le calorie'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Conversione macronutrienti a double
    double proteine = 0.0;
    double carboidrati = 0.0;
    double grassi = 0.0;

    try {
      proteine = _proteineController.text.isEmpty
          ? 0.0
          : double.parse(_proteineController.text);
      carboidrati = _carboidratiController.text.isEmpty
          ? 0.0
          : double.parse(_carboidratiController.text);
      grassi = _grassiController.text.isEmpty
          ? 0.0
          : double.parse(_grassiController.text);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Inserisci valori numerici validi per i macronutrienti'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Creazione pasto aggiornato preservando la lista degli alimenti
    final pastoAggiornato = Meal(
      nome: _nomeController.text,
      orario: _orarioController.text,
      calorie: calorie,
      proteine: proteine,
      carboidrati: carboidrati,
      grassi: grassi,
      completato: _completato,
      foods: widget.pasto.foods, // Preserva la lista degli alimenti originali
    );

    // Chiamata callback per salvare
    widget.onSave(pastoAggiornato);

    // Torna indietro
    Navigator.of(context).pop();
  }
}
