import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/ultra_detailed_profile.dart';
import '../models/diet_plan.dart';
import '../models/food.dart';
import '../models/user_profile.dart';
import '../services/ultra_advanced_diet_generator.dart';
import '../services/ultra_profile_service.dart';
import '../widgets/planned_meal_card.dart';
import '../widgets/portion_analysis_widget.dart';
import '../theme/dr_staffilano_theme.dart';
import '../theme/app_theme.dart';
import 'ultra_profile_creation_screen.dart';
import 'diet_plan_view_screen.dart';

/// SCHERMATA PRINCIPALE GENERAZIONE DIETA ULTRA-AVANZATA
/// Sostituisce la DietGeneratorScreen di base con funzionalità avanzate
class UltraAdvancedDietScreen extends StatefulWidget {
  const UltraAdvancedDietScreen({Key? key}) : super(key: key);

  @override
  State<UltraAdvancedDietScreen> createState() => _UltraAdvancedDietScreenState();
}

class _UltraAdvancedDietScreenState extends State<UltraAdvancedDietScreen> {
  UltraDetailedProfile? _profile;
  DailyDietPlan? _dailyPlan;
  bool _isLoading = false;
  bool _isGenerating = false;
  String _errorMessage = '';
  bool _showAdvancedAnalysis = false;
  bool _enableRefeedDay = false;

  @override
  void initState() {
    super.initState();
    _loadExistingProfile();
  }

  Future<void> _loadExistingProfile() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final profileService = UltraProfileService();
      final existingProfile = await profileService.loadUltraProfile();

      setState(() {
        _profile = existingProfile;
        _isLoading = false;
      });

      // Se esiste un profilo, carica anche l'ultimo piano generato
      if (_profile != null) {
        await _loadExistingPlan();
      }

    } catch (e) {
      setState(() {
        _errorMessage = 'Errore caricamento profilo: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadExistingPlan() async {
    try {
      // Qui potresti caricare un piano esistente dal storage
      // Per ora lasciamo vuoto, l'utente dovrà generare un nuovo piano
    } catch (e) {
      print('Errore caricamento piano esistente: $e');
    }
  }

  Future<void> _navigateToProfileCreation() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UltraProfileCreationScreen(
          existingProfile: _profile,
        ),
      ),
    );

    if (result != null && result is UltraDetailedProfile) {
      setState(() {
        _profile = result;
      });
    }
  }

  Future<void> _generateUltraAdvancedPlan() async {
    if (_profile == null) {
      _showErrorSnackBar('Crea prima il tuo profilo per generare un piano personalizzato');
      return;
    }

    setState(() {
      _isGenerating = true;
      _errorMessage = '';
    });

    try {
      final generator = await UltraAdvancedDietGenerator.getInstance();
      final plan = await generator.generateUltraPersonalizedDiet(
        profile: _profile!,
        targetDate: DateTime.now(),
        enableRefeedDay: _enableRefeedDay,
      );

      setState(() {
        _dailyPlan = plan;
        _isGenerating = false;
      });

      _showSuccessSnackBar('Piano ultra-personalizzato generato con successo! 🎉');

    } catch (e) {
      setState(() {
        _errorMessage = 'Errore generazione piano: $e';
        _isGenerating = false;
      });
      _showErrorSnackBar('Errore nella generazione del piano: $e');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: DrStaffilanoTheme.primaryGreen,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  void _navigateToPlanView() {
    if (_dailyPlan == null) {
      _showErrorSnackBar('Nessun piano disponibile da visualizzare');
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DietPlanViewScreen(
          dailyPlan: _dailyPlan!,
          profile: _profile!,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DrStaffilanoTheme.backgroundLight,
      appBar: AppBar(
        title: const Text('Generatore Dieta AI'),
        backgroundColor: DrStaffilanoTheme.primaryGreen,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (_profile != null)
            IconButton(
              icon: const Icon(FontAwesomeIcons.userGear),
              tooltip: 'Modifica Profilo',
              onPressed: _navigateToProfileCreation,
            ),
          if (_dailyPlan != null)
            IconButton(
              icon: Icon(_showAdvancedAnalysis ? Icons.visibility_off : Icons.analytics),
              tooltip: _showAdvancedAnalysis ? 'Nascondi Analisi' : 'Mostra Analisi Avanzata',
              onPressed: () {
                setState(() {
                  _showAdvancedAnalysis = !_showAdvancedAnalysis;
                });
              },
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: DrStaffilanoTheme.primaryGreen),
            SizedBox(height: 16),
            Text('Caricamento profilo...'),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(),
          const SizedBox(height: 24),
          _buildProfileSection(),
          const SizedBox(height: 24),
          _buildGenerationSection(),
          if (_dailyPlan != null) ...[
            const SizedBox(height: 24),
            _buildPlanPreview(),
          ],
          const SizedBox(height: 80), // Extra space for FAB
        ],
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: DrStaffilanoTheme.primaryGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  FontAwesomeIcons.wandMagicSparkles,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Sistema Ultra-Avanzato',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'Generazione diete personalizzate con AI del Dr. Staffilano',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate().fadeIn(duration: 300.ms).slideY(begin: -0.2, end: 0);
  }

  Widget _buildProfileSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.user,
                color: DrStaffilanoTheme.primaryGreen,
                size: 20,
              ),
              const SizedBox(width: 12),
              const Text(
                'Il Tuo Profilo',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          if (_profile == null) ...[
            const Text(
              'Per generare un piano dietetico ultra-personalizzato, crea il tuo profilo dettagliato.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _navigateToProfileCreation,
                icon: const Icon(FontAwesomeIcons.plus),
                label: const Text('Crea Profilo Avanzato'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: DrStaffilanoTheme.primaryGreen,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ] else ...[
            _buildProfileSummary(),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _navigateToProfileCreation,
                icon: const Icon(FontAwesomeIcons.edit),
                label: const Text('Modifica Profilo'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: DrStaffilanoTheme.primaryGreen,
                  side: BorderSide(color: DrStaffilanoTheme.primaryGreen),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    ).animate().fadeIn(duration: 300.ms, delay: 100.ms).slideY(begin: 0.2, end: 0);
  }

  Widget _buildProfileSummary() {
    if (_profile == null) return const SizedBox.shrink();

    final validation = _profile!.validateNutritionalSafety();

    return Column(
      children: [
        Row(
          children: [
            CircleAvatar(
              backgroundColor: DrStaffilanoTheme.primaryGreen,
              radius: 25,
              child: Text(
                _profile!.baseProfile.name[0].toUpperCase(),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _profile!.baseProfile.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${_profile!.baseProfile.age} anni • ${_profile!.baseProfile.weight}kg • ${_profile!.primaryGoal.toString().split('.').last}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      _buildValidationChip(
                        'Sicurezza',
                        validation['isValid'],
                        validation['isValid'] ? Icons.check_circle : Icons.warning,
                      ),
                      const SizedBox(width: 8),
                      _buildValidationChip(
                        'Completo',
                        _profile!.isProfileComplete(),
                        _profile!.isProfileComplete() ? Icons.verified : Icons.info,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                FontAwesomeIcons.calculator,
                color: DrStaffilanoTheme.primaryGreen,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'BMR: ${_profile!.calculateBMR().round()} kcal • TDEE: ${_profile!.calculateTDEE().round()} kcal',
                style: TextStyle(
                  fontSize: 12,
                  color: DrStaffilanoTheme.primaryGreen,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildValidationChip(String label, bool isGood, IconData icon) {
    final color = isGood ? Colors.green : Colors.orange;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 12),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenerationSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.wandMagicSparkles,
                color: DrStaffilanoTheme.primaryGreen,
                size: 20,
              ),
              const SizedBox(width: 12),
              const Text(
                'Generazione Piano',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          if (_profile != null) ...[
            // Opzioni avanzate
            Row(
              children: [
                Expanded(
                  child: CheckboxListTile(
                    title: const Text(
                      'Refeed Day',
                      style: TextStyle(fontSize: 14),
                    ),
                    subtitle: const Text(
                      'Giorno ad alto contenuto calorico',
                      style: TextStyle(fontSize: 12),
                    ),
                    value: _enableRefeedDay,
                    onChanged: (value) {
                      setState(() {
                        _enableRefeedDay = value ?? false;
                      });
                    },
                    activeColor: DrStaffilanoTheme.primaryGreen,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Pulsante generazione
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isGenerating ? null : _generateUltraAdvancedPlan,
                icon: _isGenerating
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                    : const Icon(FontAwesomeIcons.wandMagicSparkles),
                label: Text(_isGenerating
                    ? 'Generazione in corso...'
                    : 'Genera Piano Ultra-Personalizzato'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: DrStaffilanoTheme.primaryGreen,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),

            if (_dailyPlan != null) ...[
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: _navigateToPlanView,
                  icon: const Icon(FontAwesomeIcons.eye),
                  label: const Text('Visualizza Piano Completo'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: DrStaffilanoTheme.primaryGreen,
                    side: BorderSide(color: DrStaffilanoTheme.primaryGreen),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ] else ...[
            const Text(
              'Crea il tuo profilo per sbloccare la generazione di piani ultra-personalizzati.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],

          if (_errorMessage.isNotEmpty) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.error_outline, color: Colors.red, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _errorMessage,
                      style: const TextStyle(
                        color: Colors.red,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    ).animate().fadeIn(duration: 300.ms, delay: 200.ms).slideY(begin: 0.2, end: 0);
  }

  Widget _buildPlanPreview() {
    if (_dailyPlan == null) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.utensils,
                color: DrStaffilanoTheme.primaryGreen,
                size: 20,
              ),
              const SizedBox(width: 12),
              const Text(
                'Anteprima Piano',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (_showAdvancedAnalysis)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'ANALISI AVANZATA',
                    style: TextStyle(
                      color: DrStaffilanoTheme.primaryGreen,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),

          // Statistiche piano
          _buildPlanStatistics(),

          const SizedBox(height: 16),

          // Lista pasti (anteprima)
          Text(
            'Pasti del giorno (${_dailyPlan!.meals.length})',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),

          ...(_dailyPlan!.meals.take(2).map((meal) => _buildMealPreviewCard(meal))),

          if (_dailyPlan!.meals.length > 2) ...[
            const SizedBox(height: 8),
            Text(
              '... e altri ${_dailyPlan!.meals.length - 2} pasti',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    ).animate().fadeIn(duration: 300.ms, delay: 300.ms).slideY(begin: 0.2, end: 0);
  }

  Widget _buildPlanStatistics() {
    if (_dailyPlan == null || _profile == null) return const SizedBox.shrink();

    final totalCalories = _dailyPlan!.meals.fold(0, (sum, meal) =>
      sum + meal.foods.fold(0, (mealSum, portion) => mealSum + portion.calories));

    final bmr = _profile!.calculateBMR();
    final tdee = _profile!.calculateTDEE();

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.primaryGreen.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildStatChip('BMR', '${bmr.round()}', 'kcal', DrStaffilanoTheme.secondaryBlue),
          _buildStatChip('TDEE', '${tdee.round()}', 'kcal', DrStaffilanoTheme.primaryGreen),
          _buildStatChip('Target', '${_dailyPlan!.calorieTarget}', 'kcal', DrStaffilanoTheme.accentGold),
          _buildStatChip('Piano', '$totalCalories', 'kcal',
            totalCalories <= _dailyPlan!.calorieTarget * 1.05 ? Colors.green : Colors.orange),
        ],
      ),
    );
  }

  Widget _buildStatChip(String label, String value, String unit, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            color: color,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          unit,
          style: TextStyle(
            color: color,
            fontSize: 8,
          ),
        ),
      ],
    );
  }

  Widget _buildMealPreviewCard(PlannedMeal meal) {
    final totalCalories = meal.foods.fold(0, (sum, portion) => sum + portion.calories);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getMealIcon(meal.type),
              color: DrStaffilanoTheme.primaryGreen,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  meal.name,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${meal.foods.length} alimenti • $totalCalories kcal',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Text(
            meal.time,
            style: TextStyle(
              fontSize: 12,
              color: DrStaffilanoTheme.primaryGreen,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getMealIcon(MealType type) {
    switch (type) {
      case MealType.breakfast:
        return FontAwesomeIcons.mugHot;
      case MealType.lunch:
        return FontAwesomeIcons.utensils;
      case MealType.dinner:
        return FontAwesomeIcons.bowlFood;
      case MealType.snack:
        return FontAwesomeIcons.cookie;
      default:
        return FontAwesomeIcons.utensils;
    }
  }
}