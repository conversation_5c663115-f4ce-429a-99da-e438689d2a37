# Flutter Native Splash Configuration
# Generates native splash screens for all platforms with Dr<PERSON> Staff<PERSON> branding

flutter_native_splash:
  # Background color (<PERSON><PERSON> primary green)
  color: "#4CAF50"
  color_dark: "#2E7D32"
  
  # Splash screen image (should be PNG, recommended size: 1152x1152)
  image: assets/images/splash_logo.png
  image_dark: assets/images/splash_logo_dark.png
  
  # Branding image (appears at bottom, optional)
  branding: assets/images/dr_staffilano_branding.png
  branding_dark: assets/images/dr_staffilano_branding_dark.png
  
  # Platform-specific configurations
  android_12:
    # Android 12+ splash screen
    image: assets/images/splash_logo.png
    icon_background_color: "#4CAF50"
    icon_background_color_dark: "#2E7D32"
    
  android:
    # Legacy Android splash screen
    image: assets/images/splash_logo.png
    color: "#4CAF50"
    
  ios:
    # iOS splash screen
    image: assets/images/splash_logo.png
    color: "#4CAF50"
    
  web:
    # Web splash screen
    image: assets/images/splash_logo.png
    color: "#4CAF50"
    image_mode: center
    
  # Responsive sizing
  android_gravity: center
  ios_content_mode: center
  web_image_mode: center
  
  # Remove splash screen after app loads
  android_12_remove_timeout: 5000
  
  # Full screen splash (removes status bar)
  fullscreen: true
  
  # Info.plist modifications for iOS
  info_plist_files:
    - 'ios/Runner/Info.plist'
