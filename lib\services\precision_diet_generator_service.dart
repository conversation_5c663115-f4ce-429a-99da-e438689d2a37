import 'dart:math';
import 'package:uuid/uuid.dart';
import '../models/user_profile.dart';
import '../models/food.dart';
import '../models/diet_plan.dart';
import '../data/food_database.dart';

/// Servizio per la generazione di piani dietetici ad alta precisione.
/// Implementa un algoritmo di ottimizzazione per la selezione degli alimenti
/// e il calcolo delle grammature precise.
class PrecisionDietGeneratorService {
  final FoodDatabase _foodDatabase;
  final Uuid _uuid = const Uuid();

  // Tolleranze per la precisione
  static const double _calorieTolerancePercent = 0.01; // 1% di tolleranza per le calorie
  static const int _calorieToleranceAbsolute = 20; // ±20 kcal di tolleranza assoluta
  static const int _macroToleranceGrams = 2; // ±2g di tolleranza per i macronutrienti

  PrecisionDietGeneratorService(this._foodDatabase);

  /// Genera un piano dietetico settimanale completo con alta precisione
  Future<WeeklyDietPlan> generateWeeklyDietPlan(UserProfile userProfile) async {
    print('Iniziando la generazione del piano dietetico di precisione...');

    // Assicurati che il database degli alimenti sia inizializzato
    await _foodDatabase.resetDatabase();

    // Verifica che il database contenga alimenti
    final allFoods = await _foodDatabase.getAllFoods();
    print('Numero di alimenti nel database: ${allFoods.length}');

    if (allFoods.isEmpty) {
      print('ERRORE: Il database degli alimenti è vuoto!');
      throw Exception('Il database degli alimenti è vuoto. Impossibile generare un piano dietetico.');
    }

    // Calcola l'obiettivo calorico e la distribuzione dei macronutrienti
    final calorieTarget = userProfile.calculateCalorieTarget();
    final macroGrams = userProfile.calculateMacroGrams();

    print('Obiettivo calorico: $calorieTarget kcal');
    print('Obiettivo macronutrienti: $macroGrams');

    // Crea un piano settimanale vuoto
    final startDate = _getStartOfWeek(DateTime.now());
    final weeklyPlan = WeeklyDietPlan(
      id: _uuid.v4(),
      name: 'Piano Settimanale di Precisione ${startDate.day}/${startDate.month}/${startDate.year}',
      startDate: _formatDate(startDate),
      dailyPlans: [],
      userProfile: userProfile,
    );

    // Genera un piano per ogni giorno della settimana
    WeeklyDietPlan updatedWeeklyPlan = weeklyPlan;

    // Mappa per tenere traccia degli alimenti utilizzati nei giorni precedenti
    final Map<String, int> usedFoodsCount = {};

    for (int i = 0; i < 7; i++) {
      final date = startDate.add(Duration(days: i));
      final dailyPlan = await generateDailyDietPlan(
        userProfile,
        _formatDate(date),
        calorieTarget,
        macroGrams,
        usedFoodsCount,
        dayOfWeek: i,
      );

      // Aggiorna il conteggio degli alimenti utilizzati
      for (var meal in dailyPlan.meals) {
        for (var foodPortion in meal.foods) {
          final foodId = foodPortion.food.id;
          usedFoodsCount[foodId] = (usedFoodsCount[foodId] ?? 0) + 1;
        }
      }

      updatedWeeklyPlan = updatedWeeklyPlan.updateDailyPlan(dailyPlan);
    }

    return updatedWeeklyPlan;
  }

  /// Genera un piano dietetico giornaliero con alta precisione
  Future<DailyDietPlan> generateDailyDietPlan(
    UserProfile userProfile,
    String date,
    int calorieTarget,
    Map<String, int> macroTargets,
    Map<String, int> usedFoodsCount, {
    int dayOfWeek = 0,
  }) async {
    print('Generazione piano giornaliero di precisione per la data: $date (giorno ${dayOfWeek + 1})');
    print('Obiettivo calorico: $calorieTarget kcal');
    print('Obiettivi macronutrienti: $macroTargets');

    // Verifica che la mappa macroTargets contenga tutte le chiavi necessarie
    if (macroTargets.isEmpty ||
        !macroTargets.containsKey('proteins') ||
        !macroTargets.containsKey('carbs') ||
        !macroTargets.containsKey('fats')) {
      print('ERRORE: La mappa macroTargets non contiene tutte le chiavi necessarie: $macroTargets');

      // Usa valori predefiniti basati su una dieta standard
      macroTargets = {
        'proteins': (calorieTarget * 0.3 / 4).round(), // 30% proteine
        'carbs': (calorieTarget * 0.4 / 4).round(),    // 40% carboidrati
        'fats': (calorieTarget * 0.3 / 9).round(),     // 30% grassi
      };

      print('Usando valori predefiniti per macroTargets: $macroTargets');
    }

    // Determina il numero di pasti in base alle preferenze dell'utente
    final mealsPerDay = userProfile.mealsPerDay;
    print('Numero di pasti al giorno: $mealsPerDay');

    // Distribuisci le calorie tra i pasti
    final mealCalorieDistribution = _distributeMealCalories(calorieTarget, mealsPerDay, dayOfWeek);
    print('Distribuzione calorie per pasto: $mealCalorieDistribution');

    // Distribuisci i macronutrienti tra i pasti
    final mealMacroDistribution = _distributeMealMacros(macroTargets, mealsPerDay, mealCalorieDistribution);
    print('Distribuzione macronutrienti per pasto: $mealMacroDistribution');

    // Genera i pasti
    final meals = <PlannedMeal>[];

    // Orari predefiniti per i pasti
    final mealTimes = _getMealTimes(mealsPerDay);

    // Genera ogni pasto
    for (int i = 0; i < mealsPerDay; i++) {
      final mealType = _getMealType(i, mealsPerDay);
      final mealCalories = mealCalorieDistribution[i];
      final mealMacros = mealMacroDistribution[i];

      print('Generazione pasto $i: tipo=$mealType, calorie=$mealCalories, macros=$mealMacros');

      final meal = await _generatePrecisionMeal(
        userProfile,
        mealType,
        mealCalories,
        mealMacros,
        mealTimes[i],
        usedFoodsCount,
      );

      meals.add(meal);
    }

    // Crea il piano giornaliero
    final dailyPlan = DailyDietPlan(
      date: date,
      meals: meals,
      calorieTarget: calorieTarget,
      macroTargets: macroTargets,
    );

    // Verifica e correggi il piano giornaliero per garantire la precisione
    return _validateAndCorrectDailyPlan(dailyPlan, userProfile);
  }

  /// Distribuisce le calorie tra i pasti del giorno
  List<int> _distributeMealCalories(int totalCalories, int mealsPerDay, int dayOfWeek) {
    // Distribuzione percentuale standard per i pasti
    final distributions = {
      3: [0.25, 0.40, 0.35], // Colazione, Pranzo, Cena
      4: [0.20, 0.10, 0.35, 0.35], // Colazione, Spuntino, Pranzo, Cena
      5: [0.20, 0.10, 0.30, 0.10, 0.30], // Colazione, Spuntino, Pranzo, Spuntino, Cena
      6: [0.15, 0.10, 0.25, 0.10, 0.25, 0.15], // 6 pasti
    };

    final distribution = distributions[mealsPerDay] ?? distributions[3]!;

    return distribution.map((percentage) => (totalCalories * percentage).round()).toList();
  }

  /// Distribuisce i macronutrienti tra i pasti
  List<Map<String, int>> _distributeMealMacros(Map<String, int> totalMacros, int mealsPerDay, List<int> calorieDistribution) {
    final totalCalories = calorieDistribution.fold(0, (sum, calories) => sum + calories);

    return calorieDistribution.map((mealCalories) {
      final percentage = mealCalories / totalCalories;

      return {
        'proteins': (totalMacros['proteins']! * percentage).round(),
        'carbs': (totalMacros['carbs']! * percentage).round(),
        'fats': (totalMacros['fats']! * percentage).round(),
      };
    }).toList();
  }

  /// Ottiene gli orari dei pasti
  List<String> _getMealTimes(int mealsPerDay) {
    switch (mealsPerDay) {
      case 3:
        return ['08:00', '13:00', '19:30'];
      case 4:
        return ['08:00', '10:30', '13:00', '19:30'];
      case 5:
        return ['08:00', '10:30', '13:00', '16:00', '19:30'];
      case 6:
        return ['07:30', '10:00', '12:30', '15:30', '18:00', '20:30'];
      default:
        return ['08:00', '13:00', '19:30'];
    }
  }

  /// Determina il tipo di pasto in base all'indice
  String _getMealType(int mealIndex, int totalMeals) {
    if (totalMeals == 3) {
      return ['breakfast', 'lunch', 'dinner'][mealIndex];
    } else if (totalMeals == 4) {
      return ['breakfast', 'snack', 'lunch', 'dinner'][mealIndex];
    } else if (totalMeals == 5) {
      return ['breakfast', 'snack', 'lunch', 'snack', 'dinner'][mealIndex];
    } else {
      return ['breakfast', 'snack', 'lunch', 'snack', 'dinner', 'snack'][mealIndex];
    }
  }

  /// Genera un pasto preciso
  Future<PlannedMeal> _generatePrecisionMeal(
    UserProfile userProfile,
    String mealType,
    int targetCalories,
    Map<String, int> targetMacros,
    String mealTime,
    Map<String, int> usedFoodsCount,
  ) async {
    // Implementazione semplificata - in una versione reale useremmo algoritmi di ottimizzazione
    final foods = <FoodPortion>[];

    // Per ora, restituiamo un pasto vuoto
    return PlannedMeal(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: _getMealName(mealType),
      type: mealType,
      foods: foods,
      time: mealTime,
    );
  }

  /// Ottiene il nome del pasto in italiano
  String _getMealName(String mealType) {
    switch (mealType) {
      case 'breakfast':
        return 'Colazione';
      case 'lunch':
        return 'Pranzo';
      case 'dinner':
        return 'Cena';
      case 'snack':
        return 'Spuntino';
      default:
        return 'Pasto';
    }
  }

  /// Valida e corregge il piano giornaliero
  Future<DailyDietPlan> _validateAndCorrectDailyPlan(DailyDietPlan plan, UserProfile userProfile) async {
    // Per ora, restituiamo il piano così com'è
    // In una versione reale, implementeremmo la validazione e correzione
    return plan;
  }

  /// Ottiene l'inizio della settimana (lunedì)
  DateTime _getStartOfWeek(DateTime date) {
    final daysFromMonday = date.weekday - 1;
    return DateTime(date.year, date.month, date.day).subtract(Duration(days: daysFromMonday));
  }

  /// Formatta una data nel formato YYYY-MM-DD
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
