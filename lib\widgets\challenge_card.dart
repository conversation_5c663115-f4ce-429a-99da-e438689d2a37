import 'package:flutter/material.dart';
import '../theme/new_app_theme.dart';
import '../utils/image_placeholder_helper.dart';

/// Widget per visualizzare una sfida o un obiettivo giornaliero
class ChallengeCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final List<String> participantAvatars;
  final int totalParticipants;
  final VoidCallback onTap;
  final String? backgroundImageUrl;

  const ChallengeCard({
    Key? key,
    required this.title,
    required this.subtitle,
    required this.participantAvatars,
    required this.totalParticipants,
    required this.onTap,
    this.backgroundImageUrl,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: NewAppTheme.spacing),
      height: 120,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(NewAppTheme.borderRadius),
        color: backgroundImageUrl == null ? NewAppTheme.primaryColor : null,
        boxShadow: [NewAppTheme.cardShadow],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(NewAppTheme.borderRadius),
          child: Stack(
            children: [
              // Background image or placeholder
              if (backgroundImageUrl != null)
                _buildBackgroundWithImage()
              else
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(NewAppTheme.borderRadius),
                    color: NewAppTheme.primaryColor,
                  ),
                ),

              // Gradient overlay
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(NewAppTheme.borderRadius),
                  gradient: LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: [
                      Colors.black.withOpacity(0.7),
                      Colors.black.withOpacity(0.3),
                    ],
                  ),
                ),
              ),

              // Content
              Padding(
                padding: const EdgeInsets.all(NewAppTheme.spacing),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: NewAppTheme.subtitleLarge.copyWith(
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          subtitle,
                          style: NewAppTheme.bodySmall.copyWith(
                            color: Colors.white.withOpacity(0.8),
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _buildParticipants(),
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius:
                                BorderRadius.circular(NewAppTheme.smallBorderRadius),
                          ),
                          padding: const EdgeInsets.symmetric(
                            horizontal: NewAppTheme.spacing,
                            vertical: NewAppTheme.smallSpacing / 2,
                          ),
                          child: Row(
                            children: [
                              Text(
                                'Inizia',
                                style: NewAppTheme.bodySmall.copyWith(
                                  color: NewAppTheme.primaryColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(width: 4),
                              const Icon(
                                Icons.arrow_forward,
                                size: 14,
                                color: NewAppTheme.primaryColor,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBackgroundWithImage() {
    try {
      return ClipRRect(
        borderRadius: BorderRadius.circular(NewAppTheme.borderRadius),
        child: Image.asset(
          backgroundImageUrl!,
          fit: BoxFit.cover,
          width: double.infinity,
          height: double.infinity,
          errorBuilder: (context, error, stackTrace) {
            return ImagePlaceholderHelper.createBackgroundPlaceholder(
              width: double.infinity,
              height: double.infinity,
              title: title,
              backgroundColor: NewAppTheme.primaryColor,
            );
          },
        ),
      );
    } catch (e) {
      return ImagePlaceholderHelper.createBackgroundPlaceholder(
        width: double.infinity,
        height: double.infinity,
        title: title,
        backgroundColor: NewAppTheme.primaryColor,
      );
    }
  }

  Widget _buildParticipants() {
    return Row(
      children: [
        // Mostriamo solo un avatar con il numero totale di partecipanti
        Container(
          width: 30,
          height: 30,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: NewAppTheme.primaryColor.withOpacity(0.7),
            border: Border.all(color: Colors.white, width: 2),
          ),
          child: Center(
            child: Text(
              '$totalParticipants',
              style: NewAppTheme.bodySmall.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 10,
              ),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          'Partecipanti',
          style: NewAppTheme.bodySmall.copyWith(
            color: Colors.white.withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildParticipantAvatar(int index) {
    // Utilizziamo sempre il fallback per evitare problemi con le immagini
    return Container(
      width: 30,
      height: 30,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: NewAppTheme.primaryColor.withOpacity(0.7),
        border: Border.all(color: Colors.white, width: 2),
      ),
      child: Center(
        child: Text(
          '${index + 1}',
          style: NewAppTheme.bodySmall.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}
