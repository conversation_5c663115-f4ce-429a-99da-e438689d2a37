import 'services/specific_diet_generator_service.dart';
import 'models/user_profile.dart';

/// TEST LOGGING SISTEMA VARIETÀ
/// Verifica se i metodi di varietà vengono chiamati
Future<void> main() async {
  print('🔍 TEST LOGGING SISTEMA VARIETÀ');
  print('=' * 40);
  print('Obiettivo: Verificare se i metodi di varietà vengono chiamati\n');

  try {
    // Inizializza il servizio
    print('1️⃣ Inizializzazione servizio...');
    final generator = await SpecificDietGeneratorService.getInstance();
    print('✅ SpecificDietGeneratorService inizializzato\n');
    
    // Crea profilo test
    print('2️⃣ Creazione profilo test...');
    final testProfile = UserProfile(
      id: 'test_logging_${DateTime.now().millisecondsSinceEpoch}',
      name: 'Test Logging',
      age: 30,
      gender: Gender.male,
      height: 175,
      weight: 70,
      activityLevel: ActivityLevel.moderate,
      goal: Goal.maintain,
      dietType: DietType.omnivore,
      allergies: [],
      dislikedFoods: [],
      mealsPerDay: 3,
    );
    print('✅ Profilo creato: ${testProfile.calculateCalorieTarget()} kcal/giorno\n');
    
    // Genera una dieta e osserva i log
    print('3️⃣ Generazione dieta con logging dettagliato...');
    print('-' * 50);
    
    final weeklyPlan = await generator.generateWeeklyDietPlan(
      testProfile,
      weeks: 1,
    );
    
    print('-' * 50);
    print('✅ Generazione completata\n');
    
    // Mostra risultati
    if (weeklyPlan.dailyPlans.isNotEmpty) {
      final dailyPlan = weeklyPlan.dailyPlans.first;
      print('4️⃣ Risultati generazione:');
      
      for (final meal in dailyPlan.meals) {
        final mealFoods = meal.foods.map((fp) => fp.food.name).toList();
        print('   ${_getMealName(meal.type)}: ${mealFoods.join(', ')}');
      }
      
      print('\n📊 Statistiche:');
      final allFoods = dailyPlan.meals.expand((m) => m.foods.map((fp) => fp.food.name)).toList();
      print('   - Alimenti totali: ${allFoods.length}');
      print('   - Alimenti unici: ${allFoods.toSet().length}');
      print('   - Varietà: ${(allFoods.toSet().length / allFoods.length * 100).toStringAsFixed(1)}%');
    } else {
      print('❌ Nessun piano generato');
    }
    
    print('\n🎯 ANALISI DEI LOG:');
    print('Controlla i log sopra per vedere se appaiono messaggi come:');
    print('   🔄 "Selezione alimento per categoria..."');
    print('   🎯 "_selectFoodWithVariety chiamato..."');
    print('   🎯 "_selectFoodWithVarietyForMacro chiamato..."');
    print('   ✅ "Selezionato: [nome alimento]..."');
    
    print('\nSe NON vedi questi messaggi, significa che i metodi di varietà');
    print('non vengono chiamati e il problema è nell\'integrazione.');
    
  } catch (e, stackTrace) {
    print('\n❌ ERRORE: $e');
    print('Stack trace: $stackTrace');
  }
}

String _getMealName(String mealType) {
  switch (mealType) {
    case 'breakfast': return 'Colazione';
    case 'lunch': return 'Pranzo';
    case 'dinner': return 'Cena';
    case 'snack': return 'Spuntino';
    default: return mealType;
  }
}
