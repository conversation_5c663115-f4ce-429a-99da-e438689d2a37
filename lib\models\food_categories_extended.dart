import 'package:flutter/material.dart';
import 'food.dart';

/// Categorie alimentari estese per una classificazione più dettagliata
enum FoodCategoryExtended {
  // Frutta
  fruitFresh,        // Frutta fresca
  fruitDried,        // Frutta secca/disidratata
  fruitFrozen,       // Frutta surgelata
  fruitCanned,       // Frutta in scatola/conservata
  fruitJuice,        // Succhi di frutta
  
  // Verdura
  vegetableLeafy,    // Verdure a foglia (spinaci, lattuga, ecc.)
  vegetableRoot,     // Verdure a radice (carote, patate, ecc.)
  vegetableCrucifers,// Crucifere (broccoli, cavolfiori, ecc.)
  vegetableAllium,   // Alliacee (cipolle, aglio, ecc.)
  vegetableNightshade,// Solanacee (pomodori, melanzane, ecc.)
  vegetableSquash,   // Zucche e zucchine
  vegetableFrozen,   // Verdure surgelate
  vegetableCanned,   // Verdure in scatola/conservate
  
  // Cereali e derivati
  grainWhole,        // Cereali integrali
  grainRefined,      // Cereali raffinati
  grainBread,        // Pane e prodotti da forno
  grainPasta,        // Pasta
  grainBreakfast,    // Cereali per colazione
  grainRice,         // Riso e altri cereali in chicco
  
  // Proteine
  proteinMeatRed,    // Carni rosse
  proteinMeatWhite,  // Carni bianche
  proteinMeatProcessed, // Carni lavorate/salumi
  proteinFish,       // Pesce
  proteinSeafood,    // Frutti di mare
  proteinEggs,       // Uova
  proteinLegumes,    // Legumi
  proteinNuts,       // Frutta a guscio
  proteinSeeds,      // Semi
  proteinTofu,       // Tofu e prodotti a base di soia
  
  // Latticini
  dairyMilk,         // Latte
  dairyYogurt,       // Yogurt
  dairyCheeseFresh,  // Formaggi freschi
  dairyCheeseAged,   // Formaggi stagionati
  dairyCream,        // Panna e creme
  dairyButter,       // Burro
  dairyAlternative,  // Alternative vegetali ai latticini
  
  // Grassi e oli
  fatOilOlive,       // Olio d'oliva
  fatOilSeed,        // Oli di semi
  fatAnimal,         // Grassi animali
  fatNut,            // Oli di frutta a guscio
  
  // Dolci e snack
  sweetBaked,        // Dolci da forno
  sweetChocolate,    // Cioccolato e derivati
  sweetCandy,        // Caramelle e dolciumi
  sweetIceCream,     // Gelati e dessert freddi
  sweetJamHoney,     // Marmellate, miele, sciroppi
  snackSalty,        // Snack salati
  snackBar,          // Barrette energetiche/proteiche
  
  // Bevande
  beverageWater,     // Acqua
  beverageTea,       // Tè e infusi
  beverageCoffee,    // Caffè
  beverageSoda,      // Bevande gassate
  beverageAlcohol,   // Bevande alcoliche
  beverageSports,    // Bevande sportive/energetiche
  beverageJuice,     // Succhi e spremute
  beveragePlant,     // Bevande vegetali
  
  // Piatti composti
  mixedSoup,         // Zuppe e minestre
  mixedSalad,        // Insalate composte
  mixedSandwich,     // Panini e tramezzini
  mixedPasta,        // Primi piatti
  mixedMain,         // Secondi piatti
  mixedSide,         // Contorni
  mixedPizza,        // Pizza
  mixedStew,         // Stufati e spezzatini
  
  // Alimenti funzionali e supplementi
  functionalProbiotic, // Alimenti probiotici
  functionalFortified, // Alimenti fortificati
  functionalSupplement, // Integratori alimentari
}

/// Estensione per aggiungere funzionalità alla enum FoodCategoryExtended
extension FoodCategoryExtendedExtension on FoodCategoryExtended {
  /// Ottieni la categoria principale corrispondente
  FoodCategory get mainCategory {
    switch (this) {
      case FoodCategoryExtended.fruitFresh:
      case FoodCategoryExtended.fruitDried:
      case FoodCategoryExtended.fruitFrozen:
      case FoodCategoryExtended.fruitCanned:
      case FoodCategoryExtended.fruitJuice:
        return FoodCategory.fruit;
        
      case FoodCategoryExtended.vegetableLeafy:
      case FoodCategoryExtended.vegetableRoot:
      case FoodCategoryExtended.vegetableCrucifers:
      case FoodCategoryExtended.vegetableAllium:
      case FoodCategoryExtended.vegetableNightshade:
      case FoodCategoryExtended.vegetableSquash:
      case FoodCategoryExtended.vegetableFrozen:
      case FoodCategoryExtended.vegetableCanned:
        return FoodCategory.vegetable;
        
      case FoodCategoryExtended.grainWhole:
      case FoodCategoryExtended.grainRefined:
      case FoodCategoryExtended.grainBread:
      case FoodCategoryExtended.grainPasta:
      case FoodCategoryExtended.grainBreakfast:
      case FoodCategoryExtended.grainRice:
        return FoodCategory.grain;
        
      case FoodCategoryExtended.proteinMeatRed:
      case FoodCategoryExtended.proteinMeatWhite:
      case FoodCategoryExtended.proteinMeatProcessed:
      case FoodCategoryExtended.proteinFish:
      case FoodCategoryExtended.proteinSeafood:
      case FoodCategoryExtended.proteinEggs:
      case FoodCategoryExtended.proteinLegumes:
      case FoodCategoryExtended.proteinNuts:
      case FoodCategoryExtended.proteinSeeds:
      case FoodCategoryExtended.proteinTofu:
        return FoodCategory.protein;
        
      case FoodCategoryExtended.dairyMilk:
      case FoodCategoryExtended.dairyYogurt:
      case FoodCategoryExtended.dairyCheeseFresh:
      case FoodCategoryExtended.dairyCheeseAged:
      case FoodCategoryExtended.dairyCream:
      case FoodCategoryExtended.dairyButter:
      case FoodCategoryExtended.dairyAlternative:
        return FoodCategory.dairy;
        
      case FoodCategoryExtended.fatOilOlive:
      case FoodCategoryExtended.fatOilSeed:
      case FoodCategoryExtended.fatAnimal:
      case FoodCategoryExtended.fatNut:
        return FoodCategory.fat;
        
      case FoodCategoryExtended.sweetBaked:
      case FoodCategoryExtended.sweetChocolate:
      case FoodCategoryExtended.sweetCandy:
      case FoodCategoryExtended.sweetIceCream:
      case FoodCategoryExtended.sweetJamHoney:
      case FoodCategoryExtended.snackSalty:
      case FoodCategoryExtended.snackBar:
        return FoodCategory.sweet;
        
      case FoodCategoryExtended.beverageWater:
      case FoodCategoryExtended.beverageTea:
      case FoodCategoryExtended.beverageCoffee:
      case FoodCategoryExtended.beverageSoda:
      case FoodCategoryExtended.beverageAlcohol:
      case FoodCategoryExtended.beverageSports:
      case FoodCategoryExtended.beverageJuice:
      case FoodCategoryExtended.beveragePlant:
        return FoodCategory.beverage;
        
      case FoodCategoryExtended.mixedSoup:
      case FoodCategoryExtended.mixedSalad:
      case FoodCategoryExtended.mixedSandwich:
      case FoodCategoryExtended.mixedPasta:
      case FoodCategoryExtended.mixedMain:
      case FoodCategoryExtended.mixedSide:
      case FoodCategoryExtended.mixedPizza:
      case FoodCategoryExtended.mixedStew:
      case FoodCategoryExtended.functionalProbiotic:
      case FoodCategoryExtended.functionalFortified:
      case FoodCategoryExtended.functionalSupplement:
        return FoodCategory.mixed;
    }
  }
  
  /// Ottieni il nome visualizzato della categoria
  String get displayName {
    switch (this) {
      // Frutta
      case FoodCategoryExtended.fruitFresh: return 'Frutta fresca';
      case FoodCategoryExtended.fruitDried: return 'Frutta secca';
      case FoodCategoryExtended.fruitFrozen: return 'Frutta surgelata';
      case FoodCategoryExtended.fruitCanned: return 'Frutta in conserva';
      case FoodCategoryExtended.fruitJuice: return 'Succhi di frutta';
      
      // Verdura
      case FoodCategoryExtended.vegetableLeafy: return 'Verdure a foglia';
      case FoodCategoryExtended.vegetableRoot: return 'Verdure a radice';
      case FoodCategoryExtended.vegetableCrucifers: return 'Crucifere';
      case FoodCategoryExtended.vegetableAllium: return 'Alliacee';
      case FoodCategoryExtended.vegetableNightshade: return 'Solanacee';
      case FoodCategoryExtended.vegetableSquash: return 'Zucche e zucchine';
      case FoodCategoryExtended.vegetableFrozen: return 'Verdure surgelate';
      case FoodCategoryExtended.vegetableCanned: return 'Verdure in conserva';
      
      // Cereali
      case FoodCategoryExtended.grainWhole: return 'Cereali integrali';
      case FoodCategoryExtended.grainRefined: return 'Cereali raffinati';
      case FoodCategoryExtended.grainBread: return 'Pane e prodotti da forno';
      case FoodCategoryExtended.grainPasta: return 'Pasta';
      case FoodCategoryExtended.grainBreakfast: return 'Cereali per colazione';
      case FoodCategoryExtended.grainRice: return 'Riso e altri cereali';
      
      // Proteine
      case FoodCategoryExtended.proteinMeatRed: return 'Carni rosse';
      case FoodCategoryExtended.proteinMeatWhite: return 'Carni bianche';
      case FoodCategoryExtended.proteinMeatProcessed: return 'Salumi e carni lavorate';
      case FoodCategoryExtended.proteinFish: return 'Pesce';
      case FoodCategoryExtended.proteinSeafood: return 'Frutti di mare';
      case FoodCategoryExtended.proteinEggs: return 'Uova';
      case FoodCategoryExtended.proteinLegumes: return 'Legumi';
      case FoodCategoryExtended.proteinNuts: return 'Frutta a guscio';
      case FoodCategoryExtended.proteinSeeds: return 'Semi';
      case FoodCategoryExtended.proteinTofu: return 'Tofu e derivati della soia';
      
      // Latticini
      case FoodCategoryExtended.dairyMilk: return 'Latte';
      case FoodCategoryExtended.dairyYogurt: return 'Yogurt';
      case FoodCategoryExtended.dairyCheeseFresh: return 'Formaggi freschi';
      case FoodCategoryExtended.dairyCheeseAged: return 'Formaggi stagionati';
      case FoodCategoryExtended.dairyCream: return 'Panna e creme';
      case FoodCategoryExtended.dairyButter: return 'Burro';
      case FoodCategoryExtended.dairyAlternative: return 'Alternative vegetali ai latticini';
      
      // Grassi
      case FoodCategoryExtended.fatOilOlive: return 'Olio d\'oliva';
      case FoodCategoryExtended.fatOilSeed: return 'Oli di semi';
      case FoodCategoryExtended.fatAnimal: return 'Grassi animali';
      case FoodCategoryExtended.fatNut: return 'Oli di frutta a guscio';
      
      // Dolci e snack
      case FoodCategoryExtended.sweetBaked: return 'Dolci da forno';
      case FoodCategoryExtended.sweetChocolate: return 'Cioccolato e derivati';
      case FoodCategoryExtended.sweetCandy: return 'Caramelle e dolciumi';
      case FoodCategoryExtended.sweetIceCream: return 'Gelati e dessert freddi';
      case FoodCategoryExtended.sweetJamHoney: return 'Marmellate e miele';
      case FoodCategoryExtended.snackSalty: return 'Snack salati';
      case FoodCategoryExtended.snackBar: return 'Barrette energetiche';
      
      // Bevande
      case FoodCategoryExtended.beverageWater: return 'Acqua';
      case FoodCategoryExtended.beverageTea: return 'Tè e infusi';
      case FoodCategoryExtended.beverageCoffee: return 'Caffè';
      case FoodCategoryExtended.beverageSoda: return 'Bevande gassate';
      case FoodCategoryExtended.beverageAlcohol: return 'Bevande alcoliche';
      case FoodCategoryExtended.beverageSports: return 'Bevande sportive';
      case FoodCategoryExtended.beverageJuice: return 'Succhi e spremute';
      case FoodCategoryExtended.beveragePlant: return 'Bevande vegetali';
      
      // Piatti composti
      case FoodCategoryExtended.mixedSoup: return 'Zuppe e minestre';
      case FoodCategoryExtended.mixedSalad: return 'Insalate composte';
      case FoodCategoryExtended.mixedSandwich: return 'Panini e tramezzini';
      case FoodCategoryExtended.mixedPasta: return 'Primi piatti';
      case FoodCategoryExtended.mixedMain: return 'Secondi piatti';
      case FoodCategoryExtended.mixedSide: return 'Contorni';
      case FoodCategoryExtended.mixedPizza: return 'Pizza';
      case FoodCategoryExtended.mixedStew: return 'Stufati e spezzatini';
      
      // Alimenti funzionali
      case FoodCategoryExtended.functionalProbiotic: return 'Alimenti probiotici';
      case FoodCategoryExtended.functionalFortified: return 'Alimenti fortificati';
      case FoodCategoryExtended.functionalSupplement: return 'Integratori alimentari';
    }
  }
  
  /// Ottieni l'icona associata alla categoria
  IconData get icon {
    switch (this) {
      // Frutta
      case FoodCategoryExtended.fruitFresh: return Icons.apple;
      case FoodCategoryExtended.fruitDried: return Icons.grain;
      case FoodCategoryExtended.fruitFrozen: return Icons.ac_unit;
      case FoodCategoryExtended.fruitCanned: return Icons.inventory_2;
      case FoodCategoryExtended.fruitJuice: return Icons.local_drink;
      
      // Verdura
      case FoodCategoryExtended.vegetableLeafy: return Icons.eco;
      case FoodCategoryExtended.vegetableRoot: return Icons.spa;
      case FoodCategoryExtended.vegetableCrucifers: return Icons.grass;
      case FoodCategoryExtended.vegetableAllium: return Icons.spa;
      case FoodCategoryExtended.vegetableNightshade: return Icons.brightness_1;
      case FoodCategoryExtended.vegetableSquash: return Icons.circle;
      case FoodCategoryExtended.vegetableFrozen: return Icons.ac_unit;
      case FoodCategoryExtended.vegetableCanned: return Icons.inventory_2;
      
      // Cereali
      case FoodCategoryExtended.grainWhole: return Icons.grain;
      case FoodCategoryExtended.grainRefined: return Icons.grain;
      case FoodCategoryExtended.grainBread: return Icons.bakery_dining;
      case FoodCategoryExtended.grainPasta: return Icons.ramen_dining;
      case FoodCategoryExtended.grainBreakfast: return Icons.free_breakfast;
      case FoodCategoryExtended.grainRice: return Icons.rice_bowl;
      
      // Proteine
      case FoodCategoryExtended.proteinMeatRed: return Icons.restaurant_menu;
      case FoodCategoryExtended.proteinMeatWhite: return Icons.restaurant_menu;
      case FoodCategoryExtended.proteinMeatProcessed: return Icons.restaurant_menu;
      case FoodCategoryExtended.proteinFish: return Icons.set_meal;
      case FoodCategoryExtended.proteinSeafood: return Icons.set_meal;
      case FoodCategoryExtended.proteinEggs: return Icons.egg;
      case FoodCategoryExtended.proteinLegumes: return Icons.grain;
      case FoodCategoryExtended.proteinNuts: return Icons.grain;
      case FoodCategoryExtended.proteinSeeds: return Icons.grain;
      case FoodCategoryExtended.proteinTofu: return Icons.square;
      
      // Latticini
      case FoodCategoryExtended.dairyMilk: return Icons.opacity;
      case FoodCategoryExtended.dairyYogurt: return Icons.kitchen;
      case FoodCategoryExtended.dairyCheeseFresh: return Icons.kitchen;
      case FoodCategoryExtended.dairyCheeseAged: return Icons.kitchen;
      case FoodCategoryExtended.dairyCream: return Icons.opacity;
      case FoodCategoryExtended.dairyButter: return Icons.kitchen;
      case FoodCategoryExtended.dairyAlternative: return Icons.opacity;
      
      // Grassi
      case FoodCategoryExtended.fatOilOlive: return Icons.water_drop;
      case FoodCategoryExtended.fatOilSeed: return Icons.water_drop;
      case FoodCategoryExtended.fatAnimal: return Icons.water_drop;
      case FoodCategoryExtended.fatNut: return Icons.water_drop;
      
      // Dolci e snack
      case FoodCategoryExtended.sweetBaked: return Icons.cake;
      case FoodCategoryExtended.sweetChocolate: return Icons.cookie;
      case FoodCategoryExtended.sweetCandy: return Icons.cookie;
      case FoodCategoryExtended.sweetIceCream: return Icons.icecream;
      case FoodCategoryExtended.sweetJamHoney: return Icons.water_drop;
      case FoodCategoryExtended.snackSalty: return Icons.fastfood;
      case FoodCategoryExtended.snackBar: return Icons.breakfast_dining;
      
      // Bevande
      case FoodCategoryExtended.beverageWater: return Icons.water;
      case FoodCategoryExtended.beverageTea: return Icons.emoji_food_beverage;
      case FoodCategoryExtended.beverageCoffee: return Icons.coffee;
      case FoodCategoryExtended.beverageSoda: return Icons.local_drink;
      case FoodCategoryExtended.beverageAlcohol: return Icons.wine_bar;
      case FoodCategoryExtended.beverageSports: return Icons.sports_bar;
      case FoodCategoryExtended.beverageJuice: return Icons.local_drink;
      case FoodCategoryExtended.beveragePlant: return Icons.local_drink;
      
      // Piatti composti
      case FoodCategoryExtended.mixedSoup: return Icons.soup_kitchen;
      case FoodCategoryExtended.mixedSalad: return Icons.restaurant;
      case FoodCategoryExtended.mixedSandwich: return Icons.lunch_dining;
      case FoodCategoryExtended.mixedPasta: return Icons.ramen_dining;
      case FoodCategoryExtended.mixedMain: return Icons.dinner_dining;
      case FoodCategoryExtended.mixedSide: return Icons.restaurant;
      case FoodCategoryExtended.mixedPizza: return Icons.local_pizza;
      case FoodCategoryExtended.mixedStew: return Icons.soup_kitchen;
      
      // Alimenti funzionali
      case FoodCategoryExtended.functionalProbiotic: return Icons.health_and_safety;
      case FoodCategoryExtended.functionalFortified: return Icons.health_and_safety;
      case FoodCategoryExtended.functionalSupplement: return Icons.medication;
    }
  }
}
