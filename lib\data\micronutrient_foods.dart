import '../models/food.dart';
import '../utils/micronutrients_helper.dart';

/// Database di alimenti con dati completi sui micronutrienti
class MicronutrientFoods {
  static List<Food> getDairyFoods() {
    return [
      Food(
        id: 'micro_dairy_1',
        name: 'Latte vaccino intero',
        description: 'Latte vaccino intero (3,6% grassi)',
        imageUrl: 'https://images.unsplash.com/photo-1550583724-b2692b85b150?q=80&w=500',
        calories: 64,
        proteins: 3.3,
        carbs: 4.8,
        fats: 3.6,
        fiber: 0.0,
        sugar: 4.8,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.dairy, FoodCategory.beverage],
        isVegetarian: true,
        allergens: const ['latticini'],
        servingSize: '100ml',
        servingSizeGrams: 103,
        foodState: FoodState.prepared,
        volumeToWeightFactor: 1.03,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['latte', 'intero', 'base', 'colazione'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 119.0,
          phosphorus: 93.0,
          magnesium: 12.0,
          sodium: 50.0,
          potassium: 150.0,
        ),
      ),

      Food(
        id: 'micro_dairy_2',
        name: 'Yogurt di latte intero',
        description: 'Yogurt bianco intero',
        imageUrl: 'https://images.unsplash.com/photo-1488477181946-6428a0291777?q=80&w=500',
        calories: 66,
        proteins: 3.8,
        carbs: 4.7,
        fats: 3.3,
        fiber: 0.0,
        sugar: 4.7,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.dairy],
        isVegetarian: true,
        allergens: const ['latticini'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['yogurt', 'intero', 'base', 'colazione'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 125.0,
          phosphorus: 105.0,
          magnesium: 12.0,
          sodium: 48.0,
          potassium: 170.0,
        ),
      ),

      Food(
        id: 'micro_dairy_3',
        name: 'Parmigiano',
        description: 'Parmigiano stagionato',
        imageUrl: 'https://images.unsplash.com/photo-1528747045269-390fe33c19f2?q=80&w=500',
        calories: 387,
        proteins: 33.5,
        carbs: 0.0,
        fats: 30.0,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner, MealType.snack],
        categories: const [FoodCategory.dairy],
        isVegetarian: true,
        allergens: const ['latticini'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['formaggio', 'stagionato', 'dop', 'italiano'],
        italianRegions: const [ItalianRegion.emiliaRomagna, ItalianRegion.lombardia],
        isTraditionalItalian: true,
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 1159.0,
          phosphorus: 678.0,
          magnesium: 43.0,
          sodium: 600.0,
          potassium: 102.0,
        ),
      ),

      Food(
        id: 'micro_dairy_4',
        name: 'Grana',
        description: 'Grana stagionato',
        imageUrl: 'https://images.unsplash.com/photo-1528747045269-390fe33c19f2?q=80&w=500',
        calories: 406,
        proteins: 33.9,
        carbs: 0.0,
        fats: 30.0,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner, MealType.snack],
        categories: const [FoodCategory.dairy],
        isVegetarian: true,
        allergens: const ['latticini'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['formaggio', 'stagionato', 'dop', 'italiano'],
        italianRegions: const [ItalianRegion.emiliaRomagna, ItalianRegion.lombardia],
        isTraditionalItalian: true,
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 1169.0,
          phosphorus: 692.0,
          magnesium: 63.0,
          sodium: 700.0,
          potassium: 120.0,
        ),
      ),

      Food(
        id: 'micro_dairy_5',
        name: 'Mozzarella di mucca',
        description: 'Mozzarella fresca vaccina',
        imageUrl: 'https://images.unsplash.com/photo-1551504734-5ee1c4a1479b?q=80&w=500',
        calories: 253,
        proteins: 18.7,
        carbs: 2.5,
        fats: 19.0,
        fiber: 0.0,
        sugar: 2.5,
        suitableForMeals: const [MealType.lunch, MealType.dinner], // Rimossa da snack - troppo pesante
        categories: const [FoodCategory.dairy],
        isVegetarian: true,
        allergens: const ['latticini'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['formaggio', 'fresco', 'mozzarella', 'italiano'],
        italianRegions: const [ItalianRegion.campania],
        isTraditionalItalian: true,
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 500.0,
          phosphorus: 350.0,
          magnesium: 10.0,
          sodium: 200.0,
          potassium: 145.0,
        ),
      ),

      Food(
        id: 'micro_dairy_6',
        name: 'Ricotta di mucca',
        description: 'Ricotta fresca vaccina',
        imageUrl: 'https://images.unsplash.com/photo-1551504734-5ee1c4a1479b?q=80&w=500',
        calories: 146,
        proteins: 8.8,
        carbs: 3.5,
        fats: 13.0,
        fiber: 0.0,
        sugar: 3.5,
        suitableForMeals: const [MealType.breakfast, MealType.lunch, MealType.dinner, MealType.snack],
        categories: const [FoodCategory.dairy],
        isVegetarian: true,
        allergens: const ['latticini'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['formaggio', 'fresco', 'ricotta', 'italiano'],
        italianRegions: const [],
        isTraditionalItalian: true,
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 295.0,
          phosphorus: 237.0,
          magnesium: 17.0,
          sodium: 78.0,
          potassium: 119.0,
        ),
      ),
    ];
  }

  static List<Food> getEggFoods() {
    return [
      Food(
        id: 'micro_egg_1',
        name: 'Uovo intero',
        description: 'Uovo di gallina intero',
        imageUrl: 'https://images.unsplash.com/photo-1607690424560-35d967d6ad7a?q=80&w=500',
        calories: 128,
        proteins: 12.4,
        carbs: 1.1,
        fats: 9.5,
        fiber: 0.0,
        sugar: 1.1,
        suitableForMeals: const [MealType.breakfast, MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isVegetarian: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['uova'],
        servingSize: '100g (circa 2 uova)',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['uova', 'base', 'proteico', 'colazione'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 48.0,
          phosphorus: 210.0,
          magnesium: 13.0,
          sodium: 137.0,
          potassium: 133.0,
        ),
      ),

      Food(
        id: 'micro_egg_2',
        name: 'Albume',
        description: 'Albume d\'uovo',
        imageUrl: 'https://images.unsplash.com/photo-1607690424560-35d967d6ad7a?q=80&w=500',
        calories: 43,
        proteins: 10.7,
        carbs: 0.7,
        fats: 0.2,
        fiber: 0.0,
        sugar: 0.7,
        suitableForMeals: const [MealType.breakfast, MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isVegetarian: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['uova'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['uova', 'albumi', 'base', 'proteico', 'magro'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 7.0,
          phosphorus: 15.0,
          magnesium: 12.0,
          sodium: 179.0,
          potassium: 135.0,
        ),
      ),

      Food(
        id: 'micro_egg_3',
        name: 'Tuorlo',
        description: 'Tuorlo d\'uovo',
        imageUrl: 'https://images.unsplash.com/photo-1607690424560-35d967d6ad7a?q=80&w=500',
        calories: 325,
        proteins: 15.8,
        carbs: 3.6,
        fats: 27.5,
        fiber: 0.0,
        sugar: 3.6,
        suitableForMeals: const [MealType.breakfast, MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isVegetarian: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['uova'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['uova', 'tuorlo', 'base', 'proteico'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 116.0,
          phosphorus: 586.0,
          magnesium: 14.0,
          sodium: 43.0,
          potassium: 90.0,
        ),
      ),
    ];
  }

  static List<Food> getMeatFoods() {
    return [
      Food(
        id: 'micro_meat_1',
        name: 'Carne bovina (fesa)',
        description: 'Carne di manzo magra (fesa)',
        imageUrl: 'https://images.unsplash.com/photo-1588168333986-5078d3ae3976?q=80&w=500',
        calories: 103,
        proteins: 21.8,
        carbs: 0.0,
        fats: 3.5,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g crudo',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        rawToCookedFactor: 0.7,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['carne', 'manzo', 'base', 'proteico'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 4.0,
          phosphorus: 214.0,
          magnesium: 18.0,
          sodium: 41.0,
          potassium: 342.0,
        ),
      ),

      Food(
        id: 'micro_meat_2',
        name: 'Maiale magro',
        description: 'Carne di maiale magra',
        imageUrl: 'https://images.unsplash.com/photo-1602470520998-f4a52199a3d6?q=80&w=500',
        calories: 157,
        proteins: 21.3,
        carbs: 0.0,
        fats: 9.5,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g crudo',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        rawToCookedFactor: 0.8,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['carne', 'maiale', 'base', 'proteico'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 8.0,
          phosphorus: 180.0,
          magnesium: 17.0,
          sodium: 73.0,
          potassium: 220.0,
        ),
      ),

      Food(
        id: 'micro_meat_3',
        name: 'Pollo (petto)',
        description: 'Petto di pollo crudo senza pelle',
        imageUrl: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?q=80&w=500',
        calories: 100,
        proteins: 23.3,
        carbs: 0.0,
        fats: 0.8,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g crudo',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        rawToCookedFactor: 0.8,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['carne', 'pollo', 'base', 'magro', 'proteico'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 4.0,
          phosphorus: 210.0,
          magnesium: 32.0,
          sodium: 33.0,
          potassium: 370.0,
        ),
      ),

      Food(
        id: 'micro_meat_4',
        name: 'Tacchino (fesa)',
        description: 'Fesa di tacchino cruda senza pelle',
        imageUrl: 'https://images.unsplash.com/photo-1602470520998-f4a52199a3d6?q=80&w=500',
        calories: 107,
        proteins: 24.0,
        carbs: 0.0,
        fats: 1.0,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g crudo',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        rawToCookedFactor: 0.8,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['carne', 'tacchino', 'base', 'magro', 'proteico'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 6.0,
          phosphorus: 200.0,
          magnesium: 32.0,
          sodium: 43.0,
          potassium: 320.0,
        ),
      ),

      Food(
        id: 'micro_meat_5',
        name: 'Prosciutto crudo di Parma',
        description: 'Prosciutto crudo di Parma DOP',
        imageUrl: 'https://images.unsplash.com/photo-1625938144755-652e08e359b7?q=80&w=500',
        calories: 268,
        proteins: 25.5,
        carbs: 0.0,
        fats: 17.0,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.breakfast, MealType.lunch, MealType.dinner, MealType.snack],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['carne', 'salume', 'prosciutto', 'italiano', 'dop'],
        italianRegions: const [ItalianRegion.emiliaRomagna],
        isTraditionalItalian: true,
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 16.0,
          phosphorus: 261.0,
          magnesium: 26.0,
          sodium: 2578.0,
          potassium: 373.0,
        ),
      ),
    ];
  }

  static List<Food> getFishFoods() {
    return [
      Food(
        id: 'micro_fish_1',
        name: 'Merluzzo',
        description: 'Merluzzo fresco',
        imageUrl: 'https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?q=80&w=500',
        calories: 71,
        proteins: 17.0,
        carbs: 0.0,
        fats: 0.0,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['pesce'],
        servingSize: '100g crudo',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        rawToCookedFactor: 0.8,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['pesce', 'merluzzo', 'base', 'magro', 'proteico'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 31.0,
          phosphorus: 218.0,
          magnesium: 25.0,
          sodium: 72.0,
          potassium: 356.0,
        ),
      ),

      Food(
        id: 'micro_fish_2',
        name: 'Salmone',
        description: 'Salmone fresco',
        imageUrl: 'https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?q=80&w=500',
        calories: 185,
        proteins: 18.4,
        carbs: 0.0,
        fats: 12.0,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['pesce'],
        servingSize: '100g crudo',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        rawToCookedFactor: 0.8,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['pesce', 'salmone', 'base', 'proteico', 'omega3'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 27.0,
          phosphorus: 280.0,
          magnesium: 0.0,
          sodium: 98.0,
          potassium: 310.0,
        ),
      ),

      Food(
        id: 'micro_fish_3',
        name: 'Tonno',
        description: 'Tonno fresco',
        imageUrl: 'https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?q=80&w=500',
        calories: 159,
        proteins: 21.5,
        carbs: 0.0,
        fats: 8.0,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['pesce'],
        servingSize: '100g crudo',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        rawToCookedFactor: 0.8,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['pesce', 'tonno', 'base', 'proteico', 'omega3'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 38.0,
          phosphorus: 264.0,
          magnesium: 26.0,
          sodium: 43.0,
          potassium: 0.0,
        ),
      ),

      Food(
        id: 'micro_fish_4',
        name: 'Gambero',
        description: 'Gamberi freschi',
        imageUrl: 'https://images.unsplash.com/photo-1565680018160-64b74dd0fd7b?q=80&w=500',
        calories: 71,
        proteins: 13.6,
        carbs: 0.0,
        fats: 0.8,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['crostacei'],
        servingSize: '100g crudo',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        rawToCookedFactor: 0.8,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['pesce', 'crostacei', 'gamberi', 'base', 'magro', 'proteico'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 110.0,
          phosphorus: 349.0,
          magnesium: 39.0,
          sodium: 146.0,
          potassium: 266.0,
        ),
      ),
    ];
  }

  static List<Food> getVegetableFoods() {
    return [
      Food(
        id: 'micro_veg_1',
        name: 'Spinaci',
        description: 'Spinaci freschi crudi',
        imageUrl: 'https://images.unsplash.com/photo-1576064535185-c2526884001c?q=80&w=500',
        calories: 31,
        proteins: 3.4,
        carbs: 1.1,
        fats: 0.4,
        fiber: 2.2,
        sugar: 0.4,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [9, 10, 11, 12, 1, 2, 3, 4, 5],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'fresco', 'foglia', 'stagionale'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 78.0,
          phosphorus: 62.0,
          magnesium: 60.0,
          sodium: 100.0,
          potassium: 530.0,
          iron: 2.7,
        ),
      ),

      Food(
        id: 'micro_veg_2',
        name: 'Carote',
        description: 'Carote fresche',
        imageUrl: 'https://images.unsplash.com/photo-1598170845058-32b9d6a5da37?q=80&w=500',
        calories: 35,
        proteins: 1.1,
        carbs: 10.0,
        fats: 0.2,
        fiber: 2.8,
        sugar: 4.7,
        suitableForMeals: const [MealType.lunch, MealType.dinner, MealType.snack],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['verdura', 'fresco', 'radice'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 44.0,
          phosphorus: 37.0,
          magnesium: 11.0,
          sodium: 95.0,
          potassium: 220.0,
          vitaminA: 1200.0,
        ),
      ),
    ];
  }

  static List<Food> getFruitFoods() {
    return [
      Food(
        id: 'micro_fruit_1',
        name: 'Arance',
        description: 'Arance fresche',
        imageUrl: 'https://images.unsplash.com/photo-1611080626919-7cf5a9dbab12?q=80&w=500',
        calories: 34,
        proteins: 0.7,
        carbs: 12.0,
        fats: 0.1,
        fiber: 2.4,
        sugar: 9.0,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [11, 12, 1, 2, 3, 4],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'fresco', 'agrumi', 'stagionale'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 49.0,
          phosphorus: 22.0,
          magnesium: 14.0,
          sodium: 3.0,
          potassium: 200.0,
          vitaminC: 50.0,
        ),
      ),

      Food(
        id: 'micro_fruit_2',
        name: 'Mele',
        description: 'Mele fresche con buccia',
        imageUrl: 'https://images.unsplash.com/photo-1570913149827-d2ac84ab3f9a?q=80&w=500',
        calories: 53,
        proteins: 0.3,
        carbs: 14.0,
        fats: 0.2,
        fiber: 2.4,
        sugar: 10.0,
        suitableForMeals: const [MealType.breakfast, MealType.snack],
        categories: const [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [9, 10, 11, 12, 1, 2, 3],
        dataSource: DataSource.crea,
        validationStatus: ValidationStatus.validated,
        tags: const ['frutta', 'fresco', 'stagionale'],
        micronutrients: MicronutrientsHelper.createMicronutrientsMap(
          calcium: 7.0,
          phosphorus: 12.0,
          magnesium: 6.0,
          sodium: 2.0,
          potassium: 125.0,
        ),
      ),
    ];
  }

  static List<Food> getAllFoods() {
    return [
      ...getDairyFoods(),
      ...getEggFoods(),
      ...getMeatFoods(),
      ...getFishFoods(),
      ...getVegetableFoods(),
      ...getFruitFoods(),
    ];
  }
}
