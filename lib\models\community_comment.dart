import 'package:json_annotation/json_annotation.dart';
import 'community_user.dart';

part 'community_comment.g.dart';

/// Modello per i commenti dei post della community
@JsonSerializable()
class CommunityComment {
  /// ID univoco del commento
  final String id;
  
  /// ID del post a cui appartiene il commento
  final String postId;
  
  /// Autore del commento
  final CommunityUser author;
  
  /// Contenuto del commento
  final String content;
  
  /// Data e ora di creazione
  final DateTime createdAt;
  
  /// Data e ora di ultima modifica
  final DateTime? updatedAt;
  
  /// Numero di like ricevuti
  final int likesCount;
  
  /// Se il commento è stato messo like dall'utente corrente
  final bool isLikedByCurrentUser;
  
  /// Se il commento è stato modificato
  final bool isEdited;
  
  /// Metadati aggiuntivi
  final Map<String, dynamic>? metadata;

  const CommunityComment({
    required this.id,
    required this.postId,
    required this.author,
    required this.content,
    required this.createdAt,
    this.updatedAt,
    this.likesCount = 0,
    this.isLikedByCurrentUser = false,
    this.isEdited = false,
    this.metadata,
  });

  /// Factory constructor per creare un nuovo commento
  factory CommunityComment.create({
    required String postId,
    required CommunityUser author,
    required String content,
    Map<String, dynamic>? metadata,
  }) {
    return CommunityComment(
      id: 'comment_${DateTime.now().millisecondsSinceEpoch}_${author.id}',
      postId: postId,
      author: author,
      content: content,
      createdAt: DateTime.now(),
      metadata: metadata,
    );
  }

  /// Crea una copia del commento con modifiche
  CommunityComment copyWith({
    String? id,
    String? postId,
    CommunityUser? author,
    String? content,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? likesCount,
    bool? isLikedByCurrentUser,
    bool? isEdited,
    Map<String, dynamic>? metadata,
  }) {
    return CommunityComment(
      id: id ?? this.id,
      postId: postId ?? this.postId,
      author: author ?? this.author,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      likesCount: likesCount ?? this.likesCount,
      isLikedByCurrentUser: isLikedByCurrentUser ?? this.isLikedByCurrentUser,
      isEdited: isEdited ?? this.isEdited,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Marca il commento come modificato
  CommunityComment markAsEdited(String newContent) {
    return copyWith(
      content: newContent,
      updatedAt: DateTime.now(),
      isEdited: true,
    );
  }

  /// Toggle del like per il commento
  CommunityComment toggleLike() {
    return copyWith(
      isLikedByCurrentUser: !isLikedByCurrentUser,
      likesCount: isLikedByCurrentUser ? likesCount - 1 : likesCount + 1,
    );
  }

  /// Tempo relativo dalla creazione (es. "2 ore fa")
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays} ${difference.inDays == 1 ? 'giorno' : 'giorni'} fa';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ${difference.inHours == 1 ? 'ora' : 'ore'} fa';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minuto' : 'minuti'} fa';
    } else {
      return 'Ora';
    }
  }

  /// Tempo relativo dall'ultima modifica (se modificato)
  String? get editedTimeAgo {
    if (!isEdited || updatedAt == null) return null;
    
    final now = DateTime.now();
    final difference = now.difference(updatedAt!);

    if (difference.inDays > 0) {
      return 'modificato ${difference.inDays} ${difference.inDays == 1 ? 'giorno' : 'giorni'} fa';
    } else if (difference.inHours > 0) {
      return 'modificato ${difference.inHours} ${difference.inHours == 1 ? 'ora' : 'ore'} fa';
    } else if (difference.inMinutes > 0) {
      return 'modificato ${difference.inMinutes} ${difference.inMinutes == 1 ? 'minuto' : 'minuti'} fa';
    } else {
      return 'modificato ora';
    }
  }

  /// Anteprima del contenuto (primi 100 caratteri)
  String get contentPreview {
    if (content.length <= 100) return content;
    return '${content.substring(0, 100)}...';
  }

  /// Verifica se il commento è recente (meno di 1 ora)
  bool get isRecent {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    return difference.inHours < 1;
  }

  /// Serializzazione JSON
  factory CommunityComment.fromJson(Map<String, dynamic> json) =>
      _$CommunityCommentFromJson(json);

  Map<String, dynamic> toJson() => _$CommunityCommentToJson(this);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CommunityComment &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CommunityComment{id: $id, postId: $postId, author: ${author.displayName}, content: ${contentPreview}}';
  }
}
