import '../models/food.dart';

/// Database di alimenti base con dati nutrizionali accurati
class BasicFoods {
  static List<Food> getBasicFoods() {
    return [
      // CEREALI E DERIVATI
      Food(
        id: 'basic_grain_1',
        name: 'Riso integrale',
        description: 'Riso integrale cotto',
        imageUrl: 'https://images.unsplash.com/photo-1536304993881-ff6e9eefa2a6?q=80&w=500',
        calories: 111,
        proteins: 2.6,
        carbs: 23.5,
        fats: 0.9,
        fiber: 1.8,
        sugar: 0.3,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 3.0,
        glycemicIndex: 55,
        glycemicLoad: 13,
        micronutrients: const {
          'magnesio': 44.0, // mg
          'fosforo': 83.0, // mg
          'potassio': 79.0, // mg
          'zinco': 0.6, // mg
          'vitamina B1': 0.1, // mg
          'vitamina B3': 1.6, // mg
          'vitamina B6': 0.15, // mg
        },
        dataSource: DataSource.crea,
        sourceId: 'CREA-AN-RI001',
        sourceDescription: 'Tabelle di Composizione degli Alimenti CREA',
        validationStatus: ValidationStatus.validated,
        lastValidatedAt: DateTime(2023, 4, 10),
        validatedBy: 'Nutrizionista CREA',
        tags: const ['cereale', 'base', 'integrale', 'senza glutine'],
        commonServingSize1Description: '1 porzione',
        commonServingSize1Grams: 80,
      ),
      
      Food(
        id: 'basic_grain_2',
        name: 'Pasta di semola',
        description: 'Pasta di semola di grano duro cotta',
        imageUrl: 'https://images.unsplash.com/photo-1551462147-ff29053bfc14?q=80&w=500',
        calories: 158,
        proteins: 5.8,
        carbs: 31.0,
        fats: 0.6,
        fiber: 1.8,
        sugar: 0.9,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        allergens: const ['glutine'],
        servingSize: '100g cotta',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 2.5,
        glycemicIndex: 50,
        glycemicLoad: 15,
        micronutrients: const {
          'ferro': 1.0, // mg
          'fosforo': 69.0, // mg
          'potassio': 44.0, // mg
          'vitamina B1': 0.05, // mg
          'vitamina B2': 0.03, // mg
          'vitamina B3': 0.7, // mg
        },
        dataSource: DataSource.crea,
        sourceId: 'CREA-AN-PA001',
        sourceDescription: 'Tabelle di Composizione degli Alimenti CREA',
        validationStatus: ValidationStatus.validated,
        lastValidatedAt: DateTime(2023, 4, 15),
        validatedBy: 'Nutrizionista CREA',
        tags: const ['cereale', 'base', 'pasta', 'grano duro'],
        commonServingSize1Description: '1 porzione',
        commonServingSize1Grams: 80,
      ),
      
      Food(
        id: 'basic_grain_3',
        name: 'Pane integrale',
        description: 'Pane con farina integrale',
        imageUrl: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?q=80&w=500',
        calories: 224,
        proteins: 8.5,
        carbs: 41.3,
        fats: 1.9,
        fiber: 6.5,
        sugar: 1.8,
        suitableForMeals: const [MealType.breakfast, MealType.lunch, MealType.dinner, MealType.snack],
        categories: const [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        allergens: const ['glutine'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        glycemicIndex: 65,
        glycemicLoad: 27,
        micronutrients: const {
          'ferro': 2.5, // mg
          'calcio': 58.0, // mg
          'fosforo': 200.0, // mg
          'potassio': 230.0, // mg
          'magnesio': 76.0, // mg
          'zinco': 1.8, // mg
          'vitamina B1': 0.3, // mg
          'vitamina B3': 3.0, // mg
          'vitamina B6': 0.15, // mg
        },
        dataSource: DataSource.crea,
        sourceId: 'CREA-AN-PI001',
        sourceDescription: 'Tabelle di Composizione degli Alimenti CREA',
        validationStatus: ValidationStatus.validated,
        lastValidatedAt: DateTime(2023, 4, 20),
        validatedBy: 'Nutrizionista CREA',
        tags: const ['cereale', 'base', 'pane', 'integrale'],
        commonServingSize1Description: '1 fetta',
        commonServingSize1Grams: 50,
      ),
      
      // PROTEINE ANIMALI
      Food(
        id: 'basic_protein_1',
        name: 'Petto di pollo',
        description: 'Petto di pollo cotto alla griglia senza pelle',
        imageUrl: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?q=80&w=500',
        calories: 165,
        proteins: 31.0,
        carbs: 0.0,
        fats: 3.6,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 0.8, // perde peso durante la cottura
        glycemicIndex: 0,
        glycemicLoad: 0,
        micronutrients: const {
          'vitamina B3': 13.0, // mg
          'vitamina B6': 0.6, // mg
          'vitamina B12': 0.3, // μg
          'fosforo': 210.0, // mg
          'potassio': 320.0, // mg
          'selenio': 24.0, // μg
          'zinco': 1.0, // mg
        },
        dataSource: DataSource.crea,
        sourceId: 'CREA-AN-PC001',
        sourceDescription: 'Tabelle di Composizione degli Alimenti CREA',
        validationStatus: ValidationStatus.validated,
        lastValidatedAt: DateTime(2023, 5, 5),
        validatedBy: 'Nutrizionista CREA',
        tags: const ['carne', 'pollo', 'base', 'magro', 'proteico'],
        commonServingSize1Description: '1 fetta',
        commonServingSize1Grams: 100,
      ),
      
      Food(
        id: 'basic_protein_2',
        name: 'Salmone fresco',
        description: 'Filetto di salmone fresco cotto al forno',
        imageUrl: 'https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?q=80&w=500',
        calories: 208,
        proteins: 20.0,
        carbs: 0.0,
        fats: 14.0,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['pesce'],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 0.85,
        glycemicIndex: 0,
        glycemicLoad: 0,
        micronutrients: const {
          'vitamina D': 11.0, // μg
          'vitamina B12': 3.2, // μg
          'vitamina B3': 7.8, // mg
          'vitamina B6': 0.6, // mg
          'calcio': 12.0, // mg
          'fosforo': 280.0, // mg
          'potassio': 380.0, // mg
          'selenio': 40.0, // μg
          'omega 3': 2500.0, // mg
        },
        dataSource: DataSource.crea,
        sourceId: 'CREA-AN-SF001',
        sourceDescription: 'Tabelle di Composizione degli Alimenti CREA',
        validationStatus: ValidationStatus.validated,
        lastValidatedAt: DateTime(2023, 5, 10),
        validatedBy: 'Nutrizionista CREA',
        tags: const ['pesce', 'salmone', 'base', 'omega 3', 'proteico'],
        commonServingSize1Description: '1 filetto',
        commonServingSize1Grams: 150,
      ),
      
      Food(
        id: 'basic_protein_3',
        name: 'Uova intere',
        description: 'Uova di gallina intere cotte (alla coque)',
        imageUrl: 'https://images.unsplash.com/photo-1607690424560-35d967d6ad7a?q=80&w=500',
        calories: 155,
        proteins: 12.5,
        carbs: 1.1,
        fats: 11.0,
        fiber: 0.0,
        sugar: 1.1,
        suitableForMeals: const [MealType.breakfast, MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isVegetarian: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['uova'],
        servingSize: '100g (circa 2 uova)',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        glycemicIndex: 0,
        glycemicLoad: 0,
        micronutrients: const {
          'vitamina A': 190.0, // μg
          'vitamina D': 1.7, // μg
          'vitamina E': 1.9, // mg
          'vitamina B2': 0.4, // mg
          'vitamina B12': 1.9, // μg
          'folati': 51.0, // μg
          'calcio': 56.0, // mg
          'ferro': 1.9, // mg
          'fosforo': 200.0, // mg
          'selenio': 30.0, // μg
          'zinco': 1.3, // mg
          'colina': 294.0, // mg
        },
        dataSource: DataSource.crea,
        sourceId: 'CREA-AN-UI001',
        sourceDescription: 'Tabelle di Composizione degli Alimenti CREA',
        validationStatus: ValidationStatus.validated,
        lastValidatedAt: DateTime(2023, 5, 15),
        validatedBy: 'Nutrizionista CREA',
        tags: const ['uova', 'base', 'proteico', 'colazione'],
        commonServingSize1Description: '1 uovo medio',
        commonServingSize1Grams: 50,
      ),
      
      // PROTEINE VEGETALI
      Food(
        id: 'basic_veg_protein_1',
        name: 'Lenticchie cotte',
        description: 'Lenticchie secche cotte in acqua senza condimenti',
        imageUrl: 'https://images.unsplash.com/photo-1615485290382-441e4d049cb5?q=80&w=500',
        calories: 116,
        proteins: 9.0,
        carbs: 20.0,
        fats: 0.4,
        fiber: 7.9,
        sugar: 1.8,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein, FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotte',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 2.5,
        glycemicIndex: 30,
        glycemicLoad: 6,
        micronutrients: const {
          'ferro': 3.3, // mg
          'calcio': 19.0, // mg
          'fosforo': 180.0, // mg
          'potassio': 370.0, // mg
          'magnesio': 36.0, // mg
          'zinco': 1.3, // mg
          'folati': 181.0, // μg
          'vitamina B1': 0.17, // mg
          'vitamina B3': 1.1, // mg
          'vitamina B6': 0.18, // mg
        },
        dataSource: DataSource.crea,
        sourceId: 'CREA-AN-LC001',
        sourceDescription: 'Tabelle di Composizione degli Alimenti CREA',
        validationStatus: ValidationStatus.validated,
        lastValidatedAt: DateTime(2023, 5, 20),
        validatedBy: 'Nutrizionista CREA',
        tags: const ['legumi', 'lenticchie', 'base', 'proteico', 'vegetale', 'vegano'],
        commonServingSize1Description: '1 porzione',
        commonServingSize1Grams: 150,
      ),
      
      Food(
        id: 'basic_veg_protein_2',
        name: 'Tofu',
        description: 'Tofu naturale senza condimenti',
        imageUrl: 'https://images.unsplash.com/photo-1584321094050-6f5e8006a95d?q=80&w=500',
        calories: 76,
        proteins: 8.1,
        carbs: 1.9,
        fats: 4.2,
        fiber: 0.3,
        sugar: 0.7,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const ['soia'],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.prepared,
        glycemicIndex: 15,
        glycemicLoad: 0,
        micronutrients: const {
          'calcio': 350.0, // mg (se preparato con sali di calcio)
          'ferro': 1.6, // mg
          'fosforo': 95.0, // mg
          'potassio': 120.0, // mg
          'magnesio': 37.0, // mg
          'zinco': 0.8, // mg
          'selenio': 8.0, // μg
          'vitamina B1': 0.08, // mg
          'vitamina B2': 0.05, // mg
          'isoflavoni': 25.0, // mg
        },
        dataSource: DataSource.crea,
        sourceId: 'CREA-AN-TO001',
        sourceDescription: 'Tabelle di Composizione degli Alimenti CREA',
        validationStatus: ValidationStatus.validated,
        lastValidatedAt: DateTime(2023, 5, 25),
        validatedBy: 'Nutrizionista CREA',
        tags: const ['soia', 'tofu', 'base', 'proteico', 'vegetale', 'vegano'],
        commonServingSize1Description: '1 porzione',
        commonServingSize1Grams: 100,
      ),
      
      // VERDURE
      Food(
        id: 'basic_veg_1',
        name: 'Spinaci freschi',
        description: 'Spinaci freschi cotti al vapore',
        imageUrl: 'https://images.unsplash.com/photo-1576064535185-c2526884001c?q=80&w=500',
        calories: 23,
        proteins: 2.9,
        carbs: 3.6,
        fats: 0.4,
        fiber: 2.2,
        sugar: 0.4,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g cotti',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        rawToCookedFactor: 0.5, // perdono volume durante la cottura
        isSeasonal: true,
        seasonalMonths: const [9, 10, 11, 12, 1, 2, 3, 4, 5],
        glycemicIndex: 15,
        glycemicLoad: 0,
        micronutrients: const {
          'vitamina A': 542.0, // μg
          'vitamina C': 28.0, // mg
          'vitamina K': 483.0, // μg
          'folati': 146.0, // μg
          'calcio': 99.0, // mg
          'ferro': 2.7, // mg
          'potassio': 558.0, // mg
          'magnesio': 79.0, // mg
          'manganese': 0.9, // mg
        },
        dataSource: DataSource.crea,
        sourceId: 'CREA-AN-SP001',
        sourceDescription: 'Tabelle di Composizione degli Alimenti CREA',
        validationStatus: ValidationStatus.validated,
        lastValidatedAt: DateTime(2023, 6, 5),
        validatedBy: 'Nutrizionista CREA',
        tags: const ['verdura', 'spinaci', 'base', 'verde', 'foglia', 'stagionale'],
        commonServingSize1Description: '1 porzione',
        commonServingSize1Grams: 200,
      ),
      
      Food(
        id: 'basic_veg_2',
        name: 'Pomodori freschi',
        description: 'Pomodori freschi crudi',
        imageUrl: 'https://images.unsplash.com/photo-1582284540020-8acbe03f4924?q=80&w=500',
        calories: 18,
        proteins: 0.9,
        carbs: 3.9,
        fats: 0.2,
        fiber: 1.2,
        sugar: 2.6,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: const [],
        servingSize: '100g',
        servingSizeGrams: 100,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: const [6, 7, 8, 9],
        glycemicIndex: 30,
        glycemicLoad: 1,
        micronutrients: const {
          'vitamina A': 42.0, // μg
          'vitamina C': 14.0, // mg
          'vitamina K': 7.9, // μg
          'potassio': 237.0, // mg
          'folati': 15.0, // μg
          'licopene': 2573.0, // μg
        },
        dataSource: DataSource.crea,
        sourceId: 'CREA-AN-PF001',
        sourceDescription: 'Tabelle di Composizione degli Alimenti CREA',
        validationStatus: ValidationStatus.validated,
        lastValidatedAt: DateTime(2023, 6, 10),
        validatedBy: 'Nutrizionista CREA',
        tags: const ['verdura', 'pomodori', 'base', 'rosso', 'stagionale', 'crudo'],
        commonServingSize1Description: '1 pomodoro medio',
        commonServingSize1Grams: 150,
      ),
    ];
  }
}
