import 'dart:convert';

enum Gender { male, female }

enum ActivityLevel {
  sedentary,
  lightlyActive,
  moderatelyActive,
  veryActive,
  extremelyActive
}

enum Goal { weightLoss, maintenance, weightGain }
// Alias per compatibilità con il codice esistente
typedef DietGoal = Goal;

enum DietType { omnivore, vegetarian, vegan, pescatarian, keto, paleo }

class UserProfile {
  final String id;
  final String name;
  final int age;
  final Gender gender;
  final int height; // in cm
  final int weight; // in kg
  final ActivityLevel activityLevel;
  final Goal goal;
  final DietType dietType;
  final List<String> allergies;
  final List<String> dislikedFoods;
  final List<String> dietaryPreferences;
  final int mealsPerDay;
  final int targetWeight; // in kg, optional for weight loss/gain
  final double weeklyGoal; // in kg per week, for weight loss/gain

  UserProfile({
    required this.id,
    this.name = 'Utente',
    required this.age,
    required this.gender,
    required this.height,
    required this.weight,
    required this.activityLevel,
    required this.goal,
    this.dietType = DietType.omnivore,
    this.allergies = const [],
    this.dislikedFoods = const [],
    this.dietaryPreferences = const [],
    this.mealsPerDay = 3,
    this.targetWeight = 0,
    this.weeklyGoal = 0.5,
  });

  // Calcola il BMI (Indice di Massa Corporea)
  double get bmi {
    // BMI = peso (kg) / (altezza (m))²
    final heightInMeters = height / 100;
    return weight / (heightInMeters * heightInMeters);
  }

  // Ottieni la categoria di BMI
  String get bmiCategory {
    final bmiValue = bmi;
    if (bmiValue < 18.5) {
      return 'Sottopeso';
    } else if (bmiValue < 25) {
      return 'Normopeso';
    } else if (bmiValue < 30) {
      return 'Sovrappeso';
    } else {
      return 'Obesità';
    }
  }

  // Calcola il BMR (Metabolismo Basale) usando la formula di Mifflin-St Jeor
  double calculateBMR() {
    if (gender == Gender.male) {
      return (10 * weight) + (6.25 * height) - (5 * age) + 5;
    } else {
      return (10 * weight) + (6.25 * height) - (5 * age) - 161;
    }
  }

  // Ottieni il moltiplicatore per il livello di attività
  double get activityMultiplier {
    switch (activityLevel) {
      case ActivityLevel.sedentary:
        return 1.2; // Poco o nessun esercizio
      case ActivityLevel.lightlyActive:
        return 1.375; // Esercizio leggero 1-3 volte/settimana
      case ActivityLevel.moderatelyActive:
        return 1.55; // Esercizio moderato 3-5 volte/settimana
      case ActivityLevel.veryActive:
        return 1.725; // Esercizio intenso 6-7 volte/settimana
      case ActivityLevel.extremelyActive:
        return 1.9; // Esercizio molto intenso, lavoro fisico
    }
  }

  // Calcola il TDEE (Fabbisogno Calorico Giornaliero Totale)
  int calculateTDEE() {
    return (calculateBMR() * activityMultiplier).round();
  }

  // Calcola l'obiettivo calorico giornaliero in base all'obiettivo
  int calculateCalorieTarget() {
    final tdee = calculateTDEE();

    switch (goal) {
      case Goal.weightLoss:
        // Deficit calorico per perdita di peso (500-750 kcal/giorno per perdere ~0.5-0.75 kg/settimana)
        final deficit = (weeklyGoal * 1000).round(); // 1 kg di grasso = ~7700 kcal
        final target = tdee - (deficit ~/ 7);

        // Assicurati che l'obiettivo non sia troppo basso (minimo 1200 per donne, 1500 per uomini)
        final minCalories = gender == Gender.female ? 1200 : 1500;
        return target < minCalories ? minCalories : target;

      case Goal.maintenance:
        return tdee;

      case Goal.weightGain:
        // Surplus calorico per aumento di massa (250-500 kcal/giorno)
        final surplus = (weeklyGoal * 1000).round();
        return tdee + (surplus ~/ 7);
    }
  }

  // Calcola la distribuzione dei macronutrienti in base all'obiettivo e livello di attività
  Map<String, double> calculateMacroDistribution() {
    // Ottieni la distribuzione base per l'obiettivo
    Map<String, double> baseMacros = _getBaseMacroDistribution();

    // Applica gli aggiustamenti per il livello di attività (per atleti e persone molto attive)
    return _adjustMacrosForActivityLevel(baseMacros);
  }

  // Distribuzione base dei macronutrienti per obiettivo
  Map<String, double> _getBaseMacroDistribution() {
    switch (goal) {
      case Goal.weightLoss:
        return {
          'proteine': 0.35, // 35% proteine per sazietà e preservazione muscolare
          'carboidrati': 0.40, // 40% carboidrati
          'grassi': 0.25, // 25% grassi
          'proteins': 0.35, // Inglese
          'carbs': 0.40, // Inglese
          'fats': 0.25, // Inglese
        };

      case Goal.maintenance:
        return {
          'proteine': 0.30, // 30% proteine
          'carboidrati': 0.45, // 45% carboidrati
          'grassi': 0.25, // 25% grassi
          'proteins': 0.30, // Inglese
          'carbs': 0.45, // Inglese
          'fats': 0.25, // Inglese
        };

      case Goal.weightGain:
        return {
          'proteine': 0.30, // 30% proteine per costruzione muscolare
          'carboidrati': 0.50, // 50% carboidrati per energia
          'grassi': 0.20, // 20% grassi
          'proteins': 0.30, // Inglese
          'carbs': 0.50, // Inglese
          'fats': 0.20, // Inglese
        };
    }
  }

  // Aggiusta i macronutrienti in base al livello di attività per supportare atleti
  Map<String, double> _adjustMacrosForActivityLevel(Map<String, double> baseMacros) {
    double proteinRatio = baseMacros['proteins'] ?? 0.30;
    double carbRatio = baseMacros['carbs'] ?? 0.45;
    double fatRatio = baseMacros['fats'] ?? 0.25;

    // Aggiustamenti specifici per livello di attività
    switch (activityLevel) {
      case ActivityLevel.sedentary:
        // Mantieni i valori base (15-20% proteine)
        break;

      case ActivityLevel.lightlyActive:
        // Leggero aumento proteine (20-25%)
        proteinRatio = _clampProteinRatio(proteinRatio + 0.05);
        break;

      case ActivityLevel.moderatelyActive:
        // Moderato aumento proteine (20-25%)
        proteinRatio = _clampProteinRatio(proteinRatio + 0.10);
        break;

      case ActivityLevel.veryActive:
        // Alto aumento proteine (25-30%)
        proteinRatio = _clampProteinRatio(proteinRatio + 0.15);
        break;

      case ActivityLevel.extremelyActive:
        // Massimo aumento proteine per atleti (30-35%)
        proteinRatio = _clampProteinRatio(proteinRatio + 0.20);
        break;
    }

    // Ribilancia carboidrati e grassi mantenendo le proporzioni
    double remainingRatio = 1.0 - proteinRatio;
    double originalNonProtein = carbRatio + fatRatio;

    if (originalNonProtein > 0) {
      carbRatio = (carbRatio / originalNonProtein) * remainingRatio;
      fatRatio = (fatRatio / originalNonProtein) * remainingRatio;
    }

    // Assicurati che i grassi non scendano sotto il 20% per la salute ormonale
    if (fatRatio < 0.20) {
      fatRatio = 0.20;
      carbRatio = 1.0 - proteinRatio - fatRatio;
    }

    return {
      'proteine': proteinRatio,
      'carboidrati': carbRatio,
      'grassi': fatRatio,
      'proteins': proteinRatio,
      'carbs': carbRatio,
      'fats': fatRatio,
    };
  }

  // Limita il rapporto proteico tra 15% e 35% per sicurezza medica
  double _clampProteinRatio(double ratio) {
    return ratio.clamp(0.15, 0.35);
  }

  /// Calcola il fabbisogno proteico basato sul peso corporeo per atleti
  /// Restituisce i grammi di proteine per kg di peso corporeo
  double calculateProteinPerKgBodyWeight() {
    switch (activityLevel) {
      case ActivityLevel.sedentary:
        return 0.8; // 0.8g/kg - RDA standard

      case ActivityLevel.lightlyActive:
        return 1.2; // 1.0-1.2g/kg - attività leggera

      case ActivityLevel.moderatelyActive:
        return 1.6; // 1.4-1.6g/kg - attività moderata

      case ActivityLevel.veryActive:
        return 2.0; // 1.8-2.0g/kg - atleti di resistenza

      case ActivityLevel.extremelyActive:
        return 2.2; // 2.0-2.2g/kg - atleti di forza/elite
    }
  }

  /// Calcola i grammi totali di proteine basati sul peso corporeo
  int calculateWeightBasedProteinGrams() {
    final proteinPerKg = calculateProteinPerKgBodyWeight();
    return (weight * proteinPerKg).round();
  }

  /// Determina se usare il calcolo basato sul peso o sulla percentuale
  /// Per atleti molto attivi, usa il metodo che fornisce più proteine
  bool shouldUseWeightBasedProtein() {
    return activityLevel == ActivityLevel.veryActive ||
           activityLevel == ActivityLevel.extremelyActive;
  }

  /// Calcola i grammi di proteine ottimali combinando entrambi i metodi
  int calculateOptimalProteinGrams() {
    final calorieTarget = calculateCalorieTarget();
    final macroDistribution = calculateMacroDistribution();

    // Calcolo basato sulla percentuale
    final percentageBasedProtein = ((calorieTarget * (macroDistribution['proteins'] ?? 0.30)) / 4).round();

    // Calcolo basato sul peso corporeo
    final weightBasedProtein = calculateWeightBasedProteinGrams();

    // Per atleti, usa il valore più alto per garantire recupero muscolare ottimale
    if (shouldUseWeightBasedProtein()) {
      final optimalProtein = [percentageBasedProtein, weightBasedProtein].reduce((a, b) => a > b ? a : b);

      // Verifica che non superi il 35% delle calorie totali per sicurezza
      final maxProteinFromCalories = ((calorieTarget * 0.35) / 4).round();
      return optimalProtein > maxProteinFromCalories ? maxProteinFromCalories : optimalProtein;
    }

    // Per non atleti, usa il calcolo percentuale standard
    return percentageBasedProtein;
  }

  // Calcola i grammi di macronutrienti in base all'obiettivo calorico con supporto per atleti
  Map<String, int> calculateMacroGrams() {
    final calorieTarget = calculateCalorieTarget();
    final macroDistribution = calculateMacroDistribution();

    // Verifica che la mappa contenga tutte le chiavi necessarie
    if (!macroDistribution.containsKey('proteine') ||
        !macroDistribution.containsKey('carboidrati') ||
        !macroDistribution.containsKey('grassi')) {
      print('ERRORE: La mappa macroDistribution non contiene tutte le chiavi necessarie: $macroDistribution');

      // Usa valori predefiniti se mancano le chiavi
      return {
        'proteine': 50,     // Valore predefinito
        'carboidrati': 100, // Valore predefinito
        'grassi': 30,       // Valore predefinito
        'proteins': 50,     // Valore predefinito (inglese)
        'carbs': 100,       // Valore predefinito (inglese)
        'fats': 30,         // Valore predefinito (inglese)
      };
    }

    // Calcola le proteine ottimali (combinando percentuale e peso corporeo per atleti)
    final proteineGrams = calculateOptimalProteinGrams();

    // Calcola le calorie rimanenti dopo le proteine
    final proteinCalories = proteineGrams * 4;
    final remainingCalories = calorieTarget - proteinCalories;

    // Distribuisci le calorie rimanenti tra carboidrati e grassi
    final grassiValue = macroDistribution['grassi'] ?? 0.25;
    final carboidratiValue = macroDistribution['carboidrati'] ?? 0.45;

    // Calcola la proporzione tra carboidrati e grassi
    final totalNonProteinRatio = carboidratiValue + grassiValue;
    final adjustedCarbRatio = totalNonProteinRatio > 0 ? carboidratiValue / totalNonProteinRatio : 0.75;
    final adjustedFatRatio = totalNonProteinRatio > 0 ? grassiValue / totalNonProteinRatio : 0.25;

    // Calcola i grammi finali
    final grassiGrams = ((remainingCalories * adjustedFatRatio) / 9).round();
    final carboidratiGrams = ((remainingCalories * adjustedCarbRatio) / 4).round();

    return {
      'proteine': proteineGrams,     // Italiano
      'carboidrati': carboidratiGrams, // Italiano
      'grassi': grassiGrams,         // Italiano
      'proteins': proteineGrams,     // Inglese
      'carbs': carboidratiGrams,     // Inglese
      'fats': grassiGrams,           // Inglese
    };
  }

  // Converti da UserProfile a Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'age': age,
      'gender': gender.toString().split('.').last,
      'height': height,
      'weight': weight,
      'activityLevel': activityLevel.toString().split('.').last,
      'dietGoal': goal.toString().split('.').last,
      'dietType': dietType.toString().split('.').last,
      'allergies': allergies,
      'dislikedFoods': dislikedFoods,
      'mealsPerDay': mealsPerDay,
      'targetWeight': targetWeight,
      'weeklyGoal': weeklyGoal,
    };
  }

  // Converti da Map a UserProfile
  factory UserProfile.fromMap(Map<String, dynamic> map) {
    return UserProfile(
      id: map['id'] as String,
      name: map['name'] as String? ?? 'Utente',
      age: map['age'] as int,
      gender: Gender.values.firstWhere(
        (e) => e.toString().split('.').last == map['gender'],
      ),
      height: (map['height'] is int) ? map['height'] : (map['height'] as double).toInt(),
      weight: (map['weight'] is int) ? map['weight'] : (map['weight'] as double).toInt(),
      activityLevel: ActivityLevel.values.firstWhere(
        (e) => e.toString().split('.').last == map['activityLevel'],
      ),
      goal: Goal.values.firstWhere(
        (e) => e.toString().split('.').last == map['dietGoal'],
        orElse: () => Goal.maintenance,
      ),
      dietType: DietType.values.firstWhere(
        (e) => e.toString().split('.').last == map['dietType'],
      ),
      allergies: List<String>.from(map['allergies']),
      dislikedFoods: List<String>.from(map['dislikedFoods']),
      mealsPerDay: map['mealsPerDay'] as int,
      targetWeight: map['targetWeight'] != null ? (map['targetWeight'] is int ? map['targetWeight'] : map['targetWeight'] as double) : 0,
      weeklyGoal: map['weeklyGoal'] != null ? (map['weeklyGoal'] is int ? map['weeklyGoal'].toDouble() : map['weeklyGoal'] as double) : 0.5,
    );
  }

  // Converti da UserProfile a JSON
  String toJson() => json.encode(toMap());

  // Converti da JSON a UserProfile
  factory UserProfile.fromJson(String source) =>
      UserProfile.fromMap(json.decode(source) as Map<String, dynamic>);

  // Crea una copia del profilo con possibilità di sovrascrivere alcuni campi
  UserProfile copyWith({
    String? id,
    String? name,
    int? age,
    Gender? gender,
    int? height,
    int? weight,
    ActivityLevel? activityLevel,
    Goal? goal,
    DietType? dietType,
    List<String>? allergies,
    List<String>? dislikedFoods,
    List<String>? dietaryPreferences,
    int? mealsPerDay,
    int? targetWeight,
    double? weeklyGoal,
  }) {
    return UserProfile(
      id: id ?? this.id,
      name: name ?? this.name,
      age: age ?? this.age,
      gender: gender ?? this.gender,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      activityLevel: activityLevel ?? this.activityLevel,
      goal: goal ?? this.goal,
      dietType: dietType ?? this.dietType,
      allergies: allergies ?? this.allergies,
      dislikedFoods: dislikedFoods ?? this.dislikedFoods,
      dietaryPreferences: dietaryPreferences ?? this.dietaryPreferences,
      mealsPerDay: mealsPerDay ?? this.mealsPerDay,
      targetWeight: targetWeight ?? this.targetWeight,
      weeklyGoal: weeklyGoal ?? this.weeklyGoal,
    );
  }
}
