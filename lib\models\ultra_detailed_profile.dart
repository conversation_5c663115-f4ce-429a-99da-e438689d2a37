import 'dart:convert';
import 'user_profile.dart';

/// MODELLO ULTRA-DETTAGLIATO PER PROFILAZIONE UTENTE AVANZATA
/// Implementa tutte le specifiche della SEZIONE 1 del documento

// ENUMS PER NUOVE FUNZIONALITÀ

/// Metodi di misurazione composizione corporea
enum BodyCompositionMethod {
  bioelectricalImpedance, // Bilancia impedenziometrica
  skinfoldCalipers,       // Plicometria
  dexa,                   // DEXA scan
  bodPod,                 // Bod Pod
  hydrostatic,            // Pesata idrostatica
  estimated,              // Stima da formule
}

/// Formule per calcolo BMR
enum BMRFormula {
  mifflinStJeor,    // Più accurata per popolazione generale
  katchMcArdle,     // Migliore se si conosce massa magra
  harrisBenedict,   // Formula classica
}

/// Livello di attività lavorativa
enum WorkActivity {
  sedentary,    // Ufficio, scrivania
  light,        // Commesso, insegnante
  moderate,     // Artigiano, infermiere
  heavy,        // Operaio edile, muratore
}

/// Livello NEAT (Non-Exercise Activity Thermogenesis)
enum NEATLevel {
  low,      // Ascensore, auto, sedentario
  medium,   // Qualche scala, camminate brevi
  high,     // Scale sempre, molto attivo
}

/// Obiettivi primari
enum PrimaryGoal {
  weightLoss,           // Perdita di peso
  weightGain,           // Aumento di peso
  maintenance,          // Mantenimento
  bodyRecomposition,    // Ricomposizione corporea
  performance,          // Performance atletica
}

/// Obiettivi secondari
enum SecondaryGoal {
  improveHealth,        // Migliorare salute generale
  increaseEnergy,       // Aumentare energia
  betterSleep,          // Migliorare sonno
  reduceInflammation,   // Ridurre infiammazione
  improveDigestion,     // Migliorare digestione
  buildMuscle,          // Costruire muscolo
  loseFat,              // Perdere grasso
}

/// Regimi alimentari specifici
enum DietaryRegimen {
  mediterranean,    // Dieta Mediterranea
  paleo,           // Dieta Paleolitica
  ketogenic,       // Dieta Chetogenica
  lowCarb,         // Low-Carb
  dash,            // DASH (per ipertensione)
  plantBased,      // A base vegetale
  intermittentFasting, // Digiuno intermittente
  none,            // Nessun regime specifico
}

/// Livello di budget
enum BudgetLevel {
  low,      // Economico
  medium,   // Medio
  high,     // Nessun limite
}

/// Tempo disponibile per cucinare
enum CookingTime {
  minimal,  // <20 minuti
  medium,   // 20-45 minuti
  extended, // >45 minuti
}

/// Abitudini meal prep
enum MealPrepHabits {
  never,        // Mai
  occasional,   // Occasionale
  weekly,       // Settimanale
  daily,        // Giornaliero
}

/// Livello abilità culinaria
enum CookingSkillLevel {
  beginner,     // Principiante
  intermediate, // Intermedio
  advanced,     // Avanzato
  expert,       // Esperto
}

/// Tipi di cucina preferiti
enum CuisineType {
  italian,      // Italiana
  mediterranean, // Mediterranea
  asian,        // Asiatica
  mexican,      // Messicana
  indian,       // Indiana
  american,     // Americana
  french,       // Francese
  japanese,     // Giapponese
}

// CLASSI DI SUPPORTO

/// Entry per storia del peso
class WeightHistoryEntry {
  final DateTime date;
  final double weight;
  final String? notes;

  const WeightHistoryEntry({
    required this.date,
    required this.weight,
    this.notes,
  });

  Map<String, dynamic> toMap() => {
    'date': date.toIso8601String(),
    'weight': weight,
    'notes': notes,
  };

  factory WeightHistoryEntry.fromMap(Map<String, dynamic> map) => WeightHistoryEntry(
    date: DateTime.parse(map['date']),
    weight: map['weight']?.toDouble() ?? 0.0,
    notes: map['notes'],
  );
}

/// Entry per storia delle diete
class DietHistoryEntry {
  final String dietName;
  final DateTime startDate;
  final DateTime? endDate;
  final bool wasSuccessful;
  final String? difficulties;
  final double? weightLost;

  const DietHistoryEntry({
    required this.dietName,
    required this.startDate,
    this.endDate,
    required this.wasSuccessful,
    this.difficulties,
    this.weightLost,
  });

  Map<String, dynamic> toMap() => {
    'dietName': dietName,
    'startDate': startDate.toIso8601String(),
    'endDate': endDate?.toIso8601String(),
    'wasSuccessful': wasSuccessful,
    'difficulties': difficulties,
    'weightLost': weightLost,
  };

  factory DietHistoryEntry.fromMap(Map<String, dynamic> map) => DietHistoryEntry(
    dietName: map['dietName'] ?? '',
    startDate: DateTime.parse(map['startDate']),
    endDate: map['endDate'] != null ? DateTime.parse(map['endDate']) : null,
    wasSuccessful: map['wasSuccessful'] ?? false,
    difficulties: map['difficulties'],
    weightLost: map['weightLost']?.toDouble(),
  );
}

/// Valori ematochimici (opzionali)
class BloodValues {
  final double? fastingGlucose;     // mg/dL
  final double? totalCholesterol;   // mg/dL
  final double? ldlCholesterol;     // mg/dL
  final double? hdlCholesterol;     // mg/dL
  final double? triglycerides;      // mg/dL
  final double? tsh;                // mIU/L
  final double? iron;               // μg/dL
  final double? uricAcid;           // mg/dL
  final DateTime? testDate;

  const BloodValues({
    this.fastingGlucose,
    this.totalCholesterol,
    this.ldlCholesterol,
    this.hdlCholesterol,
    this.triglycerides,
    this.tsh,
    this.iron,
    this.uricAcid,
    this.testDate,
  });

  Map<String, dynamic> toMap() => {
    'fastingGlucose': fastingGlucose,
    'totalCholesterol': totalCholesterol,
    'ldlCholesterol': ldlCholesterol,
    'hdlCholesterol': hdlCholesterol,
    'triglycerides': triglycerides,
    'tsh': tsh,
    'iron': iron,
    'uricAcid': uricAcid,
    'testDate': testDate?.toIso8601String(),
  };

  factory BloodValues.fromMap(Map<String, dynamic> map) => BloodValues(
    fastingGlucose: map['fastingGlucose']?.toDouble(),
    totalCholesterol: map['totalCholesterol']?.toDouble(),
    ldlCholesterol: map['ldlCholesterol']?.toDouble(),
    hdlCholesterol: map['hdlCholesterol']?.toDouble(),
    triglycerides: map['triglycerides']?.toDouble(),
    tsh: map['tsh']?.toDouble(),
    iron: map['iron']?.toDouble(),
    uricAcid: map['uricAcid']?.toDouble(),
    testDate: map['testDate'] != null ? DateTime.parse(map['testDate']) : null,
  );
}

/// Esercizio pianificato
class PlannedExercise {
  final String name;
  final ExerciseType type;
  final int durationMinutes;
  final int frequencyPerWeek;
  final ExerciseIntensity intensity;
  final double metValue; // Metabolic Equivalent of Task

  const PlannedExercise({
    required this.name,
    required this.type,
    required this.durationMinutes,
    required this.frequencyPerWeek,
    required this.intensity,
    required this.metValue,
  });

  Map<String, dynamic> toMap() => {
    'name': name,
    'type': type.toString(),
    'durationMinutes': durationMinutes,
    'frequencyPerWeek': frequencyPerWeek,
    'intensity': intensity.toString(),
    'metValue': metValue,
  };

  factory PlannedExercise.fromMap(Map<String, dynamic> map) => PlannedExercise(
    name: map['name'] ?? '',
    type: ExerciseType.values.firstWhere((e) => e.toString() == map['type']),
    durationMinutes: map['durationMinutes']?.toInt() ?? 0,
    frequencyPerWeek: map['frequencyPerWeek']?.toInt() ?? 0,
    intensity: ExerciseIntensity.values.firstWhere((e) => e.toString() == map['intensity']),
    metValue: map['metValue']?.toDouble() ?? 0.0,
  );
}

/// Tipi di esercizio
enum ExerciseType {
  cardio,           // Cardio
  strength,         // Forza
  flexibility,      // Flessibilità
  sports,           // Sport
  hiit,             // HIIT
  yoga,             // Yoga
  pilates,          // Pilates
}

/// Intensità esercizio
enum ExerciseIntensity {
  light,      // Leggera
  moderate,   // Moderata
  vigorous,   // Vigorosa
}

/// Dettagli perdita peso
class WeightLossDetails {
  final double targetWeightLossPerWeek; // kg/settimana
  final double deficitPercentage;       // % deficit calorico
  final double proteinMultiplier;       // g/kg peso corporeo
  final bool preserveLeanMass;          // Focus su preservazione massa magra

  const WeightLossDetails({
    required this.targetWeightLossPerWeek,
    required this.deficitPercentage,
    required this.proteinMultiplier,
    this.preserveLeanMass = true,
  });

  Map<String, dynamic> toMap() => {
    'targetWeightLossPerWeek': targetWeightLossPerWeek,
    'deficitPercentage': deficitPercentage,
    'proteinMultiplier': proteinMultiplier,
    'preserveLeanMass': preserveLeanMass,
  };

  factory WeightLossDetails.fromMap(Map<String, dynamic> map) => WeightLossDetails(
    targetWeightLossPerWeek: map['targetWeightLossPerWeek']?.toDouble() ?? 0.5,
    deficitPercentage: map['deficitPercentage']?.toDouble() ?? 0.20,
    proteinMultiplier: map['proteinMultiplier']?.toDouble() ?? 2.0,
    preserveLeanMass: map['preserveLeanMass'] ?? true,
  );
}

/// Dettagli aumento massa
class MuscleGainDetails {
  final double surplusPercentage;       // % surplus calorico
  final double proteinMultiplier;       // g/kg peso corporeo
  final bool leanBulk;                  // Bulk pulito vs aggressivo

  const MuscleGainDetails({
    required this.surplusPercentage,
    required this.proteinMultiplier,
    this.leanBulk = true,
  });

  Map<String, dynamic> toMap() => {
    'surplusPercentage': surplusPercentage,
    'proteinMultiplier': proteinMultiplier,
    'leanBulk': leanBulk,
  };

  factory MuscleGainDetails.fromMap(Map<String, dynamic> map) => MuscleGainDetails(
    surplusPercentage: map['surplusPercentage']?.toDouble() ?? 0.15,
    proteinMultiplier: map['proteinMultiplier']?.toDouble() ?? 1.8,
    leanBulk: map['leanBulk'] ?? true,
  );
}

/// Obiettivi performance
class PerformanceGoals {
  final String sportType;
  final double proteinMultiplier;
  final bool optimizeCarbTiming;        // Timing carboidrati
  final bool focusOnHydration;          // Focus idratazione

  const PerformanceGoals({
    required this.sportType,
    required this.proteinMultiplier,
    this.optimizeCarbTiming = true,
    this.focusOnHydration = true,
  });

  Map<String, dynamic> toMap() => {
    'sportType': sportType,
    'proteinMultiplier': proteinMultiplier,
    'optimizeCarbTiming': optimizeCarbTiming,
    'focusOnHydration': focusOnHydration,
  };

  factory PerformanceGoals.fromMap(Map<String, dynamic> map) => PerformanceGoals(
    sportType: map['sportType'] ?? '',
    proteinMultiplier: map['proteinMultiplier']?.toDouble() ?? 1.6,
    optimizeCarbTiming: map['optimizeCarbTiming'] ?? true,
    focusOnHydration: map['focusOnHydration'] ?? true,
  );
}

/// Allergia alimentare
class FoodAllergy {
  final String allergen;
  final AllergySeverity severity;
  final List<String> crossReactiveAllergens;

  const FoodAllergy({
    required this.allergen,
    required this.severity,
    this.crossReactiveAllergens = const [],
  });

  Map<String, dynamic> toMap() => {
    'allergen': allergen,
    'severity': severity.toString(),
    'crossReactiveAllergens': crossReactiveAllergens,
  };

  factory FoodAllergy.fromMap(Map<String, dynamic> map) => FoodAllergy(
    allergen: map['allergen'] ?? '',
    severity: AllergySeverity.values.firstWhere((e) => e.toString() == map['severity']),
    crossReactiveAllergens: List<String>.from(map['crossReactiveAllergens'] ?? []),
  );
}

/// Severità allergia
enum AllergySeverity {
  mild,       // Lieve
  moderate,   // Moderata
  severe,     // Severa
  anaphylactic, // Anafilattica
}

/// Intolleranza alimentare
class FoodIntolerance {
  final String food;
  final IntoleranceType type;
  final IntoleranceSeverity severity;

  const FoodIntolerance({
    required this.food,
    required this.type,
    required this.severity,
  });

  Map<String, dynamic> toMap() => {
    'food': food,
    'type': type.toString(),
    'severity': severity.toString(),
  };

  factory FoodIntolerance.fromMap(Map<String, dynamic> map) => FoodIntolerance(
    food: map['food'] ?? '',
    type: IntoleranceType.values.firstWhere((e) => e.toString() == map['type']),
    severity: IntoleranceSeverity.values.firstWhere((e) => e.toString() == map['severity']),
  );
}

/// Tipo intolleranza
enum IntoleranceType {
  lactose,      // Lattosio
  gluten,       // Glutine
  fructose,     // Fruttosio
  histamine,    // Istamina
  fodmap,       // FODMAP
}

/// Severità intolleranza
enum IntoleranceSeverity {
  mild,         // Lieve
  moderate,     // Moderata
  severe,       // Severa
}

/// CLASSE PRINCIPALE: PROFILO UTENTE ULTRA-DETTAGLIATO
class UltraDetailedProfile {
  // SEZIONE 1: IDENTIFICAZIONE E TIMESTAMP
  final String id;
  final DateTime createdAt;
  final DateTime lastUpdated;

  // SEZIONE 2: DATI BASE (dal profilo esistente)
  final UserProfile baseProfile;

  // SEZIONE 3: COMPOSIZIONE CORPOREA AVANZATA
  final double? bodyFatPercentage;
  final double? leanMass;
  final BodyCompositionMethod? bodyCompositionMethod;
  final Map<String, double>? circumferences; // vita, fianchi, collo, etc.

  // SEZIONE 4: STORIA E BACKGROUND
  final List<WeightHistoryEntry> weightHistory;
  final List<DietHistoryEntry> dietHistory;
  final BloodValues? bloodValues;

  // SEZIONE 5: METABOLISMO E CALCOLI AVANZATI
  final BMRFormula preferredBMRFormula;
  final double? customBMR;
  final double? customTDEE;

  // SEZIONE 6: ATTIVITÀ FISICA DETTAGLIATA
  final WorkActivity workActivity;
  final List<PlannedExercise> plannedExercises;
  final NEATLevel neatLevel;

  // SEZIONE 7: OBIETTIVI GERARCHIZZATI
  final PrimaryGoal primaryGoal;
  final List<SecondaryGoal> secondaryGoals;
  final WeightLossDetails? weightLossDetails;
  final MuscleGainDetails? muscleGainDetails;
  final PerformanceGoals? performanceGoals;

  // SEZIONE 8: PREFERENZE ALIMENTARI AVANZATE
  final DietaryRegimen? dietaryRegimen;
  final List<String> dislikedFoods;
  final List<String> preferredFoods;
  final BudgetLevel budgetLevel;
  final CookingTime cookingTime;
  final int mealsPerDay;
  final MealPrepHabits mealPrepHabits;
  final CookingSkillLevel cookingSkillLevel;
  final List<CuisineType> preferredCuisines;

  // SEZIONE 9: CONDIZIONI MEDICHE (con disclaimer)
  final List<String> medicalConditions; // Stringhe per flessibilità
  final List<FoodAllergy> foodAllergies;
  final List<FoodIntolerance> foodIntolerances;
  final bool hasConsultedDoctor;

  // SEZIONE 10: FLESSIBILITÀ E PERSONALIZZAZIONE
  final bool allowCheatMeals;
  final int cheatMealsPerWeek;
  final bool preferOrganicFoods;
  final bool preferLocalFoods;
  final bool enableCalorieCycling;
  final bool enableMealTiming;

  const UltraDetailedProfile({
    required this.id,
    required this.createdAt,
    required this.lastUpdated,
    required this.baseProfile,
    this.bodyFatPercentage,
    this.leanMass,
    this.bodyCompositionMethod,
    this.circumferences,
    this.weightHistory = const [],
    this.dietHistory = const [],
    this.bloodValues,
    this.preferredBMRFormula = BMRFormula.mifflinStJeor,
    this.customBMR,
    this.customTDEE,
    required this.workActivity,
    this.plannedExercises = const [],
    required this.neatLevel,
    required this.primaryGoal,
    this.secondaryGoals = const [],
    this.weightLossDetails,
    this.muscleGainDetails,
    this.performanceGoals,
    this.dietaryRegimen,
    this.dislikedFoods = const [],
    this.preferredFoods = const [],
    this.budgetLevel = BudgetLevel.medium,
    this.cookingTime = CookingTime.medium,
    this.mealsPerDay = 3,
    this.mealPrepHabits = MealPrepHabits.occasional,
    this.cookingSkillLevel = CookingSkillLevel.intermediate,
    this.preferredCuisines = const [],
    this.medicalConditions = const [],
    this.foodAllergies = const [],
    this.foodIntolerances = const [],
    this.hasConsultedDoctor = false,
    this.allowCheatMeals = false,
    this.cheatMealsPerWeek = 0,
    this.preferOrganicFoods = false,
    this.preferLocalFoods = false,
    this.enableCalorieCycling = false,
    this.enableMealTiming = false,
  });

  /// CALCOLO BMR AVANZATO con formule multiple
  double calculateBMR() {
    if (customBMR != null) return customBMR!;

    switch (preferredBMRFormula) {
      case BMRFormula.mifflinStJeor:
        return _calculateMifflinStJeor();
      case BMRFormula.katchMcArdle:
        return _calculateKatchMcArdle();
      case BMRFormula.harrisBenedict:
        return _calculateHarrisBenedict();
    }
  }

  /// CALCOLO TDEE AVANZATO considerando tutti i fattori
  double calculateTDEE() {
    if (customTDEE != null) return customTDEE!;

    final bmr = calculateBMR();
    final workFactor = _getWorkActivityFactor();
    final exerciseFactor = _calculateExerciseFactor();
    final neatFactor = _getNEATFactor();
    final tefFactor = 0.1; // 10% per effetto termico del cibo

    return bmr * (1 + workFactor + exerciseFactor + neatFactor + tefFactor);
  }

  /// CALCOLO TARGET CALORICO basato su obiettivi specifici
  int calculateCalorieTarget() {
    final tdee = calculateTDEE();

    switch (primaryGoal) {
      case PrimaryGoal.weightLoss:
        final deficitPercent = weightLossDetails?.deficitPercentage ?? 0.20;
        final targetCalories = tdee * (1 - deficitPercent);
        // Sicurezza: non scendere sotto BMR
        final bmr = calculateBMR();
        return targetCalories.clamp(bmr, tdee).round();

      case PrimaryGoal.weightGain:
        final surplusPercent = muscleGainDetails?.surplusPercentage ?? 0.15;
        return (tdee * (1 + surplusPercent)).round();

      case PrimaryGoal.bodyRecomposition:
        // Leggero deficit o normocalorica
        return (tdee * 0.95).round();

      case PrimaryGoal.performance:
        // Leggero surplus per performance
        return (tdee * 1.05).round();

      case PrimaryGoal.maintenance:
        return tdee.round();
    }
  }

  /// CALCOLO DISTRIBUZIONE MACRONUTRIENTI PERSONALIZZATA
  Map<String, double> calculateMacroDistribution() {
    final totalCalories = calculateCalorieTarget().toDouble();

    // STEP 1: Calcola proteine (priorità assoluta)
    final proteinGrams = _calculateProteinTarget();
    final proteinCalories = proteinGrams * 4;

    // STEP 2: Calcola grassi (minimo essenziale)
    final fatGrams = _calculateFatTarget(totalCalories);
    final fatCalories = fatGrams * 9;

    // STEP 3: Carboidrati per differenza
    final carbCalories = (totalCalories - proteinCalories - fatCalories).clamp(0, totalCalories);
    final carbGrams = carbCalories / 4;

    return {
      'proteins': proteinGrams,
      'carbs': carbGrams,
      'fats': fatGrams,
    };
  }

  /// VALIDAZIONE SICUREZZA NUTRIZIONALE
  Map<String, dynamic> validateNutritionalSafety() {
    final calories = calculateCalorieTarget();
    final bmr = calculateBMR();
    final macros = calculateMacroDistribution();

    final warnings = <String>[];
    final errors = <String>[];

    // Controllo calorie minime
    if (calories < bmr * 0.8) {
      errors.add('Calorie troppo basse rispetto al metabolismo basale');
    }

    // Controllo proteine
    final proteinPercent = (macros['proteins']! * 4) / calories;
    if (proteinPercent > 0.35) {
      warnings.add('Apporto proteico molto elevato (>${(proteinPercent * 100).round()}%)');
    }

    // Controllo grassi
    final fatPercent = (macros['fats']! * 9) / calories;
    if (fatPercent < 0.15) {
      errors.add('Apporto di grassi troppo basso (<15%)');
    }

    // Controlli specifici per condizioni mediche
    if (medicalConditions.contains('kidneyDisease') && macros['proteins']! > baseProfile.weight * 0.8) {
      errors.add('Proteine eccessive per condizione renale');
    }

    return {
      'isValid': errors.isEmpty,
      'warnings': warnings,
      'errors': errors,
      'requiresMedicalSupervision': medicalConditions.isNotEmpty && !hasConsultedDoctor,
    };
  }

  /// VERIFICA COMPLETEZZA PROFILO
  bool isProfileComplete() {
    // Controlla se i dati essenziali sono presenti
    final hasBasicInfo = baseProfile.name.isNotEmpty &&
                        baseProfile.age > 0 &&
                        baseProfile.weight > 0 &&
                        baseProfile.height > 0;

    final hasGoals = primaryGoal != null;
    final hasActivityInfo = workActivity != null && neatLevel != null;

    // Profilo considerato completo se ha almeno le informazioni base
    return hasBasicInfo && hasGoals && hasActivityInfo;
  }

  // METODI PRIVATI PER CALCOLI

  double _calculateMifflinStJeor() {
    if (baseProfile.gender == Gender.male) {
      return 10 * baseProfile.weight + 6.25 * baseProfile.height - 5 * baseProfile.age + 5;
    } else {
      return 10 * baseProfile.weight + 6.25 * baseProfile.height - 5 * baseProfile.age - 161;
    }
  }

  double _calculateKatchMcArdle() {
    if (leanMass != null) {
      return 370 + (21.6 * leanMass!);
    }
    // Fallback se non abbiamo massa magra
    return _calculateMifflinStJeor();
  }

  double _calculateHarrisBenedict() {
    if (baseProfile.gender == Gender.male) {
      return 88.362 + (13.397 * baseProfile.weight) + (4.799 * baseProfile.height) - (5.677 * baseProfile.age);
    } else {
      return 447.593 + (9.247 * baseProfile.weight) + (3.098 * baseProfile.height) - (4.330 * baseProfile.age);
    }
  }

  double _getWorkActivityFactor() {
    switch (workActivity) {
      case WorkActivity.sedentary:
        return 0.2;
      case WorkActivity.light:
        return 0.375;
      case WorkActivity.moderate:
        return 0.55;
      case WorkActivity.heavy:
        return 0.725;
    }
  }

  double _calculateExerciseFactor() {
    if (plannedExercises.isEmpty) return 0.0;

    double totalMETMinutes = 0;
    for (final exercise in plannedExercises) {
      totalMETMinutes += exercise.metValue * exercise.durationMinutes * exercise.frequencyPerWeek;
    }

    // Converti in fattore settimanale
    return (totalMETMinutes / 10080) * 0.3; // 10080 = minuti in una settimana
  }

  double _getNEATFactor() {
    switch (neatLevel) {
      case NEATLevel.low:
        return 0.15;
      case NEATLevel.medium:
        return 0.175;
      case NEATLevel.high:
        return 0.2;
    }
  }

  double _calculateProteinTarget() {
    double baseProtein = baseProfile.weight * 0.8; // Minimo RDA

    switch (primaryGoal) {
      case PrimaryGoal.weightLoss:
        baseProtein = baseProfile.weight * (weightLossDetails?.proteinMultiplier ?? 2.0);
        break;
      case PrimaryGoal.weightGain:
        baseProtein = baseProfile.weight * (muscleGainDetails?.proteinMultiplier ?? 1.8);
        break;
      case PrimaryGoal.bodyRecomposition:
        baseProtein = baseProfile.weight * 2.2;
        break;
      case PrimaryGoal.performance:
        baseProtein = baseProfile.weight * (performanceGoals?.proteinMultiplier ?? 1.6);
        break;
      case PrimaryGoal.maintenance:
        baseProtein = baseProfile.weight * 1.2;
        break;
    }

    // Aggiustamenti per condizioni mediche
    if (medicalConditions.contains('kidneyDisease')) {
      baseProtein = baseProfile.weight * 0.6; // Riduzione per problemi renali
    }

    return baseProtein;
  }

  double _calculateFatTarget(double totalCalories) {
    // Minimo 20%, massimo 35% delle calorie
    double targetFatPercent = 0.25; // Default 25%

    // Aggiustamenti per regime alimentare
    switch (dietaryRegimen) {
      case DietaryRegimen.ketogenic:
        targetFatPercent = 0.70;
        break;
      case DietaryRegimen.lowCarb:
        targetFatPercent = 0.35;
        break;
      case DietaryRegimen.mediterranean:
        targetFatPercent = 0.30;
        break;
      default:
        break;
    }

    // Aggiustamenti per obiettivi
    if (primaryGoal == PrimaryGoal.weightLoss) {
      targetFatPercent = 0.30; // Più grassi per sazietà
    }

    final fatCalories = totalCalories * targetFatPercent;
    return (fatCalories / 9).clamp(totalCalories * 0.15 / 9, totalCalories * 0.35 / 9);
  }

  // SERIALIZZAZIONE
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'createdAt': createdAt.toIso8601String(),
      'lastUpdated': lastUpdated.toIso8601String(),
      'baseProfile': baseProfile.toMap(),
      'bodyFatPercentage': bodyFatPercentage,
      'leanMass': leanMass,
      'bodyCompositionMethod': bodyCompositionMethod?.toString(),
      'circumferences': circumferences,
      'weightHistory': weightHistory.map((e) => e.toMap()).toList(),
      'dietHistory': dietHistory.map((e) => e.toMap()).toList(),
      'bloodValues': bloodValues?.toMap(),
      'preferredBMRFormula': preferredBMRFormula.toString(),
      'customBMR': customBMR,
      'customTDEE': customTDEE,
      'workActivity': workActivity.toString(),
      'plannedExercises': plannedExercises.map((e) => e.toMap()).toList(),
      'neatLevel': neatLevel.toString(),
      'primaryGoal': primaryGoal.toString(),
      'secondaryGoals': secondaryGoals.map((e) => e.toString()).toList(),
      'weightLossDetails': weightLossDetails?.toMap(),
      'muscleGainDetails': muscleGainDetails?.toMap(),
      'performanceGoals': performanceGoals?.toMap(),
      'dietaryRegimen': dietaryRegimen?.toString(),
      'dislikedFoods': dislikedFoods,
      'preferredFoods': preferredFoods,
      'budgetLevel': budgetLevel.toString(),
      'cookingTime': cookingTime.toString(),
      'mealsPerDay': mealsPerDay,
      'mealPrepHabits': mealPrepHabits.toString(),
      'cookingSkillLevel': cookingSkillLevel.toString(),
      'preferredCuisines': preferredCuisines.map((e) => e.toString()).toList(),
      'medicalConditions': medicalConditions,
      'foodAllergies': foodAllergies.map((e) => e.toMap()).toList(),
      'foodIntolerances': foodIntolerances.map((e) => e.toMap()).toList(),
      'hasConsultedDoctor': hasConsultedDoctor,
      'allowCheatMeals': allowCheatMeals,
      'cheatMealsPerWeek': cheatMealsPerWeek,
      'preferOrganicFoods': preferOrganicFoods,
      'preferLocalFoods': preferLocalFoods,
      'enableCalorieCycling': enableCalorieCycling,
      'enableMealTiming': enableMealTiming,
    };
  }

  String toJson() => json.encode(toMap());

  // Factory constructor per deserializzazione
  factory UltraDetailedProfile.fromMap(Map<String, dynamic> map) {
    return UltraDetailedProfile(
      id: map['id'] ?? '',
      createdAt: DateTime.parse(map['createdAt']),
      lastUpdated: DateTime.parse(map['lastUpdated']),
      baseProfile: UserProfile.fromMap(map['baseProfile']),
      bodyFatPercentage: map['bodyFatPercentage']?.toDouble(),
      leanMass: map['leanMass']?.toDouble(),
      bodyCompositionMethod: map['bodyCompositionMethod'] != null
          ? BodyCompositionMethod.values.firstWhere((e) => e.toString() == map['bodyCompositionMethod'])
          : null,
      circumferences: map['circumferences'] != null
          ? Map<String, double>.from(map['circumferences'])
          : null,
      weightHistory: map['weightHistory'] != null
          ? (map['weightHistory'] as List).map((e) => WeightHistoryEntry.fromMap(e)).toList()
          : [],
      dietHistory: map['dietHistory'] != null
          ? (map['dietHistory'] as List).map((e) => DietHistoryEntry.fromMap(e)).toList()
          : [],
      bloodValues: map['bloodValues'] != null
          ? BloodValues.fromMap(map['bloodValues'])
          : null,
      preferredBMRFormula: BMRFormula.values.firstWhere(
        (e) => e.toString() == map['preferredBMRFormula'],
        orElse: () => BMRFormula.mifflinStJeor,
      ),
      customBMR: map['customBMR']?.toDouble(),
      customTDEE: map['customTDEE']?.toDouble(),
      workActivity: WorkActivity.values.firstWhere(
        (e) => e.toString() == map['workActivity'],
        orElse: () => WorkActivity.sedentary,
      ),
      plannedExercises: map['plannedExercises'] != null
          ? (map['plannedExercises'] as List).map((e) => PlannedExercise.fromMap(e)).toList()
          : [],
      neatLevel: NEATLevel.values.firstWhere(
        (e) => e.toString() == map['neatLevel'],
        orElse: () => NEATLevel.medium,
      ),
      primaryGoal: PrimaryGoal.values.firstWhere(
        (e) => e.toString() == map['primaryGoal'],
        orElse: () => PrimaryGoal.maintenance,
      ),
      secondaryGoals: map['secondaryGoals'] != null
          ? (map['secondaryGoals'] as List).map((e) =>
              SecondaryGoal.values.firstWhere((sg) => sg.toString() == e)).toList()
          : [],
      weightLossDetails: map['weightLossDetails'] != null
          ? WeightLossDetails.fromMap(map['weightLossDetails'])
          : null,
      muscleGainDetails: map['muscleGainDetails'] != null
          ? MuscleGainDetails.fromMap(map['muscleGainDetails'])
          : null,
      performanceGoals: map['performanceGoals'] != null
          ? PerformanceGoals.fromMap(map['performanceGoals'])
          : null,
      dietaryRegimen: map['dietaryRegimen'] != null
          ? DietaryRegimen.values.firstWhere((e) => e.toString() == map['dietaryRegimen'])
          : null,
      dislikedFoods: List<String>.from(map['dislikedFoods'] ?? []),
      preferredFoods: List<String>.from(map['preferredFoods'] ?? []),
      budgetLevel: BudgetLevel.values.firstWhere(
        (e) => e.toString() == map['budgetLevel'],
        orElse: () => BudgetLevel.medium,
      ),
      cookingTime: CookingTime.values.firstWhere(
        (e) => e.toString() == map['cookingTime'],
        orElse: () => CookingTime.medium,
      ),
      mealsPerDay: map['mealsPerDay']?.toInt() ?? 3,
      mealPrepHabits: MealPrepHabits.values.firstWhere(
        (e) => e.toString() == map['mealPrepHabits'],
        orElse: () => MealPrepHabits.occasional,
      ),
      cookingSkillLevel: CookingSkillLevel.values.firstWhere(
        (e) => e.toString() == map['cookingSkillLevel'],
        orElse: () => CookingSkillLevel.intermediate,
      ),
      preferredCuisines: map['preferredCuisines'] != null
          ? (map['preferredCuisines'] as List).map((e) =>
              CuisineType.values.firstWhere((ct) => ct.toString() == e)).toList()
          : [],
      medicalConditions: List<String>.from(map['medicalConditions'] ?? []),
      foodAllergies: map['foodAllergies'] != null
          ? (map['foodAllergies'] as List).map((e) => FoodAllergy.fromMap(e)).toList()
          : [],
      foodIntolerances: map['foodIntolerances'] != null
          ? (map['foodIntolerances'] as List).map((e) => FoodIntolerance.fromMap(e)).toList()
          : [],
      hasConsultedDoctor: map['hasConsultedDoctor'] ?? false,
      allowCheatMeals: map['allowCheatMeals'] ?? false,
      cheatMealsPerWeek: map['cheatMealsPerWeek']?.toInt() ?? 0,
      preferOrganicFoods: map['preferOrganicFoods'] ?? false,
      preferLocalFoods: map['preferLocalFoods'] ?? false,
      enableCalorieCycling: map['enableCalorieCycling'] ?? false,
      enableMealTiming: map['enableMealTiming'] ?? false,
    );
  }

  factory UltraDetailedProfile.fromJson(String source) =>
      UltraDetailedProfile.fromMap(json.decode(source));
}
