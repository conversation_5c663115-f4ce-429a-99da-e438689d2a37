import 'package:flutter/material.dart';
import '../models/advanced_user_profile.dart';

/// Widget per selezionare condizioni mediche speciali
class MedicalConditionsSelector extends StatelessWidget {
  final List<MedicalCondition> selectedConditions;
  final Function(List<MedicalCondition>) onChanged;

  const MedicalConditionsSelector({
    Key? key,
    required this.selectedConditions,
    required this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Condizioni Mediche',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8.0),
            Text(
              'Seleziona le condizioni mediche che potrebbero influenzare la tua dieta:',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16.0),
            Wrap(
              spacing: 8.0,
              runSpacing: 8.0,
              children: MedicalCondition.values.map((condition) {
                // Salta la condizione "none" se ci sono altre condizioni selezionate
                if (condition == MedicalCondition.none &&
                    selectedConditions.length > 1 &&
                    selectedConditions.contains(MedicalCondition.none)) {
                  return const SizedBox.shrink();
                }

                final isSelected = selectedConditions.contains(condition);

                return FilterChip(
                  label: Text(_getMedicalConditionName(condition)),
                  selected: isSelected,
                  onSelected: (selected) {
                    List<MedicalCondition> newSelection = List.from(selectedConditions);

                    if (condition == MedicalCondition.none) {
                      // Se "Nessuna condizione" è selezionato, deseleziona tutte le altre
                      newSelection = selected ? [MedicalCondition.none] : [];
                    } else {
                      // Altrimenti, gestisci la selezione/deselezione normalmente
                      if (selected) {
                        // Rimuovi "Nessuna condizione" se presente
                        newSelection.remove(MedicalCondition.none);
                        newSelection.add(condition);
                      } else {
                        newSelection.remove(condition);
                        // Se non ci sono più condizioni, aggiungi "Nessuna condizione"
                        if (newSelection.isEmpty) {
                          newSelection.add(MedicalCondition.none);
                        }
                      }
                    }

                    onChanged(newSelection);
                  },
                  backgroundColor: Colors.grey[200],
                  selectedColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                  checkmarkColor: Theme.of(context).colorScheme.primary,
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  /// Ottieni il nome visualizzato di una condizione medica
  String _getMedicalConditionName(MedicalCondition condition) {
    switch (condition) {
      case MedicalCondition.none:
        return 'Nessuna condizione';
      case MedicalCondition.diabetes:
        return 'Diabete';
      case MedicalCondition.hypertension:
        return 'Ipertensione';
      case MedicalCondition.heartDisease:
        return 'Malattie cardiache';
      case MedicalCondition.kidneyDisease:
        return 'Malattie renali';
      case MedicalCondition.liverDisease:
        return 'Malattie epatiche';
      case MedicalCondition.ibs:
        return 'Sindrome dell\'intestino irritabile';
      case MedicalCondition.gerd:
        return 'Reflusso gastroesofageo';
      case MedicalCondition.celiacDisease:
        return 'Celiachia';
      case MedicalCondition.lactoseIntolerance:
        return 'Intolleranza al lattosio';
      case MedicalCondition.gout:
        return 'Gotta';
      case MedicalCondition.hypothyroidism:
        return 'Ipotiroidismo';
      case MedicalCondition.hyperthyroidism:
        return 'Ipertiroidismo';
      case MedicalCondition.pcos:
        return 'Sindrome dell\'ovaio policistico';
      case MedicalCondition.anemia:
        return 'Anemia';
      case MedicalCondition.osteoporosis:
        return 'Osteoporosi';
      case MedicalCondition.thyroidDisorders:
        return 'Disturbi tiroidei';
    }
  }
}

/// Widget per selezionare intolleranze alimentari
class FoodIntolerancesSelector extends StatelessWidget {
  final List<String> selectedIntolerances;
  final Function(List<String>) onChanged;

  const FoodIntolerancesSelector({
    Key? key,
    required this.selectedIntolerances,
    required this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Lista di intolleranze comuni
    final commonIntolerances = [
      'Glutine',
      'Lattosio',
      'Frutta a guscio',
      'Uova',
      'Soia',
      'Pesce',
      'Crostacei',
      'Arachidi',
      'Sesamo',
      'Lupini',
      'Senape',
      'Sedano',
      'Solfiti',
    ];

    return Card(
      margin: const EdgeInsets.all(8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Intolleranze Alimentari',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8.0),
            Text(
              'Seleziona le tue intolleranze alimentari:',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16.0),
            Wrap(
              spacing: 8.0,
              runSpacing: 8.0,
              children: commonIntolerances.map((intolerance) {
                final isSelected = selectedIntolerances.contains(intolerance);

                return FilterChip(
                  label: Text(intolerance),
                  selected: isSelected,
                  onSelected: (selected) {
                    List<String> newSelection = List.from(selectedIntolerances);

                    if (selected) {
                      newSelection.add(intolerance);
                    } else {
                      newSelection.remove(intolerance);
                    }

                    onChanged(newSelection);
                  },
                  backgroundColor: Colors.grey[200],
                  selectedColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                  checkmarkColor: Theme.of(context).colorScheme.primary,
                );
              }).toList(),
            ),
            const SizedBox(height: 16.0),
            TextField(
              decoration: InputDecoration(
                labelText: 'Aggiungi altra intolleranza',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.add),
                  onPressed: () {
                    // Implementazione per aggiungere intolleranze personalizzate
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Widget per selezionare obiettivi di fitness
class FitnessGoalSelector extends StatelessWidget {
  final FitnessGoal selectedGoal;
  final Function(FitnessGoal) onChanged;

  const FitnessGoalSelector({
    Key? key,
    required this.selectedGoal,
    required this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Obiettivo di Fitness',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8.0),
            Text(
              'Seleziona il tuo obiettivo principale:',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16.0),
            Wrap(
              spacing: 8.0,
              runSpacing: 8.0,
              children: FitnessGoal.values.map((goal) {
                final isSelected = selectedGoal == goal;

                return ChoiceChip(
                  label: Text(_getFitnessGoalName(goal)),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      onChanged(goal);
                    }
                  },
                  backgroundColor: Colors.grey[200],
                  selectedColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                );
              }).toList(),
            ),
            const SizedBox(height: 16.0),
            Text(
              _getFitnessGoalDescription(selectedGoal),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontStyle: FontStyle.italic,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Ottieni il nome visualizzato di un obiettivo di fitness
  String _getFitnessGoalName(FitnessGoal goal) {
    switch (goal) {
      case FitnessGoal.general:
        return 'Benessere generale';
      case FitnessGoal.weightLoss:
        return 'Perdita di peso';
      case FitnessGoal.muscleGain:
        return 'Aumento massa muscolare';
      case FitnessGoal.endurance:
        return 'Resistenza';
      case FitnessGoal.strength:
        return 'Forza';
      case FitnessGoal.flexibility:
        return 'Flessibilità';
      case FitnessGoal.athleticPerformance:
        return 'Prestazioni atletiche';
      case FitnessGoal.rehabilitation:
        return 'Riabilitazione';
    }
  }

  /// Ottieni la descrizione di un obiettivo di fitness
  String _getFitnessGoalDescription(FitnessGoal goal) {
    switch (goal) {
      case FitnessGoal.general:
        return 'Dieta bilanciata per il benessere generale e il mantenimento del peso.';
      case FitnessGoal.weightLoss:
        return 'Dieta con deficit calorico controllato per favorire la perdita di peso in modo sano.';
      case FitnessGoal.muscleGain:
        return 'Dieta ricca di proteine con surplus calorico per favorire la crescita muscolare.';
      case FitnessGoal.endurance:
        return 'Dieta ricca di carboidrati complessi per sostenere l\'attività aerobica prolungata.';
      case FitnessGoal.strength:
        return 'Dieta con adeguato apporto proteico e calorico per supportare l\'allenamento di forza.';
      case FitnessGoal.flexibility:
        return 'Dieta anti-infiammatoria per supportare la flessibilità e la mobilità articolare.';
      case FitnessGoal.athleticPerformance:
        return 'Dieta personalizzata per ottimizzare le prestazioni atletiche specifiche.';
      case FitnessGoal.rehabilitation:
        return 'Dieta ricca di nutrienti per supportare il recupero da infortuni o interventi.';
    }
  }
}
