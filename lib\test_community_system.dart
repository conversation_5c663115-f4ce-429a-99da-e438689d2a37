import 'package:flutter/material.dart';
import 'services/community_service.dart';
import 'models/community_user.dart';
import 'models/community_post.dart';
import 'models/community_group.dart';
import 'models/community_challenge.dart';

/// TEST SISTEMA COMMUNITY
/// Verifica il funzionamento della Staffilano InnerCircle™
Future<void> main() async {
  print('🌟 TEST SISTEMA COMMUNITY - STAFFILANO INNERCIRCLE™');
  print('=' * 60);
  print('Obiettivo: Verificare il funzionamento completo della community\n');

  try {
    // FASE 1: INIZIALIZZAZIONE SERVICE
    print('1️⃣ INIZIALIZZAZIONE COMMUNITY SERVICE');
    print('-' * 40);
    
    final communityService = CommunityService.instance;
    print('✅ CommunityService istanziato');
    
    await communityService.initialize();
    print('✅ CommunityService inizializzato con dati di esempio');
    
    // FASE 2: VERIFICA UTENTI
    print('\n2️⃣ VERIFICA UTENTI COMMUNITY');
    print('-' * 30);
    
    final users = await communityService.getUsers(limit: 10);
    print('📊 Utenti caricati: ${users.length}');
    
    for (int i = 0; i < users.length; i++) {
      final user = users[i];
      print('   ${i + 1}. ${user.displayName} (@${user.username})');
      print('      - Livello: ${user.membershipLevel.displayName} ${user.membershipLevel.icon}');
      print('      - Punti: ${user.communityPoints}');
      print('      - Post: ${user.totalPosts}, Like: ${user.totalLikes}');
      print('      - Follower: ${user.followersCount}');
      print('      - Verificato: ${user.isVerified ? '✅' : '❌'}');
      print('      - Mentor: ${user.isMentor ? '👨‍🏫' : '❌'}');
      print('      - Attivo: ${user.isActive ? '🟢' : '🔴'}');
      print('');
    }
    
    // FASE 3: VERIFICA POST
    print('3️⃣ VERIFICA POST FEED');
    print('-' * 20);
    
    final posts = await communityService.getFeedPosts(limit: 10);
    print('📊 Post caricati: ${posts.length}');
    
    for (int i = 0; i < posts.length; i++) {
      final post = posts[i];
      print('   ${i + 1}. ${post.type.emoji} ${post.type.displayName}');
      print('      - Autore: ${post.author?.displayName ?? 'Sconosciuto'}');
      print('      - Contenuto: ${post.content.substring(0, post.content.length > 50 ? 50 : post.content.length)}${post.content.length > 50 ? '...' : ''}');
      print('      - Like: ${post.likesCount}, Commenti: ${post.commentsCount}, Condivisioni: ${post.sharesCount}');
      print('      - Tempo: ${post.timeAgo}');
      print('      - Verificato: ${post.isVerified ? '✅' : '❌'}');
      print('      - Pinned: ${post.isPinned ? '📌' : '❌'}');
      print('      - Immagini: ${post.imageUrls.length}');
      print('      - Dati allegati: ${post.attachedData != null ? '📎' : '❌'}');
      print('');
    }
    
    // FASE 4: VERIFICA GRUPPI
    print('4️⃣ VERIFICA GRUPPI COMMUNITY');
    print('-' * 25);
    
    final groups = await communityService.getGroups(limit: 10);
    print('📊 Gruppi caricati: ${groups.length}');
    
    for (int i = 0; i < groups.length; i++) {
      final group = groups[i];
      print('   ${i + 1}. ${group.fullDisplayName}');
      print('      - Tipo: ${group.type.displayName} ${group.type.emoji}');
      print('      - Categoria: ${group.category.displayName}');
      print('      - Membri: ${group.membersCount}');
      print('      - Post: ${group.postsCount}');
      print('      - Attivo: ${group.isRecentlyActive ? '🟢' : '🔴'}');
      print('      - In evidenza: ${group.isFeatured ? '⭐' : '❌'}');
      print('      - Popolare: ${group.isPopular ? '🔥' : '❌'}');
      print('      - Tasso attività: ${group.activityRate.toStringAsFixed(2)}');
      print('');
    }
    
    // FASE 5: VERIFICA SFIDE
    print('5️⃣ VERIFICA SFIDE COMMUNITY');
    print('-' * 25);
    
    final challenges = await communityService.getChallenges(limit: 10);
    print('📊 Sfide caricate: ${challenges.length}');
    
    for (int i = 0; i < challenges.length; i++) {
      final challenge = challenges[i];
      print('   ${i + 1}. ${challenge.fullDisplayName}');
      print('      - Tipo: ${challenge.type.displayName} ${challenge.type.emoji}');
      print('      - Difficoltà: ${challenge.difficulty.displayName} ${challenge.difficulty.emoji}');
      print('      - Partecipanti: ${challenge.currentParticipants}');
      print('      - Completati: ${challenge.completedCount}');
      print('      - Tasso completamento: ${(challenge.completionRate * 100).toStringAsFixed(1)}%');
      print('      - Stato: ${challenge.isOngoing ? '🔥 Attiva' : challenge.isUpcoming ? '⏰ Prossima' : '✅ Completata'}');
      print('      - Giorni rimanenti: ${challenge.daysRemaining}');
      print('      - Punti reward: ${challenge.difficulty.points}');
      print('      - Posti disponibili: ${challenge.hasAvailableSlots ? '✅' : '❌'}');
      print('');
    }
    
    // FASE 6: STATISTICHE GENERALI
    print('6️⃣ STATISTICHE GENERALI COMMUNITY');
    print('-' * 35);
    
    final totalUsers = users.length;
    final verifiedUsers = users.where((u) => u.isVerified).length;
    final mentors = users.where((u) => u.isMentor).length;
    final activeUsers = users.where((u) => u.isActive).length;
    
    final totalPosts = posts.length;
    final verifiedPosts = posts.where((p) => p.isVerified).length;
    final pinnedPosts = posts.where((p) => p.isPinned).length;
    final postsWithImages = posts.where((p) => p.imageUrls.isNotEmpty).length;
    
    final totalGroups = groups.length;
    final featuredGroups = groups.where((g) => g.isFeatured).length;
    final activeGroups = groups.where((g) => g.isRecentlyActive).length;
    final popularGroups = groups.where((g) => g.isPopular).length;
    
    final totalChallenges = challenges.length;
    final activeChallenges = challenges.where((c) => c.isOngoing).length;
    final upcomingChallenges = challenges.where((c) => c.isUpcoming).length;
    final completedChallenges = challenges.where((c) => c.isCompleted).length;
    
    print('👥 UTENTI:');
    print('   - Totali: $totalUsers');
    print('   - Verificati: $verifiedUsers (${(verifiedUsers / totalUsers * 100).toStringAsFixed(1)}%)');
    print('   - Mentor: $mentors (${(mentors / totalUsers * 100).toStringAsFixed(1)}%)');
    print('   - Attivi: $activeUsers (${(activeUsers / totalUsers * 100).toStringAsFixed(1)}%)');
    
    print('\n📝 POST:');
    print('   - Totali: $totalPosts');
    print('   - Verificati: $verifiedPosts (${(verifiedPosts / totalPosts * 100).toStringAsFixed(1)}%)');
    print('   - Pinned: $pinnedPosts (${(pinnedPosts / totalPosts * 100).toStringAsFixed(1)}%)');
    print('   - Con immagini: $postsWithImages (${(postsWithImages / totalPosts * 100).toStringAsFixed(1)}%)');
    
    print('\n👥 GRUPPI:');
    print('   - Totali: $totalGroups');
    print('   - In evidenza: $featuredGroups (${(featuredGroups / totalGroups * 100).toStringAsFixed(1)}%)');
    print('   - Attivi: $activeGroups (${(activeGroups / totalGroups * 100).toStringAsFixed(1)}%)');
    print('   - Popolari: $popularGroups (${(popularGroups / totalGroups * 100).toStringAsFixed(1)}%)');
    
    print('\n🏆 SFIDE:');
    print('   - Totali: $totalChallenges');
    print('   - Attive: $activeChallenges (${(activeChallenges / totalChallenges * 100).toStringAsFixed(1)}%)');
    print('   - Prossime: $upcomingChallenges (${(upcomingChallenges / totalChallenges * 100).toStringAsFixed(1)}%)');
    print('   - Completate: $completedChallenges (${(completedChallenges / totalChallenges * 100).toStringAsFixed(1)}%)');
    
    // FASE 7: VERIFICA FUNZIONALITÀ SPECIFICHE
    print('\n7️⃣ VERIFICA FUNZIONALITÀ SPECIFICHE');
    print('-' * 35);
    
    // Test livelli membership
    print('🏅 LIVELLI MEMBERSHIP:');
    for (final level in MembershipLevel.values) {
      final usersAtLevel = users.where((u) => u.membershipLevel == level).length;
      print('   - ${level.displayName} ${level.icon}: $usersAtLevel utenti (${level.requiredPoints} punti richiesti)');
    }
    
    // Test tipi di post
    print('\n📝 TIPI DI POST:');
    for (final type in PostType.values) {
      final postsOfType = posts.where((p) => p.type == type).length;
      print('   - ${type.displayName} ${type.emoji}: $postsOfType post');
    }
    
    // Test categorie gruppi
    print('\n👥 CATEGORIE GRUPPI:');
    for (final category in GroupCategory.values) {
      final groupsInCategory = groups.where((g) => g.category == category).length;
      print('   - ${category.displayName} ${category.emoji}: $groupsInCategory gruppi');
    }
    
    // Test tipi di sfide
    print('\n🏆 TIPI DI SFIDE:');
    for (final type in ChallengeType.values) {
      final challengesOfType = challenges.where((c) => c.type == type).length;
      print('   - ${type.displayName} ${type.emoji}: $challengesOfType sfide');
    }
    
    // FASE 8: VALUTAZIONE FINALE
    print('\n8️⃣ VALUTAZIONE FINALE');
    print('-' * 20);
    
    final hasUsers = totalUsers > 0;
    final hasPosts = totalPosts > 0;
    final hasGroups = totalGroups > 0;
    final hasChallenges = totalChallenges > 0;
    final hasVerifiedContent = verifiedUsers > 0 && verifiedPosts > 0;
    final hasActiveContent = activeUsers > 0 && activeGroups > 0 && activeChallenges > 0;
    
    print('✅ CONTROLLI SISTEMA:');
    print('   ${hasUsers ? '✅' : '❌'} Utenti presenti: $hasUsers');
    print('   ${hasPosts ? '✅' : '❌'} Post presenti: $hasPosts');
    print('   ${hasGroups ? '✅' : '❌'} Gruppi presenti: $hasGroups');
    print('   ${hasChallenges ? '✅' : '❌'} Sfide presenti: $hasChallenges');
    print('   ${hasVerifiedContent ? '✅' : '❌'} Contenuto verificato: $hasVerifiedContent');
    print('   ${hasActiveContent ? '✅' : '❌'} Contenuto attivo: $hasActiveContent');
    
    print('\n' + '=' * 60);
    
    if (hasUsers && hasPosts && hasGroups && hasChallenges && hasVerifiedContent && hasActiveContent) {
      print('🎉 SUCCESSO COMPLETO!');
      print('LA STAFFILANO INNERCIRCLE™ È PRONTA!');
      
      print('\n🌟 CARATTERISTICHE IMPLEMENTATE:');
      print('   ✅ Sistema utenti con livelli membership');
      print('   ✅ Feed post con diversi tipi di contenuto');
      print('   ✅ Gruppi tematici con categorizzazione');
      print('   ✅ Sistema sfide con gamificazione');
      print('   ✅ Contenuto verificato e di qualità');
      print('   ✅ Utenti mentor e sistema di supporto');
      print('   ✅ Statistiche e metriche complete');
      
      print('\n🚀 PRONTO PER IL LANCIO:');
      print('   - Community completamente funzionale');
      print('   - Dati di esempio realistici');
      print('   - Sistema di gamificazione attivo');
      print('   - Integrazione con WellJourney™ pronta');
      print('   - Design Dr. Staffilano implementato');
      
    } else {
      print('⚠️ PROBLEMI RILEVATI');
      print('Alcuni componenti della community necessitano di attenzione.');
      
      if (!hasUsers) print('❌ Problema: Nessun utente caricato');
      if (!hasPosts) print('❌ Problema: Nessun post caricato');
      if (!hasGroups) print('❌ Problema: Nessun gruppo caricato');
      if (!hasChallenges) print('❌ Problema: Nessuna sfida caricata');
      if (!hasVerifiedContent) print('❌ Problema: Manca contenuto verificato');
      if (!hasActiveContent) print('❌ Problema: Manca contenuto attivo');
    }
    
    print('\n🎯 PROSSIMI PASSI:');
    print('   1. Testare l\'interfaccia utente della community');
    print('   2. Verificare la navigazione tra le sezioni');
    print('   3. Testare le interazioni (like, commenti, condivisioni)');
    print('   4. Validare l\'integrazione con WellJourney™');
    print('   5. Controllare le prestazioni con dati reali');
    
  } catch (e, stackTrace) {
    print('\n❌ ERRORE DURANTE IL TEST: $e');
    print('Stack trace: $stackTrace');
  }
}
