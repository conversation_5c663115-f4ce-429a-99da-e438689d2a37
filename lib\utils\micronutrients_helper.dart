/// Classe di utilità per la gestione dei micronutrienti
class MicronutrientsHelper {
  // Costanti per i nomi standardizzati dei micronutrienti
  static const String CALCIUM = 'calcium';
  static const String PHOSPHORUS = 'phosphorus';
  static const String MAGNESIUM = 'magnesium';
  static const String SODIUM = 'sodium';
  static const String POTASSIUM = 'potassium';
  static const String IRON = 'iron';
  static const String ZINC = 'zinc';
  static const String VITAMIN_A = 'vitamin_a';
  static const String VITAMIN_C = 'vitamin_c';
  static const String VITAMIN_D = 'vitamin_d';
  static const String VITAMIN_E = 'vitamin_e';
  static const String VITAMIN_K = 'vitamin_k';
  static const String VITAMIN_B1 = 'vitamin_b1'; // Tiamina
  static const String VITAMIN_B2 = 'vitamin_b2'; // Riboflavina
  static const String VITAMIN_B3 = 'vitamin_b3'; // Niacina
  static const String VITAMIN_B5 = 'vitamin_b5'; // Acido pantotenico
  static const String VITAMIN_B6 = 'vitamin_b6'; // Piridossina
  static const String VITAMIN_B9 = 'vitamin_b9'; // Folati
  static const String VITAMIN_B12 = 'vitamin_b12'; // Cobalamina

  // Unità di misura per i micronutrienti
  static const Map<String, String> UNITS = {
    CALCIUM: 'mg',
    PHOSPHORUS: 'mg',
    MAGNESIUM: 'mg',
    SODIUM: 'mg',
    POTASSIUM: 'mg',
    IRON: 'mg',
    ZINC: 'mg',
    VITAMIN_A: 'μg',
    VITAMIN_C: 'mg',
    VITAMIN_D: 'μg',
    VITAMIN_E: 'mg',
    VITAMIN_K: 'μg',
    VITAMIN_B1: 'mg',
    VITAMIN_B2: 'mg',
    VITAMIN_B3: 'mg',
    VITAMIN_B5: 'mg',
    VITAMIN_B6: 'mg',
    VITAMIN_B9: 'μg',
    VITAMIN_B12: 'μg',
  };

  // Nomi visualizzati per i micronutrienti
  static const Map<String, String> DISPLAY_NAMES = {
    CALCIUM: 'Calcio',
    PHOSPHORUS: 'Fosforo',
    MAGNESIUM: 'Magnesio',
    SODIUM: 'Sodio',
    POTASSIUM: 'Potassio',
    IRON: 'Ferro',
    ZINC: 'Zinco',
    VITAMIN_A: 'Vitamina A',
    VITAMIN_C: 'Vitamina C',
    VITAMIN_D: 'Vitamina D',
    VITAMIN_E: 'Vitamina E',
    VITAMIN_K: 'Vitamina K',
    VITAMIN_B1: 'Vitamina B1 (Tiamina)',
    VITAMIN_B2: 'Vitamina B2 (Riboflavina)',
    VITAMIN_B3: 'Vitamina B3 (Niacina)',
    VITAMIN_B5: 'Vitamina B5 (Acido pantotenico)',
    VITAMIN_B6: 'Vitamina B6 (Piridossina)',
    VITAMIN_B9: 'Vitamina B9 (Folati)',
    VITAMIN_B12: 'Vitamina B12 (Cobalamina)',
  };

  // Valori di riferimento giornalieri (RDA) per adulti
  static const Map<String, double> DAILY_REFERENCE_VALUES = {
    CALCIUM: 1000.0, // mg
    PHOSPHORUS: 700.0, // mg
    MAGNESIUM: 400.0, // mg
    SODIUM: 1500.0, // mg
    POTASSIUM: 3500.0, // mg
    IRON: 14.0, // mg
    ZINC: 10.0, // mg
    VITAMIN_A: 800.0, // μg
    VITAMIN_C: 80.0, // mg
    VITAMIN_D: 5.0, // μg
    VITAMIN_E: 12.0, // mg
    VITAMIN_K: 75.0, // μg
    VITAMIN_B1: 1.1, // mg
    VITAMIN_B2: 1.4, // mg
    VITAMIN_B3: 16.0, // mg
    VITAMIN_B5: 6.0, // mg
    VITAMIN_B6: 1.4, // mg
    VITAMIN_B9: 200.0, // μg
    VITAMIN_B12: 2.5, // μg
  };

  // Crea una mappa di micronutrienti con i valori specificati
  static Map<String, double> createMicronutrientsMap({
    double calcium = 0.0,
    double phosphorus = 0.0,
    double magnesium = 0.0,
    double sodium = 0.0,
    double potassium = 0.0,
    double iron = 0.0,
    double zinc = 0.0,
    double vitaminA = 0.0,
    double vitaminC = 0.0,
    double vitaminD = 0.0,
    double vitaminE = 0.0,
    double vitaminK = 0.0,
    double vitaminB1 = 0.0,
    double vitaminB2 = 0.0,
    double vitaminB3 = 0.0,
    double vitaminB5 = 0.0,
    double vitaminB6 = 0.0,
    double vitaminB9 = 0.0,
    double vitaminB12 = 0.0,
  }) {
    final Map<String, double> micronutrients = {};

    if (calcium > 0) micronutrients[CALCIUM] = calcium;
    if (phosphorus > 0) micronutrients[PHOSPHORUS] = phosphorus;
    if (magnesium > 0) micronutrients[MAGNESIUM] = magnesium;
    if (sodium > 0) micronutrients[SODIUM] = sodium;
    if (potassium > 0) micronutrients[POTASSIUM] = potassium;
    if (iron > 0) micronutrients[IRON] = iron;
    if (zinc > 0) micronutrients[ZINC] = zinc;
    if (vitaminA > 0) micronutrients[VITAMIN_A] = vitaminA;
    if (vitaminC > 0) micronutrients[VITAMIN_C] = vitaminC;
    if (vitaminD > 0) micronutrients[VITAMIN_D] = vitaminD;
    if (vitaminE > 0) micronutrients[VITAMIN_E] = vitaminE;
    if (vitaminK > 0) micronutrients[VITAMIN_K] = vitaminK;
    if (vitaminB1 > 0) micronutrients[VITAMIN_B1] = vitaminB1;
    if (vitaminB2 > 0) micronutrients[VITAMIN_B2] = vitaminB2;
    if (vitaminB3 > 0) micronutrients[VITAMIN_B3] = vitaminB3;
    if (vitaminB5 > 0) micronutrients[VITAMIN_B5] = vitaminB5;
    if (vitaminB6 > 0) micronutrients[VITAMIN_B6] = vitaminB6;
    if (vitaminB9 > 0) micronutrients[VITAMIN_B9] = vitaminB9;
    if (vitaminB12 > 0) micronutrients[VITAMIN_B12] = vitaminB12;

    return micronutrients;
  }

  // Calcola la percentuale del valore giornaliero raccomandato
  static double calculateDailyValuePercentage(String nutrient, double amount) {
    if (!DAILY_REFERENCE_VALUES.containsKey(nutrient)) return 0.0;
    final referenceValue = DAILY_REFERENCE_VALUES[nutrient]!;
    if (referenceValue <= 0) return 0.0;
    return (amount / referenceValue) * 100;
  }

  // Formatta il valore del micronutriente con l'unità di misura
  static String formatMicronutrientValue(String nutrient, double amount) {
    if (!UNITS.containsKey(nutrient)) return '$amount';
    return '$amount ${UNITS[nutrient]}';
  }

  // Ottieni il nome visualizzato del micronutriente
  static String getDisplayName(String nutrient) {
    return DISPLAY_NAMES[nutrient] ?? nutrient;
  }

  // Verifica se una chiave è un minerale
  static bool isMineralKey(String key) {
    return key == CALCIUM ||
           key == PHOSPHORUS ||
           key == MAGNESIUM ||
           key == SODIUM ||
           key == POTASSIUM ||
           key == IRON ||
           key == ZINC;
  }

  // Calcola i micronutrienti per una porzione specifica
  static Map<String, double> calculateMicronutrientsForServing(
    Map<String, dynamic> micronutrients,
    int grams
  ) {
    final factor = grams / 100;
    final Map<String, double> result = {};

    micronutrients.forEach((key, value) {
      final double doubleValue = value is double ? value : (value as num).toDouble();
      result[key] = doubleValue * factor;
    });

    return result;
  }
}
