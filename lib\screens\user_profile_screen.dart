import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:uuid/uuid.dart';
import '../models/user_profile.dart';
import '../services/storage_service.dart';
import '../theme/app_theme.dart';

class UserProfileScreen extends StatefulWidget {
  final UserProfile? initialProfile;
  final Function(UserProfile) onProfileSaved;

  const UserProfileScreen({
    super.key,
    this.initialProfile,
    required this.onProfileSaved,
  });

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  StorageService? _storageService;

  // Controllers per i campi di input
  final _ageController = TextEditingController();
  final _heightController = TextEditingController();
  final _weightController = TextEditingController();
  final _targetWeightController = TextEditingController();
  final _weeklyGoalController = TextEditingController();

  // Valori selezionati per i dropdown
  Gender _selectedGender = Gender.male;
  ActivityLevel _selectedActivityLevel = ActivityLevel.moderatelyActive;
  DietGoal _selectedDietGoal = DietGoal.weightLoss;
  DietType _selectedDietType = DietType.omnivore;
  int _selectedMealsPerDay = 3;

  // Liste per allergeni e cibi non graditi
  final List<String> _allergies = [];
  final List<String> _dislikedFoods = [];

  // Allergeni comuni
  final List<String> _commonAllergies = [
    'Latticini',
    'Glutine',
    'Frutta a guscio',
    'Uova',
    'Pesce',
    'Crostacei',
    'Soia',
    'Arachidi',
  ];

  @override
  void initState() {
    super.initState();
    _initializeServices();

    // Se c'è un profilo iniziale, inizializza i campi con i suoi valori
    if (widget.initialProfile != null) {
      _initializeFromProfile(widget.initialProfile!);
    } else {
      // Valori predefiniti
      _ageController.text = '30';
      _heightController.text = '170';
      _weightController.text = '70';
      _targetWeightController.text = '65';
      _weeklyGoalController.text = '0.5';
    }
  }

  Future<void> _initializeServices() async {
    try {
      _storageService = await StorageService.getInstance();
    } catch (e) {
      print('Errore nell\'inizializzazione dei servizi: $e');
    }
  }

  void _initializeFromProfile(UserProfile profile) {
    _ageController.text = profile.age.toString();
    _heightController.text = profile.height.toString();
    _weightController.text = profile.weight.toString();
    _targetWeightController.text = profile.targetWeight.toString();
    _weeklyGoalController.text = profile.weeklyGoal.toString();

    _selectedGender = profile.gender;
    _selectedActivityLevel = profile.activityLevel;
    _selectedDietGoal = profile.goal;
    _selectedDietType = profile.dietType;
    _selectedMealsPerDay = profile.mealsPerDay;

    _allergies.addAll(profile.allergies);
    _dislikedFoods.addAll(profile.dislikedFoods);
  }

  @override
  void dispose() {
    _ageController.dispose();
    _heightController.dispose();
    _weightController.dispose();
    _targetWeightController.dispose();
    _weeklyGoalController.dispose();
    super.dispose();
  }

  void _saveProfile() async {
    if (_formKey.currentState!.validate() && _storageService != null) {
      // Crea un nuovo profilo utente con i valori inseriti
      final userProfile = UserProfile(
        id: const Uuid().v4(),
        age: int.parse(_ageController.text),
        gender: _selectedGender,
        height: int.parse(_heightController.text),
        weight: int.parse(_weightController.text),
        activityLevel: _selectedActivityLevel,
        goal: _selectedDietGoal,
        dietType: _selectedDietType,
        allergies: _allergies,
        dislikedFoods: _dislikedFoods,
        mealsPerDay: _selectedMealsPerDay,
        targetWeight: _selectedDietGoal == Goal.maintenance ?
            int.parse(_weightController.text) :
            int.parse(_targetWeightController.text),
        weeklyGoal: double.parse(_weeklyGoalController.text),
      );

      // Salva il profilo
      await _storageService!.salvaUserProfile(userProfile);

      // Notifica il parent
      widget.onProfileSaved(userProfile);

      // Mostra un messaggio di conferma
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Profilo salvato con successo'),
            backgroundColor: AppTheme.successColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );

        // Torna indietro
        Navigator.of(context).pop();
      }
    }
  }

  void _addAllergy() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Aggiungi allergia'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Seleziona un allergene o inseriscine uno personalizzato:'),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _commonAllergies.map((allergy) {
                final isSelected = _allergies.contains(allergy);
                return FilterChip(
                  label: Text(allergy),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        _allergies.add(allergy);
                      } else {
                        _allergies.remove(allergy);
                      }
                    });
                  },
                  selectedColor: AppTheme.primaryColor.withOpacity(0.2),
                  checkmarkColor: AppTheme.primaryColor,
                );
              }).toList(),
            ),
            const SizedBox(height: 16),
            TextField(
              decoration: const InputDecoration(
                labelText: 'Altro allergene',
                hintText: 'Inserisci un allergene personalizzato',
                border: OutlineInputBorder(),
              ),
              onSubmitted: (value) {
                if (value.isNotEmpty) {
                  setState(() {
                    _allergies.add(value);
                  });
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Chiudi'),
          ),
        ],
      ),
    );
  }

  void _addDislikedFood() {
    final controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Aggiungi cibo non gradito'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Inserisci un alimento che non ti piace:'),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              decoration: const InputDecoration(
                labelText: 'Nome alimento',
                hintText: 'es. Broccoli, Funghi, ecc.',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annulla'),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text.isNotEmpty) {
                setState(() {
                  _dislikedFoods.add(controller.text);
                });
                Navigator.of(context).pop();
              }
            },
            child: const Text('Aggiungi'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Il tuo profilo'),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Sezione dati anagrafici e biometrici
              _buildSectionHeader('Dati personali', FontAwesomeIcons.userLarge),

              // Età
              TextFormField(
                controller: _ageController,
                decoration: const InputDecoration(
                  labelText: 'Età',
                  hintText: 'Inserisci la tua età',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(FontAwesomeIcons.cakeCandles),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Inserisci la tua età';
                  }
                  final age = int.tryParse(value);
                  if (age == null || age < 18 || age > 100) {
                    return 'Inserisci un\'età valida (18-100)';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Sesso
              DropdownButtonFormField<Gender>(
                value: _selectedGender,
                decoration: const InputDecoration(
                  labelText: 'Sesso',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(FontAwesomeIcons.venusMars),
                ),
                items: Gender.values.map((gender) {
                  return DropdownMenuItem<Gender>(
                    value: gender,
                    child: Text(gender == Gender.male ? 'Uomo' : 'Donna'),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedGender = value;
                    });
                  }
                },
              ),

              const SizedBox(height: 16),

              // Altezza e peso (in una riga)
              Row(
                children: [
                  // Altezza
                  Expanded(
                    child: TextFormField(
                      controller: _heightController,
                      decoration: const InputDecoration(
                        labelText: 'Altezza (cm)',
                        hintText: 'es. 170',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(FontAwesomeIcons.rulerVertical),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Inserisci la tua altezza';
                        }
                        final height = double.tryParse(value);
                        if (height == null || height < 100 || height > 250) {
                          return 'Altezza non valida';
                        }
                        return null;
                      },
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Peso
                  Expanded(
                    child: TextFormField(
                      controller: _weightController,
                      decoration: const InputDecoration(
                        labelText: 'Peso (kg)',
                        hintText: 'es. 70',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(FontAwesomeIcons.weightScale),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Inserisci il tuo peso';
                        }
                        final weight = double.tryParse(value);
                        if (weight == null || weight < 30 || weight > 300) {
                          return 'Peso non valido';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Livello di attività fisica
              DropdownButtonFormField<ActivityLevel>(
                value: _selectedActivityLevel,
                decoration: const InputDecoration(
                  labelText: 'Livello di attività fisica',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(FontAwesomeIcons.personRunning),
                ),
                items: [
                  DropdownMenuItem<ActivityLevel>(
                    value: ActivityLevel.sedentary,
                    child: Text('Sedentario (poco o nessun esercizio)'),
                  ),
                  DropdownMenuItem<ActivityLevel>(
                    value: ActivityLevel.lightlyActive,
                    child: Text('Leggermente attivo (esercizio 1-3 volte/settimana)'),
                  ),
                  DropdownMenuItem<ActivityLevel>(
                    value: ActivityLevel.moderatelyActive,
                    child: Text('Moderatamente attivo (esercizio 3-5 volte/settimana)'),
                  ),
                  DropdownMenuItem<ActivityLevel>(
                    value: ActivityLevel.veryActive,
                    child: Text('Molto attivo (esercizio 6-7 volte/settimana)'),
                  ),
                  DropdownMenuItem<ActivityLevel>(
                    value: ActivityLevel.extremelyActive,
                    child: Text('Estremamente attivo (esercizio intenso, lavoro fisico)'),
                  ),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedActivityLevel = value;
                    });
                  }
                },
              ),

              const SizedBox(height: 24),

              // Sezione obiettivo
              _buildSectionHeader('Obiettivo', FontAwesomeIcons.bullseye),

              // Obiettivo principale
              DropdownButtonFormField<DietGoal>(
                value: _selectedDietGoal,
                decoration: const InputDecoration(
                  labelText: 'Obiettivo principale',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(FontAwesomeIcons.chartLine),
                ),
                items: [
                  DropdownMenuItem<DietGoal>(
                    value: DietGoal.weightLoss,
                    child: Text('Perdita di peso'),
                  ),
                  DropdownMenuItem<DietGoal>(
                    value: DietGoal.maintenance,
                    child: Text('Mantenimento del peso'),
                  ),
                  DropdownMenuItem<DietGoal>(
                    value: DietGoal.weightGain,
                    child: Text('Aumento di peso'),
                  ),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedDietGoal = value;
                    });
                  }
                },
              ),

              const SizedBox(height: 16),

              // Peso target (solo per perdita peso o aumento massa)
              if (_selectedDietGoal != DietGoal.maintenance)
                TextFormField(
                  controller: _targetWeightController,
                  decoration: InputDecoration(
                    labelText: _selectedDietGoal == DietGoal.weightLoss
                        ? 'Peso target (kg)'
                        : 'Peso target (kg)',
                    hintText: 'es. 65',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(FontAwesomeIcons.weightScale),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Inserisci il peso target';
                    }
                    final targetWeight = double.tryParse(value);
                    if (targetWeight == null || targetWeight < 30 || targetWeight > 300) {
                      return 'Peso target non valido';
                    }
                    return null;
                  },
                ),

              if (_selectedDietGoal != DietGoal.maintenance)
                const SizedBox(height: 16),

              // Velocità di cambiamento (kg/settimana)
              if (_selectedDietGoal != DietGoal.maintenance)
                TextFormField(
                  controller: _weeklyGoalController,
                  decoration: InputDecoration(
                    labelText: _selectedDietGoal == DietGoal.weightLoss
                        ? 'Velocità di perdita (kg/settimana)'
                        : 'Velocità di aumento (kg/settimana)',
                    hintText: 'es. 0.5',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(FontAwesomeIcons.gauge),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Inserisci la velocità di cambiamento';
                    }
                    final weeklyGoal = double.tryParse(value);
                    if (weeklyGoal == null || weeklyGoal < 0.1 || weeklyGoal > 1.0) {
                      return 'Valore non valido (0.1-1.0)';
                    }
                    return null;
                  },
                ),

              if (_selectedDietGoal != DietGoal.maintenance)
                const SizedBox(height: 16),

              // Numero di pasti giornalieri
              DropdownButtonFormField<int>(
                value: _selectedMealsPerDay,
                decoration: const InputDecoration(
                  labelText: 'Numero di pasti giornalieri',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(FontAwesomeIcons.utensils),
                ),
                items: [3, 4, 5, 6].map((mealsCount) {
                  String description;
                  switch (mealsCount) {
                    case 3:
                      description = '3 pasti principali';
                      break;
                    case 4:
                      description = '3 pasti principali + 1 spuntino';
                      break;
                    case 5:
                      description = '3 pasti principali + 2 spuntini';
                      break;
                    case 6:
                      description = '3 pasti principali + 3 spuntini';
                      break;
                    default:
                      description = '$mealsCount pasti';
                  }
                  return DropdownMenuItem<int>(
                    value: mealsCount,
                    child: Text(description),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedMealsPerDay = value;
                    });
                  }
                },
              ),

              const SizedBox(height: 24),

              // Sezione preferenze alimentari
              _buildSectionHeader('Preferenze alimentari', FontAwesomeIcons.bowlFood),

              // Tipo di dieta
              DropdownButtonFormField<DietType>(
                value: _selectedDietType,
                decoration: const InputDecoration(
                  labelText: 'Tipo di dieta',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(FontAwesomeIcons.leaf),
                ),
                items: [
                  DropdownMenuItem<DietType>(
                    value: DietType.omnivore,
                    child: Text('Onnivoro (mangio di tutto)'),
                  ),
                  DropdownMenuItem<DietType>(
                    value: DietType.vegetarian,
                    child: Text('Vegetariano (no carne)'),
                  ),
                  DropdownMenuItem<DietType>(
                    value: DietType.vegan,
                    child: Text('Vegano (no prodotti animali)'),
                  ),
                  DropdownMenuItem<DietType>(
                    value: DietType.pescatarian,
                    child: Text('Pescetariano (vegetariano + pesce)'),
                  ),
                  DropdownMenuItem<DietType>(
                    value: DietType.keto,
                    child: Text('Keto (basso contenuto di carboidrati)'),
                  ),
                  DropdownMenuItem<DietType>(
                    value: DietType.paleo,
                    child: Text('Paleo (cibi non processati)'),
                  ),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedDietType = value;
                    });
                  }
                },
              ),

              const SizedBox(height: 16),

              // Allergie
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Allergie e intolleranze',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      TextButton.icon(
                        onPressed: _addAllergy,
                        icon: const Icon(Icons.add),
                        label: const Text('Aggiungi'),
                      ),
                    ],
                  ),
                  if (_allergies.isEmpty)
                    const Padding(
                      padding: EdgeInsets.symmetric(vertical: 8.0),
                      child: Text('Nessuna allergia o intolleranza specificata'),
                    )
                  else
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: _allergies.map((allergy) {
                        return Chip(
                          label: Text(allergy),
                          deleteIcon: const Icon(Icons.close, size: 18),
                          onDeleted: () {
                            setState(() {
                              _allergies.remove(allergy);
                            });
                          },
                        );
                      }).toList(),
                    ),
                ],
              ),

              const SizedBox(height: 16),

              // Cibi non graditi
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Cibi non graditi',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      TextButton.icon(
                        onPressed: _addDislikedFood,
                        icon: const Icon(Icons.add),
                        label: const Text('Aggiungi'),
                      ),
                    ],
                  ),
                  if (_dislikedFoods.isEmpty)
                    const Padding(
                      padding: EdgeInsets.symmetric(vertical: 8.0),
                      child: Text('Nessun cibo non gradito specificato'),
                    )
                  else
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: _dislikedFoods.map((food) {
                        return Chip(
                          label: Text(food),
                          deleteIcon: const Icon(Icons.close, size: 18),
                          onDeleted: () {
                            setState(() {
                              _dislikedFoods.remove(food);
                            });
                          },
                        );
                      }).toList(),
                    ),
                ],
              ),

              const SizedBox(height: 32),

              // Pulsante salva
              Center(
                child: ElevatedButton.icon(
                  onPressed: _saveProfile,
                  icon: const Icon(Icons.save),
                  label: const Text('Salva profilo'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 16,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        children: [
          Icon(icon, color: AppTheme.primaryColor),
          const SizedBox(width: 8),
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
