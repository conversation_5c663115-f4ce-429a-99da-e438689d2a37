import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import '../models/welljourney_models.dart';
import '../services/advanced_challenges_service.dart';
import '../services/storage_service.dart';

/// Controller per gestire lo stato delle sfide avanzate Dr. Staffilano
class AdvancedChallengesController extends ChangeNotifier {
  static const String _selectedCategoryKey = 'selected_challenge_category';
  static const String _filterDifficultyKey = 'filter_challenge_difficulty';
  static const String _sortOrderKey = 'challenge_sort_order';

  final AdvancedChallengesService _challengesService = AdvancedChallengesService.instance;

  // Stato del controller
  ChallengeCategory? _selectedCategory;
  ChallengeDifficulty? _filterDifficulty;
  ChallengeSortOrder _sortOrder = ChallengeSortOrder.newest;
  bool _isLoading = false;
  String? _errorMessage;
  bool _isInitialized = false;

  // Getters
  List<AdvancedChallenge> get activeChallenges => _challengesService.activeChallenges;
  List<AdvancedChallenge> get availableChallenges => _getFilteredAvailableChallenges();
  Map<String, ChallengeProgress> get challengeProgress => _challengesService.challengeProgress;
  List<ChallengeHistory> get challengeHistory => _challengesService.challengeHistory;

  ChallengeCategory? get selectedCategory => _selectedCategory;
  ChallengeDifficulty? get filterDifficulty => _filterDifficulty;
  ChallengeSortOrder get sortOrder => _sortOrder;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isInitialized => _isInitialized;

  /// Inizializza il controller
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      print('🔄 Inizializzazione AdvancedChallengesController...');
      _isLoading = true;

      // Inizializza il servizio
      await _challengesService.initialize();

      // Carica le preferenze salvate
      await _loadPreferences();

      // Ascolta i cambiamenti del servizio
      _challengesService.addListener(_onServiceChanged);

      _isInitialized = true;
      _isLoading = false;
      _errorMessage = null;

      print('✅ AdvancedChallengesController inizializzato con successo');

      // Notifica i cambiamenti dopo l'inizializzazione
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Errore nell\'inizializzazione: $e';
      print('❌ Errore nell\'inizializzazione AdvancedChallengesController: $e');

      // Notifica gli errori dopo l'inizializzazione
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }

  /// Callback per i cambiamenti del servizio
  void _onServiceChanged() {
    notifyListeners();
  }

  /// Carica le preferenze salvate
  Future<void> _loadPreferences() async {
    try {
      final storage = await StorageService.getInstance();

      // Carica categoria selezionata
      final categoryIndex = await storage.getInt(_selectedCategoryKey);
      if (categoryIndex != null && categoryIndex < ChallengeCategory.values.length) {
        _selectedCategory = ChallengeCategory.values[categoryIndex];
      }

      // Carica filtro difficoltà
      final difficultyIndex = await storage.getInt(_filterDifficultyKey);
      if (difficultyIndex != null && difficultyIndex < ChallengeDifficulty.values.length) {
        _filterDifficulty = ChallengeDifficulty.values[difficultyIndex];
      }

      // Carica ordinamento
      final sortIndex = await storage.getInt(_sortOrderKey);
      if (sortIndex != null && sortIndex < ChallengeSortOrder.values.length) {
        _sortOrder = ChallengeSortOrder.values[sortIndex];
      }
    } catch (e) {
      print('❌ Errore nel caricamento preferenze: $e');
    }
  }

  /// Salva le preferenze
  Future<void> _savePreferences() async {
    try {
      final storage = await StorageService.getInstance();

      if (_selectedCategory != null) {
        await storage.setInt(_selectedCategoryKey, _selectedCategory!.index);
      }

      if (_filterDifficulty != null) {
        await storage.setInt(_filterDifficultyKey, _filterDifficulty!.index);
      }

      await storage.setInt(_sortOrderKey, _sortOrder.index);
    } catch (e) {
      print('❌ Errore nel salvataggio preferenze: $e');
    }
  }

  /// Ottieni sfide disponibili filtrate
  List<AdvancedChallenge> _getFilteredAvailableChallenges() {
    var challenges = List<AdvancedChallenge>.from(_challengesService.availableChallenges);

    // Filtra per categoria
    if (_selectedCategory != null) {
      challenges = challenges.where((c) => c.category == _selectedCategory).toList();
    }

    // Filtra per difficoltà
    if (_filterDifficulty != null) {
      challenges = challenges.where((c) => c.difficulty == _filterDifficulty).toList();
    }

    // Ordina
    switch (_sortOrder) {
      case ChallengeSortOrder.newest:
        challenges.sort((a, b) => b.startDate.compareTo(a.startDate));
        break;
      case ChallengeSortOrder.oldest:
        challenges.sort((a, b) => a.startDate.compareTo(b.startDate));
        break;
      case ChallengeSortOrder.pointsHigh:
        challenges.sort((a, b) => b.points.compareTo(a.points));
        break;
      case ChallengeSortOrder.pointsLow:
        challenges.sort((a, b) => a.points.compareTo(b.points));
        break;
      case ChallengeSortOrder.difficulty:
        challenges.sort((a, b) => b.difficulty.index.compareTo(a.difficulty.index));
        break;
      case ChallengeSortOrder.duration:
        challenges.sort((a, b) => a.durationDays.compareTo(b.durationDays));
        break;
    }

    return challenges;
  }

  /// Imposta la categoria selezionata
  Future<void> setSelectedCategory(ChallengeCategory? category) async {
    if (_selectedCategory != category) {
      _selectedCategory = category;
      await _savePreferences();
      notifyListeners();
    }
  }

  /// Imposta il filtro difficoltà
  Future<void> setFilterDifficulty(ChallengeDifficulty? difficulty) async {
    if (_filterDifficulty != difficulty) {
      _filterDifficulty = difficulty;
      await _savePreferences();
      notifyListeners();
    }
  }

  /// Imposta l'ordinamento
  Future<void> setSortOrder(ChallengeSortOrder sortOrder) async {
    if (_sortOrder != sortOrder) {
      _sortOrder = sortOrder;
      await _savePreferences();
      notifyListeners();
    }
  }

  /// Partecipa a una sfida
  Future<bool> joinChallenge(String challengeId) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final success = await _challengesService.joinChallenge(challengeId);

      _isLoading = false;
      if (!success) {
        _errorMessage = 'Impossibile partecipare alla sfida';
      }
      notifyListeners();

      return success;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Errore nella partecipazione: $e';
      notifyListeners();
      return false;
    }
  }

  /// Abbandona una sfida
  Future<bool> leaveChallenge(String challengeId) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final success = await _challengesService.leaveChallenge(challengeId);

      _isLoading = false;
      if (!success) {
        _errorMessage = 'Impossibile abbandonare la sfida';
      }
      notifyListeners();

      return success;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Errore nell\'abbandono: $e';
      notifyListeners();
      return false;
    }
  }

  /// Aggiorna manualmente il progresso
  Future<void> updateManualProgress(String challengeId, double progress) async {
    try {
      await _challengesService.updateManualProgress(challengeId, progress);
    } catch (e) {
      _errorMessage = 'Errore nell\'aggiornamento progresso: $e';
      notifyListeners();
    }
  }

  /// Ottieni il progresso di una sfida
  ChallengeProgress? getChallengeProgress(String challengeId) {
    return challengeProgress[challengeId];
  }

  /// Ottieni statistiche delle sfide
  ChallengeStatistics getStatistics() {
    final totalCompleted = challengeHistory.length;
    final totalPoints = challengeHistory.fold<int>(0, (sum, history) => sum + history.pointsEarned);
    final averageCompletion = challengeHistory.isNotEmpty
        ? challengeHistory.map((h) => h.finalProgress).reduce((a, b) => a + b) / challengeHistory.length
        : 0.0;

    final categoryStats = <ChallengeCategory, int>{};
    for (final history in challengeHistory) {
      // Trova la sfida corrispondente per ottenere la categoria
      final challenge = _challengesService.availableChallenges
          .cast<AdvancedChallenge?>()
          .firstWhere((c) => c?.id == history.challengeId, orElse: () => null);
      if (challenge != null) {
        categoryStats[challenge.category] = (categoryStats[challenge.category] ?? 0) + 1;
      }
    }

    return ChallengeStatistics(
      totalCompleted: totalCompleted,
      totalActive: activeChallenges.length,
      totalPoints: totalPoints,
      averageCompletion: averageCompletion,
      categoryStats: categoryStats,
      longestStreak: _calculateLongestStreak(),
      currentStreak: _calculateCurrentStreak(),
    );
  }

  /// Calcola la striscia più lunga
  int _calculateLongestStreak() {
    // TODO: Implementare calcolo striscia più lunga
    return 0;
  }

  /// Calcola la striscia corrente
  int _calculateCurrentStreak() {
    // TODO: Implementare calcolo striscia corrente
    return 0;
  }

  /// Pulisce le risorse
  @override
  void dispose() {
    _challengesService.removeListener(_onServiceChanged);
    super.dispose();
  }
}

/// Ordinamento delle sfide
enum ChallengeSortOrder {
  newest,
  oldest,
  pointsHigh,
  pointsLow,
  difficulty,
  duration,
}

/// Statistiche delle sfide
class ChallengeStatistics {
  final int totalCompleted;
  final int totalActive;
  final int totalPoints;
  final double averageCompletion;
  final Map<ChallengeCategory, int> categoryStats;
  final int longestStreak;
  final int currentStreak;

  const ChallengeStatistics({
    required this.totalCompleted,
    required this.totalActive,
    required this.totalPoints,
    required this.averageCompletion,
    required this.categoryStats,
    required this.longestStreak,
    required this.currentStreak,
  });
}

extension ChallengeSortOrderExtension on ChallengeSortOrder {
  String get displayName {
    switch (this) {
      case ChallengeSortOrder.newest:
        return 'Più Recenti';
      case ChallengeSortOrder.oldest:
        return 'Più Vecchie';
      case ChallengeSortOrder.pointsHigh:
        return 'Punti Alti';
      case ChallengeSortOrder.pointsLow:
        return 'Punti Bassi';
      case ChallengeSortOrder.difficulty:
        return 'Difficoltà';
      case ChallengeSortOrder.duration:
        return 'Durata';
    }
  }
}
