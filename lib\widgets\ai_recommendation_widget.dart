import 'package:flutter/material.dart';
import '../ai/models/ai_models.dart';
import '../ai/services/ai_service.dart';
import '../models/food.dart';
import '../models/user_profile.dart';
import '../utils/ui_utils.dart';

/// Widget che mostra le raccomandazioni dell'AI
class AIRecommendationWidget extends StatefulWidget {
  final UserProfile userProfile;
  final String? mealType;
  final Function(Food) onFoodSelected;

  const AIRecommendationWidget({
    Key? key,
    required this.userProfile,
    this.mealType,
    required this.onFoodSelected,
  }) : super(key: key);

  @override
  _AIRecommendationWidgetState createState() => _AIRecommendationWidgetState();
}

class _AIRecommendationWidgetState extends State<AIRecommendationWidget> {
  bool _isLoading = true;
  String _errorMessage = '';
  List<FoodRecommendation> _recommendations = [];

  @override
  void initState() {
    super.initState();
    _loadRecommendations();
  }

  @override
  void didUpdateWidget(AIRecommendationWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.userProfile.id != widget.userProfile.id ||
        oldWidget.mealType != widget.mealType) {
      _loadRecommendations();
    }
  }

  Future<void> _loadRecommendations() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final aiService = await AIService.getInstance();
      
      final request = AIRecommendationRequest(
        userProfile: widget.userProfile,
        mealType: widget.mealType,
        targetCalories: widget.userProfile.calculateCalorieTarget() ~/ 4, // Circa un pasto
        targetMacros: widget.userProfile.calculateMacroGrams(),
      );
      
      final response = await aiService.getRecommendations(request);
      
      setState(() {
        _recommendations = response.recommendedFoods;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Errore nel caricamento delle raccomandazioni: $e';
        _isLoading = false;
      });
      print(_errorMessage);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage.isNotEmpty) {
      return Center(
        child: Text(
          _errorMessage,
          style: const TextStyle(color: Colors.red),
        ),
      );
    }

    if (_recommendations.isEmpty) {
      return const Center(
        child: Text('Nessuna raccomandazione disponibile'),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Text(
            'Raccomandazioni personalizzate',
            style: Theme.of(context).textTheme.titleLarge,
          ),
        ),
        const SizedBox(height: 8),
        Expanded(
          child: ListView.builder(
            itemCount: _recommendations.length,
            itemBuilder: (context, index) {
              final recommendation = _recommendations[index];
              return _buildRecommendationCard(recommendation);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildRecommendationCard(FoodRecommendation recommendation) {
    final food = recommendation.food;
    final score = recommendation.score;
    final reason = recommendation.reason;
    
    // Calcola il colore in base al punteggio
    final scoreColor = _getScoreColor(score);
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      elevation: 2,
      child: InkWell(
        onTap: () => widget.onFoodSelected(food),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      food.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: scoreColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${(score * 100).round()}%',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                reason,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildNutrientChip('Calorie', '${food.calories} kcal', Colors.orange),
                  _buildNutrientChip('Proteine', '${food.proteins}g', Colors.red),
                  _buildNutrientChip('Carboidrati', '${food.carbs}g', Colors.blue),
                  _buildNutrientChip('Grassi', '${food.fats}g', Colors.green),
                ],
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: food.categories.map((category) {
                  return Chip(
                    label: Text(UIUtils.getFoodCategoryName(category)),
                    backgroundColor: UIUtils.getCategoryColor(category).withOpacity(0.2),
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNutrientChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color.withOpacity(0.8),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Color _getScoreColor(double score) {
    if (score >= 0.8) {
      return Colors.green;
    } else if (score >= 0.6) {
      return Colors.lightGreen;
    } else if (score >= 0.4) {
      return Colors.amber;
    } else if (score >= 0.2) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
