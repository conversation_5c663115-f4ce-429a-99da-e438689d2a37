import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/community_user.dart';
import '../../models/friend_request.dart';
import '../../services/friendship_service.dart';
import '../../services/community_profile_service.dart';
import '../../theme/dr_staffilano_theme.dart';
import '../../widgets/responsive_text.dart';
import '../../utils/responsive_utils.dart';
import 'user_profile_screen.dart';

/// Schermata per la gestione degli amici
class FriendsScreen extends StatefulWidget {
  const FriendsScreen({super.key});

  @override
  State<FriendsScreen> createState() => _FriendsScreenState();
}

class _FriendsScreenState extends State<FriendsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Barra di ricerca
          _buildSearchBar(),

          // Tab bar
          _buildTabBar(),

          // Contenuto delle tab
          Expanded(
            child: _buildTabBarView(),
          ),
        ],
      ),
    );
  }

  /// Costruisce la barra di ricerca
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Cerca amici...',
          prefixIcon: const Icon(Icons.search, color: DrStaffilanoTheme.primaryGreen),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _searchController.clear();
                    setState(() => _searchQuery = '');
                  },
                  icon: const Icon(Icons.clear),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25),
            borderSide: BorderSide(color: Colors.grey.withOpacity(0.3)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25),
            borderSide: const BorderSide(color: DrStaffilanoTheme.primaryGreen),
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        ),
        onChanged: (value) {
          setState(() => _searchQuery = value);
        },
      ),
    );
  }

  /// Costruisce la TabBar
  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        indicatorColor: DrStaffilanoTheme.primaryGreen,
        labelColor: DrStaffilanoTheme.primaryGreen,
        unselectedLabelColor: Colors.grey[600],
        labelStyle: const TextStyle(fontWeight: FontWeight.w600),
        tabs: [
          Tab(
            child: Consumer<FriendshipService>(
              builder: (context, friendshipService, child) {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.people, size: ResponsiveUtils.getResponsiveIconSize(context, 16)),
                    SizedBox(width: ResponsiveUtils.isMobile(context) ? 2 : 4),
                    Flexible(
                      child: ResponsiveText(
                        'Amici (${friendshipService.friends.length})',
                        baseFontSize: 12,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
          Tab(
            child: Consumer<FriendshipService>(
              builder: (context, friendshipService, child) {
                final currentUser = context.read<CommunityProfileService>().currentUser;
                final incomingRequests = currentUser != null
                    ? friendshipService.getIncomingRequests(currentUser.id)
                    : <FriendRequest>[];

                return Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.person_add, size: ResponsiveUtils.getResponsiveIconSize(context, 16)),
                    SizedBox(width: ResponsiveUtils.isMobile(context) ? 2 : 4),
                    Flexible(
                      child: ResponsiveText(
                        'Richieste (${incomingRequests.length})',
                        baseFontSize: 12,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (incomingRequests.isNotEmpty) ...[
                      SizedBox(width: ResponsiveUtils.isMobile(context) ? 2 : 4),
                      Container(
                        width: 6,
                        height: 6,
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ],
                  ],
                );
              },
            ),
          ),
          Tab(
            child: Consumer<FriendshipService>(
              builder: (context, friendshipService, child) {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.lightbulb_outline, size: ResponsiveUtils.getResponsiveIconSize(context, 16)),
                    SizedBox(width: ResponsiveUtils.isMobile(context) ? 2 : 4),
                    Flexible(
                      child: ResponsiveText(
                        'Suggerimenti (${friendshipService.friendSuggestions.length})',
                        baseFontSize: 12,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Costruisce il contenuto delle tab
  Widget _buildTabBarView() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildFriendsTab(),
        _buildRequestsTab(),
        _buildSuggestionsTab(),
      ],
    );
  }

  /// Tab degli amici
  Widget _buildFriendsTab() {
    return Consumer<FriendshipService>(
      builder: (context, friendshipService, child) {
        final friends = friendshipService.friends.toList();

        if (friends.isEmpty) {
          return _buildEmptyState(
            'Nessun amico ancora',
            'Inizia a connetterti con altri membri\ndella community!',
            Icons.people_outline,
          );
        }

        // Filtra per ricerca
        final filteredFriends = _searchQuery.isEmpty
            ? friends
            : friends.where((friendId) =>
                friendId.toLowerCase().contains(_searchQuery.toLowerCase())
              ).toList();

        if (filteredFriends.isEmpty) {
          return _buildEmptyState(
            'Nessun risultato',
            'Nessun amico trovato per "$_searchQuery"',
            Icons.search_off,
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: filteredFriends.length,
          itemBuilder: (context, index) {
            return _buildFriendCard(filteredFriends[index], true);
          },
        );
      },
    );
  }

  /// Tab delle richieste
  Widget _buildRequestsTab() {
    return Consumer2<FriendshipService, CommunityProfileService>(
      builder: (context, friendshipService, profileService, child) {
        final currentUser = profileService.currentUser;
        if (currentUser == null) {
          return _buildEmptyState(
            'Utente non trovato',
            'Effettua l\'accesso per vedere le richieste',
            Icons.person_off,
          );
        }

        final incomingRequests = friendshipService.getIncomingRequests(currentUser.id);
        final outgoingRequests = friendshipService.getOutgoingRequests(currentUser.id);

        if (incomingRequests.isEmpty && outgoingRequests.isEmpty) {
          return _buildEmptyState(
            'Nessuna richiesta',
            'Non hai richieste di amicizia\nin entrata o in uscita',
            Icons.inbox,
          );
        }

        return ListView(
          padding: const EdgeInsets.all(16),
          children: [
            if (incomingRequests.isNotEmpty) ...[
              _buildSectionHeader('Richieste ricevute', incomingRequests.length),
              ...incomingRequests.map((request) => _buildRequestCard(request, true)),
              const SizedBox(height: 16),
            ],
            if (outgoingRequests.isNotEmpty) ...[
              _buildSectionHeader('Richieste inviate', outgoingRequests.length),
              ...outgoingRequests.map((request) => _buildRequestCard(request, false)),
            ],
          ],
        );
      },
    );
  }

  /// Tab dei suggerimenti
  Widget _buildSuggestionsTab() {
    return Consumer<FriendshipService>(
      builder: (context, friendshipService, child) {
        final suggestions = friendshipService.friendSuggestions;

        if (suggestions.isEmpty) {
          return _buildEmptyState(
            'Nessun suggerimento',
            'Al momento non ci sono suggerimenti\ndi amicizia disponibili',
            Icons.lightbulb_outline,
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: suggestions.length,
          itemBuilder: (context, index) {
            return _buildSuggestionCard(suggestions[index]);
          },
        );
      },
    );
  }

  /// Costruisce una card dell'amico
  Widget _buildFriendCard(String friendId, bool isFriend) {
    // Per ora usiamo dati simulati
    final mockUser = _generateMockUser(friendId);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: DrStaffilanoTheme.primaryGreen,
          child: Text(
            mockUser['displayName'][0].toUpperCase(),
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          mockUser['displayName'],
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('@${mockUser['username']}'),
            if (mockUser['bio'] != null)
              Text(
                mockUser['bio'],
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () => _showUserProfile(mockUser),
              icon: const Icon(Icons.person),
              tooltip: 'Vedi profilo',
            ),
            if (isFriend)
              IconButton(
                onPressed: () => _showFriendOptions(friendId),
                icon: const Icon(Icons.more_vert),
                tooltip: 'Opzioni',
              ),
          ],
        ),
        onTap: () => _showUserProfile(mockUser),
      ),
    );
  }

  /// Costruisce una card della richiesta
  Widget _buildRequestCard(FriendRequest request, bool isIncoming) {
    final mockUser = _generateMockUser(isIncoming ? request.fromUserId : request.toUserId);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: DrStaffilanoTheme.primaryGreen,
          child: Text(
            mockUser['displayName'][0].toUpperCase(),
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          mockUser['displayName'],
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('@${mockUser['username']}'),
            Text(
              _formatDate(request.createdAt),
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            if (request.message != null)
              Text(
                request.message!,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[700],
                  fontStyle: FontStyle.italic,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
          ],
        ),
        trailing: isIncoming
            ? Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    onPressed: () => _acceptRequest(request),
                    icon: const Icon(Icons.check, color: Colors.green),
                    tooltip: 'Accetta',
                  ),
                  IconButton(
                    onPressed: () => _rejectRequest(request),
                    icon: const Icon(Icons.close, color: Colors.red),
                    tooltip: 'Rifiuta',
                  ),
                ],
              )
            : Text(
                'In attesa',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
      ),
    );
  }

  /// Costruisce una card del suggerimento
  Widget _buildSuggestionCard(String suggestionId) {
    final mockUser = _generateMockUser(suggestionId);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: DrStaffilanoTheme.primaryGreen,
          child: Text(
            mockUser['displayName'][0].toUpperCase(),
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          mockUser['displayName'],
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('@${mockUser['username']}'),
            Text(
              'Interessi comuni: ${mockUser['commonInterests']}',
              style: TextStyle(
                fontSize: 12,
                color: DrStaffilanoTheme.primaryGreen,
              ),
            ),
          ],
        ),
        trailing: ElevatedButton(
          onPressed: () => _sendFriendRequest(suggestionId),
          style: ElevatedButton.styleFrom(
            backgroundColor: DrStaffilanoTheme.primaryGreen,
            foregroundColor: Colors.white,
            minimumSize: const Size(80, 32),
          ),
          child: const Text('Aggiungi', style: TextStyle(fontSize: 12)),
        ),
        onTap: () => _showUserProfile(mockUser),
      ),
    );
  }

  /// Costruisce l'header di una sezione
  Widget _buildSectionHeader(String title, int count) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: DrStaffilanoTheme.primaryGreen,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              count.toString(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Costruisce lo stato vuoto
  Widget _buildEmptyState(String title, String description, IconData icon) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              description,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Genera un utente simulato
  Map<String, dynamic> _generateMockUser(String userId) {
    final names = ['Marco Rossi', 'Laura Bianchi', 'Giuseppe Verdi', 'Anna Neri', 'Francesco Blu'];
    final bios = [
      'Appassionato di cardiologia preventiva',
      'Medico specializzando in medicina interna',
      'Ricercatore in scienze cardiovascolari',
      'Nutrizionista clinica',
      'Fisioterapista specializzato in riabilitazione cardiaca',
    ];
    final interests = ['Cardiologia, Prevenzione', 'Nutrizione, Benessere', 'Ricerca, Medicina'];

    final index = userId.hashCode % names.length;

    return {
      'id': userId,
      'displayName': names[index],
      'username': 'user_${userId.substring(0, 8)}',
      'bio': bios[index],
      'commonInterests': interests[index % interests.length],
    };
  }

  /// Mostra il profilo di un utente
  void _showUserProfile(Map<String, dynamic> userData) {
    // Crea un CommunityUser temporaneo per la visualizzazione
    final user = CommunityUser.create(
      username: userData['username'],
      displayName: userData['displayName'],
      bio: userData['bio'],
    );

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => UserProfileScreen(user: user),
      ),
    );
  }

  /// Mostra le opzioni per un amico
  void _showFriendOptions(String friendId) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.message, color: DrStaffilanoTheme.primaryGreen),
              title: const Text('Invia messaggio'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('💬 Messaggi - Coming soon!')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.person_remove, color: Colors.red),
              title: const Text('Rimuovi amico'),
              onTap: () {
                Navigator.pop(context);
                _removeFriend(friendId);
              },
            ),
            ListTile(
              leading: const Icon(Icons.block, color: Colors.red),
              title: const Text('Blocca utente'),
              onTap: () {
                Navigator.pop(context);
                _blockUser(friendId);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Accetta una richiesta di amicizia
  Future<void> _acceptRequest(FriendRequest request) async {
    try {
      final friendshipService = context.read<FriendshipService>();
      final success = await friendshipService.acceptFriendRequest(request.id);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('✅ Richiesta di amicizia accettata!'),
            backgroundColor: DrStaffilanoTheme.primaryGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Errore: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Rifiuta una richiesta di amicizia
  Future<void> _rejectRequest(FriendRequest request) async {
    try {
      final friendshipService = context.read<FriendshipService>();
      final success = await friendshipService.rejectFriendRequest(request.id);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('❌ Richiesta di amicizia rifiutata'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Errore: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Invia una richiesta di amicizia
  Future<void> _sendFriendRequest(String toUserId) async {
    try {
      final currentUser = context.read<CommunityProfileService>().currentUser;
      if (currentUser == null) return;

      final friendshipService = context.read<FriendshipService>();
      final success = await friendshipService.sendFriendRequest(
        fromUserId: currentUser.id,
        toUserId: toUserId,
        message: 'Ciao! Vorrei aggiungerti come amico su Staffilano InnerCircle™',
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('📨 Richiesta di amicizia inviata!'),
            backgroundColor: DrStaffilanoTheme.primaryGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Errore: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Rimuove un amico
  Future<void> _removeFriend(String friendId) async {
    try {
      final currentUser = context.read<CommunityProfileService>().currentUser;
      if (currentUser == null) return;

      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Rimuovi amico'),
          content: const Text('Sei sicuro di voler rimuovere questo amico?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Annulla'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('Rimuovi', style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        final friendshipService = context.read<FriendshipService>();
        final success = await friendshipService.removeFriend(currentUser.id, friendId);

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('💔 Amico rimosso'),
              backgroundColor: DrStaffilanoTheme.primaryGreen,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Errore: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Blocca un utente
  Future<void> _blockUser(String userId) async {
    try {
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Blocca utente'),
          content: const Text('Sei sicuro di voler bloccare questo utente? Non potrai più vedere i suoi contenuti.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Annulla'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('Blocca', style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        final friendshipService = context.read<FriendshipService>();
        final success = await friendshipService.blockUser(userId);

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('🚫 Utente bloccato'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Errore: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Formatta una data
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes} minuti fa';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} ore fa';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} giorni fa';
    } else {
      return '${(difference.inDays / 7).floor()} settimane fa';
    }
  }
}
