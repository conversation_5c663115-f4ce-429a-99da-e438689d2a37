name: nutrip<PERSON>_staffilano
description: "Un ecosistema di benessere nutrizionale iper-personalizzato, adattivo e proattivo, guidato dal Dr. Staffilano."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 2.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # Persistenza dei dati
  shared_preferences: ^2.2.2

  # Grafici per statistiche
  fl_chart: ^1.0.0

  # Formattazione date
  intl: ^0.19.0

  # Tipografia
  google_fonts: ^6.1.0

  # Animazioni
  flutter_animate: ^4.5.0
  confetti: ^0.7.0

  # Icone
  font_awesome_flutter: ^10.7.0

  # Immagini
  cached_network_image: ^3.3.1

  # Generazione ID
  uuid: ^4.3.3

  # HTTP client per API
  http: ^1.2.1

  # Supabase Database
  supabase_flutter: ^2.3.4
  crypto: ^3.0.3

  # JSON Serialization
  json_annotation: ^4.8.1

  # Selezione file
  file_picker: 10.1.8

  # Dependency injection
  get_it: ^7.6.7

  # State management
  provider: ^6.1.2

  # Camera e immagini
  camera: ^0.10.5+9
  image_picker: ^1.0.7
  image: ^4.1.7
  path_provider: ^2.1.2
  path: ^1.8.3

  # Emoji picker
  emoji_picker_flutter: ^2.0.0

  # Geolocalizzazione
  geolocator: ^10.1.0
  geocoding: ^2.1.1

  # Machine Learning
  # tflite_flutter: ^0.9.0  # Temporaneamente disabilitato per build APK
  # tflite_flutter_helper: ^0.3.1  # Conflitto di versioni

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  build_runner: ^2.4.15
  json_serializable: ^6.9.5

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/
    - assets/models/
    - assets/ml_models/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Playfair
  #     fonts:
  #       - asset: assets/fonts/PlayfairDisplay-Regular.ttf
  #       - asset: assets/fonts/PlayfairDisplay-Italic.ttf
  #         style: italic
  #       - asset: assets/fonts/PlayfairDisplay-Bold.ttf
  #         weight: 700
  #   - family: Montserrat
  #     fonts:
  #       - asset: assets/fonts/Montserrat-Regular.ttf
  #       - asset: assets/fonts/Montserrat-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Montserrat-Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
