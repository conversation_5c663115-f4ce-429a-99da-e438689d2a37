import 'package:flutter/material.dart';
import 'package:camera/camera.dart';

/// Widget per l'anteprima della fotocamera
class CameraPreviewWidget extends StatelessWidget {
  /// Controller della fotocamera
  final CameraController? controller;

  /// Callback quando viene toccata l'anteprima
  final VoidCallback? onTap;

  const CameraPreviewWidget({
    Key? key,
    this.controller,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Verifica che il controller sia disponibile e inizializzato
    if (controller == null || !controller!.value.isInitialized) {
      return Container(
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                color: Colors.white,
              ),
              SizedBox(height: 16),
              Text(
                'Inizializzazione fotocamera...',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      );
    }

    try {
      // Ottieni le dimensioni dello schermo
      final size = MediaQuery.of(context).size;

      // Calcola il rapporto di aspetto della fotocamera in modo sicuro
      final cameraAspectRatio = controller!.value.aspectRatio;
      final screenAspectRatio = size.aspectRatio;

      // Evita divisioni per zero e valori non validi
      double scale = 1.0;
      if (cameraAspectRatio > 0 && screenAspectRatio > 0) {
        scale = 1 / (cameraAspectRatio * screenAspectRatio);
        // Limita il scale a valori ragionevoli
        scale = scale.clamp(0.5, 2.0);
      }

      return GestureDetector(
        onTap: onTap,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8.0),
          child: Transform.scale(
            scale: scale,
            alignment: Alignment.center,
            child: Center(
              child: CameraPreview(controller!),
            ),
          ),
        ),
      );
    } catch (e) {
      print('❌ Errore nel CameraPreviewWidget: $e');

      // Widget di fallback in caso di errore
      return Container(
        decoration: BoxDecoration(
          color: Colors.red.shade100,
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.camera_alt_outlined,
                size: 48,
                color: Colors.red,
              ),
              SizedBox(height: 16),
              Text(
                'Errore fotocamera',
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Riavvia l\'app per riprovare',
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      );
    }
  }
}
