import 'dart:math';
import 'package:flutter/material.dart' hide Badge;
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/badge_models.dart';
import '../services/badge_service.dart';
import '../controllers/badge_controller.dart';
import '../widgets/badge_widgets.dart';
import '../theme/dr_staffilano_theme.dart';

/// Schermata per visualizzare tutti i badge
class BadgesScreen extends StatefulWidget {
  const BadgesScreen({Key? key}) : super(key: key);

  @override
  State<BadgesScreen> createState() => _BadgesScreenState();
}

class _BadgesScreenState extends State<BadgesScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _backgroundAnimation;
  final BadgeController _badgeController = BadgeController.instance;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 7, vsync: this);

    // Background animation for particle effects
    _backgroundAnimation = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();

    _initializeBadgeSystem();
  }

  /// Inizializza il sistema badge
  Future<void> _initializeBadgeSystem() async {
    if (!_badgeController.isInitialized) {
      await _badgeController.initialize();
    }
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _backgroundAnimation.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDarkMode
          ? DrStaffilanoTheme.backgroundDark
          : DrStaffilanoTheme.backgroundLight,
      body: FutureBuilder(
        future: _loadBadgeData(),
        builder: (context, snapshot) {
          if (!snapshot.hasData) {
            return const Center(child: CircularProgressIndicator());
          }

          final data = snapshot.data as Map<String, dynamic>;
          final allBadges = data['allBadges'] as List<AppBadge>;
          final progressList = data['progressList'] as List<BadgeProgress>;
          final unlockedBadges = data['unlockedBadges'] as List<String>;

          return NestedScrollView(
            physics: const BouncingScrollPhysics(),
            headerSliverBuilder: (context, innerBoxIsScrolled) => [
              _buildCollapsingSliverAppBar(unlockedBadges.length, allBadges.length),
            ],
            body: Column(
              children: [
                // Contenuto con tab
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      // Tutti i badge
                      _buildBadgeTab(allBadges, progressList, unlockedBadges),
                      // Badge punti
                      _buildBadgeTab(
                        allBadges.where((b) => b.type == BadgeType.points).toList(),
                        progressList,
                        unlockedBadges,
                      ),
                      // Badge percorsi
                      _buildBadgeTab(
                        allBadges.where((b) => b.type == BadgeType.pathway).toList(),
                        progressList,
                        unlockedBadges,
                      ),
                      // Badge streak
                      _buildBadgeTab(
                        allBadges.where((b) => b.type == BadgeType.streak).toList(),
                        progressList,
                        unlockedBadges,
                      ),
                      // Badge Food Oracle
                      _buildBadgeTab(
                        allBadges.where((b) => b.type == BadgeType.foodOracle).toList(),
                        progressList,
                        unlockedBadges,
                      ),
                      // Badge NutriScore
                      _buildBadgeTab(
                        allBadges.where((b) => b.type == BadgeType.nutriScore).toList(),
                        progressList,
                        unlockedBadges,
                      ),
                      // Badge speciali
                      _buildBadgeTab(
                        allBadges.where((b) => b.type == BadgeType.special).toList(),
                        progressList,
                        unlockedBadges,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// Collapsing SliverAppBar per i badge
  Widget _buildCollapsingSliverAppBar(int unlockedCount, int totalCount) {
    return SliverAppBar(
      backgroundColor: Colors.transparent,
      foregroundColor: Colors.white,
      expandedHeight: 320, // Increased height to accommodate integrated tab bar
      floating: true, // Enables floating behavior
      pinned: false, // Allows complete hiding
      snap: true, // Smooth snap animations
      flexibleSpace: FlexibleSpaceBar(
        centerTitle: true,
        titlePadding: const EdgeInsets.only(bottom: 230), // Increased to accommodate integrated tab bar
        title: const Text(
          'Badge Dr. Staffilano',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
            color: Colors.white,
            shadows: [
              Shadow(
                offset: Offset(0, 1),
                blurRadius: 3,
                color: Colors.black54,
              ),
            ],
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                DrStaffilanoTheme.primaryGreen,
                DrStaffilanoTheme.professionalBlue,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow: [
              BoxShadow(
                color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Stack(
            children: [
              // Title-focused particles positioned around the title area
              ...List.generate(12, (index) => _buildBadgesTitleParticle(index)),
              SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Column(
                    children: [
                      const SizedBox(height: 100), // Increased spacing to avoid title overlap with statistics
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Icon(
                              FontAwesomeIcons.trophy,
                              color: Colors.white,
                              size: 18,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              '$unlockedCount/$totalCount badge sbloccati',
                              style: TextStyle(
                                fontSize: 13,
                                color: Colors.white.withOpacity(0.9),
                              ),
                            ),
                          ),
                          // Test actions menu
                          if (true) // TODO: Change to kDebugMode
                            PopupMenuButton<String>(
                              icon: const Icon(Icons.more_vert, color: Colors.white, size: 20),
                              onSelected: _handleTestAction,
                              itemBuilder: (context) => [
                                const PopupMenuItem(
                                  value: 'test_points',
                                  child: Text('Test Punti (+100)'),
                                ),
                                const PopupMenuItem(
                                  value: 'test_challenge',
                                  child: Text('Test Sfida Completata'),
                                ),
                                const PopupMenuItem(
                                  value: 'test_scan',
                                  child: Text('Test Scansione Food Oracle'),
                                ),
                                const PopupMenuItem(
                                  value: 'force_check',
                                  child: Text('Forza Controllo Badge'),
                                ),
                              ],
                            ),
                        ],
                      ),
                      const SizedBox(height: 20), // Increased spacing before progress indicator
                      // Progress indicator
                      _buildCompactProgressIndicator(unlockedCount, totalCount),
                      const SizedBox(height: 16), // Spacing before tab bar
                      // Integrated tab bar in header
                      _buildIntegratedTabBar(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Compact progress indicator for header
  Widget _buildCompactProgressIndicator(int unlocked, int total) {
    final progress = total > 0 ? unlocked / total : 0.0;
    return Container(
      margin: const EdgeInsets.only(top: 8), // Additional top margin for separation
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.white.withOpacity(0.3)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.emoji_events,
                  color: DrStaffilanoTheme.accentGold,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  'Progresso: ${(progress * 100).round()}%',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.white.withOpacity(0.3),
            valueColor: AlwaysStoppedAnimation<Color>(DrStaffilanoTheme.accentGold),
            minHeight: 5,
          ),
        ],
      ),
    );
  }

  /// Integrated tab bar for header
  Widget _buildIntegratedTabBar() {
    return Container(
      height: 50,
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: TabBar(
        controller: _tabController,
        indicatorColor: DrStaffilanoTheme.accentGold,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white.withOpacity(0.7),
        indicatorWeight: 3,
        labelStyle: const TextStyle(fontWeight: FontWeight.w600, fontSize: 11),
        unselectedLabelStyle: const TextStyle(fontWeight: FontWeight.normal, fontSize: 10),
        indicator: BoxDecoration(
          color: DrStaffilanoTheme.accentGold.withOpacity(0.3),
          borderRadius: BorderRadius.circular(8),
        ),
        indicatorPadding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
        labelPadding: const EdgeInsets.symmetric(horizontal: 8),
        tabs: const [
          Tab(text: 'Tutti'),
          Tab(text: 'Punti'),
          Tab(text: 'Percorsi'),
          Tab(text: 'Streak'),
          Tab(text: 'Food Oracle'),
        ],
      ),
    );
  }

  /// TabBar per i badge con styling migliorato
  PreferredSizeWidget _buildBadgesTabBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(kToolbarHeight + 16), // Altezza aumentata per tab più grandi
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8), // Padding aumentato per centrare meglio
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              DrStaffilanoTheme.primaryGreen.withOpacity(0.95),
              DrStaffilanoTheme.professionalBlue.withOpacity(0.85),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: TabBar(
          controller: _tabController,
          isScrollable: true,
          indicatorColor: DrStaffilanoTheme.accentGold,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white.withOpacity(0.7),
          indicatorWeight: 4,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 15, // Aumentato per renderli più grandi
            height: 1.3,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14, // Leggermente più piccolo per i non selezionati
            height: 1.3,
          ),
          labelPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12), // Padding aumentato
          indicatorPadding: const EdgeInsets.symmetric(horizontal: 12),
          tabAlignment: TabAlignment.center, // Centrato per miglior distribuzione
          tabs: [
            Tab(
              height: 50, // Altezza fissa per centraggio perfetto
              child: Container(
                alignment: Alignment.center,
                child: const Text('Tutti', textAlign: TextAlign.center),
              ),
            ),
            Tab(
              height: 50,
              child: Container(
                alignment: Alignment.center,
                child: const Text('Punti', textAlign: TextAlign.center),
              ),
            ),
            Tab(
              height: 50,
              child: Container(
                alignment: Alignment.center,
                child: const Text('Percorsi', textAlign: TextAlign.center),
              ),
            ),
            Tab(
              height: 50,
              child: Container(
                alignment: Alignment.center,
                child: const Text('Streak', textAlign: TextAlign.center),
              ),
            ),
            Tab(
              height: 50,
              child: Container(
                alignment: Alignment.center,
                child: const Text('Food Oracle', textAlign: TextAlign.center),
              ),
            ),
            Tab(
              height: 50,
              child: Container(
                alignment: Alignment.center,
                child: const Text('NutriScore', textAlign: TextAlign.center),
              ),
            ),
            Tab(
              height: 50,
              child: Container(
                alignment: Alignment.center,
                child: const Text('Speciali', textAlign: TextAlign.center),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Particles positioned around the title area for enhanced visual appeal
  Widget _buildBadgesTitleParticle(int index) {
    return AnimatedBuilder(
      animation: _backgroundAnimation,
      builder: (context, child) {
        final offset = (_backgroundAnimation.value + index * 0.15) % 1.0;

        // Calculate positions around the title area (center of header)
        final screenWidth = MediaQuery.of(context).size.width;
        final centerX = screenWidth / 2;
        final titleY = 55; // Spostato ancora più in alto per seguire il titolo

        // Create circular pattern around title
        final angle = (index * 30.0) + (offset * 360.0); // Degrees
        final radius = 80 + (index % 3) * 20; // Varying distances from title
        final radians = angle * (3.14159 / 180);

        final particleX = centerX + (radius * cos(radians));
        final particleY = titleY + (radius * sin(radians)) * 0.5; // Flatten vertically

        return Positioned(
          left: particleX,
          top: particleY,
          child: Opacity(
            opacity: 0.4 + (index % 2) * 0.2, // Varying opacity (0.4-0.6)
            child: Container(
              width: 3 + (index % 3).toDouble(), // Varying sizes (3-6px)
              height: 3 + (index % 3).toDouble(),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.white.withOpacity(0.6),
                    blurRadius: 6,
                    spreadRadius: 1,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// Carica i dati dei badge
  Future<Map<String, dynamic>> _loadBadgeData() async {
    final badgeService = BadgeService.instance;

    if (!badgeService.isInitialized) {
      await badgeService.initialize();
    }

    return {
      'allBadges': badgeService.allBadges,
      'progressList': badgeService.userCollection?.badgeProgress ?? [],
      'unlockedBadges': badgeService.userCollection?.unlockedBadges ?? [],
    };
  }

  /// Widget per l'header con statistiche (compatto)
  Widget _buildStatsHeader(int unlocked, int total) {
    final progress = total > 0 ? unlocked / total : 0.0;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            DrStaffilanoTheme.primaryGreen,
            DrStaffilanoTheme.professionalBlue,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildCompactStatCard(
                  'Badge Sbloccati',
                  '$unlocked',
                  Icons.emoji_events,
                  DrStaffilanoTheme.accentGold,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildCompactStatCard(
                  'Progresso Totale',
                  '${(progress * 100).round()}%',
                  Icons.trending_up,
                  Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.white.withOpacity(0.3),
            valueColor: AlwaysStoppedAnimation<Color>(DrStaffilanoTheme.accentGold),
            minHeight: 4,
          ),
        ],
      ),
    );
  }

  /// Widget per una card statistica compatta
  Widget _buildCompactStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.white.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 6),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 9,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Widget per una tab di badge
  Widget _buildBadgeTab(
    List<AppBadge> badges,
    List<BadgeProgress> progressList,
    List<String> unlockedBadges,
  ) {
    if (badges.isEmpty) {
      return const Center(
        child: Text('Nessun badge in questa categoria'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: BadgeGrid(
        badges: badges,
        progressList: progressList,
        unlockedBadges: unlockedBadges,
        crossAxisCount: 3,
        childAspectRatio: 0.75,
        showProgress: true,
        onBadgeTap: (badge) => _showBadgeDetail(badge),
      ),
    );
  }

  /// Mostra il dettaglio di un badge
  void _showBadgeDetail(AppBadge badge) {
    showDialog(
      context: context,
      builder: (context) => BadgeDetailDialog(badge: badge),
    );
  }

  /// Gestisce le azioni di test
  void _handleTestAction(String action) async {
    switch (action) {
      case 'test_points':
        await _badgeController.simulateWellJourneyPoints(100);
        setState(() {});
        _showSnackBar('Aggiunti 100 punti WellJourney!');
        break;
      case 'test_challenge':
        await _badgeController.simulateChallengeCompleted();
        setState(() {});
        _showSnackBar('Sfida completata!');
        break;
      case 'test_scan':
        await _badgeController.simulateFoodOracleScan();
        setState(() {});
        _showSnackBar('Scansione Food Oracle effettuata!');
        break;
      case 'force_check':
        await _badgeController.forceCheckAllBadges();
        setState(() {});
        _showSnackBar('Controllo badge forzato!');
        break;
    }
  }

  /// Mostra una snackbar
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: DrStaffilanoTheme.primaryGreen,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
