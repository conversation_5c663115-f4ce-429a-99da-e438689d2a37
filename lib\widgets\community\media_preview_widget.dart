import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../theme/dr_staffilano_theme.dart';
import '../../services/media_service.dart';

/// Widget per visualizzare l'anteprima dei media selezionati
class MediaPreviewWidget extends StatelessWidget {
  final List<MediaFile> mediaFiles;
  final Function(MediaFile) onRemoveMedia;
  final Function(MediaFile) onMediaTap;

  const MediaPreviewWidget({
    super.key,
    required this.mediaFiles,
    required this.onRemoveMedia,
    required this.onMediaTap,
  });

  @override
  Widget build(BuildContext context) {
    debugPrint('🎨 MediaPreviewWidget.build chiamato con ${mediaFiles.length} file');
    for (int i = 0; i < mediaFiles.length; i++) {
      debugPrint('🎨 MediaPreviewWidget file $i: ${mediaFiles[i].name} (${mediaFiles[i].type})');
    }

    if (mediaFiles.isEmpty) {
      debugPrint('🎨 MediaPreviewWidget: lista vuota, ritornando <PERSON>zed<PERSON>ox.shrink()');
      return const SizedBox.shrink();
    }

    debugPrint('🎨 MediaPreviewWidget: rendering container con ${mediaFiles.length} file');
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header con conteggio
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              children: [
                Icon(
                  FontAwesomeIcons.image,
                  size: 16,
                  color: DrStaffilanoTheme.primaryGreen,
                ),
                const SizedBox(width: 8),
                Text(
                  _getMediaCountText(),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
          ),

          // Grid di anteprime
          _buildMediaGrid(),
        ],
      ),
    );
  }

  Widget _buildMediaGrid() {
    if (mediaFiles.length == 1) {
      return _buildSingleMediaPreview(mediaFiles.first);
    } else if (mediaFiles.length <= 4) {
      return _buildSmallGrid();
    } else {
      return _buildLargeGrid();
    }
  }

  Widget _buildSingleMediaPreview(MediaFile mediaFile) {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey[100],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            _buildMediaContent(mediaFile, isLarge: true),
            _buildMediaOverlay(mediaFile),
            _buildRemoveButton(mediaFile),
          ],
        ),
      ),
    );
  }

  Widget _buildSmallGrid() {
    return SizedBox(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: mediaFiles.length,
        itemBuilder: (context, index) {
          final mediaFile = mediaFiles[index];
          return Container(
            width: 120,
            margin: EdgeInsets.only(right: index < mediaFiles.length - 1 ? 8 : 0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.grey[100],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Stack(
                children: [
                  _buildMediaContent(mediaFile),
                  _buildMediaOverlay(mediaFile),
                  _buildRemoveButton(mediaFile),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLargeGrid() {
    return Column(
      children: [
        // Prima riga con 3 elementi
        SizedBox(
          height: 120,
          child: Row(
            children: [
              for (int i = 0; i < 3 && i < mediaFiles.length; i++)
                Expanded(
                  child: Container(
                    margin: EdgeInsets.only(right: i < 2 ? 4 : 0),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.grey[100],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Stack(
                        children: [
                          _buildMediaContent(mediaFiles[i]),
                          _buildMediaOverlay(mediaFiles[i]),
                          _buildRemoveButton(mediaFiles[i]),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),

        const SizedBox(height: 4),

        // Seconda riga con elementi rimanenti
        if (mediaFiles.length > 3)
          SizedBox(
            height: 80,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: mediaFiles.length - 3,
              itemBuilder: (context, index) {
                final mediaFile = mediaFiles[index + 3];
                return Container(
                  width: 80,
                  margin: EdgeInsets.only(right: index < mediaFiles.length - 4 ? 4 : 0),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                    color: Colors.grey[100],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(6),
                    child: Stack(
                      children: [
                        _buildMediaContent(mediaFile),
                        _buildMediaOverlay(mediaFile),
                        _buildRemoveButton(mediaFile),
                        // Mostra "+X" se ci sono più di 6 elementi
                        if (index == 2 && mediaFiles.length > 6)
                          _buildMoreIndicator(mediaFiles.length - 6),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
      ],
    );
  }

  Widget _buildMediaContent(MediaFile mediaFile, {bool isLarge = false}) {
    debugPrint('🖼️ _buildMediaContent per ${mediaFile.name}');
    debugPrint('🖼️ - Ha thumbnailData: ${mediaFile.thumbnailData != null}');
    debugPrint('🖼️ - È immagine: ${mediaFile.isImage}');
    debugPrint('🖼️ - Path: ${mediaFile.path}');

    return GestureDetector(
      onTap: () => onMediaTap(mediaFile),
      child: Container(
        width: double.infinity,
        height: double.infinity,
        child: _buildImageContent(mediaFile, isLarge),
      ),
    );
  }

  Widget _buildMediaOverlay(MediaFile mediaFile) {
    if (!mediaFile.isVideo) return const SizedBox.shrink();

    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withOpacity(0.3),
            ],
          ),
        ),
        child: const Center(
          child: Icon(
            Icons.play_circle_filled,
            color: Colors.white,
            size: 32,
          ),
        ),
      ),
    );
  }

  Widget _buildRemoveButton(MediaFile mediaFile) {
    return Positioned(
      top: 4,
      right: 4,
      child: GestureDetector(
        onTap: () => onRemoveMedia(mediaFile),
        child: Container(
          width: 24,
          height: 24,
          decoration: const BoxDecoration(
            color: Colors.black54,
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.close,
            color: Colors.white,
            size: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildMoreIndicator(int count) {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.6),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Center(
          child: Text(
            '+$count',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildVideoPlaceholder(bool isLarge) {
    return Container(
      color: Colors.grey[300],
      child: Center(
        child: Icon(
          FontAwesomeIcons.video,
          color: Colors.grey[600],
          size: isLarge ? 32 : 24,
        ),
      ),
    );
  }

  Widget _buildErrorPlaceholder() {
    return Container(
      color: Colors.grey[300],
      child: Center(
        child: Icon(
          Icons.error_outline,
          color: Colors.grey[600],
          size: 24,
        ),
      ),
    );
  }

  Widget _buildImageContent(MediaFile mediaFile, bool isLarge) {
    debugPrint('🖼️ _buildImageContent per ${mediaFile.name}');
    debugPrint('🖼️ - Ha thumbnailData: ${mediaFile.thumbnailData != null}');
    debugPrint('🖼️ - È immagine: ${mediaFile.isImage}');
    debugPrint('🖼️ - kIsWeb: $kIsWeb');
    debugPrint('🖼️ - Path type: ${mediaFile.path.startsWith('data:') ? 'data URL' : mediaFile.path.startsWith('blob:') ? 'blob URL' : 'other'}');

    // Priorità: thumbnail > immagine originale > placeholder
    if (mediaFile.thumbnailData != null) {
      debugPrint('🖼️ ✅ Usando thumbnail (${mediaFile.thumbnailData!.length} bytes)');
      return Image.memory(
        mediaFile.thumbnailData!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          debugPrint('🖼️ ❌ Errore nel caricamento thumbnail: $error');
          return mediaFile.isImage ? _buildImageWidget(mediaFile) : _buildErrorPlaceholder();
        },
      );
    } else if (mediaFile.isImage) {
      debugPrint('🖼️ ⚠️ Nessun thumbnail, usando immagine originale');
      return _buildImageWidget(mediaFile);
    } else {
      debugPrint('🖼️ 📹 Usando placeholder video');
      return _buildVideoPlaceholder(isLarge);
    }
  }

  Widget _buildImageWidget(MediaFile mediaFile) {
    debugPrint('🖼️ _buildImageWidget per ${mediaFile.name}');
    debugPrint('🖼️ - kIsWeb: $kIsWeb');
    debugPrint('🖼️ - Path: ${mediaFile.path}');

    if (kIsWeb) {
      // Su web, tutti i path sono URLs (data:, blob:, http:, https:)
      if (mediaFile.path.startsWith('data:')) {
        debugPrint('🖼️ Caricamento da data URL');
        return Image.network(
          mediaFile.path,
          fit: BoxFit.cover,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            debugPrint('🖼️ Caricamento data URL in corso...');
            return const Center(child: CircularProgressIndicator());
          },
          errorBuilder: (context, error, stackTrace) {
            debugPrint('🖼️ Errore nel caricamento da data URL: $error');
            return _buildErrorPlaceholder();
          },
        );
      } else if (mediaFile.path.startsWith('blob:')) {
        debugPrint('🖼️ Caricamento da blob URL');
        return Image.network(
          mediaFile.path,
          fit: BoxFit.cover,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            debugPrint('🖼️ Caricamento blob URL in corso...');
            return const Center(child: CircularProgressIndicator());
          },
          errorBuilder: (context, error, stackTrace) {
            debugPrint('🖼️ Errore nel caricamento da blob URL: $error');
            debugPrint('🖼️ Stack trace: $stackTrace');
            return _buildErrorPlaceholder();
          },
        );
      } else {
        debugPrint('🖼️ Caricamento da network URL standard');
        return Image.network(
          mediaFile.path,
          fit: BoxFit.cover,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            debugPrint('🖼️ Caricamento network URL in corso...');
            return const Center(child: CircularProgressIndicator());
          },
          errorBuilder: (context, error, stackTrace) {
            debugPrint('🖼️ Errore nel caricamento da network: $error');
            return _buildErrorPlaceholder();
          },
        );
      }
    } else {
      // Su mobile, usa Image.file
      debugPrint('🖼️ Caricamento da file locale');
      return Image.file(
        File(mediaFile.path),
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          debugPrint('🖼️ Errore nel caricamento da file: $error');
          return _buildErrorPlaceholder();
        },
      );
    }
  }

  String _getMediaCountText() {
    if (mediaFiles.length == 1) {
      final file = mediaFiles.first;
      return '1 ${file.isImage ? 'foto' : 'video'} selezionato';
    } else {
      final imageCount = mediaFiles.where((file) => file.isImage).length;
      final videoCount = mediaFiles.where((file) => file.isVideo).length;

      if (videoCount == 0) {
        return '$imageCount foto selezionate';
      } else if (imageCount == 0) {
        return '$videoCount video selezionati';
      } else {
        return '${mediaFiles.length} file selezionati ($imageCount foto, $videoCount video)';
      }
    }
  }
}
