import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:convert';
import '../services/translation_service.dart';
import '../theme/app_theme.dart';

/// Schermata per la gestione delle traduzioni
class TranslationManagerScreen extends StatefulWidget {
  const TranslationManagerScreen({super.key});

  @override
  State<TranslationManagerScreen> createState() => _TranslationManagerScreenState();
}

class _TranslationManagerScreenState extends State<TranslationManagerScreen> {
  late TranslationService _translationService;
  Map<String, String> _translations = {};
  bool _isLoading = true;
  bool _useOnlineApi = false;
  String _apiKey = '';
  String _sourceLanguage = 'en';
  String _targetLanguage = 'it';

  final TextEditingController _originalController = TextEditingController();
  final TextEditingController _translationController = TextEditingController();
  final TextEditingController _apiKeyController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();

  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadTranslations();
  }

  Future<void> _loadTranslations() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _translationService = await TranslationService.getInstance();

      setState(() {
        _translations = _translationService.getAllTranslations();
        _useOnlineApi = _translationService.useOnlineApi;
        _apiKey = _translationService.apiKey ?? '';
        _sourceLanguage = _translationService.sourceLanguage;
        _targetLanguage = _translationService.targetLanguage;
        _apiKeyController.text = _apiKey;
        _isLoading = false;
      });
    } catch (e) {
      _showErrorSnackBar('Errore nel caricamento delle traduzioni: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _addTranslation() async {
    final original = _originalController.text.trim();
    final translation = _translationController.text.trim();

    if (original.isEmpty || translation.isEmpty) {
      _showErrorSnackBar('Inserisci sia il testo originale che la traduzione');
      return;
    }

    try {
      await _translationService.addTranslation(original, translation);

      setState(() {
        _translations = _translationService.getAllTranslations();
        _originalController.clear();
        _translationController.clear();
      });

      _showSuccessSnackBar('Traduzione aggiunta con successo');
    } catch (e) {
      _showErrorSnackBar('Errore nell\'aggiunta della traduzione: $e');
    }
  }

  Future<void> _removeTranslation(String original) async {
    try {
      await _translationService.removeTranslation(original);

      setState(() {
        _translations = _translationService.getAllTranslations();
      });

      _showSuccessSnackBar('Traduzione rimossa con successo');
    } catch (e) {
      _showErrorSnackBar('Errore nella rimozione della traduzione: $e');
    }
  }

  Future<void> _exportTranslations() async {
    try {
      final jsonContent = _translationService.exportTranslations();

      // Copia negli appunti
      await Clipboard.setData(ClipboardData(text: jsonContent));

      _showSuccessSnackBar('Traduzioni esportate negli appunti');
    } catch (e) {
      _showErrorSnackBar('Errore nell\'esportazione delle traduzioni: $e');
    }
  }

  Future<void> _importTranslations() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
      );

      if (result == null || result.files.isEmpty) {
        return;
      }

      final file = result.files.first;
      String content = '';

      if (file.bytes != null) {
        // Web
        content = String.fromCharCodes(file.bytes!);
      } else if (file.path != null) {
        // Mobile/Desktop
        content = await rootBundle.loadString(file.path!);
      } else {
        _showErrorSnackBar('Impossibile leggere il file');
        return;
      }

      final count = await _translationService.importTranslations(content);

      setState(() {
        _translations = _translationService.getAllTranslations();
      });

      _showSuccessSnackBar('Importate $count traduzioni con successo');
    } catch (e) {
      _showErrorSnackBar('Errore nell\'importazione delle traduzioni: $e');
    }
  }

  Future<void> _updateApiSettings() async {
    try {
      await _translationService.setUseOnlineApi(_useOnlineApi);
      await _translationService.setApiKey(_apiKeyController.text.trim());
      await _translationService.setSourceLanguage(_sourceLanguage);
      await _translationService.setTargetLanguage(_targetLanguage);

      _showSuccessSnackBar('Impostazioni API aggiornate con successo');
    } catch (e) {
      _showErrorSnackBar('Errore nell\'aggiornamento delle impostazioni API: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  void dispose() {
    _originalController.dispose();
    _translationController.dispose();
    _apiKeyController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestione Traduzioni'),
        actions: [
          IconButton(
            icon: const Icon(Icons.file_upload),
            tooltip: 'Importa traduzioni',
            onPressed: _importTranslations,
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            tooltip: 'Esporta traduzioni',
            onPressed: _exportTranslations,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : DefaultTabController(
              length: 2,
              child: Column(
                children: [
                  const TabBar(
                    tabs: [
                      Tab(text: 'Traduzioni'),
                      Tab(text: 'Impostazioni'),
                    ],
                  ),
                  Expanded(
                    child: TabBarView(
                      children: [
                        _buildTranslationsTab(),
                        _buildSettingsTab(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildTranslationsTab() {
    // Filtra le traduzioni in base alla ricerca
    final filteredTranslations = _searchQuery.isEmpty
        ? _translations
        : Map<String, String>.from(_translations)
            ..removeWhere((key, value) =>
                !key.toLowerCase().contains(_searchQuery.toLowerCase()) &&
                !value.toLowerCase().contains(_searchQuery.toLowerCase()));

    return Column(
      children: [
        // Form per aggiungere una nuova traduzione
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Aggiungi traduzione',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _originalController,
                          decoration: const InputDecoration(
                            labelText: 'Testo originale',
                            border: OutlineInputBorder(),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextField(
                          controller: _translationController,
                          decoration: const InputDecoration(
                            labelText: 'Traduzione',
                            border: OutlineInputBorder(),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      ElevatedButton.icon(
                        onPressed: _addTranslation,
                        icon: const Icon(Icons.add),
                        label: const Text('Aggiungi'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),

        // Barra di ricerca
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: TextField(
            controller: _searchController,
            decoration: const InputDecoration(
              labelText: 'Cerca traduzioni',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
        ),

        const SizedBox(height: 16),

        // Lista delle traduzioni
        Expanded(
          child: filteredTranslations.isEmpty
              ? const Center(
                  child: Text('Nessuna traduzione trovata'),
                )
              : ListView.builder(
                  itemCount: filteredTranslations.length,
                  itemBuilder: (context, index) {
                    final original = filteredTranslations.keys.elementAt(index);
                    final translation = filteredTranslations[original]!;

                    return Card(
                      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
                      child: ListTile(
                        title: Text(original),
                        subtitle: Text(translation),
                        trailing: IconButton(
                          icon: const Icon(Icons.delete, color: Colors.red),
                          onPressed: () => _removeTranslation(original),
                        ),
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }

  Widget _buildSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Impostazioni API di traduzione',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: const Text('Utilizza API online'),
                    subtitle: const Text('Abilita per utilizzare un servizio di traduzione online'),
                    value: _useOnlineApi,
                    onChanged: (value) {
                      setState(() {
                        _useOnlineApi = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _apiKeyController,
                    decoration: const InputDecoration(
                      labelText: 'Chiave API',
                      hintText: 'Inserisci la chiave API per il servizio di traduzione',
                      border: OutlineInputBorder(),
                    ),
                    enabled: _useOnlineApi,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _sourceLanguage,
                          decoration: const InputDecoration(
                            labelText: 'Lingua di origine',
                            border: OutlineInputBorder(),
                          ),
                          items: const [
                            DropdownMenuItem(value: 'en', child: Text('Inglese')),
                            DropdownMenuItem(value: 'it', child: Text('Italiano')),
                            DropdownMenuItem(value: 'es', child: Text('Spagnolo')),
                            DropdownMenuItem(value: 'fr', child: Text('Francese')),
                            DropdownMenuItem(value: 'de', child: Text('Tedesco')),
                          ],
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _sourceLanguage = value;
                              });
                            }
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _targetLanguage,
                          decoration: const InputDecoration(
                            labelText: 'Lingua di destinazione',
                            border: OutlineInputBorder(),
                          ),
                          items: const [
                            DropdownMenuItem(value: 'it', child: Text('Italiano')),
                            DropdownMenuItem(value: 'en', child: Text('Inglese')),
                            DropdownMenuItem(value: 'es', child: Text('Spagnolo')),
                            DropdownMenuItem(value: 'fr', child: Text('Francese')),
                            DropdownMenuItem(value: 'de', child: Text('Tedesco')),
                          ],
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _targetLanguage = value;
                              });
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: _updateApiSettings,
                    icon: const Icon(Icons.save),
                    label: const Text('Salva impostazioni'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Informazioni',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Il servizio di traduzione può utilizzare un\'API online (come Google Translate) '
                    'o un database locale di traduzioni. Se utilizzi l\'API online, dovrai fornire '
                    'una chiave API valida.\n\n'
                    'Per ottenere una chiave API per Google Translate, visita la '
                    'Google Cloud Console e abilita l\'API Cloud Translation.',
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Traduzioni salvate: ${_translations.length}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
