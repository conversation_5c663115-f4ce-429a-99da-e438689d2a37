import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/food.dart';

/// Classe di utilità per l'interfaccia utente
class UIUtils {
  /// Ottiene il nome di una categoria di alimenti
  static String getFoodCategoryName(FoodCategory category) {
    switch (category) {
      case FoodCategory.protein:
        return 'Proteine';
      case FoodCategory.grain:
        return 'Cereali';
      case FoodCategory.vegetable:
        return 'Verdure';
      case FoodCategory.fruit:
        return 'Frutta';
      case FoodCategory.dairy:
        return 'Latticini';
      case FoodCategory.fat:
        return 'Grassi';
      case FoodCategory.sweet:
        return 'Dolci';
      case FoodCategory.beverage:
        return 'Bevande';
      case FoodCategory.mixed:
        return 'Misti';
      case FoodCategory.condiment:
        return 'Condimenti';
      case FoodCategory.other:
        return 'Altro';
    }
  }

  /// Ottiene l'icona per una categoria di alimenti
  static IconData getCategoryIcon(FoodCategory? category) {
    if (category == null) return FontAwesomeIcons.utensils;

    switch (category) {
      case FoodCategory.protein:
        return FontAwesomeIcons.drumstickBite;
      case FoodCategory.grain:
        return FontAwesomeIcons.breadSlice;
      case FoodCategory.vegetable:
        return FontAwesomeIcons.carrot;
      case FoodCategory.fruit:
        return FontAwesomeIcons.appleWhole;
      case FoodCategory.dairy:
        return FontAwesomeIcons.cheese;
      case FoodCategory.fat:
        return FontAwesomeIcons.oilWell;
      case FoodCategory.sweet:
        return FontAwesomeIcons.candyCane;
      case FoodCategory.beverage:
        return FontAwesomeIcons.wineGlass;
      case FoodCategory.mixed:
        return FontAwesomeIcons.bowlFood;
      case FoodCategory.condiment:
        return FontAwesomeIcons.pepperHot;
      case FoodCategory.other:
        return FontAwesomeIcons.utensils;
    }
  }

  /// Ottiene il colore per una categoria di alimenti
  static Color getCategoryColor(FoodCategory category) {
    switch (category) {
      case FoodCategory.protein:
        return Colors.red;
      case FoodCategory.grain:
        return Colors.amber;
      case FoodCategory.vegetable:
        return Colors.green;
      case FoodCategory.fruit:
        return Colors.orange;
      case FoodCategory.dairy:
        return Colors.blue;
      case FoodCategory.fat:
        return Colors.deepPurple;
      case FoodCategory.sweet:
        return Colors.pink;
      case FoodCategory.beverage:
        return Colors.lightBlue;
      case FoodCategory.mixed:
        return Colors.teal;
      case FoodCategory.condiment:
        return Colors.deepOrange;
      case FoodCategory.other:
        return Colors.grey;
    }
  }

  /// Ottiene il nome di un tipo di pasto
  static String getMealTypeName(String mealType) {
    switch (mealType) {
      case 'breakfast':
        return 'Colazione';
      case 'lunch':
        return 'Pranzo';
      case 'dinner':
        return 'Cena';
      case 'snack':
        return 'Spuntino';
      default:
        return mealType;
    }
  }

  /// Ottiene l'icona per un tipo di pasto
  static IconData getMealTypeIcon(String mealType) {
    switch (mealType) {
      case 'breakfast':
        return FontAwesomeIcons.mugSaucer;
      case 'lunch':
        return FontAwesomeIcons.bowlFood;
      case 'dinner':
        return FontAwesomeIcons.utensils;
      case 'snack':
        return FontAwesomeIcons.apple;
      default:
        return FontAwesomeIcons.utensils;
    }
  }

  /// Formatta una data in formato italiano
  static String formatDate(DateTime date) {
    final months = [
      'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',
      'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'
    ];

    final days = [
      'Lunedì', 'Martedì', 'Mercoledì', 'Giovedì', 'Venerdì', 'Sabato', 'Domenica'
    ];

    final dayOfWeek = days[date.weekday - 1];
    final dayOfMonth = date.day;
    final month = months[date.month - 1];
    final year = date.year;

    return '$dayOfWeek, $dayOfMonth $month $year';
  }

  /// Formatta un orario in formato italiano
  static String formatTime(TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');

    return '$hour:$minute';
  }

  /// Ottiene un colore in base a un valore percentuale
  static Color getPercentageColor(double percentage) {
    if (percentage >= 0.8) {
      return Colors.green;
    } else if (percentage >= 0.6) {
      return Colors.lightGreen;
    } else if (percentage >= 0.4) {
      return Colors.amber;
    } else if (percentage >= 0.2) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  /// Ottiene un'icona in base a un valore percentuale
  static IconData getPercentageIcon(double percentage) {
    if (percentage >= 0.8) {
      return Icons.sentiment_very_satisfied;
    } else if (percentage >= 0.6) {
      return Icons.sentiment_satisfied;
    } else if (percentage >= 0.4) {
      return Icons.sentiment_neutral;
    } else if (percentage >= 0.2) {
      return Icons.sentiment_dissatisfied;
    } else {
      return Icons.sentiment_very_dissatisfied;
    }
  }
}
