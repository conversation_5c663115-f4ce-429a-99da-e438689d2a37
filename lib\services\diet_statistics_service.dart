import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';
import '../models/diet_statistics.dart';
import '../models/diet_plan.dart';
import '../models/meal_plan.dart';
import '../models/user_profile.dart';
import 'storage_service.dart';

class DietStatisticsService {
  static const String _dailyStatsKey = 'daily_diet_stats';
  static const String _achievementsKey = 'achievements';
  static const String _weightHistoryKey = 'weight_history';

  StorageService? _storageService;

  /// Inizializza il servizio
  Future<void> _ensureInitialized() async {
    if (_storageService == null) {
      _storageService = await StorageService.getInstance();
    }
  }

  /// Calcola e salva le statistiche giornaliere basate sui pasti completati
  Future<DailyDietStats> calculateAndSaveDailyStats(String date) async {
    await _ensureInitialized();

    // Carica i dati necessari
    final dietPlan = await _storageService!.caricaDietPlan();
    final mealPlan = await _storageService!.caricaPianoPasti();
    final userProfile = await _storageService!.caricaUserProfile();

    if (dietPlan == null || userProfile == null) {
      throw Exception('Piano dietetico o profilo utente non disponibili');
    }

    // Trova il piano giornaliero per la data specificata
    final dailyDietPlan = dietPlan.getPlanForDate(date);
    final dailyMealPlan = mealPlan?.getPlanForDate(date);

    if (dailyDietPlan == null) {
      throw Exception('Piano giornaliero non trovato per la data $date');
    }

    // Calcola i target dai piani AI
    final caloriesTarget = dailyDietPlan.calorieTarget;
    final macroTargets = dailyDietPlan.macroTargets;

    // Calcola i valori consumati dai pasti completati
    int caloriesConsumed = 0;
    double proteinsConsumed = 0;
    double carbsConsumed = 0;
    double fatsConsumed = 0;
    int mealsCompleted = 0;
    int mealsPlanned = dailyDietPlan.meals.length;

    // Se abbiamo il meal plan, usa quello per i dati di completamento
    if (dailyMealPlan != null) {
      for (var meal in dailyMealPlan.meals) {
        if (meal.completato) {
          caloriesConsumed += meal.calorie;
          proteinsConsumed += meal.proteine;
          carbsConsumed += meal.carboidrati;
          fatsConsumed += meal.grassi;
          mealsCompleted++;
        }
      }
    } else {
      // Altrimenti usa i dati dal piano dietetico
      for (var meal in dailyDietPlan.meals) {
        if (meal.isCompleted) {
          caloriesConsumed += meal.totalCalories;
          proteinsConsumed += meal.getMacroValue('proteins');
          carbsConsumed += meal.getMacroValue('carbs');
          fatsConsumed += meal.getMacroValue('fats');
          mealsCompleted++;
        }
      }
    }

    // Carica dati aggiuntivi
    final waterIntake = await _getWaterIntakeForDate(date);
    final weight = await _getWeightForDate(date);

    // Crea le statistiche giornaliere
    final dailyStats = DailyDietStats(
      date: date,
      caloriesConsumed: caloriesConsumed,
      caloriesTarget: caloriesTarget,
      proteinsConsumed: proteinsConsumed,
      proteinsTarget: macroTargets['proteins']?.toDouble() ?? 0,
      carbsConsumed: carbsConsumed,
      carbsTarget: macroTargets['carbs']?.toDouble() ?? 0,
      fatsConsumed: fatsConsumed,
      fatsTarget: macroTargets['fats']?.toDouble() ?? 0,
      mealsCompleted: mealsCompleted,
      mealsPlanned: mealsPlanned,
      waterIntake: waterIntake,
      waterTarget: 2.0, // Default 2 litri
      weight: weight,
    );

    // Salva le statistiche
    await _saveDailyStats(dailyStats);

    // Controlla e sblocca achievement
    await _checkAndUnlockAchievements(dailyStats);

    return dailyStats;
  }

  /// Salva le statistiche giornaliere
  Future<void> _saveDailyStats(DailyDietStats stats) async {
    final prefs = await SharedPreferences.getInstance();
    final existingStatsJson = prefs.getString(_dailyStatsKey);

    Map<String, dynamic> allStats = {};
    if (existingStatsJson != null) {
      allStats = json.decode(existingStatsJson);
    }

    allStats[stats.date] = stats.toMap();
    await prefs.setString(_dailyStatsKey, json.encode(allStats));
  }

  /// Carica le statistiche giornaliere per una data specifica
  Future<DailyDietStats?> getDailyStats(String date) async {
    final prefs = await SharedPreferences.getInstance();
    final statsJson = prefs.getString(_dailyStatsKey);

    if (statsJson == null) return null;

    final allStats = json.decode(statsJson) as Map<String, dynamic>;
    final dayStats = allStats[date];

    if (dayStats == null) return null;

    return DailyDietStats.fromMap(dayStats);
  }

  /// Carica le statistiche per un range di date
  Future<List<DailyDietStats>> getStatsForDateRange(DateTime startDate, DateTime endDate) async {
    final prefs = await SharedPreferences.getInstance();
    final statsJson = prefs.getString(_dailyStatsKey);

    if (statsJson == null) return [];

    final allStats = json.decode(statsJson) as Map<String, dynamic>;
    final List<DailyDietStats> rangeStats = [];

    for (var date = startDate; date.isBefore(endDate.add(const Duration(days: 1))); date = date.add(const Duration(days: 1))) {
      final dateString = DateFormat('yyyy-MM-dd').format(date);
      final dayStats = allStats[dateString];

      if (dayStats != null) {
        rangeStats.add(DailyDietStats.fromMap(dayStats));
      }
    }

    return rangeStats;
  }

  /// Carica le statistiche settimanali
  Future<WeeklyDietStats> getWeeklyStats(DateTime weekStart) async {
    final weekEnd = weekStart.add(const Duration(days: 6));
    final dailyStats = await getStatsForDateRange(weekStart, weekEnd);

    return WeeklyDietStats(
      weekStartDate: DateFormat('yyyy-MM-dd').format(weekStart),
      dailyStats: dailyStats,
    );
  }

  /// Carica le statistiche degli ultimi 30 giorni
  Future<List<DailyDietStats>> getMonthlyStats() async {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(const Duration(days: 30));
    return getStatsForDateRange(startDate, endDate);
  }

  /// Ottiene l'assunzione di acqua per una data (placeholder)
  Future<double> _getWaterIntakeForDate(String date) async {
    // TODO: Implementare il tracking dell'acqua
    return 0.0;
  }

  /// Ottiene il peso per una data specifica
  Future<double?> _getWeightForDate(String date) async {
    final prefs = await SharedPreferences.getInstance();
    final weightHistoryJson = prefs.getString(_weightHistoryKey);

    if (weightHistoryJson == null) return null;

    final weightHistory = json.decode(weightHistoryJson) as Map<String, dynamic>;
    final weight = weightHistory[date];

    return weight?.toDouble();
  }

  /// Salva il peso per una data specifica
  Future<void> saveWeightForDate(String date, double weight) async {
    final prefs = await SharedPreferences.getInstance();
    final weightHistoryJson = prefs.getString(_weightHistoryKey);

    Map<String, dynamic> weightHistory = {};
    if (weightHistoryJson != null) {
      weightHistory = json.decode(weightHistoryJson);
    }

    weightHistory[date] = weight;
    await prefs.setString(_weightHistoryKey, json.encode(weightHistory));
  }

  /// Controlla e sblocca achievement basati sulle statistiche
  Future<void> _checkAndUnlockAchievements(DailyDietStats stats) async {
    final achievements = <Achievement>[];
    final now = DateTime.now();

    // Achievement: Calorie on track
    if (stats.isCaloriesOnTrack) {
      achievements.add(Achievement(
        type: AchievementType.caloriesOnTrack,
        title: 'Obiettivo Calorico Raggiunto',
        description: 'Hai raggiunto il tuo obiettivo calorico giornaliero!',
        iconName: 'target',
        dateEarned: now,
        isUnlocked: true,
      ));
    }

    // Achievement: Macros balanced
    if (stats.areMacrosOnTrack) {
      achievements.add(Achievement(
        type: AchievementType.macrosBalanced,
        title: 'Macronutrienti Bilanciati',
        description: 'Hai mantenuto un perfetto equilibrio dei macronutrienti!',
        iconName: 'balance',
        dateEarned: now,
        isUnlocked: true,
      ));
    }

    // Achievement: All meals completed
    if (stats.mealsCompletionPercentage == 100) {
      achievements.add(Achievement(
        type: AchievementType.allMealsCompleted,
        title: 'Giornata Completa',
        description: 'Hai completato tutti i pasti pianificati!',
        iconName: 'check_circle',
        dateEarned: now,
        isUnlocked: true,
      ));
    }

    // Salva gli achievement
    for (var achievement in achievements) {
      await _saveAchievement(achievement);
    }
  }

  /// Salva un achievement
  Future<void> _saveAchievement(Achievement achievement) async {
    final prefs = await SharedPreferences.getInstance();
    final achievementsJson = prefs.getString(_achievementsKey);

    List<dynamic> achievements = [];
    if (achievementsJson != null) {
      achievements = json.decode(achievementsJson);
    }

    // Controlla se l'achievement esiste già per oggi
    final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
    final existingIndex = achievements.indexWhere((a) =>
        a['type'] == achievement.type.toString() &&
        a['dateEarned'].toString().startsWith(today));

    if (existingIndex == -1) {
      achievements.add(achievement.toMap());
      await prefs.setString(_achievementsKey, json.encode(achievements));
    }
  }

  /// Carica tutti gli achievement
  Future<List<Achievement>> getAchievements() async {
    final prefs = await SharedPreferences.getInstance();
    final achievementsJson = prefs.getString(_achievementsKey);

    if (achievementsJson == null) return [];

    final achievementsList = json.decode(achievementsJson) as List<dynamic>;
    return achievementsList.map((a) => Achievement.fromMap(a)).toList();
  }

  /// Aggiorna le statistiche quando un pasto viene completato
  Future<void> updateStatsOnMealCompletion() async {
    final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
    await calculateAndSaveDailyStats(today);
  }
}
