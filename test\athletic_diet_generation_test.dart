import 'package:flutter_test/flutter_test.dart';
import '../lib/models/user_profile.dart';
import '../lib/models/food.dart';
import '../lib/services/diet_generator_service.dart';
import '../lib/services/athletic_food_selector_service.dart';
import '../lib/data/athletic_italian_proteins.dart';

void main() {
  group('Athletic Diet Generation Tests', () {
    late DietGeneratorService dietGenerator;

    setUpAll(() async {
      dietGenerator = await DietGeneratorService.getInstance();
    });

    test('Enhanced protein calculation for athletes', () {
      // Test per atleta molto attivo
      final athleteProfile = UserProfile(
        id: 'test_athlete',
        name: '<PERSON>',
        age: 28,
        gender: Gender.male,
        height: 180,
        weight: 75,
        activityLevel: ActivityLevel.veryActive,
        goal: Goal.maintenance,
        dietType: DietType.omnivore,
        allergies: [],
        dislikedFoods: [],
        mealsPerDay: 4,
      );

      // Test calcolo proteine basato sul peso
      final proteinPerKg = athleteProfile.calculateProteinPerKgBodyWeight();
      expect(proteinPerKg, equals(2.0)); // 2.0g/kg per atleti molto attivi

      // Test calcolo proteine totali basate sul peso
      final weightBasedProtein = athleteProfile.calculateWeightBasedProteinGrams();
      expect(weightBasedProtein, equals(150)); // 75kg * 2.0g/kg = 150g

      // Test che per atleti si usi il calcolo basato sul peso
      expect(athleteProfile.shouldUseWeightBasedProtein(), isTrue);

      // Test distribuzione macronutrienti per atleti
      final macroDistribution = athleteProfile.calculateMacroDistribution();
      expect(macroDistribution['proteins'], greaterThan(0.25)); // Almeno 25% proteine
      expect(macroDistribution['proteins'], lessThanOrEqualTo(0.35)); // Max 35% per sicurezza

      print('Atleta - Proteine per kg: ${proteinPerKg}g');
      print('Atleta - Proteine totali: ${weightBasedProtein}g');
      print('Atleta - Distribuzione macro: $macroDistribution');
    });

    test('Standard protein calculation for sedentary users', () {
      // Test per utente sedentario
      final sedentaryProfile = UserProfile(
        id: 'test_sedentary',
        name: 'Anna Sedentaria',
        age: 35,
        gender: Gender.female,
        height: 165,
        weight: 60,
        activityLevel: ActivityLevel.sedentary,
        goal: Goal.weightLoss,
        dietType: DietType.omnivore,
        allergies: [],
        dislikedFoods: [],
        mealsPerDay: 3,
      );

      // Test calcolo proteine basato sul peso
      final proteinPerKg = sedentaryProfile.calculateProteinPerKgBodyWeight();
      expect(proteinPerKg, equals(0.8)); // 0.8g/kg per sedentari

      // Test che per sedentari non si usi il calcolo basato sul peso
      expect(sedentaryProfile.shouldUseWeightBasedProtein(), isFalse);

      // Test distribuzione macronutrienti per sedentari
      final macroDistribution = sedentaryProfile.calculateMacroDistribution();
      expect(macroDistribution['proteins'], lessThanOrEqualTo(0.25)); // Max 25% proteine

      print('Sedentario - Proteine per kg: ${proteinPerKg}g');
      print('Sedentario - Distribuzione macro: $macroDistribution');
    });

    test('Athletic Italian proteins database', () {
      final athleticProteins = AthleticItalianProteins.getAthleticProteins();

      // Verifica che ci siano alimenti proteici
      expect(athleticProteins.isNotEmpty, isTrue);

      // Verifica che tutti gli alimenti abbiano alto contenuto proteico
      for (final protein in athleticProteins) {
        expect(protein.proteins, greaterThanOrEqualTo(8.0)); // Almeno 8g per 100g
        expect(protein.isTraditionalItalian, isTrue); // Tutti devono essere italiani
      }

      // Verifica presenza di categorie specifiche
      final hasProteinCategory = athleticProteins.any((food) =>
          food.categories.contains(FoodCategory.protein));
      final hasDairyCategory = athleticProteins.any((food) =>
          food.categories.contains(FoodCategory.dairy));

      expect(hasProteinCategory, isTrue);
      expect(hasDairyCategory, isTrue);

      print('Alimenti proteici italiani: ${athleticProteins.length}');
      print('Proteine medie: ${athleticProteins.map((f) => f.proteins).reduce((a, b) => a + b) / athleticProteins.length}g per 100g');
    });

    test('Athletic food selector prioritization', () {
      final athleteProfile = UserProfile(
        id: 'test_athlete_selector',
        name: 'Luca Sportivo',
        age: 25,
        gender: Gender.male,
        height: 175,
        weight: 70,
        activityLevel: ActivityLevel.extremelyActive,
        goal: Goal.weightGain,
        dietType: DietType.omnivore,
        allergies: [],
        dislikedFoods: [],
        mealsPerDay: 5,
      );

      // Test selezione alimenti per atleti
      final allProteins = AthleticItalianProteins.getAthleticProteins();
      final selectedFoods = AthleticFoodSelectorService.selectHighProteinItalianFoods(
        allProteins,
        athleteProfile,
        MealType.lunch,
      );

      // Verifica che la selezione prioritizzi alimenti ad alto contenuto proteico
      expect(selectedFoods.isNotEmpty, isTrue);

      // Verifica che gli alimenti siano ordinati per contenuto proteico
      for (int i = 0; i < selectedFoods.length - 1; i++) {
        final currentScore = AthleticFoodSelectorService.calculateProteinScore(selectedFoods[i]);
        final nextScore = AthleticFoodSelectorService.calculateProteinScore(selectedFoods[i + 1]);
        expect(currentScore, greaterThanOrEqualTo(nextScore));
      }

      print('Alimenti selezionati per atleta: ${selectedFoods.length}');
      print('Proteine primo alimento: ${selectedFoods.first.proteins}g per 100g');
    });

    test('Protein distribution across meals for athletes', () {
      const totalDailyProtein = 150; // 150g per atleta di 75kg
      const mealsPerDay = 4;

      final distribution = AthleticFoodSelectorService.distributeProteinAcrossMeals(
        totalDailyProtein,
        mealsPerDay,
      );

      // Verifica che la distribuzione sia corretta
      expect(distribution.length, equals(4));

      // Verifica che ogni pasto abbia almeno 20g di proteine per la sintesi proteica
      for (final proteinAmount in distribution.values) {
        expect(proteinAmount, greaterThanOrEqualTo(20.0));
      }

      // Verifica che la somma sia corretta
      final totalDistributed = distribution.values.reduce((a, b) => a + b);
      expect(totalDistributed, closeTo(totalDailyProtein, 5)); // Tolleranza di 5g

      // Verifica che la distribuzione sia valida per il recupero muscolare
      expect(AthleticFoodSelectorService.validateProteinDistribution(distribution), isTrue);

      print('Distribuzione proteine per pasto: $distribution');
      print('Totale distribuito: ${totalDistributed}g');
    });

    test('Complete athletic diet plan generation', () async {
      final athleteProfile = UserProfile(
        id: 'test_complete_athlete',
        name: 'Sofia Atleta',
        age: 26,
        gender: Gender.female,
        height: 170,
        weight: 65,
        activityLevel: ActivityLevel.veryActive,
        goal: Goal.maintenance,
        dietType: DietType.omnivore,
        allergies: [],
        dislikedFoods: [],
        mealsPerDay: 4,
      );

      // Genera un piano dietetico settimanale per atleta
      final weeklyPlan = await dietGenerator.generateWeeklyDietPlan(athleteProfile);

      expect(weeklyPlan.dailyPlans.length, equals(7));

      // Verifica ogni piano giornaliero
      for (final dailyPlan in weeklyPlan.dailyPlans) {
        expect(dailyPlan.meals.length, equals(4));

        // Calcola le proteine totali del giorno
        int totalDailyProteins = 0;
        for (final meal in dailyPlan.meals) {
          totalDailyProteins += meal.totalMacros['proteins']!.round();
        }

        // Verifica che le proteine siano adeguate per un atleta
        final expectedMinProteins = (athleteProfile.weight * 1.6).round(); // Minimo 1.6g/kg
        expect(totalDailyProteins, greaterThanOrEqualTo(expectedMinProteins));

        // Verifica che le proteine non superino il 35% delle calorie totali
        final totalCalories = dailyPlan.calorieTarget;
        final maxProteinCalories = totalCalories * 0.35;
        final proteinCalories = totalDailyProteins * 4;
        expect(proteinCalories, lessThanOrEqualTo(maxProteinCalories));

        print('Giorno ${dailyPlan.date}: ${totalDailyProteins}g proteine (${(proteinCalories / totalCalories * 100).round()}% delle calorie)');
      }

      print('Piano settimanale generato con successo per atleta');
      print('Proteine medie giornaliere: ${weeklyPlan.dailyPlans.map((d) => d.meals.map((m) => m.totalMacros['proteins']!.round()).reduce((a, b) => a + b)).reduce((a, b) => a + b) / 7}g');
    });

    test('Backward compatibility for non-athletes', () async {
      final regularProfile = UserProfile(
        id: 'test_regular',
        name: 'Mario Normale',
        age: 40,
        gender: Gender.male,
        height: 175,
        weight: 80,
        activityLevel: ActivityLevel.lightlyActive,
        goal: Goal.maintenance,
        dietType: DietType.omnivore,
        allergies: [],
        dislikedFoods: [],
        mealsPerDay: 3,
      );

      // Genera un piano per utente normale
      final weeklyPlan = await dietGenerator.generateWeeklyDietPlan(regularProfile);

      expect(weeklyPlan.dailyPlans.length, equals(7));

      // Verifica che le proteine siano nella gamma normale (non atletica)
      for (final dailyPlan in weeklyPlan.dailyPlans) {
        int totalDailyProteins = 0;
        for (final meal in dailyPlan.meals) {
          totalDailyProteins += meal.totalMacros['proteins']!.round();
        }

        // Per utenti normali, le proteine dovrebbero essere tra 15-25% delle calorie
        final totalCalories = dailyPlan.calorieTarget;
        final proteinCalories = totalDailyProteins * 4;
        final proteinPercentage = proteinCalories / totalCalories;

        expect(proteinPercentage, greaterThanOrEqualTo(0.15)); // Almeno 15%
        expect(proteinPercentage, lessThanOrEqualTo(0.30)); // Max 30% per non atleti

        print('Utente normale - Giorno ${dailyPlan.date}: ${totalDailyProteins}g proteine (${(proteinPercentage * 100).round()}% delle calorie)');
      }

      print('Compatibilità con utenti non atleti verificata');
    });
  });
}
