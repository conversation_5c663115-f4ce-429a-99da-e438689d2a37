import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../theme/dr_staffilano_theme.dart';
import '../../services/media_service.dart';
import 'media_progress_dialog.dart';

/// Modal per la selezione di media (foto/video dalla galleria, fotocamera, registrazione video su mobile, foto multiple)
class MediaSelectionModal extends StatelessWidget {
  final Function(List<MediaFile>) onMediaSelected;

  const MediaSelectionModal({
    super.key,
    required this.onMediaSelected,
  });

  static Future<void> show(
    BuildContext context, {
    required Function(List<MediaFile>) onMediaSelected,
  }) {
    return showDialog(
      context: context,
      barrierColor: Colors.transparent,
      builder: (context) => MediaSelectionModal(
        onMediaSelected: onMediaSelected,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Debug: mostra il numero di opzioni disponibili
    final optionsCount = kIsWeb ? 3 : 4;
    debugPrint('MediaSelectionModal: Mostrando $optionsCount opzioni (Web: $kIsWeb)');

    return Material(
      color: Colors.transparent,
      child: Container(
        color: Colors.black.withOpacity(0.5),
        child: Center(
          child: Container(
            width: MediaQuery.of(context).size.width > 600
                ? 400
                : MediaQuery.of(context).size.width * 0.9,
            margin: const EdgeInsets.symmetric(horizontal: 20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    children: [
                      Text(
                        'Aggiungi al tuo post',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[800],
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                        color: Colors.grey[600],
                        iconSize: 24,
                      ),
                    ],
                  ),
                ),

                const Divider(height: 1),

                // Options
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      // Foto/video dalla galleria
                      _buildOption(
                        icon: FontAwesomeIcons.image,
                        iconColor: const Color(0xFF45BD62),
                        title: 'Foto/video',
                        subtitle: 'Dalla galleria (ripeti per più foto)',
                        onTap: () => _selectFromGallery(context),
                      ),

                      const SizedBox(height: 16),

                      // Scatta foto
                      _buildOption(
                        icon: FontAwesomeIcons.camera,
                        iconColor: const Color(0xFF1877F2),
                        title: 'Fotocamera',
                        subtitle: 'Scatta una foto',
                        onTap: () => _takePhoto(context),
                      ),

                      const SizedBox(height: 16),

                      // Registra video (solo mobile)
                      if (!kIsWeb) ...[
                        _buildOption(
                          icon: FontAwesomeIcons.video,
                          iconColor: const Color(0xFFF5533D),
                          title: 'Video',
                          subtitle: 'Registra un video',
                          onTap: () => _recordVideo(context),
                        ),
                        const SizedBox(height: 16),
                      ],

                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOption({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.withOpacity(0.2)),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: iconColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(
                icon,
                color: iconColor,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectFromGallery(BuildContext context) async {
    HapticFeedback.lightImpact();
    Navigator.of(context).pop();

    // Mostra dialog per scegliere il tipo di media
    final mediaType = await _showMediaTypeDialog(context);
    if (mediaType == null) return;

    try {
      final mediaService = MediaService();

      // Mostra dialog di loading
      SimpleLoadingDialog.show(
        context,
        title: 'Apertura galleria',
        message: mediaType == 'image'
            ? 'Seleziona un\'immagine dalla galleria...'
            : 'Seleziona un video dalla galleria...',
      );

      MediaFile? mediaFile;
      if (mediaType == 'image') {
        mediaFile = await mediaService.pickSingleImage();
      } else {
        mediaFile = await mediaService.pickVideo();
      }

      // Assicurati sempre di chiudere il dialog
      if (context.mounted) {
        SimpleLoadingDialog.hide(context);
      }

      if (mediaFile != null) {
        debugPrint('Media file selezionato: ${mediaFile.name}, tipo: ${mediaFile.type}, dimensione: ${mediaFile.sizeFormatted}');
        onMediaSelected([mediaFile]);
        if (context.mounted) {
          final message = mediaType == 'image'
              ? 'Immagine selezionata con successo!'
              : 'Video selezionato con successo!';
          _showSuccessSnackBar(context, message);
        }
      } else {
        debugPrint('Nessun media file selezionato dalla galleria');
        if (context.mounted) {
          _showInfoSnackBar(context, 'Selezione annullata');
        }
      }
    } catch (e) {
      if (context.mounted) {
        SimpleLoadingDialog.hide(context);
        _showErrorSnackBar(context, 'Errore nella selezione del file: ${e.toString()}');
      }
    }
  }

  Future<void> _takePhoto(BuildContext context) async {
    HapticFeedback.lightImpact();
    Navigator.of(context).pop();

    try {
      final mediaService = MediaService();

      // Su web, non mostrare il dialog di loading perché la fotocamera si apre immediatamente
      if (!kIsWeb) {
        SimpleLoadingDialog.show(
          context,
          title: 'Apertura fotocamera',
          message: 'Preparazione della fotocamera...',
        );
      }

      final mediaFile = await mediaService.takePhoto(context: context);

      // Chiudi il dialog solo se era stato mostrato
      if (!kIsWeb && context.mounted) {
        SimpleLoadingDialog.hide(context);
      }

      if (mediaFile != null) {
        debugPrint('Foto scattata: ${mediaFile.name}, dimensione: ${mediaFile.sizeFormatted}');
        onMediaSelected([mediaFile]);
        if (context.mounted) {
          _showSuccessSnackBar(context, 'Foto scattata con successo!');
        }
      } else {
        debugPrint('Scatto foto annullato dall\'utente');
        if (context.mounted) {
          _showInfoSnackBar(context, 'Scatto foto annullato');
        }
      }
    } catch (e) {
      // Assicurati sempre di chiudere il dialog in caso di errore
      if (!kIsWeb && context.mounted) {
        SimpleLoadingDialog.hide(context);
      }

      if (context.mounted) {
        if (e.toString().contains('permission') || e.toString().contains('Permission')) {
          _showErrorSnackBar(context, 'Permesso fotocamera negato. Controlla le impostazioni del browser.');
        } else if (e.toString().contains('non supportata')) {
          _showErrorSnackBar(context, 'Fotocamera non supportata su questo browser. Prova con Chrome o Firefox.');
        } else {
          _showErrorSnackBar(context, 'Errore nell\'apertura della fotocamera: ${e.toString()}');
        }
      }
    }
  }

  Future<void> _recordVideo(BuildContext context) async {
    HapticFeedback.lightImpact();
    Navigator.of(context).pop();

    try {
      final mediaService = MediaService();

      // Mostra dialog di loading
      SimpleLoadingDialog.show(
        context,
        title: 'Apertura videocamera',
        message: 'Preparazione della videocamera...',
      );

      final mediaFile = await mediaService.recordVideo(context: context);

      // Assicurati sempre di chiudere il dialog
      if (context.mounted) {
        SimpleLoadingDialog.hide(context);
      }

      if (mediaFile != null) {
        debugPrint('Video registrato: ${mediaFile.name}, dimensione: ${mediaFile.sizeFormatted}');
        onMediaSelected([mediaFile]);
        if (context.mounted) {
          _showSuccessSnackBar(context, 'Video registrato con successo! (${mediaFile.sizeFormatted})');
        }
      } else {
        debugPrint('Registrazione video annullata dall\'utente');
        if (context.mounted) {
          _showInfoSnackBar(context, 'Registrazione video annullata');
        }
      }
    } catch (e) {
      // Assicurati sempre di chiudere il dialog in caso di errore
      if (context.mounted) {
        SimpleLoadingDialog.hide(context);

        if (e.toString().contains('permission') || e.toString().contains('Permission')) {
          _showErrorSnackBar(context, 'Permesso videocamera negato. Controlla le impostazioni dell\'app.');
        } else if (e.toString().contains('troppo grande')) {
          _showErrorSnackBar(context, 'Video troppo grande. Massimo 100MB consentiti.');
        } else if (e.toString().contains('non supportata')) {
          _showErrorSnackBar(context, 'Registrazione video non supportata su questa piattaforma.');
        } else {
          _showErrorSnackBar(context, 'Errore nella registrazione del video: ${e.toString()}');
        }
      }
    }
  }

  Future<String?> _showMediaTypeDialog(BuildContext context) async {
    return await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Seleziona tipo di media'),
        content: const Text('Cosa vuoi selezionare dalla galleria?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annulla'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop('video'),
            style: ElevatedButton.styleFrom(
              backgroundColor: DrStaffilanoTheme.professionalBlue,
              foregroundColor: Colors.white,
            ),
            child: const Text('Video'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop('image'),
            style: ElevatedButton.styleFrom(
              backgroundColor: DrStaffilanoTheme.primaryGreen,
              foregroundColor: Colors.white,
            ),
            child: const Text('Immagine'),
          ),
        ],
      ),
    );
  }

  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showSuccessSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: DrStaffilanoTheme.primaryGreen,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showInfoSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
