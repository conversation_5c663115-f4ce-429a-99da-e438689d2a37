import 'package:flutter/material.dart';
import '../../theme/dr_staffilano_theme.dart';

/// Widget per AppBar che si nasconde durante lo scroll
class CollapsingAppBar extends StatelessWidget {
  final String title;
  final String? subtitle;
  final String? backgroundImage;
  final List<Widget>? actions;
  final Widget child;
  final bool floating;
  final bool pinned;
  final bool snap;

  const CollapsingAppBar({
    super.key,
    required this.title,
    this.subtitle,
    this.backgroundImage,
    this.actions,
    required this.child,
    this.floating = true,
    this.pinned = false,
    this.snap = true,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 200,
              floating: floating,
              pinned: pinned,
              snap: snap,
              actions: actions,
              flexibleSpace: FlexibleSpaceBar(
                title: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                    ),
                    if (subtitle != null)
                      Text(
                        subtitle!,
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                  ],
                ),
                titlePadding: const EdgeInsets.only(left: 16, bottom: 16),
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        DrStaffilanoTheme.primaryGreen,
                        DrStaffilanoTheme.primaryGreen.withOpacity(0.8),
                      ],
                    ),
                    image: backgroundImage != null
                        ? DecorationImage(
                            image: NetworkImage(backgroundImage!),
                            fit: BoxFit.cover,
                            colorFilter: ColorFilter.mode(
                              DrStaffilanoTheme.primaryGreen.withOpacity(0.7),
                              BlendMode.overlay,
                            ),
                          )
                        : null,
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.3),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ];
        },
        body: child,
      ),
    );
  }
}

/// Widget semplificato per AppBar collassabile senza immagine di sfondo
class SimpleCollapsingAppBar extends StatelessWidget {
  final String title;
  final String? subtitle;
  final List<Widget>? actions;
  final Widget child;
  final Color? backgroundColor;

  const SimpleCollapsingAppBar({
    super.key,
    required this.title,
    this.subtitle,
    this.actions,
    required this.child,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 120,
              floating: true,
              pinned: false,
              snap: true,
              actions: actions,
              backgroundColor: backgroundColor ?? DrStaffilanoTheme.primaryGreen,
              flexibleSpace: FlexibleSpaceBar(
                title: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                    if (subtitle != null)
                      Text(
                        subtitle!,
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                      ),
                  ],
                ),
                titlePadding: const EdgeInsets.only(left: 16, bottom: 16),
              ),
            ),
          ];
        },
        body: child,
      ),
    );
  }
}

/// Widget per AppBar con particelle animate
class AnimatedCollapsingAppBar extends StatefulWidget {
  final String title;
  final String? subtitle;
  final String? backgroundImage;
  final List<Widget>? actions;
  final Widget child;

  const AnimatedCollapsingAppBar({
    super.key,
    required this.title,
    this.subtitle,
    this.backgroundImage,
    this.actions,
    required this.child,
  });

  @override
  State<AnimatedCollapsingAppBar> createState() => _AnimatedCollapsingAppBarState();
}

class _AnimatedCollapsingAppBarState extends State<AnimatedCollapsingAppBar>
    with TickerProviderStateMixin {
  late AnimationController _particleController;

  @override
  void initState() {
    super.initState();
    _particleController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _particleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 200,
              floating: true,
              pinned: false,
              snap: true,
              actions: widget.actions,
              flexibleSpace: FlexibleSpaceBar(
                title: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                    ),
                    if (widget.subtitle != null)
                      Text(
                        widget.subtitle!,
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                  ],
                ),
                titlePadding: const EdgeInsets.only(left: 16, bottom: 16),
                background: Stack(
                  children: [
                    // Background
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            DrStaffilanoTheme.primaryGreen,
                            DrStaffilanoTheme.primaryGreen.withOpacity(0.8),
                          ],
                        ),
                        image: widget.backgroundImage != null
                            ? DecorationImage(
                                image: NetworkImage(widget.backgroundImage!),
                                fit: BoxFit.cover,
                                colorFilter: ColorFilter.mode(
                                  DrStaffilanoTheme.primaryGreen.withOpacity(0.7),
                                  BlendMode.overlay,
                                ),
                              )
                            : null,
                      ),
                    ),

                    // Particelle animate
                    AnimatedBuilder(
                      animation: _particleController,
                      builder: (context, child) {
                        return CustomPaint(
                          painter: ParticlePainter(_particleController.value),
                          size: Size.infinite,
                        );
                      },
                    ),

                    // Overlay gradient
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withOpacity(0.3),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ];
        },
        body: widget.child,
      ),
    );
  }
}

/// Painter per le particelle animate
class ParticlePainter extends CustomPainter {
  final double animationValue;

  ParticlePainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.3)
      ..style = PaintingStyle.fill;

    // Disegna particelle che si muovono
    for (int i = 0; i < 20; i++) {
      final x = (size.width * (i / 20) + animationValue * 50) % size.width;
      final y = (size.height * 0.3 + (i % 3) * 20 + animationValue * 30) % size.height;
      final radius = 2.0 + (i % 3);

      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
