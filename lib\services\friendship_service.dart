import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/community_user.dart';
import '../models/friend_request.dart';

/// Servizio per gestire le amicizie e richieste
class FriendshipService extends ChangeNotifier {
  static const String _friendsKey = 'user_friends';
  static const String _friendRequestsKey = 'friend_requests';
  static const String _blockedUsersKey = 'blocked_users';
  static const String _friendSuggestionsKey = 'friend_suggestions';

  final Set<String> _friends = <String>{};
  final List<FriendRequest> _friendRequests = [];
  final Set<String> _blockedUsers = <String>{};
  final List<String> _friendSuggestions = [];

  /// Lista amici
  Set<String> get friends => Set.unmodifiable(_friends);

  /// Lista richieste di amicizia
  List<FriendRequest> get friendRequests => List.unmodifiable(_friendRequests);

  /// Lista utenti bloccati
  Set<String> get blockedUsers => Set.unmodifiable(_blockedUsers);

  /// Suggerimenti di amicizia
  List<String> get friendSuggestions => List.unmodifiable(_friendSuggestions);

  /// Inizializza il servizio
  Future<void> initialize() async {
    try {
      await _loadFriends();
      await _loadFriendRequests();
      await _loadBlockedUsers();
      await _loadFriendSuggestions();

      if (kDebugMode) {
        print('✅ FriendshipService inizializzato');
        print('👥 Amici: ${_friends.length}');
        print('📨 Richieste: ${_friendRequests.length}');
        print('🚫 Bloccati: ${_blockedUsers.length}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore inizializzazione FriendshipService: $e');
      }
    }
  }

  /// Carica la lista amici
  Future<void> _loadFriends() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final friendsJson = prefs.getString(_friendsKey);

      if (friendsJson != null) {
        final List<dynamic> friendsList = jsonDecode(friendsJson);
        _friends.clear();
        _friends.addAll(friendsList.cast<String>());
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore caricamento amici: $e');
      }
    }
  }

  /// Salva la lista amici
  Future<void> _saveFriends() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final friendsJson = jsonEncode(_friends.toList());
      await prefs.setString(_friendsKey, friendsJson);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore salvataggio amici: $e');
      }
    }
  }

  /// Carica le richieste di amicizia
  Future<void> _loadFriendRequests() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final requestsJson = prefs.getString(_friendRequestsKey);

      if (requestsJson != null) {
        final List<dynamic> requestsList = jsonDecode(requestsJson);
        _friendRequests.clear();
        _friendRequests.addAll(
          requestsList.map((json) => FriendRequest.fromJson(json))
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore caricamento richieste: $e');
      }
    }
  }

  /// Salva le richieste di amicizia
  Future<void> _saveFriendRequests() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final requestsJson = jsonEncode(
        _friendRequests.map((req) => req.toJson()).toList()
      );
      await prefs.setString(_friendRequestsKey, requestsJson);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore salvataggio richieste: $e');
      }
    }
  }

  /// Carica utenti bloccati
  Future<void> _loadBlockedUsers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final blockedJson = prefs.getString(_blockedUsersKey);

      if (blockedJson != null) {
        final List<dynamic> blockedList = jsonDecode(blockedJson);
        _blockedUsers.clear();
        _blockedUsers.addAll(blockedList.cast<String>());
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore caricamento utenti bloccati: $e');
      }
    }
  }

  /// Salva utenti bloccati
  Future<void> _saveBlockedUsers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final blockedJson = jsonEncode(_blockedUsers.toList());
      await prefs.setString(_blockedUsersKey, blockedJson);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore salvataggio utenti bloccati: $e');
      }
    }
  }

  /// Carica suggerimenti di amicizia
  Future<void> _loadFriendSuggestions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final suggestionsJson = prefs.getString(_friendSuggestionsKey);

      if (suggestionsJson != null) {
        final List<dynamic> suggestionsList = jsonDecode(suggestionsJson);
        _friendSuggestions.clear();
        _friendSuggestions.addAll(suggestionsList.cast<String>());
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore caricamento suggerimenti: $e');
      }
    }
  }

  /// Salva suggerimenti di amicizia
  Future<void> _saveFriendSuggestions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final suggestionsJson = jsonEncode(_friendSuggestions);
      await prefs.setString(_friendSuggestionsKey, suggestionsJson);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore salvataggio suggerimenti: $e');
      }
    }
  }

  /// Verifica se due utenti sono amici
  bool areFriends(String userId1, String userId2) {
    return _friends.contains(userId1) || _friends.contains(userId2);
  }

  /// Verifica se un utente è bloccato
  bool isBlocked(String userId) {
    return _blockedUsers.contains(userId);
  }

  /// Ottieni richieste in entrata per un utente
  List<FriendRequest> getIncomingRequests(String userId) {
    return _friendRequests.where(
      (req) => req.toUserId == userId && req.status == FriendRequestStatus.pending
    ).toList();
  }

  /// Ottieni richieste in uscita per un utente
  List<FriendRequest> getOutgoingRequests(String userId) {
    return _friendRequests.where(
      (req) => req.fromUserId == userId && req.status == FriendRequestStatus.pending
    ).toList();
  }

  /// Invia una richiesta di amicizia
  Future<bool> sendFriendRequest({
    required String fromUserId,
    required String toUserId,
    String? message,
  }) async {
    try {
      // Verifica che non siano già amici
      if (areFriends(fromUserId, toUserId)) {
        if (kDebugMode) print('⚠️ Utenti già amici');
        return false;
      }

      // Verifica che non ci sia già una richiesta pendente
      final existingRequest = _friendRequests.any(
        (req) => (req.fromUserId == fromUserId && req.toUserId == toUserId) ||
                 (req.fromUserId == toUserId && req.toUserId == fromUserId)
      );

      if (existingRequest) {
        if (kDebugMode) print('⚠️ Richiesta già esistente');
        return false;
      }

      // Crea nuova richiesta
      final request = FriendRequest.create(
        fromUserId: fromUserId,
        toUserId: toUserId,
        message: message,
      );

      _friendRequests.add(request);
      await _saveFriendRequests();

      notifyListeners();

      if (kDebugMode) {
        print('📨 Richiesta di amicizia inviata da $fromUserId a $toUserId');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore invio richiesta: $e');
      }
      return false;
    }
  }

  /// Accetta una richiesta di amicizia
  Future<bool> acceptFriendRequest(String requestId) async {
    try {
      final requestIndex = _friendRequests.indexWhere((req) => req.id == requestId);

      if (requestIndex == -1) {
        if (kDebugMode) print('⚠️ Richiesta non trovata');
        return false;
      }

      final request = _friendRequests[requestIndex];

      // Aggiorna lo stato della richiesta
      _friendRequests[requestIndex] = request.copyWith(
        status: FriendRequestStatus.accepted,
      );

      // Aggiungi entrambi gli utenti come amici
      _friends.add(request.fromUserId);
      _friends.add(request.toUserId);

      await _saveFriendRequests();
      await _saveFriends();

      notifyListeners();

      if (kDebugMode) {
        print('✅ Richiesta di amicizia accettata: ${request.fromUserId} ↔ ${request.toUserId}');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore accettazione richiesta: $e');
      }
      return false;
    }
  }

  /// Rifiuta una richiesta di amicizia
  Future<bool> rejectFriendRequest(String requestId) async {
    try {
      final requestIndex = _friendRequests.indexWhere((req) => req.id == requestId);

      if (requestIndex == -1) {
        if (kDebugMode) print('⚠️ Richiesta non trovata');
        return false;
      }

      final request = _friendRequests[requestIndex];

      // Aggiorna lo stato della richiesta
      _friendRequests[requestIndex] = request.copyWith(
        status: FriendRequestStatus.rejected,
      );

      await _saveFriendRequests();

      notifyListeners();

      if (kDebugMode) {
        print('❌ Richiesta di amicizia rifiutata: ${request.id}');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore rifiuto richiesta: $e');
      }
      return false;
    }
  }

  /// Rimuovi un amico
  Future<bool> removeFriend(String currentUserId, String friendUserId) async {
    try {
      _friends.remove(friendUserId);

      await _saveFriends();
      notifyListeners();

      if (kDebugMode) {
        print('💔 Amicizia rimossa: $currentUserId ↔ $friendUserId');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore rimozione amico: $e');
      }
      return false;
    }
  }

  /// Blocca un utente
  Future<bool> blockUser(String userId) async {
    try {
      _blockedUsers.add(userId);

      // Rimuovi dalle amicizie se presente
      _friends.remove(userId);

      // Rimuovi dalle richieste pendenti
      _friendRequests.removeWhere(
        (req) => req.fromUserId == userId || req.toUserId == userId
      );

      await _saveBlockedUsers();
      await _saveFriends();
      await _saveFriendRequests();

      notifyListeners();

      if (kDebugMode) {
        print('🚫 Utente bloccato: $userId');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore blocco utente: $e');
      }
      return false;
    }
  }

  /// Sblocca un utente
  Future<bool> unblockUser(String userId) async {
    try {
      _blockedUsers.remove(userId);

      await _saveBlockedUsers();
      notifyListeners();

      if (kDebugMode) {
        print('✅ Utente sbloccato: $userId');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore sblocco utente: $e');
      }
      return false;
    }
  }

  /// Genera suggerimenti di amicizia basati su interessi comuni
  Future<void> generateFriendSuggestions(
    String currentUserId,
    List<CommunityUser> allUsers,
  ) async {
    try {
      _friendSuggestions.clear();

      // Trova l'utente corrente
      final currentUser = allUsers.firstWhere((u) => u.id == currentUserId);

      for (final user in allUsers) {
        // Salta se è l'utente corrente, già amico, bloccato o ha richieste pendenti
        if (user.id == currentUserId ||
            _friends.contains(user.id) ||
            _blockedUsers.contains(user.id) ||
            _friendRequests.any((req) =>
              (req.fromUserId == currentUserId && req.toUserId == user.id) ||
              (req.fromUserId == user.id && req.toUserId == currentUserId)
            )) {
          continue;
        }

        // Calcola interessi comuni
        final commonInterests = currentUser.interests
            .where((interest) => user.interests.contains(interest))
            .length;

        // Suggerisci se ha almeno 2 interessi comuni o stesso livello membership
        if (commonInterests >= 2 ||
            user.membershipLevel == currentUser.membershipLevel) {
          _friendSuggestions.add(user.id);
        }
      }

      await _saveFriendSuggestions();
      notifyListeners();

      if (kDebugMode) {
        print('💡 Generati ${_friendSuggestions.length} suggerimenti di amicizia');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore generazione suggerimenti: $e');
      }
    }
  }

  /// Pulisci tutti i dati
  Future<void> clearAllData() async {
    try {
      _friends.clear();
      _friendRequests.clear();
      _blockedUsers.clear();
      _friendSuggestions.clear();

      await _saveFriends();
      await _saveFriendRequests();
      await _saveBlockedUsers();
      await _saveFriendSuggestions();

      notifyListeners();

      if (kDebugMode) {
        print('🧹 Tutti i dati di amicizia puliti');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore pulizia dati: $e');
      }
    }
  }

  /// Ottieni statistiche amicizie
  Map<String, dynamic> getFriendshipStats() {
    return {
      'totalFriends': _friends.length,
      'pendingIncoming': _friendRequests.where(
        (req) => req.status == FriendRequestStatus.pending
      ).length,
      'pendingOutgoing': _friendRequests.where(
        (req) => req.status == FriendRequestStatus.pending
      ).length,
      'blockedUsers': _blockedUsers.length,
      'suggestions': _friendSuggestions.length,
    };
  }
}
