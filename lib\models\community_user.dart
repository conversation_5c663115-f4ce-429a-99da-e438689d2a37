import 'package:uuid/uuid.dart';

/// Livelli di membership nella Staffilano InnerCircle™
enum MembershipLevel {
  basic('Basic', 'Membro Base', 0),
  premium('Premium', 'Membro Premium', 1000),
  vip('VIP', 'Membro VIP', 5000),
  ambassador('Ambassador', 'Ambasciatore Staffilano', 10000);

  const MembershipLevel(this.id, this.displayName, this.requiredPoints);

  final String id;
  final String displayName;
  final int requiredPoints;

  /// Ottieni il colore associato al livello
  String get colorHex {
    switch (this) {
      case MembershipLevel.basic:
        return '#6B7280'; // Grigio
      case MembershipLevel.premium:
        return '#3B82F6'; // Blu professionale
      case MembershipLevel.vip:
        return '#F59E0B'; // Oro
      case MembershipLevel.ambassador:
        return '#10B981'; // Verde medico
    }
  }

  /// Ottieni l'icona associata al livello
  String get icon {
    switch (this) {
      case MembershipLevel.basic:
        return '👤';
      case MembershipLevel.premium:
        return '⭐';
      case MembershipLevel.vip:
        return '👑';
      case MembershipLevel.ambassador:
        return '🏆';
    }
  }
}

/// Modello per l'utente della community
class CommunityUser {
  final String id;
  final String username;
  final String displayName;
  final String? bio;
  final String? avatarUrl;
  final MembershipLevel membershipLevel;
  final int communityPoints;
  final int totalPosts;
  final int totalLikes;
  final int followersCount;
  final int followingCount;
  final List<String> friendIds; // IDs degli amici
  final DateTime joinDate;
  final DateTime lastActive;
  final List<String> badges;
  final List<String> interests;
  final bool isVerified;
  final bool isMentor;
  final Map<String, dynamic> stats;

  CommunityUser({
    required this.id,
    required this.username,
    required this.displayName,
    this.bio,
    this.avatarUrl,
    this.membershipLevel = MembershipLevel.basic,
    this.communityPoints = 0,
    this.totalPosts = 0,
    this.totalLikes = 0,
    this.followersCount = 0,
    this.followingCount = 0,
    this.friendIds = const [],
    required this.joinDate,
    required this.lastActive,
    this.badges = const [],
    this.interests = const [],
    this.isVerified = false,
    this.isMentor = false,
    this.stats = const {},
  });

  /// Crea un nuovo utente community
  factory CommunityUser.create({
    required String username,
    required String displayName,
    String? bio,
    String? avatarUrl,
    List<String> interests = const [],
  }) {
    return CommunityUser(
      id: const Uuid().v4(),
      username: username,
      displayName: displayName,
      bio: bio,
      avatarUrl: avatarUrl,
      joinDate: DateTime.now(),
      lastActive: DateTime.now(),
      interests: interests,
    );
  }

  /// Copia con modifiche
  CommunityUser copyWith({
    String? username,
    String? displayName,
    String? bio,
    String? avatarUrl,
    MembershipLevel? membershipLevel,
    int? communityPoints,
    int? totalPosts,
    int? totalLikes,
    int? followersCount,
    int? followingCount,
    List<String>? friendIds,
    DateTime? lastActive,
    List<String>? badges,
    List<String>? interests,
    bool? isVerified,
    bool? isMentor,
    Map<String, dynamic>? stats,
  }) {
    return CommunityUser(
      id: id,
      username: username ?? this.username,
      displayName: displayName ?? this.displayName,
      bio: bio ?? this.bio,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      membershipLevel: membershipLevel ?? this.membershipLevel,
      communityPoints: communityPoints ?? this.communityPoints,
      totalPosts: totalPosts ?? this.totalPosts,
      totalLikes: totalLikes ?? this.totalLikes,
      followersCount: followersCount ?? this.followersCount,
      followingCount: followingCount ?? this.followingCount,
      friendIds: friendIds ?? this.friendIds,
      joinDate: joinDate,
      lastActive: lastActive ?? this.lastActive,
      badges: badges ?? this.badges,
      interests: interests ?? this.interests,
      isVerified: isVerified ?? this.isVerified,
      isMentor: isMentor ?? this.isMentor,
      stats: stats ?? this.stats,
    );
  }

  /// Converti in Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'username': username,
      'displayName': displayName,
      'bio': bio,
      'avatarUrl': avatarUrl,
      'membershipLevel': membershipLevel.id,
      'communityPoints': communityPoints,
      'totalPosts': totalPosts,
      'totalLikes': totalLikes,
      'followersCount': followersCount,
      'followingCount': followingCount,
      'friendIds': friendIds,
      'joinDate': joinDate.toIso8601String(),
      'lastActive': lastActive.toIso8601String(),
      'badges': badges,
      'interests': interests,
      'isVerified': isVerified,
      'isMentor': isMentor,
      'stats': stats,
    };
  }

  /// Crea da Map
  factory CommunityUser.fromMap(Map<String, dynamic> map) {
    return CommunityUser(
      id: map['id'] ?? '',
      username: map['username'] ?? '',
      displayName: map['displayName'] ?? '',
      bio: map['bio'],
      avatarUrl: map['avatarUrl'],
      membershipLevel: MembershipLevel.values.firstWhere(
        (level) => level.id == map['membershipLevel'],
        orElse: () => MembershipLevel.basic,
      ),
      communityPoints: map['communityPoints']?.toInt() ?? 0,
      totalPosts: map['totalPosts']?.toInt() ?? 0,
      totalLikes: map['totalLikes']?.toInt() ?? 0,
      followersCount: map['followersCount']?.toInt() ?? 0,
      followingCount: map['followingCount']?.toInt() ?? 0,
      friendIds: List<String>.from(map['friendIds'] ?? []),
      joinDate: DateTime.parse(map['joinDate'] ?? DateTime.now().toIso8601String()),
      lastActive: DateTime.parse(map['lastActive'] ?? DateTime.now().toIso8601String()),
      badges: List<String>.from(map['badges'] ?? []),
      interests: List<String>.from(map['interests'] ?? []),
      isVerified: map['isVerified'] ?? false,
      isMentor: map['isMentor'] ?? false,
      stats: Map<String, dynamic>.from(map['stats'] ?? {}),
    );
  }

  /// Converti in JSON (alias per toMap)
  Map<String, dynamic> toJson() => toMap();

  /// Crea da JSON (alias per fromMap)
  factory CommunityUser.fromJson(Map<String, dynamic> json) => CommunityUser.fromMap(json);

  /// Calcola il livello di membership basato sui punti
  MembershipLevel calculateMembershipLevel() {
    if (communityPoints >= MembershipLevel.ambassador.requiredPoints) {
      return MembershipLevel.ambassador;
    } else if (communityPoints >= MembershipLevel.vip.requiredPoints) {
      return MembershipLevel.vip;
    } else if (communityPoints >= MembershipLevel.premium.requiredPoints) {
      return MembershipLevel.premium;
    } else {
      return MembershipLevel.basic;
    }
  }

  /// Ottieni il progresso verso il prossimo livello
  double getProgressToNextLevel() {
    final currentLevel = calculateMembershipLevel();
    final nextLevel = _getNextLevel(currentLevel);

    if (nextLevel == null) return 1.0; // Livello massimo raggiunto

    final currentLevelPoints = currentLevel.requiredPoints;
    final nextLevelPoints = nextLevel.requiredPoints;
    final pointsInCurrentLevel = communityPoints - currentLevelPoints;
    final pointsNeededForNext = nextLevelPoints - currentLevelPoints;

    return pointsInCurrentLevel / pointsNeededForNext;
  }

  /// Ottieni il prossimo livello
  MembershipLevel? _getNextLevel(MembershipLevel current) {
    switch (current) {
      case MembershipLevel.basic:
        return MembershipLevel.premium;
      case MembershipLevel.premium:
        return MembershipLevel.vip;
      case MembershipLevel.vip:
        return MembershipLevel.ambassador;
      case MembershipLevel.ambassador:
        return null; // Livello massimo
    }
  }

  /// Verifica se l'utente è attivo (attivo negli ultimi 7 giorni)
  bool get isActive {
    return DateTime.now().difference(lastActive).inDays <= 7;
  }

  /// Ottieni il tasso di engagement
  double get engagementRate {
    if (totalPosts == 0) return 0.0;
    return totalLikes / totalPosts;
  }

  /// Verifica se questo utente è amico di un altro utente
  bool isFriendWith(String userId) {
    return friendIds.contains(userId);
  }

  @override
  String toString() {
    return 'CommunityUser(id: $id, username: $username, level: ${membershipLevel.displayName}, points: $communityPoints)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CommunityUser && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
