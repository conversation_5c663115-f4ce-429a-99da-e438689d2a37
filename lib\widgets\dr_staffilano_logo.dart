import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../theme/dr_staffilano_theme.dart';

/// Widget per il logo Dr. Staffilano con diverse varianti
class DrStaffilanoLogo extends StatelessWidget {
  /// Dimensione del logo
  final double? width;
  final double? height;
  
  /// Stile del logo
  final LogoStyle style;
  
  /// Se mostrare il testo accanto al logo
  final bool showText;
  
  /// Testo personalizzato (se diverso dal nome dell'app)
  final String? customText;
  
  /// Colore del testo
  final Color? textColor;
  
  /// Dimensione del testo
  final double? textSize;
  
  /// Allineamento
  final MainAxisAlignment alignment;

  const DrStaffilanoLogo({
    Key? key,
    this.width,
    this.height,
    this.style = LogoStyle.full,
    this.showText = false,
    this.customText,
    this.textColor,
    this.textSize,
    this.alignment = MainAxisAlignment.center,
  }) : super(key: key);

  /// Logo piccolo per header
  const Dr<PERSON>taffilano<PERSON>ogo.small({
    Key? key,
    this.showText = false,
    this.textColor,
    this.alignment = MainAxisAlignment.center,
  }) : width = 32,
       height = 32,
       style = LogoStyle.icon,
       customText = null,
       textSize = 14,
       super(key: key);

  /// Logo medio per card e sezioni
  const DrStaffilanoLogo.medium({
    Key? key,
    this.showText = true,
    this.textColor,
    this.alignment = MainAxisAlignment.center,
  }) : width = 64,
       height = 64,
       style = LogoStyle.full,
       customText = null,
       textSize = 16,
       super(key: key);

  /// Logo grande per splash screen e home
  const DrStaffilanoLogo.large({
    Key? key,
    this.showText = true,
    this.textColor,
    this.alignment = MainAxisAlignment.center,
  }) : width = 120,
       height = 120,
       style = LogoStyle.full,
       customText = null,
       textSize = 24,
       super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: alignment,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildLogo(),
        if (showText) ...[
          const SizedBox(width: 12),
          _buildText(),
        ],
      ],
    );
  }

  /// Costruisce l'immagine del logo
  Widget _buildLogo() {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        boxShadow: style == LogoStyle.full ? [
          BoxShadow(
            color: DrStaffilanoTheme.primaryGreen.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ] : null,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.asset(
          AppConstants.logoPath,
          width: width,
          height: height,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            // Fallback se l'immagine non è disponibile
            return Container(
              width: width,
              height: height,
              decoration: BoxDecoration(
                color: DrStaffilanoTheme.primaryGreen,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.local_hospital,
                color: Colors.white,
                size: (width ?? 64) * 0.6,
              ),
            );
          },
        ),
      ),
    );
  }

  /// Costruisce il testo del brand
  Widget _buildText() {
    final text = customText ?? AppConstants.appName;
    final color = textColor ?? DrStaffilanoTheme.textPrimary;
    final size = textSize ?? 16;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'Dr. Staffilano',
          style: TextStyle(
            fontSize: size,
            fontWeight: FontWeight.bold,
            color: color,
            letterSpacing: 0.5,
          ),
        ),
        if (style == LogoStyle.full) ...[
          const SizedBox(height: 2),
          Text(
            'Nutrition',
            style: TextStyle(
              fontSize: size * 0.8,
              fontWeight: FontWeight.w500,
              color: color.withOpacity(0.8),
              letterSpacing: 0.3,
            ),
          ),
        ],
      ],
    );
  }
}

/// Stili disponibili per il logo
enum LogoStyle {
  /// Solo icona
  icon,
  
  /// Logo completo con effetti
  full,
}

/// Widget per la splash screen con animazione
class DrStaffilanoSplashLogo extends StatefulWidget {
  const DrStaffilanoSplashLogo({Key? key}) : super(key: key);

  @override
  State<DrStaffilanoSplashLogo> createState() => _DrStaffilanoSplashLogoState();
}

class _DrStaffilanoSplashLogoState extends State<DrStaffilanoSplashLogo>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeIn,
    ));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const DrStaffilanoLogo.large(
                  showText: false,
                ),
                const SizedBox(height: 24),
                Text(
                  AppConstants.appName,
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: DrStaffilanoTheme.primaryGreen,
                    letterSpacing: 1.0,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  AppConstants.appTagline,
                  style: TextStyle(
                    fontSize: 16,
                    color: DrStaffilanoTheme.textSecondary,
                    letterSpacing: 0.5,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
