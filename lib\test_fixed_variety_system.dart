import 'dart:io';
import 'dart:math' as math;
import 'services/specific_diet_generator_service.dart';
import 'services/food_variety_manager.dart';
import 'data/specific_diet_foods.dart';
import 'models/user_profile.dart';

/// TEST DEL SISTEMA DI VARIETÀ CORRETTO
/// Verifica che il nuovo sistema di varietà funzioni con i 157 alimenti specifici
Future<void> main() async {
  print('🔧 TEST SISTEMA VARIETÀ CORRETTO - VERIFICA FUNZIONAMENTO');
  print('=' * 65);
  print('Obiettivo: Verificare che il sistema utilizzi più alimenti dai 157 disponibili\n');

  try {
    // FASE 1: VERIFICA DATABASE
    print('1️⃣ VERIFICA DATABASE ALIMENTI SPECIFICI');
    print('-' * 45);

    final allSpecificFoods = SpecificDietFoods.getAllFoods();
    print('📊 Alimenti disponibili: ${allSpecificFoods.length}');

    // Analizza distribuzione per categoria
    final categoryCounts = <String, int>{};
    for (final food in allSpecificFoods) {
      for (final category in food.categories) {
        final categoryName = category.toString().split('.').last;
        categoryCounts[categoryName] = (categoryCounts[categoryName] ?? 0) + 1;
      }
    }

    print('📈 Distribuzione categorie:');
    categoryCounts.entries.forEach((entry) {
      print('   ${entry.key}: ${entry.value} alimenti');
    });

    // FASE 2: INIZIALIZZA SERVIZI
    print('\n2️⃣ INIZIALIZZAZIONE SERVIZI');
    print('-' * 30);

    final varietyManager = await FoodVarietyManager.getInstance();
    await varietyManager.resetUsageHistory();
    print('✅ FoodVarietyManager resettato');

    final specificGenerator = await SpecificDietGeneratorService.getInstance();
    print('✅ SpecificDietGeneratorService inizializzato');

    // FASE 3: CREA PROFILO UTENTE
    print('\n3️⃣ CREAZIONE PROFILO UTENTE');
    print('-' * 30);

    final testProfile = UserProfile(
      id: 'test_fixed_variety_${DateTime.now().millisecondsSinceEpoch}',
      name: 'Test Varietà Corretta',
      age: 30,
      gender: Gender.male,
      height: 175,
      weight: 70,
      activityLevel: ActivityLevel.moderate,
      goal: Goal.maintain,
      dietType: DietType.omnivore,
      allergies: [],
      dislikedFoods: [],
      mealsPerDay: 3,
    );

    print('👤 Profilo: ${testProfile.name}');
    print('   Calorie target: ${testProfile.calculateCalorieTarget()} kcal/giorno');
    print('   Macronutrienti: ${testProfile.calculateMacroGrams()}');

    // FASE 4: GENERAZIONE PIANI MULTIPLI
    print('\n4️⃣ GENERAZIONE PIANI DIETETICI MULTIPLI');
    print('-' * 45);

    final allUsedFoods = <String>[];
    final plansByDay = <int, Map<String, List<String>>>{};

    for (int day = 1; day <= 7; day++) {
      print('\n📅 GIORNO $day:');

      try {
        final weeklyPlan = await specificGenerator.generateWeeklyDietPlan(
          testProfile,
          weeks: 1,
        );

        if (weeklyPlan.dailyPlans.isNotEmpty) {
          final dailyPlan = weeklyPlan.dailyPlans.first;
          plansByDay[day] = {};

          for (final meal in dailyPlan.meals) {
            final mealFoods = meal.foods.map((fp) => fp.food.name).toList();
            final mealType = _getMealDisplayName(meal.type);

            plansByDay[day]![meal.type] = mealFoods;
            allUsedFoods.addAll(mealFoods);

            print('   $mealType: ${mealFoods.join(', ')}');
          }
        } else {
          print('   ❌ Nessun piano generato');
        }
      } catch (e) {
        print('   ❌ Errore: $e');
      }

      // Pausa per permettere al sistema di varietà di funzionare
      await Future.delayed(Duration(milliseconds: 200));
    }

    // FASE 5: ANALISI VARIETÀ
    print('\n5️⃣ ANALISI VARIETÀ OTTENUTA');
    print('-' * 30);

    final uniqueUsedFoods = allUsedFoods.toSet();
    final varietyRatio = uniqueUsedFoods.length / allUsedFoods.length;
    final databaseUtilization = uniqueUsedFoods.length / allSpecificFoods.length;

    print('📊 RISULTATI VARIETÀ:');
    print('   - Alimenti totali selezionati: ${allUsedFoods.length}');
    print('   - Alimenti unici utilizzati: ${uniqueUsedFoods.length}');
    print('   - Rapporto varietà: ${(varietyRatio * 100).toStringAsFixed(1)}%');
    print('   - Utilizzo database: ${(databaseUtilization * 100).toStringAsFixed(1)}% (${uniqueUsedFoods.length}/157)');

    // Mostra alcuni alimenti utilizzati
    print('\n🍽️ ESEMPI ALIMENTI UTILIZZATI:');
    final sortedFoods = uniqueUsedFoods.toList()..sort();
    for (int i = 0; i < math.min(15, sortedFoods.length); i++) {
      final food = sortedFoods[i];
      final count = allUsedFoods.where((f) => f == food).length;
      print('   ${i + 1}. $food (utilizzato $count volte)');
    }

    // FASE 6: ANALISI SOVRAPPOSIZIONI
    print('\n6️⃣ ANALISI SOVRAPPOSIZIONI TRA GIORNI');
    print('-' * 40);

    for (final mealType in ['breakfast', 'lunch', 'dinner']) {
      print('\n${_getMealDisplayName(mealType)}:');

      final mealFoodsByDay = <int, Set<String>>{};
      for (final dayEntry in plansByDay.entries) {
        if (dayEntry.value.containsKey(mealType)) {
          mealFoodsByDay[dayEntry.key] = dayEntry.value[mealType]!.toSet();
        }
      }

      // Calcola sovrapposizioni consecutive
      var totalOverlap = 0;
      var totalComparisons = 0;

      for (int day = 1; day <= 6; day++) {
        if (mealFoodsByDay.containsKey(day) && mealFoodsByDay.containsKey(day + 1)) {
          final foods1 = mealFoodsByDay[day]!;
          final foods2 = mealFoodsByDay[day + 1]!;
          final overlap = foods1.intersection(foods2);
          final overlapPercentage = foods1.isNotEmpty ? (overlap.length / foods1.length * 100) : 0;

          totalOverlap += overlap.length;
          totalComparisons++;

          print('   Giorno $day vs ${day + 1}: ${overlap.length} comuni (${overlapPercentage.toStringAsFixed(1)}%)');
        }
      }

      final avgOverlap = totalComparisons > 0 ? totalOverlap / totalComparisons : 0;
      print('   Media sovrapposizione: ${avgOverlap.toStringAsFixed(1)} alimenti');
    }

    // FASE 7: VERIFICA TRACKING
    print('\n7️⃣ VERIFICA TRACKING VARIETÀ');
    print('-' * 35);

    final stats = varietyManager.getUsageStatistics();
    print('📈 Statistiche FoodVarietyManager:');
    print('   - Alimenti tracciati: ${stats['totalTrackedFoods']}');
    print('   - Utilizzi totali: ${stats['totalUsages']}');
    print('   - Utilizzi recenti: ${stats['recentUsages']}');

    final recentFoods = varietyManager.getRecentlyUsedFoods();
    if (recentFoods.isNotEmpty) {
      print('   - Alimenti recenti: ${recentFoods.length}');
      print('   - Primi 5: ${recentFoods.take(5).join(', ')}');
    }

    // FASE 8: VALUTAZIONE FINALE
    print('\n8️⃣ VALUTAZIONE FINALE');
    print('-' * 25);

    final issues = <String>[];
    final successes = <String>[];

    // Verifica utilizzo database
    if (databaseUtilization >= 0.6) {
      successes.add('Ottimo utilizzo database (${(databaseUtilization * 100).toStringAsFixed(1)}%)');
    } else if (databaseUtilization >= 0.4) {
      successes.add('Buon utilizzo database (${(databaseUtilization * 100).toStringAsFixed(1)}%)');
    } else {
      issues.add('Utilizzo database insufficiente (${(databaseUtilization * 100).toStringAsFixed(1)}%)');
    }

    // Verifica varietà
    if (varietyRatio >= 0.7) {
      successes.add('Eccellente varietà (${(varietyRatio * 100).toStringAsFixed(1)}%)');
    } else if (varietyRatio >= 0.5) {
      successes.add('Buona varietà (${(varietyRatio * 100).toStringAsFixed(1)}%)');
    } else {
      issues.add('Varietà insufficiente (${(varietyRatio * 100).toStringAsFixed(1)}%)');
    }

    // Verifica tracking
    if (stats['totalTrackedFoods'] as int > 0) {
      successes.add('Sistema di tracking attivo');
    } else {
      issues.add('Sistema di tracking non funzionante');
    }

    // Verifica numero alimenti unici
    if (uniqueUsedFoods.length >= 30) {
      successes.add('Buona diversità alimenti (${uniqueUsedFoods.length} unici)');
    } else if (uniqueUsedFoods.length >= 20) {
      successes.add('Diversità moderata (${uniqueUsedFoods.length} unici)');
    } else {
      issues.add('Diversità limitata (${uniqueUsedFoods.length} unici)');
    }

    print('\n' + '=' * 65);

    if (issues.isEmpty) {
      print('🎉 SUCCESSO COMPLETO! Sistema di varietà funzionante');
      print('\n✅ RISULTATI POSITIVI:');
      for (final success in successes) {
        print('   ✅ $success');
      }

      print('\n🌟 BENEFICI OTTENUTI:');
      print('   🍽️ Piani dietetici molto più vari');
      print('   📈 Utilizzo significativo del database (${uniqueUsedFoods.length}/157 alimenti)');
      print('   🔄 Riduzione ripetizioni tra giorni');
      print('   📊 Sistema di tracking attivo e funzionante');
      print('   🇮🇹 Varietà autentica della cucina italiana');

    } else {
      print('⚠️ PROBLEMI RILEVATI:');
      for (final issue in issues) {
        print('   ❌ $issue');
      }

      if (successes.isNotEmpty) {
        print('\n✅ ASPETTI POSITIVI:');
        for (final success in successes) {
          print('   ✅ $success');
        }
      }
    }

    // Confronto con obiettivi
    print('\n🎯 CONFRONTO CON OBIETTIVI:');
    print('   Target utilizzo database: 60-70% → Ottenuto: ${(databaseUtilization * 100).toStringAsFixed(1)}%');
    print('   Target varietà: 50%+ → Ottenuto: ${(varietyRatio * 100).toStringAsFixed(1)}%');
    print('   Target alimenti unici: 30+ → Ottenuto: ${uniqueUsedFoods.length}');
    print('   Target tracking: Attivo → ${stats['totalTrackedFoods'] as int > 0 ? 'Attivo' : 'Non attivo'}');

  } catch (e, stackTrace) {
    print('\n❌ ERRORE CRITICO: $e');
    print('Stack trace: $stackTrace');
    exit(1);
  }
}

String _getMealDisplayName(String mealType) {
  switch (mealType) {
    case 'breakfast': return 'Colazione';
    case 'lunch': return 'Pranzo';
    case 'dinner': return 'Cena';
    case 'snack': return 'Spuntino';
    default: return mealType;
  }
}
