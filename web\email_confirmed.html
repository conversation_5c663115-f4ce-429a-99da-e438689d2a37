<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Confermata - Dr<PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
            animation: slideUp 0.6s ease-out;
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .success-icon {
            width: 80px;
            height: 80px;
            background: #4CAF50;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .checkmark {
            color: white;
            font-size: 40px;
            font-weight: bold;
        }
        
        h1 {
            color: #2E7D32;
            font-size: 28px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        p {
            color: #666;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        
        .app-button {
            background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .app-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(76, 175, 80, 0.3);
        }
        
        .secondary-button {
            background: transparent;
            color: #4CAF50;
            border: 2px solid #4CAF50;
        }
        
        .secondary-button:hover {
            background: #4CAF50;
            color: white;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #999;
            font-size: 14px;
        }
        
        .logo {
            font-size: 20px;
            font-weight: bold;
            color: #2E7D32;
            margin-bottom: 10px;
        }
        
        @media (max-width: 480px) {
            .container {
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 24px;
            }
            
            .app-button {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">
            <div class="checkmark">✓</div>
        </div>
        
        <h1>Email Confermata con Successo!</h1>
        
        <p>
            Perfetto! La tua email è stata confermata correttamente. 
            Ora puoi accedere alla tua app <strong>Dr. Staffilano Nutrition</strong> 
            e iniziare il tuo percorso verso una nutrizione ottimale.
        </p>
        
        <div>
            <a href="com.dietapp.app_dieta://login-callback" class="app-button" id="openApp">
                📱 Apri l'App
            </a>
            <a href="https://rnunzfuibfjpritvcfmj.supabase.co/auth/v1/callback" class="app-button secondary-button">
                🌐 Continua sul Web
            </a>
        </div>
        
        <div class="footer">
            <div class="logo">Dr. Staffilano Nutrition</div>
            <p>La tua salute cardiovascolare è la nostra priorità</p>
        </div>
    </div>

    <script>
        // Rileva se è mobile e mostra il pulsante appropriato
        function isMobile() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }
        
        // Auto-redirect per mobile dopo 3 secondi
        if (isMobile()) {
            setTimeout(() => {
                window.location.href = 'com.dietapp.app_dieta://login-callback';
            }, 3000);
            
            // Mostra countdown
            let countdown = 3;
            const button = document.getElementById('openApp');
            const originalText = button.textContent;
            
            const timer = setInterval(() => {
                button.textContent = `📱 Apertura app in ${countdown}s...`;
                countdown--;
                
                if (countdown < 0) {
                    clearInterval(timer);
                    button.textContent = originalText;
                }
            }, 1000);
        }
        
        // Gestione click pulsante app
        document.getElementById('openApp').addEventListener('click', function(e) {
            if (isMobile()) {
                // Su mobile, prova ad aprire l'app
                window.location.href = 'com.dietapp.app_dieta://login-callback';
                
                // Fallback dopo 2 secondi se l'app non si apre
                setTimeout(() => {
                    alert('App non trovata. Scarica Dr. Staffilano Nutrition dal Play Store.');
                }, 2000);
            } else {
                // Su desktop, reindirizza al web
                window.location.href = 'https://rnunzfuibfjpritvcfmj.supabase.co/auth/v1/callback';
            }
        });
    </script>
</body>
</html>
