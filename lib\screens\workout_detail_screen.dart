import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../theme/new_app_theme.dart';
import '../utils/image_placeholder_helper.dart';

class WorkoutDetailScreen extends StatelessWidget {
  final String title;
  final String description;
  final String trainerName;
  final String? trainerAvatarUrl;
  final String? backgroundImageUrl;
  final List<Map<String, dynamic>> exercises;

  const WorkoutDetailScreen({
    Key? key,
    required this.title,
    required this.description,
    required this.trainerName,
    required this.trainerAvatarUrl,
    this.backgroundImageUrl,
    required this.exercises,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: NewAppTheme.backgroundColor,
      body: CustomScrollView(
        slivers: [
          _buildAppBar(context),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(NewAppTheme.spacing),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDescription().animate().fadeIn(duration: 300.ms),
                  const SizedBox(height: NewAppTheme.spacing),
                  _buildTrainerInfo().animate().fadeIn(duration: 300.ms, delay: 100.ms),
                  const SizedBox(height: NewAppTheme.largeSpacing),
                  Text(
                    'Exercises',
                    style: NewAppTheme.subtitleLarge,
                  ).animate().fadeIn(duration: 300.ms, delay: 200.ms),
                  const SizedBox(height: NewAppTheme.spacing),
                ],
              ),
            ),
          ),
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                return _buildExerciseItem(exercises[index])
                    .animate()
                    .fadeIn(duration: 300.ms, delay: Duration(milliseconds: 300 + (index * 100)));
              },
              childCount: exercises.length,
            ),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(NewAppTheme.spacing),
              child: _buildJoinButton().animate().fadeIn(duration: 300.ms, delay: 600.ms),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return SliverAppBar(
      expandedHeight: 240,
      pinned: true,
      backgroundColor: NewAppTheme.primaryColor,
      leading: IconButton(
        icon: Container(
          padding: const EdgeInsets.all(8),
          decoration: const BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.arrow_back,
            color: NewAppTheme.primaryColor,
            size: 20,
          ),
        ),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.favorite_border,
              color: NewAppTheme.primaryColor,
              size: 20,
            ),
          ),
          onPressed: () {},
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          title,
          style: NewAppTheme.subtitleLarge.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Stack(
          fit: StackFit.expand,
          children: [
            // Utilizziamo un placeholder colorato invece di un'immagine
            Container(
              color: NewAppTheme.primaryColor,
            ),
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.7),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDescription() {
    return Text(
      description,
      style: NewAppTheme.bodyMedium,
    );
  }

  Widget _buildTrainerInfo() {
    return Row(
      children: [
        ImagePlaceholderHelper.createAvatarPlaceholder(
          size: 50,
          initials: trainerName.isNotEmpty ? trainerName[0] : 'T',
          backgroundColor: NewAppTheme.primaryColor.withOpacity(0.2),
        ),
        const SizedBox(width: NewAppTheme.spacing),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Trainer: $trainerName',
              style: NewAppTheme.subtitleMedium,
            ),
            Text(
              'Certified Yoga Instructor',
              style: NewAppTheme.bodySmall,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildExerciseItem(Map<String, dynamic> exercise) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: NewAppTheme.spacing,
        vertical: NewAppTheme.smallSpacing,
      ),
      child: Container(
        decoration: NewAppTheme.cardDecoration,
        child: Padding(
          padding: const EdgeInsets.all(NewAppTheme.spacing),
          child: Row(
            children: [
              // Utilizziamo un placeholder per l'immagine dell'esercizio
              exercise['imageUrl'] != null
                ? Image.asset(
                    exercise['imageUrl'],
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return ImagePlaceholderHelper.createPlaceholder(
                        width: 80,
                        height: 80,
                        backgroundColor: NewAppTheme.secondaryColor,
                        icon: Icons.fitness_center,
                        borderRadius: NewAppTheme.smallBorderRadius,
                      );
                    },
                  )
                : ImagePlaceholderHelper.createPlaceholder(
                    width: 80,
                    height: 80,
                    backgroundColor: NewAppTheme.secondaryColor,
                    icon: Icons.fitness_center,
                    borderRadius: NewAppTheme.smallBorderRadius,
                  ),
              const SizedBox(width: NewAppTheme.spacing),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      exercise['name'],
                      style: NewAppTheme.subtitleMedium,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      exercise['description'],
                      style: NewAppTheme.bodySmall,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      exercise['difficulty'],
                      style: NewAppTheme.caption.copyWith(
                        color: NewAppTheme.primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildJoinButton() {
    return ElevatedButton(
      onPressed: () {},
      style: ElevatedButton.styleFrom(
        backgroundColor: NewAppTheme.primaryColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: NewAppTheme.spacing),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(NewAppTheme.borderRadius),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: const [
          Text(
            'Join online',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(width: 8),
          Icon(
            Icons.arrow_forward,
            size: 18,
          ),
        ],
      ),
    );
  }
}
