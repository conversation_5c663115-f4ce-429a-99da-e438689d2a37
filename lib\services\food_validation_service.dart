import '../models/food.dart';
import '../models/recipe.dart';

/// Service per la validazione dei dati alimentari
/// Fornisce metodi per verificare la qualità e l'accuratezza dei dati alimentari
class FoodValidationService {
  // Singleton
  static FoodValidationService? _instance;
  
  // Costruttore privato
  FoodValidationService._();
  
  /// Ottieni l'istanza singleton del service
  static FoodValidationService getInstance() {
    _instance ??= FoodValidationService._();
    return _instance!;
  }
  
  /// Verifica se un alimento ha dati nutrizionali plausibili
  bool hasPlausibleNutrients(Food food) {
    // Verifica che i valori nutrizionali siano in un intervallo ragionevole
    
    // Calorie: 0-900 kcal per 100g è un intervallo ragionevole per la maggior parte degli alimenti
    if (food.calories < 0 || food.calories > 900) {
      return false;
    }
    
    // Proteine: 0-100g per 100g
    if (food.proteins < 0 || food.proteins > 100) {
      return false;
    }
    
    // Carboidrati: 0-100g per 100g
    if (food.carbs < 0 || food.carbs > 100) {
      return false;
    }
    
    // Grassi: 0-100g per 100g
    if (food.fats < 0 || food.fats > 100) {
      return false;
    }
    
    // Fibre: 0-50g per 100g
    if (food.fiber < 0 || food.fiber > 50) {
      return false;
    }
    
    // Zuccheri: 0-100g per 100g
    if (food.sugar < 0 || food.sugar > 100) {
      return false;
    }
    
    // Verifica che la somma di proteine, carboidrati e grassi non superi 100g
    if (food.proteins + food.carbs + food.fats > 105) { // Consentiamo un piccolo margine di errore
      return false;
    }
    
    // Verifica che gli zuccheri non superino i carboidrati totali
    if (food.sugar > food.carbs + 1) { // Piccolo margine di errore
      return false;
    }
    
    // Verifica che le fibre non superino i carboidrati totali
    if (food.fiber > food.carbs + 1) { // Piccolo margine di errore
      return false;
    }
    
    return true;
  }
  
  /// Verifica se un alimento ha una fonte affidabile
  bool hasReliableSource(Food food) {
    // Fonti considerate affidabili
    final reliableSources = [
      DataSource.usda,
      DataSource.crea,
      DataSource.uk_cofid,
      DataSource.fr_ciqual,
    ];
    
    return reliableSources.contains(food.dataSource);
  }
  
  /// Verifica se un alimento ha dati completi
  bool hasCompleteData(Food food) {
    // Verifica che i campi essenziali siano presenti
    if (food.name.isEmpty) {
      return false;
    }
    
    if (food.calories <= 0) {
      return false;
    }
    
    if (food.proteins <= 0 && food.carbs <= 0 && food.fats <= 0) {
      return false;
    }
    
    if (food.categories.isEmpty) {
      return false;
    }
    
    if (food.suitableForMeals.isEmpty) {
      return false;
    }
    
    return true;
  }
  
  /// Verifica se una ricetta ha dati nutrizionali plausibili
  bool hasPlausibleRecipeNutrients(Recipe recipe) {
    // Verifica che i valori nutrizionali siano in un intervallo ragionevole
    
    // Calorie per porzione: 0-2000 kcal è un intervallo ragionevole
    if (recipe.caloriesPerServing < 0 || recipe.caloriesPerServing > 2000) {
      return false;
    }
    
    // Proteine per porzione: 0-200g
    if (recipe.proteinsPerServing < 0 || recipe.proteinsPerServing > 200) {
      return false;
    }
    
    // Carboidrati per porzione: 0-300g
    if (recipe.carbsPerServing < 0 || recipe.carbsPerServing > 300) {
      return false;
    }
    
    // Grassi per porzione: 0-200g
    if (recipe.fatsPerServing < 0 || recipe.fatsPerServing > 200) {
      return false;
    }
    
    return true;
  }
  
  /// Verifica se una ricetta ha dati completi
  bool hasCompleteRecipeData(Recipe recipe) {
    // Verifica che i campi essenziali siano presenti
    if (recipe.name.isEmpty) {
      return false;
    }
    
    if (recipe.ingredients.isEmpty) {
      return false;
    }
    
    if (recipe.instructions.isEmpty) {
      return false;
    }
    
    if (recipe.servings <= 0) {
      return false;
    }
    
    if (recipe.categories.isEmpty) {
      return false;
    }
    
    if (recipe.suitableForMeals.isEmpty) {
      return false;
    }
    
    return true;
  }
  
  /// Verifica se un alimento è pronto per la validazione
  bool isReadyForValidation(Food food) {
    return hasPlausibleNutrients(food) && hasCompleteData(food);
  }
  
  /// Verifica se una ricetta è pronta per la validazione
  bool isRecipeReadyForValidation(Recipe recipe) {
    return hasPlausibleRecipeNutrients(recipe) && hasCompleteRecipeData(recipe);
  }
  
  /// Genera un rapporto di validazione per un alimento
  Map<String, dynamic> generateFoodValidationReport(Food food) {
    final report = <String, dynamic>{
      'id': food.id,
      'name': food.name,
      'hasPlausibleNutrients': hasPlausibleNutrients(food),
      'hasReliableSource': hasReliableSource(food),
      'hasCompleteData': hasCompleteData(food),
      'isReadyForValidation': isReadyForValidation(food),
      'issues': <String>[],
    };
    
    // Identifica i problemi specifici
    final issues = <String>[];
    
    if (food.name.isEmpty) {
      issues.add('Nome mancante');
    }
    
    if (food.calories <= 0) {
      issues.add('Calorie non valide');
    }
    
    if (food.proteins < 0 || food.proteins > 100) {
      issues.add('Proteine non plausibili');
    }
    
    if (food.carbs < 0 || food.carbs > 100) {
      issues.add('Carboidrati non plausibili');
    }
    
    if (food.fats < 0 || food.fats > 100) {
      issues.add('Grassi non plausibili');
    }
    
    if (food.proteins + food.carbs + food.fats > 105) {
      issues.add('La somma di proteine, carboidrati e grassi supera 100g');
    }
    
    if (food.sugar > food.carbs + 1) {
      issues.add('Gli zuccheri superano i carboidrati totali');
    }
    
    if (food.fiber > food.carbs + 1) {
      issues.add('Le fibre superano i carboidrati totali');
    }
    
    if (food.categories.isEmpty) {
      issues.add('Categorie mancanti');
    }
    
    if (food.suitableForMeals.isEmpty) {
      issues.add('Tipi di pasto mancanti');
    }
    
    report['issues'] = issues;
    
    return report;
  }
  
  /// Genera un rapporto di validazione per una ricetta
  Map<String, dynamic> generateRecipeValidationReport(Recipe recipe) {
    final report = <String, dynamic>{
      'id': recipe.id,
      'name': recipe.name,
      'hasPlausibleNutrients': hasPlausibleRecipeNutrients(recipe),
      'hasCompleteData': hasCompleteRecipeData(recipe),
      'isReadyForValidation': isRecipeReadyForValidation(recipe),
      'issues': <String>[],
    };
    
    // Identifica i problemi specifici
    final issues = <String>[];
    
    if (recipe.name.isEmpty) {
      issues.add('Nome mancante');
    }
    
    if (recipe.ingredients.isEmpty) {
      issues.add('Ingredienti mancanti');
    }
    
    if (recipe.instructions.isEmpty) {
      issues.add('Istruzioni mancanti');
    }
    
    if (recipe.servings <= 0) {
      issues.add('Numero di porzioni non valido');
    }
    
    if (recipe.caloriesPerServing < 0 || recipe.caloriesPerServing > 2000) {
      issues.add('Calorie per porzione non plausibili');
    }
    
    if (recipe.categories.isEmpty) {
      issues.add('Categorie mancanti');
    }
    
    if (recipe.suitableForMeals.isEmpty) {
      issues.add('Tipi di pasto mancanti');
    }
    
    report['issues'] = issues;
    
    return report;
  }
}
