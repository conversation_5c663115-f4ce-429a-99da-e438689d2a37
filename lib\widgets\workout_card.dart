import 'package:flutter/material.dart';
import '../theme/new_app_theme.dart';
import '../utils/image_placeholder_helper.dart';

/// Widget per visualizzare una sessione di allenamento
class WorkoutCard extends StatelessWidget {
  final String title;
  final String difficulty;
  final String duration;
  final String trainerName;
  final String? trainerAvatarUrl;
  final VoidCallback onTap;
  final String? backgroundImageUrl;
  final bool showDetails;

  const WorkoutCard({
    Key? key,
    required this.title,
    required this.difficulty,
    required this.duration,
    required this.trainerName,
    required this.trainerAvatarUrl,
    required this.onTap,
    this.backgroundImageUrl,
    this.showDetails = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: NewAppTheme.spacing),
      height: 120,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(NewAppTheme.borderRadius),
        color: backgroundImageUrl == null ? NewAppTheme.accentColor : null,
        boxShadow: [NewAppTheme.cardShadow],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(NewAppTheme.borderRadius),
          child: Stack(
            children: [
              // Background image or placeholder
              if (backgroundImageUrl != null)
                _buildBackgroundWithImage()
              else
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(NewAppTheme.borderRadius),
                    color: NewAppTheme.accentColor,
                  ),
                ),

              // Gradient overlay
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(NewAppTheme.borderRadius),
                  gradient: LinearGradient(
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: [
                      Colors.black.withOpacity(0.7),
                      Colors.black.withOpacity(0.3),
                    ],
                  ),
                ),
              ),

              // Content
              Padding(
                padding: const EdgeInsets.all(NewAppTheme.spacing),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      title,
                      style: NewAppTheme.subtitleLarge.copyWith(
                        color: Colors.white,
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            _buildInfoChip(difficulty),
                            const SizedBox(width: 8),
                            _buildInfoChip(duration),
                          ],
                        ),
                        if (showDetails)
                          Row(
                            children: [
                              _buildTrainerAvatar(),
                              const SizedBox(width: 8),
                              Text(
                                'Trainer: $trainerName',
                                style: NewAppTheme.bodySmall.copyWith(
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Container(
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.white,
                                ),
                                padding: const EdgeInsets.all(4),
                                child: const Icon(
                                  Icons.arrow_forward,
                                  size: 16,
                                  color: NewAppTheme.primaryColor,
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBackgroundWithImage() {
    try {
      return ClipRRect(
        borderRadius: BorderRadius.circular(NewAppTheme.borderRadius),
        child: Image.asset(
          backgroundImageUrl!,
          fit: BoxFit.cover,
          width: double.infinity,
          height: double.infinity,
          errorBuilder: (context, error, stackTrace) {
            return ImagePlaceholderHelper.createBackgroundPlaceholder(
              width: double.infinity,
              height: double.infinity,
              title: title,
              backgroundColor: NewAppTheme.accentColor,
            );
          },
        ),
      );
    } catch (e) {
      return ImagePlaceholderHelper.createBackgroundPlaceholder(
        width: double.infinity,
        height: double.infinity,
        title: title,
        backgroundColor: NewAppTheme.accentColor,
      );
    }
  }

  Widget _buildTrainerAvatar() {
    // Se non c'è un URL per l'avatar, mostriamo le iniziali
    if (trainerAvatarUrl == null) {
      return Container(
        width: 30,
        height: 30,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white,
          border: Border.all(color: Colors.white, width: 2),
        ),
        child: Center(
          child: Text(
            trainerName.isNotEmpty ? trainerName[0] : 'T',
            style: NewAppTheme.bodySmall.copyWith(
              color: NewAppTheme.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      );
    }

    // Altrimenti proviamo a caricare l'immagine
    try {
      return Container(
        width: 30,
        height: 30,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white, width: 2),
          image: DecorationImage(
            image: AssetImage(trainerAvatarUrl!),
            fit: BoxFit.cover,
          ),
        ),
      );
    } catch (e) {
      // In caso di errore, mostriamo le iniziali
      return Container(
        width: 30,
        height: 30,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white,
          border: Border.all(color: Colors.white, width: 2),
        ),
        child: Center(
          child: Text(
            trainerName.isNotEmpty ? trainerName[0] : 'T',
            style: NewAppTheme.bodySmall.copyWith(
              color: NewAppTheme.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      );
    }
  }

  Widget _buildInfoChip(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: NewAppTheme.spacing / 2,
        vertical: NewAppTheme.smallSpacing / 2,
      ),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(NewAppTheme.smallBorderRadius),
      ),
      child: Text(
        text,
        style: NewAppTheme.bodySmall.copyWith(
          color: Colors.white,
        ),
      ),
    );
  }
}
