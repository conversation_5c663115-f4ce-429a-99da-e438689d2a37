import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../theme/dr_staffilano_theme.dart';

/// Modello per una location
class LocationData {
  final String id;
  final String name;
  final String? address;
  final double? latitude;
  final double? longitude;
  final String? type;

  LocationData({
    required this.id,
    required this.name,
    this.address,
    this.latitude,
    this.longitude,
    this.type,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'type': type,
    };
  }

  factory LocationData.fromMap(Map<String, dynamic> map) {
    return LocationData(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      address: map['address'],
      latitude: map['latitude']?.toDouble(),
      longitude: map['longitude']?.toDouble(),
      type: map['type'],
    );
  }
}

/// Modal per la selezione della posizione
class LocationPickerModal extends StatefulWidget {
  final LocationData? currentLocation;
  final Function(LocationData?) onLocationSelected;

  const LocationPickerModal({
    Key? key,
    this.currentLocation,
    required this.onLocationSelected,
  }) : super(key: key);

  static Future<void> show(
    BuildContext context, {
    LocationData? currentLocation,
    required Function(LocationData?) onLocationSelected,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: LocationPickerModal(
          currentLocation: currentLocation,
          onLocationSelected: onLocationSelected,
        ),
      ),
    );
  }

  @override
  State<LocationPickerModal> createState() => _LocationPickerModalState();
}

class _LocationPickerModalState extends State<LocationPickerModal> {
  final TextEditingController _searchController = TextEditingController();
  List<LocationData> _searchResults = [];
  List<LocationData> _recentLocations = [];
  bool _isSearching = false;

  // Locations predefinite per l'Italia
  final List<LocationData> _popularLocations = [
    LocationData(id: '1', name: 'Roma', address: 'Roma, Italia', type: 'city'),
    LocationData(id: '2', name: 'Milano', address: 'Milano, Italia', type: 'city'),
    LocationData(id: '3', name: 'Napoli', address: 'Napoli, Italia', type: 'city'),
    LocationData(id: '4', name: 'Torino', address: 'Torino, Italia', type: 'city'),
    LocationData(id: '5', name: 'Firenze', address: 'Firenze, Italia', type: 'city'),
    LocationData(id: '6', name: 'Bologna', address: 'Bologna, Italia', type: 'city'),
    LocationData(id: '7', name: 'Venezia', address: 'Venezia, Italia', type: 'city'),
    LocationData(id: '8', name: 'Palermo', address: 'Palermo, Italia', type: 'city'),
  ];

  @override
  void initState() {
    super.initState();
    _loadRecentLocations();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadRecentLocations() {
    // TODO: Load from SharedPreferences
    _recentLocations = [
      LocationData(id: 'recent1', name: 'Casa', address: 'Via Roma 123, Milano', type: 'home'),
      LocationData(id: 'recent2', name: 'Ufficio', address: 'Via Dante 45, Milano', type: 'work'),
    ];
  }

  void _onSearchChanged() {
    final query = _searchController.text.trim();
    if (query.isEmpty) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    // Simulated search - in real app, use Google Places API or similar
    _performSearch(query);
  }

  void _performSearch(String query) {
    // Simulate API delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (!mounted) return;

      final results = _popularLocations
          .where((location) =>
              location.name.toLowerCase().contains(query.toLowerCase()) ||
              (location.address?.toLowerCase().contains(query.toLowerCase()) ?? false))
          .toList();

      setState(() {
        _searchResults = results;
        _isSearching = false;
      });
    });
  }

  void _selectLocation(LocationData location) {
    HapticFeedback.lightImpact();

    // Add to recent locations
    _recentLocations.removeWhere((loc) => loc.id == location.id);
    _recentLocations.insert(0, location);
    if (_recentLocations.length > 5) {
      _recentLocations = _recentLocations.take(5).toList();
    }
    // TODO: Save to SharedPreferences

    widget.onLocationSelected(location);
    Navigator.of(context).pop();
  }

  void _removeLocation() {
    HapticFeedback.lightImpact();
    widget.onLocationSelected(null);
    Navigator.of(context).pop();
  }

  void _useCurrentLocation() {
    HapticFeedback.lightImpact();
    // TODO: Implement geolocation
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Geolocalizzazione non ancora implementata'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final modalWidth = screenSize.width > 600 ? 500.0 : screenSize.width * 0.85;
    final modalHeight = screenSize.height * 0.7;

    return Container(
      width: modalWidth,
      height: modalHeight,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(),
          _buildSearchBar(),
          _buildQuickActions(),
          Expanded(child: _buildLocationsList()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.withOpacity(0.2)),
        ),
      ),
      child: Row(
        children: [
          Icon(
            FontAwesomeIcons.locationDot,
            color: DrStaffilanoTheme.professionalBlue,
            size: 20,
          ),
          const SizedBox(width: 12),
          const Text(
            'Aggiungi posizione',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          if (widget.currentLocation != null)
            TextButton(
              onPressed: _removeLocation,
              child: const Text('Rimuovi'),
            ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close),
            style: IconButton.styleFrom(
              backgroundColor: Colors.grey.withOpacity(0.1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Cerca una posizione...',
          prefixIcon: _isSearching
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: Padding(
                    padding: EdgeInsets.all(12),
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                )
              : const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.withOpacity(0.3)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: DrStaffilanoTheme.professionalBlue),
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _useCurrentLocation,
              icon: const Icon(Icons.my_location),
              label: const Text('Posizione attuale'),
              style: OutlinedButton.styleFrom(
                foregroundColor: DrStaffilanoTheme.professionalBlue,
                side: BorderSide(color: DrStaffilanoTheme.professionalBlue),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationsList() {
    final hasSearchQuery = _searchController.text.trim().isNotEmpty;
    final locations = hasSearchQuery ? _searchResults : _recentLocations;
    final title = hasSearchQuery ? 'Risultati ricerca' : 'Posizioni recenti';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (locations.isNotEmpty) ...[
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: locations.length,
              itemBuilder: (context, index) {
                final location = locations[index];
                return _buildLocationTile(location);
              },
            ),
          ),
        ] else if (!hasSearchQuery) ...[
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'Città popolari',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: _popularLocations.length,
              itemBuilder: (context, index) {
                final location = _popularLocations[index];
                return _buildLocationTile(location);
              },
            ),
          ),
        ] else ...[
          const Expanded(
            child: Center(
              child: Text(
                'Nessun risultato trovato',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 16,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildLocationTile(LocationData location) {
    IconData icon;
    Color iconColor;

    switch (location.type) {
      case 'home':
        icon = Icons.home;
        iconColor = DrStaffilanoTheme.primaryGreen;
        break;
      case 'work':
        icon = Icons.work;
        iconColor = DrStaffilanoTheme.professionalBlue;
        break;
      case 'city':
        icon = Icons.location_city;
        iconColor = DrStaffilanoTheme.accentGold;
        break;
      default:
        icon = Icons.place;
        iconColor = Colors.grey;
    }

    return ListTile(
      leading: Icon(icon, color: iconColor),
      title: Text(
        location.name,
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: location.address != null ? Text(location.address!) : null,
      onTap: () => _selectLocation(location),
    );
  }
}
