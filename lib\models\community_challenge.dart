import 'package:uuid/uuid.dart';

/// Tipi di sfide della community
enum ChallengeType {
  daily('daily', 'Giornaliera', '📅', 1),
  weekly('weekly', 'Settimanale', '📆', 7),
  monthly('monthly', 'Mensile', '🗓️', 30),
  special('special', 'Speciale', '⭐', 0);

  const ChallengeType(this.id, this.displayName, this.emoji, this.durationDays);
  
  final String id;
  final String displayName;
  final String emoji;
  final int durationDays;
}

/// Categorie delle sfide
enum ChallengeCategory {
  nutrition('nutrition', 'Nutrizione', '🥗', '#10B981'),
  exercise('exercise', 'Esercizio', '💪', '#3B82F6'),
  hydration('hydration', 'Idratazione', '💧', '#06B6D4'),
  sleep('sleep', 'Sonno', '😴', '#8B5CF6'),
  mindfulness('mindfulness', 'Mindfulness', '🧘', '#EC4899'),
  social('social', 'Sociale', '👥', '#F59E0B'),
  learning('learning', 'Apprendimento', '📚', '#6366F1'),
  habit('habit', 'Abitudini', '🔄', '#84CC16');

  const ChallengeCategory(this.id, this.displayName, this.emoji, this.colorHex);
  
  final String id;
  final String displayName;
  final String emoji;
  final String colorHex;
}

/// Difficoltà delle sfide
enum ChallengeDifficulty {
  easy('easy', 'Facile', '🟢', 10),
  medium('medium', 'Medio', '🟡', 25),
  hard('hard', 'Difficile', '🔴', 50),
  expert('expert', 'Esperto', '🟣', 100);

  const ChallengeDifficulty(this.id, this.displayName, this.emoji, this.points);
  
  final String id;
  final String displayName;
  final String emoji;
  final int points;
}

/// Modello per le sfide della community
class CommunityChallenge {
  final String id;
  final String title;
  final String description;
  final String? imageUrl;
  final ChallengeType type;
  final ChallengeCategory category;
  final ChallengeDifficulty difficulty;
  final DateTime startDate;
  final DateTime endDate;
  final int maxParticipants;
  final int currentParticipants;
  final int completedCount;
  final Map<String, dynamic> requirements;
  final Map<String, dynamic> rewards;
  final List<String> tags;
  final bool isActive;
  final bool isFeatured;
  final String? groupId;
  final String creatorId;
  final Map<String, dynamic> metadata;

  CommunityChallenge({
    required this.id,
    required this.title,
    required this.description,
    this.imageUrl,
    required this.type,
    required this.category,
    required this.difficulty,
    required this.startDate,
    required this.endDate,
    this.maxParticipants = 0, // 0 = illimitato
    this.currentParticipants = 0,
    this.completedCount = 0,
    this.requirements = const {},
    this.rewards = const {},
    this.tags = const [],
    this.isActive = true,
    this.isFeatured = false,
    this.groupId,
    required this.creatorId,
    this.metadata = const {},
  });

  /// Crea una nuova sfida
  factory CommunityChallenge.create({
    required String title,
    required String description,
    String? imageUrl,
    required ChallengeType type,
    required ChallengeCategory category,
    required ChallengeDifficulty difficulty,
    required DateTime startDate,
    required DateTime endDate,
    int maxParticipants = 0,
    Map<String, dynamic> requirements = const {},
    Map<String, dynamic> rewards = const {},
    List<String> tags = const [],
    String? groupId,
    required String creatorId,
    Map<String, dynamic> metadata = const {},
  }) {
    return CommunityChallenge(
      id: const Uuid().v4(),
      title: title,
      description: description,
      imageUrl: imageUrl,
      type: type,
      category: category,
      difficulty: difficulty,
      startDate: startDate,
      endDate: endDate,
      maxParticipants: maxParticipants,
      requirements: requirements,
      rewards: rewards,
      tags: tags,
      groupId: groupId,
      creatorId: creatorId,
      metadata: metadata,
    );
  }

  /// Copia con modifiche
  CommunityChallenge copyWith({
    String? title,
    String? description,
    String? imageUrl,
    ChallengeType? type,
    ChallengeCategory? category,
    ChallengeDifficulty? difficulty,
    DateTime? startDate,
    DateTime? endDate,
    int? maxParticipants,
    int? currentParticipants,
    int? completedCount,
    Map<String, dynamic>? requirements,
    Map<String, dynamic>? rewards,
    List<String>? tags,
    bool? isActive,
    bool? isFeatured,
    String? groupId,
    Map<String, dynamic>? metadata,
  }) {
    return CommunityChallenge(
      id: id,
      title: title ?? this.title,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      type: type ?? this.type,
      category: category ?? this.category,
      difficulty: difficulty ?? this.difficulty,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      maxParticipants: maxParticipants ?? this.maxParticipants,
      currentParticipants: currentParticipants ?? this.currentParticipants,
      completedCount: completedCount ?? this.completedCount,
      requirements: requirements ?? this.requirements,
      rewards: rewards ?? this.rewards,
      tags: tags ?? this.tags,
      isActive: isActive ?? this.isActive,
      isFeatured: isFeatured ?? this.isFeatured,
      groupId: groupId ?? this.groupId,
      creatorId: creatorId,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Converti in Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'imageUrl': imageUrl,
      'type': type.id,
      'category': category.id,
      'difficulty': difficulty.id,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'maxParticipants': maxParticipants,
      'currentParticipants': currentParticipants,
      'completedCount': completedCount,
      'requirements': requirements,
      'rewards': rewards,
      'tags': tags,
      'isActive': isActive,
      'isFeatured': isFeatured,
      'groupId': groupId,
      'creatorId': creatorId,
      'metadata': metadata,
    };
  }

  /// Crea da Map
  factory CommunityChallenge.fromMap(Map<String, dynamic> map) {
    return CommunityChallenge(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      imageUrl: map['imageUrl'],
      type: ChallengeType.values.firstWhere(
        (type) => type.id == map['type'],
        orElse: () => ChallengeType.daily,
      ),
      category: ChallengeCategory.values.firstWhere(
        (category) => category.id == map['category'],
        orElse: () => ChallengeCategory.nutrition,
      ),
      difficulty: ChallengeDifficulty.values.firstWhere(
        (difficulty) => difficulty.id == map['difficulty'],
        orElse: () => ChallengeDifficulty.easy,
      ),
      startDate: DateTime.parse(map['startDate'] ?? DateTime.now().toIso8601String()),
      endDate: DateTime.parse(map['endDate'] ?? DateTime.now().add(const Duration(days: 1)).toIso8601String()),
      maxParticipants: map['maxParticipants']?.toInt() ?? 0,
      currentParticipants: map['currentParticipants']?.toInt() ?? 0,
      completedCount: map['completedCount']?.toInt() ?? 0,
      requirements: Map<String, dynamic>.from(map['requirements'] ?? {}),
      rewards: Map<String, dynamic>.from(map['rewards'] ?? {}),
      tags: List<String>.from(map['tags'] ?? []),
      isActive: map['isActive'] ?? true,
      isFeatured: map['isFeatured'] ?? false,
      groupId: map['groupId'],
      creatorId: map['creatorId'] ?? '',
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
    );
  }

  /// Verifica se la sfida è in corso
  bool get isOngoing {
    final now = DateTime.now();
    return now.isAfter(startDate) && now.isBefore(endDate) && isActive;
  }

  /// Verifica se la sfida è completata
  bool get isCompleted {
    return DateTime.now().isAfter(endDate);
  }

  /// Verifica se la sfida è futura
  bool get isUpcoming {
    return DateTime.now().isBefore(startDate);
  }

  /// Verifica se la sfida ha posti disponibili
  bool get hasAvailableSlots {
    return maxParticipants == 0 || currentParticipants < maxParticipants;
  }

  /// Ottieni la durata della sfida in giorni
  int get durationDays {
    return endDate.difference(startDate).inDays;
  }

  /// Ottieni il tasso di completamento
  double get completionRate {
    if (currentParticipants == 0) return 0.0;
    return completedCount / currentParticipants;
  }

  /// Ottieni i giorni rimanenti
  int get daysRemaining {
    if (isCompleted) return 0;
    return endDate.difference(DateTime.now()).inDays;
  }

  /// Ottieni il colore della categoria
  String get colorHex => category.colorHex;

  /// Ottieni l'emoji della categoria
  String get emoji => category.emoji;

  /// Ottieni il display name completo
  String get fullDisplayName => '${category.emoji} $title';

  @override
  String toString() {
    return 'CommunityChallenge(id: $id, title: $title, type: ${type.displayName}, participants: $currentParticipants)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CommunityChallenge && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
