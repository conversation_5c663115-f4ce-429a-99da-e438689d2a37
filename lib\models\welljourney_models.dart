import 'package:flutter/material.dart';

/// Modelli per il sistema WellJourney™ Dr. Staffilano

/// Percorso guidato tematico
class GuidedPathway {
  final String id;
  final String title;
  final String description;
  final String drStaffilanoInsight;
  final PathwayCategory category;
  final int estimatedDays;
  final List<PathwayModule> modules;
  final String imageUrl;
  final Color primaryColor;
  final List<String> benefits;
  final DifficultyLevel difficulty;
  final bool isPremium;

  const GuidedPathway({
    required this.id,
    required this.title,
    required this.description,
    required this.drStaffilanoInsight,
    required this.category,
    required this.estimatedDays,
    required this.modules,
    required this.imageUrl,
    required this.primaryColor,
    required this.benefits,
    required this.difficulty,
    this.isPremium = false,
  });

  double getCompletionPercentage(List<String> completedModuleIds) {
    if (modules.isEmpty) return 0.0;
    final completed = modules.where((m) => completedModuleIds.contains(m.id)).length;
    return completed / modules.length;
  }
}

/// Modulo di un percorso
class PathwayModule {
  final String id;
  final String title;
  final String description;
  final String content;
  final List<String> keyPoints;
  final List<PracticalTip> practicalTips;
  final List<ActionableTask> tasks;
  final int estimatedMinutes;
  final String drStaffilanoQuote;
  final List<String> resources;
  const PathwayModule({
    required this.id,
    required this.title,
    required this.description,
    required this.content,
    required this.keyPoints,
    required this.practicalTips,
    required this.tasks,
    required this.estimatedMinutes,
    required this.drStaffilanoQuote,
    required this.resources,
  });
}

/// Suggerimento pratico
class PracticalTip {
  final String title;
  final String description;
  final String? drStaffilanoNote;
  final IconData icon;
  final Color color;

  const PracticalTip({
    required this.title,
    required this.description,
    this.drStaffilanoNote,
    this.icon = Icons.lightbulb,
    this.color = Colors.blue,
  });
}

/// Compito azionabile
class ActionableTask {
  final String id;
  final String title;
  final String description;
  final TaskType type;
  final int estimatedMinutes;
  final int points;
  final bool isCompleted;

  const ActionableTask({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    this.estimatedMinutes = 10,
    required this.points,
    this.isCompleted = false,
  });
}

/// Badge di achievement
class AchievementBadge {
  final String id;
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final BadgeCategory category;
  final int pointsRequired;
  final DateTime? earnedDate;
  final bool isEarned;
  final String celebrationMessage;

  const AchievementBadge({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.category,
    required this.pointsRequired,
    this.earnedDate,
    this.isEarned = false,
    required this.celebrationMessage,
  });
}

/// NutriScore personalizzato
class PersonalNutriScore {
  final DateTime date;
  final double score; // 0-100
  final Map<String, double> categoryScores;
  final List<String> strengths;
  final List<String> improvements;
  final String drStaffilanoFeedback;
  final NutriScoreGrade grade;

  const PersonalNutriScore({
    required this.date,
    required this.score,
    required this.categoryScores,
    required this.strengths,
    required this.improvements,
    required this.drStaffilanoFeedback,
    required this.grade,
  });
}

/// Sfida personale o di gruppo
class WellJourneyChallenge {
  final String id;
  final String title;
  final String description;
  final ChallengeType type;
  final int durationDays;
  final int targetValue;
  final String unit;
  final int points;
  final DateTime startDate;
  final DateTime endDate;
  final List<String> participants;
  final Map<String, int> leaderboard;
  final bool isActive;
  final String drStaffilanoMotivation;

  const WellJourneyChallenge({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.durationDays,
    required this.targetValue,
    required this.unit,
    required this.points,
    required this.startDate,
    required this.endDate,
    required this.participants,
    required this.leaderboard,
    this.isActive = true,
    required this.drStaffilanoMotivation,
  });
}

/// Progresso utente nel WellJourney
class WellJourneyProgress {
  final String userId;
  final int totalPoints;
  final int currentStreak;
  final int longestStreak;
  final List<String> completedPathways;
  final List<String> completedModules;
  final List<String> earnedBadges;
  final List<String> activePathways;
  final List<String> activeChallenges;
  final Map<DateTime, double> nutriScoreHistory;
  final DateTime lastActivity;
  final int level;

  const WellJourneyProgress({
    required this.userId,
    required this.totalPoints,
    required this.currentStreak,
    required this.longestStreak,
    required this.completedPathways,
    required this.completedModules,
    required this.earnedBadges,
    required this.activePathways,
    required this.activeChallenges,
    required this.nutriScoreHistory,
    required this.lastActivity,
    required this.level,
  });

  int getPointsForNextLevel() {
    return (level + 1) * 1000; // 1000 punti per livello
  }

  double getLevelProgress() {
    final currentLevelPoints = level * 1000;
    final nextLevelPoints = getPointsForNextLevel();
    final progressPoints = totalPoints - currentLevelPoints;
    return progressPoints / (nextLevelPoints - currentLevelPoints);
  }
}

/// Enumerazioni

enum PathwayCategory {
  heartHealth,
  mediterraneanDiet,
  weightManagement,
  sportsNutrition,
  preventiveCare,
  lifestyle,
}

enum DifficultyLevel {
  beginner,
  intermediate,
  advanced,
}

enum TaskType {
  educational,
  practical,
  tracking,
  social,
}

enum BadgeCategory {
  consistency,
  achievement,
  learning,
  social,
  special,
}

enum NutriScoreGrade {
  excellent, // 90-100
  veryGood,  // 80-89
  good,      // 70-79
  fair,      // 60-69
  needsWork, // <60
}

enum ChallengeType {
  individual,
  group,
  community,
}

/// Nuove enumerazioni per sfide avanzate

enum ChallengeCategory {
  hydration,
  nutrition,
  lifestyle,
  heartHealth,
  seasonal,
  weightManagement,
  mindfulness,
  daily,
  weekly,
  monthly,
  personal,
}

enum ChallengeDifficulty {
  easy,
  medium,
  hard,
  expert,
}

enum ChallengeTrackingMethod {
  manual,
  foodOracle,
  mealPlan,
  aiAnalysis,
  automatic,
}

enum ChallengeIntegration {
  nutriscore,
  foodOracle,
  mealPlan,
  welljourney,
  badges,
}

enum RewardType {
  points,
  badge,
  nutriScoreBoost,
  unlockFeature,
  discount,
}

/// Modelli avanzati per le sfide

/// Sfida avanzata con tracking intelligente
class AdvancedChallenge {
  final String id;
  final String title;
  final String description;
  final ChallengeCategory category;
  final ChallengeDifficulty difficulty;
  final ChallengeType type;
  final int durationDays;
  final int targetValue;
  final String unit;
  final int points;
  final DateTime startDate;
  final DateTime endDate;
  final String drStaffilanoMotivation;
  final ChallengeTrackingMethod trackingMethod;
  final List<ChallengeIntegration> integrationFeatures;
  final List<ChallengeReward> rewards;
  final Map<String, dynamic>? metadata;

  const AdvancedChallenge({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.difficulty,
    required this.type,
    required this.durationDays,
    required this.targetValue,
    required this.unit,
    required this.points,
    required this.startDate,
    required this.endDate,
    required this.drStaffilanoMotivation,
    required this.trackingMethod,
    required this.integrationFeatures,
    required this.rewards,
    this.metadata,
  });

  bool get isActive => DateTime.now().isBefore(endDate);
  bool get isExpired => DateTime.now().isAfter(endDate);
  int get daysRemaining => endDate.difference(DateTime.now()).inDays;
  double get difficultyMultiplier {
    switch (difficulty) {
      case ChallengeDifficulty.easy:
        return 1.0;
      case ChallengeDifficulty.medium:
        return 1.5;
      case ChallengeDifficulty.hard:
        return 2.0;
      case ChallengeDifficulty.expert:
        return 3.0;
    }
  }
}

/// Ricompensa per completamento sfida
class ChallengeReward {
  final RewardType type;
  final int value;
  final String description;
  final Map<String, dynamic>? metadata;

  const ChallengeReward({
    required this.type,
    required this.value,
    required this.description,
    this.metadata,
  });
}

/// Progresso dettagliato di una sfida
class ChallengeProgress {
  final String challengeId;
  final double currentProgress; // 0.0 - 1.0
  final DateTime lastUpdated;
  final Map<String, double> dailyProgress; // data -> progresso giornaliero
  final List<ChallengeMilestone> milestones;
  final Map<String, dynamic>? analytics;

  const ChallengeProgress({
    required this.challengeId,
    required this.currentProgress,
    required this.lastUpdated,
    required this.dailyProgress,
    required this.milestones,
    this.analytics,
  });

  ChallengeProgress copyWith({
    double? currentProgress,
    DateTime? lastUpdated,
    Map<String, double>? dailyProgress,
    List<ChallengeMilestone>? milestones,
    Map<String, dynamic>? analytics,
  }) {
    return ChallengeProgress(
      challengeId: challengeId,
      currentProgress: currentProgress ?? this.currentProgress,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      dailyProgress: dailyProgress ?? this.dailyProgress,
      milestones: milestones ?? this.milestones,
      analytics: analytics ?? this.analytics,
    );
  }

  bool get isCompleted => currentProgress >= 1.0;
  int get progressPercentage => (currentProgress * 100).round();
}

/// Milestone di una sfida
class ChallengeMilestone {
  final String id;
  final String title;
  final String description;
  final double threshold; // 0.0 - 1.0
  final bool isCompleted;
  final DateTime? completedAt;
  final List<ChallengeReward> rewards;

  const ChallengeMilestone({
    required this.id,
    required this.title,
    required this.description,
    required this.threshold,
    required this.isCompleted,
    this.completedAt,
    required this.rewards,
  });
}

/// Storico delle sfide completate
class ChallengeHistory {
  final String challengeId;
  final DateTime completedAt;
  final double finalProgress;
  final int pointsEarned;
  final List<ChallengeReward> rewardsEarned;
  final Duration completionTime;
  final Map<String, dynamic>? analytics;

  const ChallengeHistory({
    required this.challengeId,
    required this.completedAt,
    required this.finalProgress,
    required this.pointsEarned,
    required this.rewardsEarned,
    Duration? completionTime,
    this.analytics,
  }) : completionTime = completionTime ?? const Duration();
}

/// Estensioni per le enumerazioni

extension PathwayCategoryExtension on PathwayCategory {
  String get displayName {
    switch (this) {
      case PathwayCategory.heartHealth:
        return 'Salute del Cuore';
      case PathwayCategory.mediterraneanDiet:
        return 'Dieta Mediterranea';
      case PathwayCategory.weightManagement:
        return 'Gestione del Peso';
      case PathwayCategory.sportsNutrition:
        return 'Nutrizione Sportiva';
      case PathwayCategory.preventiveCare:
        return 'Prevenzione';
      case PathwayCategory.lifestyle:
        return 'Stile di Vita';
    }
  }

  IconData get icon {
    switch (this) {
      case PathwayCategory.heartHealth:
        return Icons.favorite;
      case PathwayCategory.mediterraneanDiet:
        return Icons.restaurant;
      case PathwayCategory.weightManagement:
        return Icons.fitness_center;
      case PathwayCategory.sportsNutrition:
        return Icons.sports;
      case PathwayCategory.preventiveCare:
        return Icons.health_and_safety;
      case PathwayCategory.lifestyle:
        return Icons.self_improvement;
    }
  }
}

extension DifficultyLevelExtension on DifficultyLevel {
  String get displayName {
    switch (this) {
      case DifficultyLevel.beginner:
        return 'Principiante';
      case DifficultyLevel.intermediate:
        return 'Intermedio';
      case DifficultyLevel.advanced:
        return 'Avanzato';
    }
  }

  Color get color {
    switch (this) {
      case DifficultyLevel.beginner:
        return Colors.green;
      case DifficultyLevel.intermediate:
        return Colors.orange;
      case DifficultyLevel.advanced:
        return Colors.red;
    }
  }
}

extension NutriScoreGradeExtension on NutriScoreGrade {
  String get displayName {
    switch (this) {
      case NutriScoreGrade.excellent:
        return 'Eccellente';
      case NutriScoreGrade.veryGood:
        return 'Molto Buono';
      case NutriScoreGrade.good:
        return 'Buono';
      case NutriScoreGrade.fair:
        return 'Discreto';
      case NutriScoreGrade.needsWork:
        return 'Da Migliorare';
    }
  }

  Color get color {
    switch (this) {
      case NutriScoreGrade.excellent:
        return Colors.green;
      case NutriScoreGrade.veryGood:
        return Colors.lightGreen;
      case NutriScoreGrade.good:
        return Colors.yellow;
      case NutriScoreGrade.fair:
        return Colors.orange;
      case NutriScoreGrade.needsWork:
        return Colors.red;
    }
  }
}

/// Estensioni per le nuove enumerazioni delle sfide

extension ChallengeCategoryExtension on ChallengeCategory {
  String get displayName {
    switch (this) {
      case ChallengeCategory.hydration:
        return 'Idratazione';
      case ChallengeCategory.nutrition:
        return 'Nutrizione';
      case ChallengeCategory.lifestyle:
        return 'Stile di Vita';
      case ChallengeCategory.heartHealth:
        return 'Salute del Cuore';
      case ChallengeCategory.seasonal:
        return 'Stagionale';
      case ChallengeCategory.weightManagement:
        return 'Gestione Peso';
      case ChallengeCategory.mindfulness:
        return 'Mindfulness';
      case ChallengeCategory.daily:
        return 'Giornaliere';
      case ChallengeCategory.weekly:
        return 'Settimanali';
      case ChallengeCategory.monthly:
        return 'Mensili';
      case ChallengeCategory.personal:
        return 'Personali';
    }
  }

  IconData get icon {
    switch (this) {
      case ChallengeCategory.hydration:
        return Icons.water_drop;
      case ChallengeCategory.nutrition:
        return Icons.restaurant_menu;
      case ChallengeCategory.lifestyle:
        return Icons.self_improvement;
      case ChallengeCategory.heartHealth:
        return Icons.favorite;
      case ChallengeCategory.seasonal:
        return Icons.eco;
      case ChallengeCategory.weightManagement:
        return Icons.fitness_center;
      case ChallengeCategory.mindfulness:
        return Icons.psychology;
      case ChallengeCategory.daily:
        return Icons.today;
      case ChallengeCategory.weekly:
        return Icons.date_range;
      case ChallengeCategory.monthly:
        return Icons.calendar_month;
      case ChallengeCategory.personal:
        return Icons.person;
    }
  }

  Color get primaryColor {
    switch (this) {
      case ChallengeCategory.hydration:
        return Colors.blue;
      case ChallengeCategory.nutrition:
        return Colors.green;
      case ChallengeCategory.lifestyle:
        return Colors.purple;
      case ChallengeCategory.heartHealth:
        return Colors.red;
      case ChallengeCategory.seasonal:
        return Colors.orange;
      case ChallengeCategory.weightManagement:
        return Colors.indigo;
      case ChallengeCategory.mindfulness:
        return Colors.teal;
      case ChallengeCategory.daily:
        return Colors.amber;
      case ChallengeCategory.weekly:
        return Colors.deepPurple;
      case ChallengeCategory.monthly:
        return Colors.brown;
      case ChallengeCategory.personal:
        return Colors.pink;
    }
  }
}

extension ChallengeDifficultyExtension on ChallengeDifficulty {
  String get displayName {
    switch (this) {
      case ChallengeDifficulty.easy:
        return 'Facile';
      case ChallengeDifficulty.medium:
        return 'Medio';
      case ChallengeDifficulty.hard:
        return 'Difficile';
      case ChallengeDifficulty.expert:
        return 'Esperto';
    }
  }

  Color get color {
    switch (this) {
      case ChallengeDifficulty.easy:
        return Colors.green;
      case ChallengeDifficulty.medium:
        return Colors.orange;
      case ChallengeDifficulty.hard:
        return Colors.red;
      case ChallengeDifficulty.expert:
        return Colors.purple;
    }
  }

  IconData get icon {
    switch (this) {
      case ChallengeDifficulty.easy:
        return Icons.sentiment_satisfied;
      case ChallengeDifficulty.medium:
        return Icons.sentiment_neutral;
      case ChallengeDifficulty.hard:
        return Icons.sentiment_dissatisfied;
      case ChallengeDifficulty.expert:
        return Icons.psychology;
    }
  }
}

extension ChallengeTrackingMethodExtension on ChallengeTrackingMethod {
  String get displayName {
    switch (this) {
      case ChallengeTrackingMethod.manual:
        return 'Manuale';
      case ChallengeTrackingMethod.foodOracle:
        return 'Food Oracle';
      case ChallengeTrackingMethod.mealPlan:
        return 'Piano Alimentare';
      case ChallengeTrackingMethod.aiAnalysis:
        return 'Analisi AI';
      case ChallengeTrackingMethod.automatic:
        return 'Automatico';
    }
  }

  IconData get icon {
    switch (this) {
      case ChallengeTrackingMethod.manual:
        return Icons.edit;
      case ChallengeTrackingMethod.foodOracle:
        return Icons.camera_alt;
      case ChallengeTrackingMethod.mealPlan:
        return Icons.restaurant;
      case ChallengeTrackingMethod.aiAnalysis:
        return Icons.psychology;
      case ChallengeTrackingMethod.automatic:
        return Icons.auto_awesome;
    }
  }
}

extension RewardTypeExtension on RewardType {
  String get displayName {
    switch (this) {
      case RewardType.points:
        return 'Punti';
      case RewardType.badge:
        return 'Badge';
      case RewardType.nutriScoreBoost:
        return 'Boost NutriScore';
      case RewardType.unlockFeature:
        return 'Sblocca Funzione';
      case RewardType.discount:
        return 'Sconto';
    }
  }

  IconData get icon {
    switch (this) {
      case RewardType.points:
        return Icons.stars;
      case RewardType.badge:
        return Icons.emoji_events;
      case RewardType.nutriScoreBoost:
        return Icons.trending_up;
      case RewardType.unlockFeature:
        return Icons.lock_open;
      case RewardType.discount:
        return Icons.local_offer;
    }
  }
}
