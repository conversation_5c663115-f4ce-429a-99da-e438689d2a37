import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/welljourney_models.dart';
import '../services/welljourney_service.dart';
import '../services/nutriscore_service.dart';
import '../controllers/badge_controller.dart';

/// Controller per gestire lo stato del sistema WellJourney™
class WellJourneyController extends ChangeNotifier {
  static const String _progressKey = 'welljourney_progress';
  static const String _enrolledPathwaysKey = 'enrolled_pathways';
  static const String _completedModulesKey = 'completed_modules';
  static const String _earnedBadgesKey = 'earned_badges';
  static const String _activeChallengesKey = 'active_challenges';
  static const String _nutriScoreHistoryKey = 'nutriscore_history';

  WellJourneyProgress? _userProgress;
  List<String> _enrolledPathways = [];
  List<String> _completedModules = [];
  List<String> _earnedBadges = [];
  List<String> _activeChallenges = [];
  Map<String, PersonalNutriScore> _nutriScoreHistory = {};
  bool _isLoading = false;

  // Getters
  WellJourneyProgress? get userProgress => _userProgress;
  List<String> get enrolledPathways => _enrolledPathways;
  List<String> get completedModules => _completedModules;
  List<String> get earnedBadges => _earnedBadges;
  List<String> get activeChallenges => _activeChallenges;
  Map<String, PersonalNutriScore> get nutriScoreHistory => _nutriScoreHistory;
  bool get isLoading => _isLoading;

  /// Inizializza il controller caricando i dati salvati
  Future<void> initialize() async {
    if (_isLoading) return; // Evita inizializzazioni multiple

    _isLoading = true;
    notifyListeners();

    try {
      print('🔄 Inizializzazione WellJourneyController...');

      // Inizializza sempre con dati di default prima di caricare
      _userProgress = WellJourneyProgress(
        userId: 'user_001',
        totalPoints: 0,
        level: 1,
        currentStreak: 0,
        longestStreak: 0,
        completedPathways: [],
        completedModules: [],
        earnedBadges: [],
        activePathways: [],
        activeChallenges: [],
        nutriScoreHistory: {},
        lastActivity: DateTime.now(),
      );

      // Prova a caricare i dati salvati
      await _loadUserProgress();
      await _loadEnrolledPathways();
      await _loadCompletedModules();
      await _loadEarnedBadges();
      await _loadActiveChallenges();
      await _loadNutriScoreHistory();

      print('✅ WellJourneyController inizializzato con successo');
      print('📊 Punti totali: ${_userProgress?.totalPoints ?? 0}');
      print('🎯 Moduli completati: ${_userProgress?.completedModules.length ?? 0}');
      print('🏆 Badge guadagnati: ${_earnedBadges.length}');

    } catch (e) {
      print('❌ Errore durante l\'inizializzazione: $e');
      // Mantieni i dati di default già impostati
    }

    _isLoading = false;
    notifyListeners();
  }

  /// Iscrive l'utente a un percorso
  Future<void> enrollInPathway(String pathwayId) async {
    if (_enrolledPathways.contains(pathwayId)) return;

    _enrolledPathways.add(pathwayId);
    await _saveEnrolledPathways();

    // Aggiorna il progresso utente
    await _updateUserProgress(pointsToAdd: 100); // Bonus per iscrizione

    // Controlla se guadagna badge
    await _checkForNewBadges();

    notifyListeners();
  }

  /// Completa un modulo di un percorso
  Future<void> completeModule(String moduleId, String pathwayId) async {
    if (_completedModules.contains(moduleId)) return;

    _completedModules.add(moduleId);
    await _saveCompletedModules();

    // Aggiorna il progresso utente
    await _updateUserProgress(pointsToAdd: 50); // Punti per modulo completato

    // Controlla se il percorso è completato
    final pathway = WellJourneyService.instance.getAvailablePathways()
        .firstWhere((p) => p.id == pathwayId);

    final completedModulesInPathway = pathway.modules
        .where((m) => _completedModules.contains(m.id))
        .length;

    if (completedModulesInPathway == pathway.modules.length) {
      await _completePathway(pathwayId);
    }

    // Controlla per nuovi badge
    await _checkForNewBadges();

    notifyListeners();
  }

  /// Completa un percorso
  Future<void> _completePathway(String pathwayId) async {
    // Aggiunge punti bonus per completamento percorso
    await _updateUserProgress(pointsToAdd: 500);

    // Aggiorna streak
    await _updateStreak();
  }

  /// Partecipa a una sfida
  Future<void> joinChallenge(String challengeId) async {
    if (_activeChallenges.contains(challengeId)) return;

    _activeChallenges.add(challengeId);
    await _saveActiveChallenges();

    await _updateUserProgress(pointsToAdd: 25); // Bonus per partecipazione

    notifyListeners();
  }

  /// Completa una sfida
  Future<void> completeChallenge(String challengeId) async {
    if (!_activeChallenges.contains(challengeId)) return;

    _activeChallenges.remove(challengeId);
    await _saveActiveChallenges();

    // Trova la sfida per i punti
    final challenges = WellJourneyService.instance.getActiveChallenges();
    final challenge = challenges.firstWhere((c) => c.id == challengeId);

    await _updateUserProgress(pointsToAdd: challenge.points);
    await _checkForNewBadges();

    notifyListeners();
  }

  /// Completa un quiz e aggiorna il progresso con integrazione NutriScore
  Future<void> completeQuiz(String quizId, String moduleId, int score, int maxScore, {int bonusPoints = 0}) async {
    try {
      print('🎯 Completamento quiz: $quizId per modulo: $moduleId');
      print('📊 Punteggio: $score/$maxScore (${(score/maxScore*100).round()}%)');

      // Calcola i punti basati sul punteggio con sistema migliorato
      final percentage = (score / maxScore * 100).round();
      int basePoints = _calculateBaseQuizPoints(percentage);
      int totalPoints = basePoints + bonusPoints;

      print('💰 Punti base: $basePoints, Bonus: $bonusPoints, Totale: $totalPoints');

      // Aggiorna il progresso utente
      await _updateUserProgress(pointsToAdd: totalPoints);

      // Segna il quiz come completato se il punteggio è sufficiente
      if (percentage >= 60) {
        await _markQuizAsCompleted(quizId, moduleId);
      }

      // Aggiorna NutriScore con i punti WellJourney
      await _updateNutriScoreWithQuizPoints(totalPoints, percentage);

      // Controlla per nuovi badge
      await _checkForNewBadges();

      // Trigger animazioni e feedback visivo
      await _triggerQuizCompletionFeedback(totalPoints, percentage);

      print('✅ Quiz completato! Punti totali aggiunti: $totalPoints');
      print('📊 Punti totali utente: ${_userProgress?.totalPoints ?? 0}');

      // Debug dettagliato
      debugPrintState();

      notifyListeners();
    } catch (e) {
      print('❌ Errore nel completamento quiz: $e');
    }
  }

  /// Calcola i punti base per un quiz basati sulla percentuale
  int _calculateBaseQuizPoints(int percentage) {
    if (percentage >= 95) {
      return 150; // Punteggio perfetto/quasi perfetto
    } else if (percentage >= 85) {
      return 125; // Punteggio eccellente
    } else if (percentage >= 75) {
      return 100; // Punteggio molto buono
    } else if (percentage >= 65) {
      return 75;  // Punteggio buono
    } else if (percentage >= 50) {
      return 50;  // Punteggio sufficiente
    } else {
      return 25;  // Punteggio minimo per il tentativo
    }
  }

  /// Marca un quiz come completato
  Future<void> _markQuizAsCompleted(String quizId, String moduleId) async {
    if (_userProgress != null) {
      // Aggiorna la lista dei moduli completati se non già presente
      final updatedModules = List<String>.from(_userProgress!.completedModules);
      if (!updatedModules.contains(moduleId)) {
        updatedModules.add(moduleId);
      }

      // Aggiorna il progresso
      _userProgress = WellJourneyProgress(
        userId: _userProgress!.userId,
        totalPoints: _userProgress!.totalPoints,
        currentStreak: _userProgress!.currentStreak,
        longestStreak: _userProgress!.longestStreak,
        completedPathways: _userProgress!.completedPathways,
        completedModules: updatedModules,
        earnedBadges: _userProgress!.earnedBadges,
        activePathways: _userProgress!.activePathways,
        activeChallenges: _userProgress!.activeChallenges,
        nutriScoreHistory: _userProgress!.nutriScoreHistory,
        lastActivity: DateTime.now(),
        level: _userProgress!.level,
      );

      await _saveUserProgress();
    }
  }

  /// Aggiorna il NutriScore con i punti ottenuti dai quiz
  Future<void> _updateNutriScoreWithQuizPoints(int quizPoints, int percentage) async {
    try {
      // Calcola il contributo del quiz al NutriScore
      final nutriScoreContribution = _calculateNutriScoreContribution(quizPoints, percentage);

      // Aggiorna il punteggio educativo nel NutriScore
      final today = DateTime.now();
      final dateKey = '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';

      // Simula un aggiornamento del NutriScore giornaliero
      // In un'implementazione reale, questo dovrebbe integrarsi con il sistema di meal tracking
      final currentNutriScore = _nutriScoreHistory[dateKey];
      if (currentNutriScore != null) {
        // Aggiorna il punteggio esistente
        final updatedScore = PersonalNutriScore(
          date: currentNutriScore.date,
          score: (currentNutriScore.score + nutriScoreContribution).clamp(0, 100),
          categoryScores: {
            ...currentNutriScore.categoryScores,
            'education': (currentNutriScore.categoryScores['education'] ?? 0) + nutriScoreContribution,
          },
          strengths: currentNutriScore.strengths,
          improvements: currentNutriScore.improvements,
          drStaffilanoFeedback: currentNutriScore.drStaffilanoFeedback,
          grade: currentNutriScore.grade,
        );
        _nutriScoreHistory[dateKey] = updatedScore;
      } else {
        // Crea un nuovo punteggio per oggi
        _nutriScoreHistory[dateKey] = PersonalNutriScore(
          date: today,
          score: nutriScoreContribution,
          categoryScores: {
            'education': nutriScoreContribution,
            'vegetables': 0,
            'fruits': 0,
            'proteins': 0,
            'wholegrains': 0,
            'healthyfats': 0,
          },
          strengths: ['Educazione nutrizionale'],
          improvements: ['Continua con i quiz per migliorare la conoscenza'],
          drStaffilanoFeedback: 'Ottimo lavoro nel completare il quiz! La conoscenza è la base della prevenzione.',
          grade: NutriScoreGrade.good,
        );
      }

      await _saveNutriScoreHistory();
      print('📈 NutriScore aggiornato con contributo quiz: +$nutriScoreContribution punti');
    } catch (e) {
      print('❌ Errore nell\'aggiornamento NutriScore: $e');
    }
  }

  /// Calcola il contributo del quiz al NutriScore
  double _calculateNutriScoreContribution(int quizPoints, int percentage) {
    // Converte i punti quiz in contributo NutriScore (scala 0-10)
    final baseContribution = (quizPoints / 15).clamp(0, 10); // Max 10 punti per quiz eccellente

    // Bonus per punteggi molto alti
    final perfectionBonus = percentage >= 95 ? 2.0 : (percentage >= 85 ? 1.0 : 0.0);

    return (baseContribution + perfectionBonus).clamp(0, 12); // Max 12 punti totali
  }

  /// Trigger feedback visivo per completamento quiz
  Future<void> _triggerQuizCompletionFeedback(int points, int percentage) async {
    // Questo metodo può essere utilizzato per triggare animazioni nell'UI
    // Per ora logga i dettagli, ma può essere esteso per notificare l'UI
    print('🎉 Feedback visivo: $points punti guadagnati con $percentage% di accuratezza');

    // Determina il tipo di feedback basato sulla performance
    String feedbackType;
    if (percentage >= 95) {
      feedbackType = 'PERFETTO';
    } else if (percentage >= 85) {
      feedbackType = 'ECCELLENTE';
    } else if (percentage >= 75) {
      feedbackType = 'MOLTO_BUONO';
    } else if (percentage >= 65) {
      feedbackType = 'BUONO';
    } else {
      feedbackType = 'SUFFICIENTE';
    }

    print('🎯 Tipo feedback: $feedbackType');
  }

  /// Aggiorna il NutriScore giornaliero
  Future<void> updateDailyNutriScore(List<Map<String, dynamic>> dailyMeals) async {
    final today = DateTime.now();
    final dateKey = '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';

    final nutriScore = WellJourneyService.instance.calculateDailyNutriScore(dailyMeals, today);
    _nutriScoreHistory[dateKey] = nutriScore;

    await _saveNutriScoreHistory();

    // Aggiorna punti basati sul punteggio
    final points = (nutriScore.score / 10).round(); // 1-10 punti basati sul punteggio
    await _updateUserProgress(pointsToAdd: points);

    await _checkForNewBadges();
    notifyListeners();
  }

  /// Ottiene il progresso di un percorso specifico (0.0 - 1.0)
  double getPathwayProgress(String pathwayId) {
    if (!_enrolledPathways.contains(pathwayId)) return 0.0;

    final pathway = WellJourneyService.instance.getAvailablePathways()
        .firstWhere((p) => p.id == pathwayId, orElse: () => throw Exception('Pathway not found'));

    final completedModules = _userProgress?.completedModules
        .where((moduleId) => pathway.modules.any((m) => m.id == moduleId))
        .length ?? 0;

    return pathway.modules.isEmpty ? 0.0 : completedModules / pathway.modules.length;
  }

  /// Ottiene il NutriScore storico per analisi trend
  Future<List<Map<String, dynamic>>> getNutriScoreHistory(int days) async {
    final history = <Map<String, dynamic>>[];

    for (int i = 0; i < days; i++) {
      final date = DateTime.now().subtract(Duration(days: i));
      final dateKey = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';

      if (_nutriScoreHistory.containsKey(dateKey)) {
        final score = _nutriScoreHistory[dateKey]!;
        history.add({
          'date': date,
          'score': score.score,
          'grade': score.grade.displayName,
        });
      }
    }

    return history.reversed.toList(); // Ordine cronologico
  }

  /// Reset completo dei progressi per testing
  Future<void> resetAllProgress() async {
    final prefs = await SharedPreferences.getInstance();

    // Reset user progress
    _userProgress = WellJourneyProgress(
      userId: 'user_001',
      totalPoints: 0,
      level: 1,
      currentStreak: 0,
      longestStreak: 0,
      completedPathways: [],
      completedModules: [],
      earnedBadges: [],
      activePathways: [],
      activeChallenges: [],
      nutriScoreHistory: {},
      lastActivity: DateTime.now(),
    );

    // Reset percorsi iscritti
    _enrolledPathways.clear();

    // Reset badge ottenuti
    _earnedBadges.clear();

    // Reset sfide attive
    _activeChallenges.clear();

    // Reset NutriScore history
    _nutriScoreHistory.clear();

    // Salva tutto
    await _saveUserProgress();
    await _saveEnrolledPathways();
    await _saveEarnedBadges();
    await _saveActiveChallenges();
    await _saveNutriScoreHistory();

    // Reset anche i quiz completati nel QuizService
    await _resetQuizProgress();

    notifyListeners();

    print('🔄 Reset completo completato! Tutti i progressi sono stati azzerati.');
  }

  /// Reset dei progressi quiz
  Future<void> _resetQuizProgress() async {
    final prefs = await SharedPreferences.getInstance();

    // Rimuovi tutte le chiavi relative ai quiz
    final keys = prefs.getKeys();
    for (final key in keys) {
      if (key.startsWith('quiz_result_') ||
          key.startsWith('quiz_attempts_') ||
          key.startsWith('quiz_passed_')) {
        await prefs.remove(key);
      }
    }
  }

  /// Controlla se l'utente ha guadagnato nuovi badge - Integrazione completa con BadgeController
  Future<void> _checkForNewBadges() async {
    try {
      print('🏆 Controllo badge automatico - Punti attuali: ${_userProgress?.totalPoints ?? 0}');

      // Sincronizza i punti WellJourney con il sistema badge
      await _syncWellJourneyPointsWithBadgeSystem();

      // Ottieni l'istanza del BadgeController
      final badgeController = BadgeController.instance;

      // Controlla badge per punti WellJourney
      await badgeController.checkWellJourneyPointsBadges(_userProgress?.totalPoints ?? 0);

      // Controlla badge per percorsi completati
      await badgeController.checkPathwayBadges(_getCompletedPathways());

      // Controlla badge per streak
      await badgeController.checkStreakBadges(_userProgress?.currentStreak ?? 0);

      print('✅ Controllo badge completato');
    } catch (e) {
      print('❌ Errore nel controllo badge: $e');
    }
  }

  /// Sincronizza i punti WellJourney con il sistema badge
  Future<void> _syncWellJourneyPointsWithBadgeSystem() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentPoints = _userProgress?.totalPoints ?? 0;
      await prefs.setInt('welljourney_total_points', currentPoints);
      print('🔄 Punti sincronizzati: $currentPoints');
    } catch (e) {
      print('❌ Errore nella sincronizzazione punti: $e');
    }
  }

  /// Assegna un badge all'utente
  Future<void> _earnBadge(String badgeId) async {
    if (_earnedBadges.contains(badgeId)) return;

    _earnedBadges.add(badgeId);
    await _saveEarnedBadges();

    // Bonus punti per badge
    await _updateUserProgress(pointsToAdd: 100);
  }

  /// Aggiorna il progresso dell'utente
  Future<void> _updateUserProgress({int pointsToAdd = 0}) async {
    final currentProgress = _userProgress ?? WellJourneyProgress(
      userId: 'user_001',
      totalPoints: 0,
      currentStreak: 0,
      longestStreak: 0,
      completedPathways: [],
      completedModules: [],
      earnedBadges: [],
      activePathways: [],
      activeChallenges: [],
      nutriScoreHistory: {},
      lastActivity: DateTime.now(),
      level: 1,
    );

    final newTotalPoints = currentProgress.totalPoints + pointsToAdd;
    final newLevel = (newTotalPoints / 1000).floor() + 1;

    _userProgress = WellJourneyProgress(
      userId: currentProgress.userId,
      totalPoints: newTotalPoints,
      currentStreak: currentProgress.currentStreak,
      longestStreak: currentProgress.longestStreak,
      completedPathways: _getCompletedPathways(),
      completedModules: _completedModules,
      earnedBadges: _earnedBadges,
      activePathways: _enrolledPathways,
      activeChallenges: _activeChallenges,
      nutriScoreHistory: {},
      lastActivity: DateTime.now(),
      level: newLevel,
    );

    await _saveUserProgress();
  }

  /// Aggiorna lo streak dell'utente
  Future<void> _updateStreak() async {
    if (_userProgress == null) return;

    final now = DateTime.now();
    final lastActivity = _userProgress!.lastActivity;
    final daysDifference = now.difference(lastActivity).inDays;

    int newStreak = _userProgress!.currentStreak;

    if (daysDifference == 1) {
      // Attività consecutiva
      newStreak++;
    } else if (daysDifference > 1) {
      // Streak interrotto
      newStreak = 1;
    }

    final newLongestStreak = newStreak > _userProgress!.longestStreak
        ? newStreak
        : _userProgress!.longestStreak;

    _userProgress = WellJourneyProgress(
      userId: _userProgress!.userId,
      totalPoints: _userProgress!.totalPoints,
      currentStreak: newStreak,
      longestStreak: newLongestStreak,
      completedPathways: _userProgress!.completedPathways,
      completedModules: _userProgress!.completedModules,
      earnedBadges: _userProgress!.earnedBadges,
      activePathways: _userProgress!.activePathways,
      activeChallenges: _userProgress!.activeChallenges,
      nutriScoreHistory: _userProgress!.nutriScoreHistory,
      lastActivity: now,
      level: _userProgress!.level,
    );

    await _saveUserProgress();
  }

  /// Verifica se l'utente è iscritto a un percorso
  bool isEnrolledInPathway(String pathwayId) {
    return _enrolledPathways.contains(pathwayId);
  }

  /// Verifica se un modulo è completato
  bool isModuleCompleted(String moduleId) {
    return _completedModules.contains(moduleId);
  }

  /// Verifica se un badge è stato guadagnato
  bool isBadgeEarned(String badgeId) {
    return _earnedBadges.contains(badgeId);
  }

  /// Ottiene i percorsi completati
  List<String> _getCompletedPathways() {
    final pathways = WellJourneyService.instance.getAvailablePathways();
    final completedPathways = <String>[];

    for (final pathway in pathways) {
      final completedModulesInPathway = pathway.modules
          .where((m) => _completedModules.contains(m.id))
          .length;

      if (completedModulesInPathway == pathway.modules.length) {
        completedPathways.add(pathway.id);
      }
    }

    return completedPathways;
  }

  /// Verifica se ha uno streak di NutriScore eccellente
  bool _hasExcellentNutriScoreStreak(int days) {
    final now = DateTime.now();
    int consecutiveDays = 0;

    for (int i = 0; i < days; i++) {
      final date = now.subtract(Duration(days: i));
      final dateKey = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';

      final score = _nutriScoreHistory[dateKey];
      if (score != null && score.score >= 90) {
        consecutiveDays++;
      } else {
        break;
      }
    }

    return consecutiveDays >= days;
  }

  // Metodi di persistenza
  Future<void> _loadUserProgress() async {
    final prefs = await SharedPreferences.getInstance();
    final progressJson = prefs.getString(_progressKey);

    if (progressJson != null) {
      try {
        final data = jsonDecode(progressJson);
        _userProgress = WellJourneyProgress(
          userId: data['userId'] ?? 'user_001',
          totalPoints: data['totalPoints'] ?? 0,
          currentStreak: data['currentStreak'] ?? 0,
          longestStreak: data['longestStreak'] ?? 0,
          completedPathways: List<String>.from(data['completedPathways'] ?? []),
          completedModules: List<String>.from(data['completedModules'] ?? []),
          earnedBadges: List<String>.from(data['earnedBadges'] ?? []),
          activePathways: List<String>.from(data['activePathways'] ?? []),
          activeChallenges: List<String>.from(data['activeChallenges'] ?? []),
          nutriScoreHistory: {},
          lastActivity: DateTime.parse(data['lastActivity'] ?? DateTime.now().toIso8601String()),
          level: data['level'] ?? 1,
        );

        // Sincronizza le liste in memoria con i dati caricati
        _completedModules = List<String>.from(data['completedModules'] ?? []);
        _earnedBadges = List<String>.from(data['earnedBadges'] ?? []);
        _enrolledPathways = List<String>.from(data['activePathways'] ?? []);
        _activeChallenges = List<String>.from(data['activeChallenges'] ?? []);

        print('🔄 Dati sincronizzati: ${_userProgress!.totalPoints} punti, ${_completedModules.length} moduli');
      } catch (e) {
        print('❌ Errore nel caricamento del progresso: $e');
      }
    }
  }

  Future<void> _saveUserProgress() async {
    if (_userProgress == null) return;

    final prefs = await SharedPreferences.getInstance();
    final progressJson = jsonEncode({
      'userId': _userProgress!.userId,
      'totalPoints': _userProgress!.totalPoints,
      'currentStreak': _userProgress!.currentStreak,
      'longestStreak': _userProgress!.longestStreak,
      'completedPathways': _userProgress!.completedPathways,
      'completedModules': _userProgress!.completedModules,
      'earnedBadges': _userProgress!.earnedBadges,
      'activePathways': _userProgress!.activePathways,
      'activeChallenges': _userProgress!.activeChallenges,
      'lastActivity': _userProgress!.lastActivity.toIso8601String(),
      'level': _userProgress!.level,
    });

    await prefs.setString(_progressKey, progressJson);
  }

  Future<void> _loadEnrolledPathways() async {
    final prefs = await SharedPreferences.getInstance();
    _enrolledPathways = prefs.getStringList(_enrolledPathwaysKey) ?? [];
  }

  Future<void> _saveEnrolledPathways() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList(_enrolledPathwaysKey, _enrolledPathways);
  }

  Future<void> _loadCompletedModules() async {
    final prefs = await SharedPreferences.getInstance();
    _completedModules = prefs.getStringList(_completedModulesKey) ?? [];
  }

  Future<void> _saveCompletedModules() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList(_completedModulesKey, _completedModules);
  }

  Future<void> _loadEarnedBadges() async {
    final prefs = await SharedPreferences.getInstance();
    _earnedBadges = prefs.getStringList(_earnedBadgesKey) ?? [];
  }

  Future<void> _saveEarnedBadges() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList(_earnedBadgesKey, _earnedBadges);
  }

  Future<void> _loadActiveChallenges() async {
    final prefs = await SharedPreferences.getInstance();
    _activeChallenges = prefs.getStringList(_activeChallengesKey) ?? [];
  }

  Future<void> _saveActiveChallenges() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList(_activeChallengesKey, _activeChallenges);
  }

  Future<void> _loadNutriScoreHistory() async {
    final prefs = await SharedPreferences.getInstance();
    final historyJson = prefs.getString(_nutriScoreHistoryKey);

    if (historyJson != null) {
      try {
        final data = jsonDecode(historyJson) as Map<String, dynamic>;
        _nutriScoreHistory = data.map((key, value) {
          final scoreData = value as Map<String, dynamic>;
          return MapEntry(key, PersonalNutriScore(
            date: DateTime.parse(scoreData['date']),
            score: scoreData['score'].toDouble(),
            categoryScores: Map<String, double>.from(scoreData['categoryScores']),
            strengths: List<String>.from(scoreData['strengths']),
            improvements: List<String>.from(scoreData['improvements']),
            drStaffilanoFeedback: scoreData['drStaffilanoFeedback'],
            grade: NutriScoreGrade.values[scoreData['grade']],
          ));
        });
      } catch (e) {
        print('Errore nel caricamento della cronologia NutriScore: $e');
      }
    }
  }

  Future<void> _saveNutriScoreHistory() async {
    final prefs = await SharedPreferences.getInstance();
    final historyJson = jsonEncode(_nutriScoreHistory.map((key, value) => MapEntry(key, {
      'date': value.date.toIso8601String(),
      'score': value.score,
      'categoryScores': value.categoryScores,
      'strengths': value.strengths,
      'improvements': value.improvements,
      'drStaffilanoFeedback': value.drStaffilanoFeedback,
      'grade': value.grade.index,
    })));

    await prefs.setString(_nutriScoreHistoryKey, historyJson);
  }

  /// Ottiene i pathway completati (metodo pubblico)
  List<String> getCompletedPathways() {
    return _getCompletedPathways();
  }

  /// Forza il ricaricamento di tutti i dati
  Future<void> forceReload() async {
    print('🔄 Forzando ricaricamento dati WellJourney...');
    _isLoading = true;
    notifyListeners();

    try {
      await _loadUserProgress();
      await _loadEnrolledPathways();
      await _loadCompletedModules();
      await _loadEarnedBadges();
      await _loadActiveChallenges();
      await _loadNutriScoreHistory();

      print('✅ Ricaricamento completato');
      debugPrintState();
    } catch (e) {
      print('❌ Errore nel ricaricamento: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  /// Debug: Stampa lo stato corrente del controller
  void debugPrintState() {
    print('🔍 === DEBUG WELLJOURNEY CONTROLLER ===');
    print('📊 Punti totali: ${_userProgress?.totalPoints ?? 0}');
    print('🎯 Livello: ${_userProgress?.level ?? 0}');
    print('📚 Moduli completati (userProgress): ${_userProgress?.completedModules.length ?? 0}');
    print('📚 Moduli completati (lista): ${_completedModules.length}');
    print('🏆 Badge guadagnati (userProgress): ${_userProgress?.earnedBadges.length ?? 0}');
    print('🏆 Badge guadagnati (lista): ${_earnedBadges.length}');
    print('🛤️ Pathway iscritti: ${_enrolledPathways.length}');
    print('🔥 Streak corrente: ${_userProgress?.currentStreak ?? 0}');
    print('📅 Ultima attività: ${_userProgress?.lastActivity ?? 'N/A'}');
    print('🔍 === FINE DEBUG ===');
  }
}
