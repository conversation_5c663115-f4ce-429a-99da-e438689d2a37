import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/community_post.dart';
import '../../models/community_user.dart';
import '../../services/community_profile_service.dart';
import '../../theme/dr_staffilano_theme.dart';

/// Widget per visualizzare i post dell'utente
class UserPostsWidget extends StatefulWidget {
  final String userId;

  const UserPostsWidget({
    super.key,
    required this.userId,
  });

  @override
  State<UserPostsWidget> createState() => _UserPostsWidgetState();
}

class _UserPostsWidgetState extends State<UserPostsWidget> {
  String _selectedFilter = 'Tutti';
  final List<String> _filters = ['Tutti', 'Recenti', 'Popolari', 'Con Media'];

  @override
  Widget build(BuildContext context) {
    return Consumer<CommunityProfileService>(
      builder: (context, profileService, child) {
        final userPostIds = profileService.getUserPosts(widget.userId);

        return Column(
          children: [
            // Filtri
            _buildFilters(),

            // Lista post
            Expanded(
              child: userPostIds.isEmpty
                  ? _buildEmptyState()
                  : _buildPostsList(userPostIds),
            ),
          ],
        );
      },
    );
  }

  /// Costruisce i filtri
  Widget _buildFilters() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _filters.length,
        itemBuilder: (context, index) {
          final filter = _filters[index];
          final isSelected = filter == _selectedFilter;

          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Text(
                filter,
                style: TextStyle(
                  color: isSelected ? Colors.white : DrStaffilanoTheme.primaryGreen,
                  fontWeight: FontWeight.w600,
                ),
              ),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedFilter = filter;
                });
              },
              backgroundColor: Colors.white,
              selectedColor: DrStaffilanoTheme.primaryGreen,
              checkmarkColor: Colors.white,
              side: BorderSide(
                color: DrStaffilanoTheme.primaryGreen,
                width: 1,
              ),
            ),
          );
        },
      ),
    );
  }

  /// Costruisce la lista dei post
  Widget _buildPostsList(List<String> postIds) {
    // Per ora mostriamo post simulati basati sugli ID
    final posts = _generateMockPosts(postIds);
    final filteredPosts = _filterPosts(posts);

    if (filteredPosts.isEmpty) {
      return _buildEmptyFilterState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: filteredPosts.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildPostCard(filteredPosts[index]),
        );
      },
    );
  }

  /// Genera post simulati
  List<CommunityPost> _generateMockPosts(List<String> postIds) {
    return postIds.asMap().entries.map((entry) {
      final index = entry.key;
      final postId = entry.value;

      final mockContents = [
        'Oggi ho imparato quanto sia importante la prevenzione cardiovascolare! 💚 #DrStaffilano #Prevenzione',
        'Condivido con voi alcuni consigli per mantenere il cuore in salute 🫀✨',
        'La dieta mediterranea è davvero un toccasana per il nostro benessere! 🥗🍅',
        'Grazie Dr. Staffilano per i preziosi consigli sulla salute cardiaca! 👨‍⚕️💚',
        'Oggi ho fatto una passeggiata di 30 minuti. Piccoli passi verso un cuore più sano! 🚶‍♀️❤️',
      ];

      return CommunityPost(
        id: postId,
        authorId: widget.userId,
        author: _getMockAuthor(),
        type: PostType.text,
        content: mockContents[index % mockContents.length],
        createdAt: DateTime.now().subtract(Duration(days: index)),
        likesCount: (index + 1) * 3,
        commentsCount: index + 1,
        sharesCount: index,
        privacy: PostPrivacy.public,
        imageUrls: index % 4 == 0 ? ['https://example.com/image.jpg'] : [],
        location: index % 5 == 0 ? {'name': 'Teramo, Italia'} : null,
        tags: ['#DrStaffilano', '#Salute'],
      );
    }).toList();
  }

  /// Ottiene un autore simulato
  CommunityUser _getMockAuthor() {
    return CommunityUser.create(
      username: 'user_${widget.userId.substring(0, 8)}',
      displayName: 'Utente Community',
      bio: 'Membro della community Staffilano InnerCircle™',
    );
  }

  /// Filtra i post in base al filtro selezionato
  List<CommunityPost> _filterPosts(List<CommunityPost> posts) {
    switch (_selectedFilter) {
      case 'Recenti':
        return posts.where((post) =>
          DateTime.now().difference(post.createdAt).inDays <= 7
        ).toList();
      case 'Popolari':
        return posts.where((post) => post.likesCount >= 5).toList();
      case 'Con Media':
        return posts.where((post) => post.imageUrls.isNotEmpty).toList();
      default:
        return posts;
    }
  }

  /// Costruisce una card del post
  Widget _buildPostCard(CommunityPost post) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header del post
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: DrStaffilanoTheme.primaryGreen,
                backgroundImage: post.author?.avatarUrl != null
                    ? NetworkImage(post.author!.avatarUrl!)
                    : null,
                child: post.author?.avatarUrl == null
                    ? Text(
                        post.author?.displayName.isNotEmpty == true
                            ? post.author!.displayName[0].toUpperCase()
                            : '?',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      )
                    : null,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      post.author?.displayName ?? 'Utente Sconosciuto',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      _formatDate(post.createdAt),
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.more_vert,
                color: Colors.grey[600],
                size: 20,
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Contenuto del post
          Text(
            post.content,
            style: const TextStyle(
              fontSize: 14,
              height: 1.4,
            ),
          ),

          // Media (se presente)
          if (post.imageUrls.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              height: 150,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Icon(
                  Icons.image,
                  size: 48,
                  color: Colors.grey,
                ),
              ),
            ),
          ],

          // Location (se presente)
          if (post.location != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  post.location!['name'] ?? 'Posizione sconosciuta',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],

          const SizedBox(height: 12),

          // Azioni del post
          Row(
            children: [
              _buildActionButton(
                Icons.favorite,
                post.likesCount.toString(),
                Colors.grey[600]!,
              ),
              const SizedBox(width: 16),
              _buildActionButton(
                Icons.chat_bubble_outline,
                post.commentsCount.toString(),
                Colors.grey[600]!,
              ),
              const SizedBox(width: 16),
              _buildActionButton(
                Icons.share,
                post.sharesCount.toString(),
                Colors.grey[600]!,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Costruisce un pulsante di azione
  Widget _buildActionButton(IconData icon, String count, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 18, color: color),
        const SizedBox(width: 4),
        Text(
          count,
          style: TextStyle(
            color: color,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// Costruisce lo stato vuoto
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.article_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Nessun post ancora',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Inizia a condividere i tuoi pensieri\ncon la community!',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Costruisce lo stato vuoto per i filtri
  Widget _buildEmptyFilterState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.filter_list_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Nessun post trovato',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Prova a cambiare il filtro\nper vedere altri post',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Formatta la data
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}g';
    } else {
      return '${(difference.inDays / 7).floor()}sett';
    }
  }
}
