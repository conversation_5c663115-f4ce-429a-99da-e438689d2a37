import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';
import '../models/supabase_models.dart';

/// Servizio per gestire l'autenticazione con Supabase
///
/// Implementa le best practice di sicurezza:
/// - OAuth redirect gestito correttamente per web/mobile
/// - Eliminazione account sicura tramite Edge Functions
/// - Eliminazione dati automatica tramite CASCADE nel database
/// - Timestamp di aggiornamento per tracking delle modifiche
class SupabaseAuthService {
  static final SupabaseAuthService _instance = SupabaseAuthService._internal();
  factory SupabaseAuthService() => _instance;
  SupabaseAuthService._internal();

  final SupabaseClient _client = SupabaseConfig.client;
  
  // Stream per monitorare lo stato di autenticazione
  Stream<AuthState> get authStateChanges => _client.auth.onAuthStateChange;
  
  // Utente corrente
  User? get currentUser => _client.auth.currentUser;
  bool get isLoggedIn => currentUser != null;
  String? get currentUserId => currentUser?.id;

  /// Registrazione con email e password
  Future<AuthResponse> signUp({
    required String email,
    required String password,
    String? nome,
    String? username,
  }) async {
    try {
      print('📝 Tentativo registrazione per: $email');
      print('🔗 URL configurato: ${SupabaseConfig.supabaseUrl}');

      final response = await _client.auth.signUp(
        email: email,
        password: password,
        data: {
          if (nome != null) 'nome': nome,
          if (username != null) 'username': username,
        },
      );

      print('✅ Registrazione completata per: $email');
      print('👤 User ID: ${response.user?.id}');

      // Se la registrazione è riuscita, crea il profilo
      if (response.user != null) {
        print('📋 Creazione profilo pubblico...');
        await _createUserProfile(
          userId: response.user!.id,
          email: email,
          nome: nome,
          username: username,
        );
        print('✅ Profilo pubblico creato');
      }

      return response;
    } catch (e) {
      print('❌ Errore registrazione: $e');
      print('🔗 URL configurato: ${SupabaseConfig.supabaseUrl}');
      rethrow;
    }
  }

  /// Login con email e password
  Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    try {
      print('🔐 Tentativo login per: $email');
      print('🔗 URL configurato: ${SupabaseConfig.supabaseUrl}');

      final response = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      print('✅ Login completato per: $email');
      print('👤 User ID: ${response.user?.id}');

      return response;
    } catch (e) {
      print('❌ Errore login: $e');
      print('🔗 URL configurato: ${SupabaseConfig.supabaseUrl}');
      rethrow;
    }
  }

  /// Login con Google
  Future<bool> signInWithGoogle() async {
    try {
      print('🔗 Tentativo login Google...');

      // Gestione corretta del redirect URL basata sulla piattaforma
      String? redirectUrl;
      if (kIsWeb) {
        // Su web, usa null per permettere a Supabase di usare l'URL del sito configurato
        redirectUrl = null;
        print('🌐 Usando callback web automatico (Site URL)');
      } else {
        // Su mobile, usa il deep link dell'app (senza / finale)
        redirectUrl = 'com.dietapp.app_dieta://login-callback';
        print('📱 Usando deep link mobile: $redirectUrl');
      }

      await _client.auth.signInWithOAuth(
        OAuthProvider.google,
        redirectTo: redirectUrl,
      );

      print('✅ Login Google avviato con successo');
      return true;
    } catch (e) {
      print('❌ Errore login Google: $e');
      return false;
    }
  }

  /// Login con Apple
  Future<bool> signInWithApple() async {
    try {
      print('🔗 Tentativo login Apple...');

      // Gestione corretta del redirect URL basata sulla piattaforma
      String? redirectUrl;
      if (kIsWeb) {
        // Su web, usa null per permettere a Supabase di usare l'URL del sito configurato
        redirectUrl = null;
        print('🌐 Usando callback web automatico (Site URL)');
      } else {
        // Su mobile, usa il deep link dell'app (senza / finale)
        redirectUrl = 'com.dietapp.app_dieta://login-callback';
        print('📱 Usando deep link mobile: $redirectUrl');
      }

      await _client.auth.signInWithOAuth(
        OAuthProvider.apple,
        redirectTo: redirectUrl,
      );
      print('✅ Login Apple avviato con successo');
      return true;
    } catch (e) {
      print('❌ Errore login Apple: $e');
      return false;
    }
  }

  /// Logout
  Future<void> signOut() async {
    try {
      await _client.auth.signOut();
    } catch (e) {
      print('❌ Errore logout: $e');
      rethrow;
    }
  }

  /// Reset password
  Future<void> resetPassword(String email) async {
    try {
      // Gestione corretta del redirect URL per reset password
      String? redirectUrl;
      if (kIsWeb) {
        // Su web, usa null per permettere a Supabase di usare l'URL del sito configurato
        redirectUrl = null;
      } else {
        // Su mobile, usa il deep link dell'app (senza / finale)
        redirectUrl = 'com.dietapp.app_dieta://reset-password';
      }

      await _client.auth.resetPasswordForEmail(
        email,
        redirectTo: redirectUrl,
      );
    } catch (e) {
      print('❌ Errore reset password: $e');
      rethrow;
    }
  }

  /// Aggiorna password
  Future<UserResponse> updatePassword(String newPassword) async {
    try {
      return await _client.auth.updateUser(
        UserAttributes(password: newPassword),
      );
    } catch (e) {
      print('❌ Errore aggiornamento password: $e');
      rethrow;
    }
  }

  /// Aggiorna email
  Future<UserResponse> updateEmail(String newEmail) async {
    try {
      return await _client.auth.updateUser(
        UserAttributes(email: newEmail),
      );
    } catch (e) {
      print('❌ Errore aggiornamento email: $e');
      rethrow;
    }
  }

  /// Ottieni il profilo dell'utente corrente
  Future<SupabaseProfile?> getCurrentUserProfile() async {
    try {
      if (!isLoggedIn) return null;

      final response = await _client
          .from('profiles')
          .select()
          .eq('id', currentUserId!)
          .single();

      return SupabaseProfile.fromJson(response);
    } catch (e) {
      print('❌ Errore caricamento profilo: $e');
      return null;
    }
  }

  /// Aggiorna il profilo dell'utente corrente
  Future<bool> updateUserProfile({
    String? username,
    String? nome,
    String? fotoProfiloUrl,
  }) async {
    try {
      if (!isLoggedIn) return false;

      final updates = <String, dynamic>{};
      if (username != null) updates['username'] = username;
      if (nome != null) updates['nome'] = nome;
      if (fotoProfiloUrl != null) updates['foto_profilo_url'] = fotoProfiloUrl;

      if (updates.isEmpty) return true;

      // Aggiungi timestamp di aggiornamento
      updates['updated_at'] = DateTime.now().toIso8601String();

      await _client
          .from('profiles')
          .update(updates)
          .eq('id', currentUserId!);

      return true;
    } catch (e) {
      print('❌ Errore aggiornamento profilo: $e');
      return false;
    }
  }

  /// Elimina account utente in modo sicuro tramite Edge Function
  Future<bool> deleteAccount() async {
    try {
      if (!isLoggedIn) return false;

      print('🗑️ Avvio eliminazione account sicura...');

      // Invoca la Edge Function per eliminare l'account in modo sicuro
      // La funzione gestirà l'eliminazione dell'utente da auth.users
      // e tutti i dati collegati verranno eliminati automaticamente tramite CASCADE
      final response = await _client.functions.invoke(
        'delete-user-account',
        body: {
          'user_id': currentUserId!,
        },
      );

      if (response.status == 200) {
        print('✅ Account eliminato con successo');
        // Effettua logout locale dopo l'eliminazione
        await signOut();
        return true;
      } else {
        print('❌ Errore eliminazione account: ${response.data}');
        return false;
      }
    } catch (e) {
      print('❌ Errore eliminazione account: $e');
      return false;
    }
  }

  /// Verifica se l'email è già registrata
  Future<bool> isEmailRegistered(String email) async {
    try {
      // Questo è un workaround, Supabase non ha un metodo diretto
      final response = await _client.auth.signInWithPassword(
        email: email,
        password: 'dummy_password_for_check',
      );
      return false; // Se non lancia errore, l'email esiste
    } catch (e) {
      // Se l'errore è "Invalid login credentials", l'email potrebbe esistere
      // Se l'errore è "User not found", l'email non esiste
      return e.toString().contains('Invalid login credentials');
    }
  }

  /// Verifica se l'username è disponibile
  Future<bool> isUsernameAvailable(String username) async {
    try {
      final response = await _client
          .from('profiles')
          .select('id')
          .eq('username', username)
          .maybeSingle();

      return response == null;
    } catch (e) {
      print('❌ Errore verifica username: $e');
      return false;
    }
  }

  /// Crea il profilo utente dopo la registrazione
  Future<void> _createUserProfile({
    required String userId,
    required String email,
    String? nome,
    String? username,
  }) async {
    try {
      // Crea il profilo pubblico (created_at viene impostato automaticamente dal database)
      await _client.from('profiles').insert({
        'id': userId,
        'username': username ?? email.split('@')[0],
        'nome': nome ?? email.split('@')[0],
      });

      // Crea anche la riga in dati_utente (created_at viene impostato automaticamente dal database)
      await _client.from('dati_utente').insert({
        'id': userId,
      });

      print('✅ Profilo utente creato con successo');
    } catch (e) {
      print('❌ Errore creazione profilo: $e');
      // Non lanciare l'errore per non bloccare la registrazione
    }
  }



  /// Ottieni statistiche utente
  Future<Map<String, dynamic>> getUserStats() async {
    try {
      if (!isLoggedIn) return {};

      final userId = currentUserId!;

      // Conta i post
      final postsResponse = await _client
          .from('posts')
          .select('id')
          .eq('user_id', userId);

      // Conta i piani dietetici
      final pianiResponse = await _client
          .from('piani_dietetici')
          .select('id')
          .eq('user_id', userId);

      // Conta i progressi
      final progressiResponse = await _client
          .from('progressi_utente')
          .select('id')
          .eq('user_id', userId);

      return {
        'posts_count': postsResponse.length,
        'piani_count': pianiResponse.length,
        'progressi_count': progressiResponse.length,
      };
    } catch (e) {
      print('❌ Errore caricamento statistiche: $e');
      return {};
    }
  }
}
