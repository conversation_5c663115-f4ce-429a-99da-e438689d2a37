import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';
import '../models/supabase_models.dart';

/// Servizio per gestire l'autenticazione con Supabase
class SupabaseAuthService {
  static final SupabaseAuthService _instance = SupabaseAuthService._internal();
  factory SupabaseAuthService() => _instance;
  SupabaseAuthService._internal();

  final SupabaseClient _client = SupabaseConfig.client;
  
  // Stream per monitorare lo stato di autenticazione
  Stream<AuthState> get authStateChanges => _client.auth.onAuthStateChange;
  
  // Utente corrente
  User? get currentUser => _client.auth.currentUser;
  bool get isLoggedIn => currentUser != null;
  String? get currentUserId => currentUser?.id;

  /// Registrazione con email e password
  Future<AuthResponse> signUp({
    required String email,
    required String password,
    String? nome,
    String? username,
  }) async {
    try {
      final response = await _client.auth.signUp(
        email: email,
        password: password,
        data: {
          if (nome != null) 'nome': nome,
          if (username != null) 'username': username,
        },
      );

      // Se la registrazione è riuscita, crea il profilo
      if (response.user != null) {
        await _createUserProfile(
          userId: response.user!.id,
          email: email,
          nome: nome,
          username: username,
        );
      }

      return response;
    } catch (e) {
      print('❌ Errore registrazione: $e');
      rethrow;
    }
  }

  /// Login con email e password
  Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    try {
      return await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );
    } catch (e) {
      print('❌ Errore login: $e');
      rethrow;
    }
  }

  /// Login con Google
  Future<bool> signInWithGoogle() async {
    try {
      await _client.auth.signInWithOAuth(
        OAuthProvider.google,
        redirectTo: 'io.supabase.nutriplan://login-callback/',
      );
      return true;
    } catch (e) {
      print('❌ Errore login Google: $e');
      return false;
    }
  }

  /// Login con Apple
  Future<bool> signInWithApple() async {
    try {
      await _client.auth.signInWithOAuth(
        OAuthProvider.apple,
        redirectTo: 'io.supabase.nutriplan://login-callback/',
      );
      return true;
    } catch (e) {
      print('❌ Errore login Apple: $e');
      return false;
    }
  }

  /// Logout
  Future<void> signOut() async {
    try {
      await _client.auth.signOut();
    } catch (e) {
      print('❌ Errore logout: $e');
      rethrow;
    }
  }

  /// Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _client.auth.resetPasswordForEmail(
        email,
        redirectTo: 'io.supabase.nutriplan://reset-password/',
      );
    } catch (e) {
      print('❌ Errore reset password: $e');
      rethrow;
    }
  }

  /// Aggiorna password
  Future<UserResponse> updatePassword(String newPassword) async {
    try {
      return await _client.auth.updateUser(
        UserAttributes(password: newPassword),
      );
    } catch (e) {
      print('❌ Errore aggiornamento password: $e');
      rethrow;
    }
  }

  /// Aggiorna email
  Future<UserResponse> updateEmail(String newEmail) async {
    try {
      return await _client.auth.updateUser(
        UserAttributes(email: newEmail),
      );
    } catch (e) {
      print('❌ Errore aggiornamento email: $e');
      rethrow;
    }
  }

  /// Ottieni il profilo dell'utente corrente
  Future<SupabaseProfile?> getCurrentUserProfile() async {
    try {
      if (!isLoggedIn) return null;

      final response = await _client
          .from('profiles')
          .select()
          .eq('id', currentUserId!)
          .single();

      return SupabaseProfile.fromJson(response);
    } catch (e) {
      print('❌ Errore caricamento profilo: $e');
      return null;
    }
  }

  /// Aggiorna il profilo dell'utente corrente
  Future<bool> updateUserProfile({
    String? username,
    String? nome,
    String? fotoProfiloUrl,
  }) async {
    try {
      if (!isLoggedIn) return false;

      final updates = <String, dynamic>{};
      if (username != null) updates['username'] = username;
      if (nome != null) updates['nome'] = nome;
      if (fotoProfiloUrl != null) updates['foto_profilo_url'] = fotoProfiloUrl;

      if (updates.isEmpty) return true;

      await _client
          .from('profiles')
          .update(updates)
          .eq('id', currentUserId!);

      return true;
    } catch (e) {
      print('❌ Errore aggiornamento profilo: $e');
      return false;
    }
  }

  /// Elimina account utente
  Future<bool> deleteAccount() async {
    try {
      if (!isLoggedIn) return false;

      // Prima elimina tutti i dati dell'utente
      await _deleteUserData();
      
      // Poi elimina l'account
      await _client.auth.admin.deleteUser(currentUserId!);
      
      return true;
    } catch (e) {
      print('❌ Errore eliminazione account: $e');
      return false;
    }
  }

  /// Verifica se l'email è già registrata
  Future<bool> isEmailRegistered(String email) async {
    try {
      // Questo è un workaround, Supabase non ha un metodo diretto
      final response = await _client.auth.signInWithPassword(
        email: email,
        password: 'dummy_password_for_check',
      );
      return false; // Se non lancia errore, l'email esiste
    } catch (e) {
      // Se l'errore è "Invalid login credentials", l'email potrebbe esistere
      // Se l'errore è "User not found", l'email non esiste
      return e.toString().contains('Invalid login credentials');
    }
  }

  /// Verifica se l'username è disponibile
  Future<bool> isUsernameAvailable(String username) async {
    try {
      final response = await _client
          .from('profiles')
          .select('id')
          .eq('username', username)
          .maybeSingle();

      return response == null;
    } catch (e) {
      print('❌ Errore verifica username: $e');
      return false;
    }
  }

  /// Crea il profilo utente dopo la registrazione
  Future<void> _createUserProfile({
    required String userId,
    required String email,
    String? nome,
    String? username,
  }) async {
    try {
      await _client.from('profiles').insert({
        'id': userId,
        'username': username ?? email.split('@')[0],
        'nome': nome ?? email.split('@')[0],
        'created_at': DateTime.now().toIso8601String(),
      });

      // Crea anche la riga in dati_utente
      await _client.from('dati_utente').insert({
        'id': userId,
        'created_at': DateTime.now().toIso8601String(),
      });

      print('✅ Profilo utente creato con successo');
    } catch (e) {
      print('❌ Errore creazione profilo: $e');
      // Non lanciare l'errore per non bloccare la registrazione
    }
  }

  /// Elimina tutti i dati dell'utente
  Future<void> _deleteUserData() async {
    if (!isLoggedIn) return;

    try {
      final userId = currentUserId!;

      // Elimina in ordine per rispettare le foreign key
      await _client.from('welljourney_progress').delete().eq('user_id', userId);
      await _client.from('progressi_utente').delete().eq('user_id', userId);
      await _client.from('piani_dietetici').delete().eq('user_id', userId);
      await _client.from('diari_alimentari').delete().eq('user_id', userId);
      await _client.from('posts').delete().eq('user_id', userId);
      await _client.from('dati_utente').delete().eq('id', userId);
      await _client.from('profiles').delete().eq('id', userId);

      print('✅ Dati utente eliminati con successo');
    } catch (e) {
      print('❌ Errore eliminazione dati utente: $e');
    }
  }

  /// Ottieni statistiche utente
  Future<Map<String, dynamic>> getUserStats() async {
    try {
      if (!isLoggedIn) return {};

      final userId = currentUserId!;

      // Conta i post
      final postsCount = await _client
          .from('posts')
          .select('id', const FetchOptions(count: CountOption.exact))
          .eq('user_id', userId);

      // Conta i piani dietetici
      final pianiCount = await _client
          .from('piani_dietetici')
          .select('id', const FetchOptions(count: CountOption.exact))
          .eq('user_id', userId);

      // Conta i progressi
      final progressiCount = await _client
          .from('progressi_utente')
          .select('id', const FetchOptions(count: CountOption.exact))
          .eq('user_id', userId);

      return {
        'posts_count': postsCount.count ?? 0,
        'piani_count': pianiCount.count ?? 0,
        'progressi_count': progressiCount.count ?? 0,
      };
    } catch (e) {
      print('❌ Errore caricamento statistiche: $e');
      return {};
    }
  }
}
