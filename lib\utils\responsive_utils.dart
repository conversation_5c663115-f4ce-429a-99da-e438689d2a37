import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Device type enumeration
enum DeviceType { mobile, tablet, desktop }

/// Responsive utilities for cross-platform design consistency
class ResponsiveUtils {
  static const double _mobileBreakpoint = 600;
  static const double _tabletBreakpoint = 1024;
  static const double _desktopBreakpoint = 1200;

  /// Get device type based on screen width
  static DeviceType getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < _mobileBreakpoint) return DeviceType.mobile;
    if (width < _tabletBreakpoint) return DeviceType.tablet;
    return DeviceType.desktop;
  }

  /// Check if device is mobile
  static bool isMobile(BuildContext context) =>
      getDeviceType(context) == DeviceType.mobile;

  /// Check if device is tablet
  static bool isTablet(BuildContext context) =>
      getDeviceType(context) == DeviceType.tablet;

  /// Check if device is desktop
  static bool isDesktop(BuildContext context) =>
      getDeviceType(context) == DeviceType.desktop;

  /// Get responsive font size based on device type
  static double getResponsiveFontSize(BuildContext context, double baseFontSize) {
    final deviceType = getDeviceType(context);
    final textScaleFactor = MediaQuery.textScaleFactorOf(context);

    double scaleFactor = switch (deviceType) {
      DeviceType.mobile => 1.0,
      DeviceType.tablet => 1.1,
      DeviceType.desktop => 1.2,
    };

    return baseFontSize * scaleFactor * textScaleFactor;
  }

  /// Get responsive padding based on device type
  static EdgeInsets getResponsivePadding(BuildContext context, {
    double mobile = 16.0,
    double tablet = 24.0,
    double desktop = 32.0,
  }) {
    final deviceType = getDeviceType(context);
    double padding = switch (deviceType) {
      DeviceType.mobile => mobile,
      DeviceType.tablet => tablet,
      DeviceType.desktop => desktop,
    };

    return EdgeInsets.all(padding);
  }

  /// Get responsive margin based on device type
  static EdgeInsets getResponsiveMargin(BuildContext context, {
    double mobile = 8.0,
    double tablet = 12.0,
    double desktop = 16.0,
  }) {
    final deviceType = getDeviceType(context);
    double margin = switch (deviceType) {
      DeviceType.mobile => mobile,
      DeviceType.tablet => tablet,
      DeviceType.desktop => desktop,
    };

    return EdgeInsets.all(margin);
  }

  /// Get responsive width based on screen size
  static double getResponsiveWidth(BuildContext context, {
    double mobileRatio = 1.0,
    double tabletRatio = 0.8,
    double desktopRatio = 0.6,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    final deviceType = getDeviceType(context);

    return switch (deviceType) {
      DeviceType.mobile => screenWidth * mobileRatio,
      DeviceType.tablet => screenWidth * tabletRatio,
      DeviceType.desktop => screenWidth * desktopRatio,
    };
  }

  /// Get minimum touch target size based on platform
  static double getMinTouchTargetSize() {
    if (kIsWeb) return 44.0; // Web minimum
    if (defaultTargetPlatform == TargetPlatform.iOS) return 44.0; // iOS minimum
    return 48.0; // Android minimum
  }

  /// Get responsive icon size
  static double getResponsiveIconSize(BuildContext context, double baseSize) {
    final deviceType = getDeviceType(context);

    return switch (deviceType) {
      DeviceType.mobile => baseSize,
      DeviceType.tablet => baseSize * 1.2,
      DeviceType.desktop => baseSize * 1.4,
    };
  }

  /// Get responsive border radius
  static double getResponsiveBorderRadius(BuildContext context, double baseRadius) {
    final deviceType = getDeviceType(context);

    return switch (deviceType) {
      DeviceType.mobile => baseRadius,
      DeviceType.tablet => baseRadius * 1.1,
      DeviceType.desktop => baseRadius * 1.2,
    };
  }

  /// Get responsive elevation
  static double getResponsiveElevation(BuildContext context, double baseElevation) {
    final deviceType = getDeviceType(context);

    return switch (deviceType) {
      DeviceType.mobile => baseElevation,
      DeviceType.tablet => baseElevation * 1.2,
      DeviceType.desktop => baseElevation * 1.5,
    };
  }

  /// Get responsive grid columns
  static int getResponsiveGridColumns(BuildContext context) {
    final deviceType = getDeviceType(context);

    return switch (deviceType) {
      DeviceType.mobile => 2,
      DeviceType.tablet => 3,
      DeviceType.desktop => 4,
    };
  }

  /// Get responsive app bar height
  static double getResponsiveAppBarHeight(BuildContext context) {
    final deviceType = getDeviceType(context);

    return switch (deviceType) {
      DeviceType.mobile => kToolbarHeight,
      DeviceType.tablet => kToolbarHeight * 1.2,
      DeviceType.desktop => kToolbarHeight * 1.4,
    };
  }

  /// Check if device is in landscape mode
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  /// Get safe area padding
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    return MediaQuery.of(context).padding;
  }

  /// Get responsive card width for lists
  static double getResponsiveCardWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final deviceType = getDeviceType(context);

    return switch (deviceType) {
      DeviceType.mobile => screenWidth - 32, // Full width with margins
      DeviceType.tablet => (screenWidth - 64) / 2, // Two columns
      DeviceType.desktop => (screenWidth - 96) / 3, // Three columns
    };
  }

  /// Get responsive maximum content width
  static double getMaxContentWidth(BuildContext context) {
    final deviceType = getDeviceType(context);

    return switch (deviceType) {
      DeviceType.mobile => double.infinity,
      DeviceType.tablet => 800,
      DeviceType.desktop => 1200,
    };
  }
}
