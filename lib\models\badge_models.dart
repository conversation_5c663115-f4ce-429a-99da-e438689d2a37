import 'package:flutter/material.dart';

/// Tipologie di badge disponibili
enum BadgeType {
  points,
  pathway,
  streak,
  foodOracle,
  nutriScore,
  challenge,
  special,
}

/// Rarità del badge
enum BadgeRarity {
  common,
  uncommon,
  rare,
  epic,
  legendary,
}

/// Modello per un badge
class AppBadge {
  final String id;
  final String name;
  final String description;
  final BadgeType type;
  final BadgeRarity rarity;
  final IconData icon;
  final Color primaryColor;
  final Color secondaryColor;
  final int requiredValue;
  final String requiredUnit;
  final String drStaffilanoQuote;
  final bool isSecret;
  final List<String> prerequisites;

  const AppBadge({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.rarity,
    required this.icon,
    required this.primaryColor,
    required this.secondaryColor,
    required this.requiredValue,
    required this.requiredUnit,
    required this.drStaffilanoQuote,
    this.isSecret = false,
    this.prerequisites = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'rarity': rarity.name,
      'requiredValue': requiredValue,
      'requiredUnit': requiredUnit,
      'drStaffilanoQuote': drStaffilanoQuote,
      'isSecret': isSecret,
      'prerequisites': prerequisites,
    };
  }

  factory AppBadge.fromJson(Map<String, dynamic> json) {
    return AppBadge(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      type: BadgeType.values.firstWhere((e) => e.name == json['type']),
      rarity: BadgeRarity.values.firstWhere((e) => e.name == json['rarity']),
      icon: Icons.star, // Default, sarà gestito dal service
      primaryColor: Colors.blue, // Default, sarà gestito dal service
      secondaryColor: Colors.lightBlue, // Default, sarà gestito dal service
      requiredValue: json['requiredValue'],
      requiredUnit: json['requiredUnit'],
      drStaffilanoQuote: json['drStaffilanoQuote'],
      isSecret: json['isSecret'] ?? false,
      prerequisites: List<String>.from(json['prerequisites'] ?? []),
    );
  }
}

/// Progresso verso un badge
class BadgeProgress {
  final String badgeId;
  final int currentValue;
  final int requiredValue;
  final DateTime lastUpdated;
  final bool isUnlocked;
  final DateTime? unlockedAt;

  const BadgeProgress({
    required this.badgeId,
    required this.currentValue,
    required this.requiredValue,
    required this.lastUpdated,
    this.isUnlocked = false,
    this.unlockedAt,
  });

  double get progress => (currentValue / requiredValue).clamp(0.0, 1.0);
  bool get isCompleted => currentValue >= requiredValue;

  Map<String, dynamic> toJson() {
    return {
      'badgeId': badgeId,
      'currentValue': currentValue,
      'requiredValue': requiredValue,
      'lastUpdated': lastUpdated.toIso8601String(),
      'isUnlocked': isUnlocked,
      'unlockedAt': unlockedAt?.toIso8601String(),
    };
  }

  factory BadgeProgress.fromJson(Map<String, dynamic> json) {
    return BadgeProgress(
      badgeId: json['badgeId'],
      currentValue: json['currentValue'],
      requiredValue: json['requiredValue'],
      lastUpdated: DateTime.parse(json['lastUpdated']),
      isUnlocked: json['isUnlocked'] ?? false,
      unlockedAt: json['unlockedAt'] != null
          ? DateTime.parse(json['unlockedAt'])
          : null,
    );
  }

  BadgeProgress copyWith({
    String? badgeId,
    int? currentValue,
    int? requiredValue,
    DateTime? lastUpdated,
    bool? isUnlocked,
    DateTime? unlockedAt,
  }) {
    return BadgeProgress(
      badgeId: badgeId ?? this.badgeId,
      currentValue: currentValue ?? this.currentValue,
      requiredValue: requiredValue ?? this.requiredValue,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      isUnlocked: isUnlocked ?? this.isUnlocked,
      unlockedAt: unlockedAt ?? this.unlockedAt,
    );
  }
}

/// Collezione di badge dell'utente
class UserBadgeCollection {
  final String userId;
  final List<BadgeProgress> badgeProgress;
  final List<String> unlockedBadges;
  final DateTime lastUpdated;
  final int totalBadgesUnlocked;
  final int totalPoints;

  const UserBadgeCollection({
    required this.userId,
    required this.badgeProgress,
    required this.unlockedBadges,
    required this.lastUpdated,
    required this.totalBadgesUnlocked,
    required this.totalPoints,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'badgeProgress': badgeProgress.map((bp) => bp.toJson()).toList(),
      'unlockedBadges': unlockedBadges,
      'lastUpdated': lastUpdated.toIso8601String(),
      'totalBadgesUnlocked': totalBadgesUnlocked,
      'totalPoints': totalPoints,
    };
  }

  factory UserBadgeCollection.fromJson(Map<String, dynamic> json) {
    return UserBadgeCollection(
      userId: json['userId'],
      badgeProgress: (json['badgeProgress'] as List)
          .map((bp) => BadgeProgress.fromJson(bp))
          .toList(),
      unlockedBadges: List<String>.from(json['unlockedBadges'] ?? []),
      lastUpdated: DateTime.parse(json['lastUpdated']),
      totalBadgesUnlocked: json['totalBadgesUnlocked'] ?? 0,
      totalPoints: json['totalPoints'] ?? 0,
    );
  }
}

/// Estensioni per BadgeType
extension BadgeTypeExtension on BadgeType {
  String get displayName {
    switch (this) {
      case BadgeType.points:
        return 'Punti WellJourney';
      case BadgeType.pathway:
        return 'Percorsi';
      case BadgeType.streak:
        return 'Streak';
      case BadgeType.foodOracle:
        return 'Food Oracle';
      case BadgeType.nutriScore:
        return 'NutriScore';
      case BadgeType.challenge:
        return 'Sfide';
      case BadgeType.special:
        return 'Speciali Dr. Staffilano';
    }
  }

  IconData get icon {
    switch (this) {
      case BadgeType.points:
        return Icons.stars;
      case BadgeType.pathway:
        return Icons.route;
      case BadgeType.streak:
        return Icons.local_fire_department;
      case BadgeType.foodOracle:
        return Icons.camera_alt;
      case BadgeType.nutriScore:
        return Icons.analytics;
      case BadgeType.challenge:
        return Icons.emoji_events;
      case BadgeType.special:
        return Icons.favorite;
    }
  }
}

/// Estensioni per BadgeRarity
extension BadgeRarityExtension on BadgeRarity {
  String get displayName {
    switch (this) {
      case BadgeRarity.common:
        return 'Comune';
      case BadgeRarity.uncommon:
        return 'Non Comune';
      case BadgeRarity.rare:
        return 'Raro';
      case BadgeRarity.epic:
        return 'Epico';
      case BadgeRarity.legendary:
        return 'Leggendario';
    }
  }

  Color get color {
    switch (this) {
      case BadgeRarity.common:
        return Colors.grey;
      case BadgeRarity.uncommon:
        return Colors.green;
      case BadgeRarity.rare:
        return Colors.blue;
      case BadgeRarity.epic:
        return Colors.purple;
      case BadgeRarity.legendary:
        return Colors.orange;
    }
  }

  Color get glowColor {
    switch (this) {
      case BadgeRarity.common:
        return Colors.grey.withOpacity(0.3);
      case BadgeRarity.uncommon:
        return Colors.green.withOpacity(0.3);
      case BadgeRarity.rare:
        return Colors.blue.withOpacity(0.3);
      case BadgeRarity.epic:
        return Colors.purple.withOpacity(0.3);
      case BadgeRarity.legendary:
        return Colors.orange.withOpacity(0.3);
    }
  }
}
