import 'dart:math' as math;
import 'services/specific_diet_generator_service.dart';
import 'data/specific_diet_foods.dart';
import 'models/user_profile.dart';

/// DEBUG SISTEMA VARIETÀ - DIAGNOSI PROBLEMI
/// Verifica perché il sistema di varietà migliorato non funziona
Future<void> main() async {
  print('🔍 DEBUG SISTEMA VARIETÀ - DIAGNOSI PROBLEMI');
  print('=' * 55);
  print('Obiettivo: Identificare perché la varietà non è migliorata\n');

  try {
    // FASE 1: VERIFICA STATO ATTUALE
    print('1️⃣ VERIFICA STATO ATTUALE DEL SISTEMA');
    print('-' * 40);
    
    final allFoods = SpecificDietFoods.getAllFoods();
    print('📊 Alimenti totali nel database: ${allFoods.length}');
    
    final generator = await SpecificDietGeneratorService.getInstance();
    print('✅ SpecificDietGeneratorService inizializzato');
    
    // FASE 2: CREA PROFILO TEST
    print('\n2️⃣ CREAZIONE PROFILO TEST');
    print('-' * 25);
    
    final testProfile = UserProfile(
      id: 'debug_variety_${DateTime.now().millisecondsSinceEpoch}',
      name: 'Debug Varietà',
      age: 30,
      gender: Gender.male,
      height: 175,
      weight: 70,
      activityLevel: ActivityLevel.moderate,
      goal: Goal.maintain,
      dietType: DietType.omnivore,
      allergies: [],
      dislikedFoods: [],
      mealsPerDay: 3,
    );
    
    print('👤 Profilo creato: ${testProfile.calculateCalorieTarget()} kcal/giorno');
    
    // FASE 3: TEST GENERAZIONE MULTIPLA
    print('\n3️⃣ TEST GENERAZIONE PIANI MULTIPLI');
    print('-' * 35);
    
    final allSelectedFoods = <String>[];
    final planDetails = <int, Map<String, dynamic>>{};
    
    for (int generation = 1; generation <= 5; generation++) {
      print('\n🔄 GENERAZIONE $generation:');
      
      try {
        final weeklyPlan = await generator.generateWeeklyDietPlan(
          testProfile,
          weeks: 1,
        );
        
        if (weeklyPlan.dailyPlans.isNotEmpty) {
          final dailyPlan = weeklyPlan.dailyPlans.first;
          final generationFoods = <String>[];
          final mealDetails = <String, List<String>>{};
          
          for (final meal in dailyPlan.meals) {
            final mealFoods = meal.foods.map((fp) => fp.food.name).toList();
            generationFoods.addAll(mealFoods);
            mealDetails[meal.type] = mealFoods;
            
            print('   ${_getMealName(meal.type)}: ${mealFoods.join(', ')} (${meal.foods.length} alimenti)');
          }
          
          allSelectedFoods.addAll(generationFoods);
          planDetails[generation] = {
            'foods': generationFoods,
            'meals': mealDetails,
            'totalFoods': generationFoods.length,
            'uniqueFoods': generationFoods.toSet().length,
          };
          
          print('   📊 Totale: ${generationFoods.length} alimenti, ${generationFoods.toSet().length} unici');
        } else {
          print('   ❌ Nessun piano generato');
        }
      } catch (e) {
        print('   ❌ Errore generazione $generation: $e');
      }
      
      // Pausa tra generazioni
      await Future.delayed(Duration(milliseconds: 200));
    }
    
    // FASE 4: ANALISI VARIETÀ DETTAGLIATA
    print('\n4️⃣ ANALISI VARIETÀ DETTAGLIATA');
    print('-' * 30);
    
    final uniqueSelectedFoods = allSelectedFoods.toSet();
    final varietyRatio = uniqueSelectedFoods.length / allSelectedFoods.length;
    
    print('📈 STATISTICHE GLOBALI:');
    print('   - Alimenti totali selezionati: ${allSelectedFoods.length}');
    print('   - Alimenti unici utilizzati: ${uniqueSelectedFoods.length}');
    print('   - Rapporto varietà: ${(varietyRatio * 100).toStringAsFixed(1)}%');
    print('   - Utilizzo database: ${(uniqueSelectedFoods.length / allFoods.length * 100).toStringAsFixed(1)}%');
    
    // Analisi frequenza utilizzi
    final foodFrequency = <String, int>{};
    for (final food in allSelectedFoods) {
      foodFrequency[food] = (foodFrequency[food] ?? 0) + 1;
    }
    
    final sortedByFrequency = foodFrequency.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    print('\n🔢 FREQUENZA UTILIZZI (Top 10):');
    for (int i = 0; i < math.min(10, sortedByFrequency.length); i++) {
      final entry = sortedByFrequency[i];
      print('   ${i + 1}. ${entry.key}: ${entry.value} volte');
    }
    
    // FASE 5: ANALISI SOVRAPPOSIZIONI TRA GENERAZIONI
    print('\n5️⃣ ANALISI SOVRAPPOSIZIONI TRA GENERAZIONI');
    print('-' * 45);
    
    for (int i = 1; i <= 4; i++) {
      if (planDetails.containsKey(i) && planDetails.containsKey(i + 1)) {
        final foods1 = (planDetails[i]!['foods'] as List<String>).toSet();
        final foods2 = (planDetails[i + 1]!['foods'] as List<String>).toSet();
        
        final overlap = foods1.intersection(foods2);
        final overlapPercentage = foods1.isNotEmpty ? (overlap.length / foods1.length * 100) : 0;
        
        print('   Gen $i vs Gen ${i + 1}: ${overlap.length}/${foods1.length} comuni (${overlapPercentage.toStringAsFixed(1)}%)');
        
        if (overlap.isNotEmpty && overlap.length <= 5) {
          print('     Alimenti comuni: ${overlap.join(', ')}');
        }
      }
    }
    
    // FASE 6: DIAGNOSI PROBLEMI
    print('\n6️⃣ DIAGNOSI PROBLEMI');
    print('-' * 20);
    
    final issues = <String>[];
    final observations = <String>[];
    
    // Verifica varietà
    if (varietyRatio < 0.4) {
      issues.add('Varietà troppo bassa (${(varietyRatio * 100).toStringAsFixed(1)}%)');
    } else {
      observations.add('Varietà accettabile (${(varietyRatio * 100).toStringAsFixed(1)}%)');
    }
    
    // Verifica utilizzo database
    final dbUtilization = uniqueSelectedFoods.length / allFoods.length;
    if (dbUtilization < 0.2) {
      issues.add('Utilizzo database molto basso (${(dbUtilization * 100).toStringAsFixed(1)}%)');
    } else {
      observations.add('Utilizzo database: ${(dbUtilization * 100).toStringAsFixed(1)}%');
    }
    
    // Verifica sovrapposizioni eccessive
    var excessiveOverlap = false;
    for (int i = 1; i <= 4; i++) {
      if (planDetails.containsKey(i) && planDetails.containsKey(i + 1)) {
        final foods1 = (planDetails[i]!['foods'] as List<String>).toSet();
        final foods2 = (planDetails[i + 1]!['foods'] as List<String>).toSet();
        final overlap = foods1.intersection(foods2);
        final overlapPercentage = foods1.isNotEmpty ? (overlap.length / foods1.length * 100) : 0;
        
        if (overlapPercentage > 70) {
          excessiveOverlap = true;
          break;
        }
      }
    }
    
    if (excessiveOverlap) {
      issues.add('Sovrapposizione eccessiva tra generazioni consecutive');
    }
    
    // Verifica alimenti più frequenti
    if (sortedByFrequency.isNotEmpty && sortedByFrequency.first.value > allSelectedFoods.length * 0.3) {
      issues.add('Alimento dominante: ${sortedByFrequency.first.key} (${sortedByFrequency.first.value} volte)');
    }
    
    // FASE 7: POSSIBILI CAUSE
    print('\n7️⃣ POSSIBILI CAUSE DEI PROBLEMI');
    print('-' * 35);
    
    if (issues.isNotEmpty) {
      print('❌ PROBLEMI IDENTIFICATI:');
      for (final issue in issues) {
        print('   - $issue');
      }
      
      print('\n💡 POSSIBILI CAUSE:');
      print('   1. I metodi di varietà migliorata non vengono chiamati');
      print('   2. Il sistema di punteggi non funziona correttamente');
      print('   3. La selezione "top 3" non è efficace');
      print('   4. Il fallback al sistema casuale viene sempre attivato');
      print('   5. I filtri di sicurezza riducono troppo gli alimenti disponibili');
      print('   6. Le categorie di alimenti sono troppo limitate');
      
      print('\n🔧 AZIONI RACCOMANDATE:');
      print('   1. Aggiungere logging dettagliato nei metodi di varietà');
      print('   2. Verificare che i punteggi siano calcolati correttamente');
      print('   3. Controllare se i try-catch causano fallback non necessari');
      print('   4. Analizzare il numero di alimenti disponibili per categoria');
      print('   5. Testare i metodi di varietà in isolamento');
      
    } else {
      print('✅ SISTEMA FUNZIONANTE:');
      for (final obs in observations) {
        print('   - $obs');
      }
    }
    
    // FASE 8: RACCOMANDAZIONI SPECIFICHE
    print('\n8️⃣ RACCOMANDAZIONI SPECIFICHE');
    print('-' * 35);
    
    if (varietyRatio < 0.3) {
      print('🚨 PRIORITÀ ALTA: Varietà critica');
      print('   - Implementare logging dettagliato');
      print('   - Verificare chiamate ai metodi di varietà');
      print('   - Controllare efficacia del sistema di punteggi');
    } else if (varietyRatio < 0.5) {
      print('⚠️ PRIORITÀ MEDIA: Varietà migliorabile');
      print('   - Ottimizzare i pesi dei punteggi');
      print('   - Aumentare il numero di candidati considerati');
      print('   - Ridurre le sovrapposizioni tra generazioni');
    } else {
      print('✅ PRIORITÀ BASSA: Sistema funzionante');
      print('   - Monitorare le prestazioni');
      print('   - Considerare ottimizzazioni minori');
    }
    
    print('\n' + '=' * 55);
    
    if (issues.isEmpty) {
      print('🎉 SISTEMA DI VARIETÀ FUNZIONANTE CORRETTAMENTE');
    } else {
      print('⚠️ PROBLEMI RILEVATI - INTERVENTO NECESSARIO');
      print('Il sistema di varietà migliorato non sta producendo i risultati attesi.');
    }
    
  } catch (e, stackTrace) {
    print('\n❌ ERRORE CRITICO DURANTE IL DEBUG: $e');
    print('Stack trace: $stackTrace');
  }
}

String _getMealName(String mealType) {
  switch (mealType) {
    case 'breakfast': return 'Colazione';
    case 'lunch': return 'Pranzo';
    case 'dinner': return 'Cena';
    case 'snack': return 'Spuntino';
    default: return mealType;
  }
}
