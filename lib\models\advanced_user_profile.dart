import 'dart:convert';
import 'user_profile.dart';

/// Enum per condizioni mediche che possono influenzare la dieta
enum MedicalCondition {
  none,               // Nessuna condizione medica
  diabetes,           // Diabete (tipo 1 o 2)
  hypertension,       // Ipertensione
  heartDisease,       // Malattie cardiache
  kidneyDisease,      // Malattie renali
  liverDisease,       // Malattie epatiche
  ibs,                // Sindrome dell'intestino irritabile
  gerd,               // Malattia da reflusso gastroesofageo
  celiacDisease,      // Celiachia
  lactoseIntolerance, // Intolleranza al lattosio
  gout,               // Gotta
  hypothyroidism,     // Ipotiroidismo
  hyperthyroidism,    // Ipertiroidismo
  pcos,               // Sindrome dell'ovaio policistico
  anemia,             // Anemia
  osteoporosis,       // Osteoporosi
  thyroidDisorders,   // Disturbi tiroidei
}

/// Enum per intolleranze alimentari
enum FoodIntolerance {
  none,               // Nessuna intolleranza
  gluten,             // Glutine
  lactose,            // <PERSON><PERSON><PERSON>
  eggs,               // Uova
  nuts,               // Frutta a guscio
  shellfish,          // Crostacei
  soy,                // Soia
  fish,               // Pesce
  fructose,           // Fruttosio
  histamine,          // Istamina
  fodmap,             // FODMAP
}

/// Enum per obiettivi di fitness
enum FitnessGoal {
  general,            // Fitness generale
  weightLoss,         // Perdita di peso
  muscleGain,         // Aumento della massa muscolare
  endurance,          // Miglioramento della resistenza
  strength,           // Aumento della forza
  flexibility,        // Miglioramento della flessibilità
  athleticPerformance, // Miglioramento delle prestazioni atletiche
  rehabilitation,     // Riabilitazione da infortunio
}

/// Enum per tipi di sport praticati
enum SportType {
  none,               // Nessuno sport
  running,            // Corsa
  cycling,            // Ciclismo
  swimming,           // Nuoto
  weightlifting,      // Sollevamento pesi
  yoga,               // Yoga
  pilates,            // Pilates
  martialArts,        // Arti marziali
  teamSports,         // Sport di squadra
  racquetSports,      // Sport con racchetta
  hiking,             // Escursionismo
  climbing,           // Arrampicata
  crossfit,           // CrossFit
  hiit,               // Allenamento ad alta intensità
  dance,              // Danza
  gymnastics,         // Ginnastica
}

/// Enum per livelli di intensità dell'allenamento
enum TrainingIntensity {
  none,               // Nessun allenamento
  light,              // Leggero (40-50% FCmax)
  moderate,           // Moderato (50-70% FCmax)
  vigorous,           // Vigoroso (70-85% FCmax)
  elite,              // Elite (85-95% FCmax)
}

/// Enum per preferenze di timing dei pasti
enum MealTiming {
  standard,           // Orari standard
  earlyBird,          // Pasti anticipati
  nightOwl,           // Pasti posticipati
  intermittentFasting, // Digiuno intermittente
  frequentSmallMeals, // Pasti piccoli e frequenti
}

/// Enum per i tipi di pasto
enum MealTypeAdvanced {
  breakfast,          // Colazione
  lunch,              // Pranzo
  dinner,             // Cena
  snack,              // Spuntino
}

/// Classe per il profilo utente avanzato
class AdvancedUserProfile {
  final UserProfile baseProfile;
  final List<MedicalCondition> medicalConditions;
  final List<String> foodIntolerances;
  final FitnessGoal fitnessGoal;
  final SportType primarySport;
  final TrainingIntensity trainingIntensity;
  final int trainingDaysPerWeek;
  final int trainingMinutesPerSession;
  final MealTiming mealTiming;
  final bool isPregnant;
  final bool isBreastfeeding;
  final Map<String, dynamic> nutritionalNeeds;
  final Map<String, dynamic> dietaryPreferences;
  final Map<String, dynamic> mealDistribution;

  AdvancedUserProfile({
    required this.baseProfile,
    this.medicalConditions = const [MedicalCondition.none],
    this.foodIntolerances = const [],
    this.fitnessGoal = FitnessGoal.general,
    this.primarySport = SportType.none,
    this.trainingIntensity = TrainingIntensity.none,
    this.trainingDaysPerWeek = 0,
    this.trainingMinutesPerSession = 0,
    this.mealTiming = MealTiming.standard,
    this.isPregnant = false,
    this.isBreastfeeding = false,
    this.nutritionalNeeds = const {},
    this.dietaryPreferences = const {},
    this.mealDistribution = const {},
  });

  /// Calcola il fabbisogno calorico giornaliero considerando le condizioni speciali
  int calculateAdjustedCalorieTarget() {
    int baseCalories = baseProfile.calculateCalorieTarget();
    double adjustmentFactor = 1.0;

    // Aggiustamenti per condizioni mediche
    if (medicalConditions.contains(MedicalCondition.hypothyroidism)) {
      adjustmentFactor -= 0.05; // -5% per ipotiroidismo
    }
    if (medicalConditions.contains(MedicalCondition.hyperthyroidism)) {
      adjustmentFactor += 0.05; // +5% per ipertiroidismo
    }

    // Aggiustamenti per gravidanza e allattamento
    if (isPregnant) {
      baseCalories += 300; // +300 kcal per gravidanza
    }
    if (isBreastfeeding) {
      baseCalories += 500; // +500 kcal per allattamento
    }

    // Aggiustamenti per attività sportiva
    if (primarySport != SportType.none) {
      // Calcola calorie aggiuntive in base a sport, intensità e durata
      int sportCalories = _calculateSportCalories();
      baseCalories += sportCalories;
    }

    return (baseCalories * adjustmentFactor).round();
  }

  /// Calcola le calorie bruciate con l'attività sportiva
  int _calculateSportCalories() {
    // Fattore di intensità dell'allenamento
    double intensityFactor = 0.0;
    switch (trainingIntensity) {
      case TrainingIntensity.light:
        intensityFactor = 0.05;
        break;
      case TrainingIntensity.moderate:
        intensityFactor = 0.075;
        break;
      case TrainingIntensity.vigorous:
        intensityFactor = 0.1;
        break;
      case TrainingIntensity.elite:
        intensityFactor = 0.125;
        break;
      case TrainingIntensity.none:
        return 0;
    }

    // Calcola calorie bruciate per sessione
    double caloriesPerSession = baseProfile.weight * intensityFactor * trainingMinutesPerSession;

    // Calcola calorie bruciate settimanali e poi giornaliere
    double weeklyCalories = caloriesPerSession * trainingDaysPerWeek;
    return (weeklyCalories / 7).round();
  }

  /// Calcola la distribuzione dei macronutrienti adattata alle condizioni speciali
  Map<String, double> calculateAdjustedMacroDistribution() {
    Map<String, double> baseMacros = baseProfile.calculateMacroDistribution();

    // Copia i valori base
    double proteinRatio = baseMacros['proteins'] ?? 0.3;
    double carbRatio = baseMacros['carbs'] ?? 0.4;
    double fatRatio = baseMacros['fats'] ?? 0.3;

    // Aggiustamenti per condizioni mediche
    if (medicalConditions.contains(MedicalCondition.diabetes)) {
      carbRatio -= 0.1; // Riduzione carboidrati per diabete
      proteinRatio += 0.05;
      fatRatio += 0.05;
    }

    if (medicalConditions.contains(MedicalCondition.kidneyDisease)) {
      proteinRatio -= 0.05; // Riduzione proteine per malattie renali
      carbRatio += 0.05;
    }

    // Aggiustamenti per obiettivi di fitness
    if (fitnessGoal == FitnessGoal.muscleGain) {
      proteinRatio += 0.05; // Aumento proteine per aumento massa
      carbRatio += 0.05; // Aumento carboidrati per energia
      fatRatio -= 0.1;
    }

    if (fitnessGoal == FitnessGoal.endurance) {
      carbRatio += 0.1; // Aumento carboidrati per resistenza
      fatRatio -= 0.05;
      proteinRatio -= 0.05;
    }

    // Normalizza le percentuali per assicurarsi che sommino a 1
    double total = proteinRatio + carbRatio + fatRatio;
    proteinRatio /= total;
    carbRatio /= total;
    fatRatio /= total;

    return {
      'proteins': proteinRatio,
      'carbs': carbRatio,
      'fats': fatRatio,
    };
  }

  /// Calcola i grammi di macronutrienti adattati alle condizioni speciali
  Map<String, int> calculateAdjustedMacroGrams() {
    int calorieTarget = calculateAdjustedCalorieTarget();
    Map<String, double> macroDistribution = calculateAdjustedMacroDistribution();

    // Calcola i grammi di macronutrienti
    double proteineValue = macroDistribution['proteins'] ?? 0.3;
    double carboidratiValue = macroDistribution['carbs'] ?? 0.4;
    double grassiValue = macroDistribution['fats'] ?? 0.3;

    int proteineGrams = ((calorieTarget * proteineValue) / 4).round();
    int carboidratiGrams = ((calorieTarget * carboidratiValue) / 4).round();
    int grassiGrams = ((calorieTarget * grassiValue) / 9).round();

    return {
      'proteins': proteineGrams,
      'carbs': carboidratiGrams,
      'fats': grassiGrams,
    };
  }

  /// Ottieni la distribuzione dei pasti in base alle preferenze
  Map<String, double> getMealDistribution() {
    // Distribuzione predefinita
    Map<String, double> distribution = {
      'breakfast': 0.25,
      'lunch': 0.35,
      'dinner': 0.30,
      'snack': 0.10,
    };

    // Aggiusta in base al timing dei pasti
    switch (mealTiming) {
      case MealTiming.earlyBird:
        distribution['breakfast'] = 0.30;
        distribution['lunch'] = 0.35;
        distribution['dinner'] = 0.25;
        distribution['snack'] = 0.10;
        break;
      case MealTiming.nightOwl:
        distribution['breakfast'] = 0.20;
        distribution['lunch'] = 0.30;
        distribution['dinner'] = 0.40;
        distribution['snack'] = 0.10;
        break;
      case MealTiming.intermittentFasting:
        distribution['breakfast'] = 0.0;
        distribution['lunch'] = 0.45;
        distribution['dinner'] = 0.45;
        distribution['snack'] = 0.10;
        break;
      case MealTiming.frequentSmallMeals:
        distribution['breakfast'] = 0.20;
        distribution['lunch'] = 0.25;
        distribution['dinner'] = 0.25;
        distribution['snack'] = 0.30;
        break;
      default:
        break;
    }

    return distribution;
  }

  /// Converti da AdvancedUserProfile a Map
  Map<String, dynamic> toMap() {
    return {
      'baseProfile': baseProfile.toMap(),
      'medicalConditions': medicalConditions.map((e) => e.toString().split('.').last).toList(),
      'foodIntolerances': foodIntolerances,
      'fitnessGoal': fitnessGoal.toString().split('.').last,
      'primarySport': primarySport.toString().split('.').last,
      'trainingIntensity': trainingIntensity.toString().split('.').last,
      'trainingDaysPerWeek': trainingDaysPerWeek,
      'trainingMinutesPerSession': trainingMinutesPerSession,
      'mealTiming': mealTiming.toString().split('.').last,
      'isPregnant': isPregnant,
      'isBreastfeeding': isBreastfeeding,
      'nutritionalNeeds': nutritionalNeeds,
      'dietaryPreferences': dietaryPreferences,
      'mealDistribution': mealDistribution,
    };
  }

  /// Converti da Map a AdvancedUserProfile
  factory AdvancedUserProfile.fromMap(Map<String, dynamic> map) {
    return AdvancedUserProfile(
      baseProfile: UserProfile.fromMap(map['baseProfile']),
      medicalConditions: (map['medicalConditions'] as List).map((e) =>
          MedicalCondition.values.firstWhere(
              (c) => c.toString().split('.').last == e)).toList(),
      foodIntolerances: List<String>.from(map['foodIntolerances']),
      fitnessGoal: FitnessGoal.values.firstWhere(
          (e) => e.toString().split('.').last == map['fitnessGoal']),
      primarySport: SportType.values.firstWhere(
          (e) => e.toString().split('.').last == map['primarySport']),
      trainingIntensity: TrainingIntensity.values.firstWhere(
          (e) => e.toString().split('.').last == map['trainingIntensity']),
      trainingDaysPerWeek: map['trainingDaysPerWeek'],
      trainingMinutesPerSession: map['trainingMinutesPerSession'],
      mealTiming: MealTiming.values.firstWhere(
          (e) => e.toString().split('.').last == map['mealTiming']),
      isPregnant: map['isPregnant'],
      isBreastfeeding: map['isBreastfeeding'],
      nutritionalNeeds: map['nutritionalNeeds'] ?? {},
      dietaryPreferences: map['dietaryPreferences'] ?? {},
      mealDistribution: map['mealDistribution'] ?? {},
    );
  }

  /// Converti da AdvancedUserProfile a JSON
  String toJson() => json.encode(toMap());

  /// Converti da JSON a AdvancedUserProfile
  factory AdvancedUserProfile.fromJson(String source) =>
      AdvancedUserProfile.fromMap(json.decode(source));
}
