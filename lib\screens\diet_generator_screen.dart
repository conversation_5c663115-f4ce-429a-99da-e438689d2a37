import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../models/user_profile.dart';
import '../models/diet_plan.dart';
import '../services/storage_service.dart';
import '../services/specific_diet_generator_service.dart';
import '../data/food_database.dart';
import '../theme/app_theme.dart';
import 'user_profile_screen.dart';
import 'diet_plan_screen.dart';
import 'settings_screen.dart';

class DietGeneratorScreen extends StatefulWidget {
  const DietGeneratorScreen({super.key});

  @override
  State<DietGeneratorScreen> createState() => _DietGeneratorScreenState();
}

class _DietGeneratorScreenState extends State<DietGeneratorScreen> {
  StorageService? _storageService;
  late final FoodDatabase _foodDatabase;
  late final Future<SpecificDietGeneratorService> _specificDietGeneratorServiceFuture;

  UserProfile? _userProfile;
  bool _isLoading = true;
  bool _isGenerating = false;
  WeeklyDietPlan? _generatedPlan;
  int _planDurationWeeks = 1; // Durata del piano in settimane (default: 1 settimana)

  @override
  void initState() {
    super.initState();
    _foodDatabase = FoodDatabase();
    _specificDietGeneratorServiceFuture = SpecificDietGeneratorService.getInstance();

    // Inizializza i servizi
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    try {
      _storageService = await StorageService.getInstance();

      // Inizializza il database degli alimenti all'avvio
      await _initializeFoodDatabase();

      // Carica il profilo utente
      await _loadUserProfile();
    } catch (e) {
      print('Errore nell\'inizializzazione dei servizi: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _initializeFoodDatabase() async {
    try {
      print('Inizializzazione del database degli alimenti...');
      await _foodDatabase.resetDatabase();

      // Verifica che il database contenga alimenti
      final foods = await _foodDatabase.getAllFoods();
      print('Database inizializzato con ${foods.length} alimenti');
    } catch (e) {
      print('Errore nell\'inizializzazione del database degli alimenti: $e');
    }
  }

  Future<void> _loadUserProfile() async {
    if (_storageService == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final userProfile = await _storageService!.caricaUserProfile();

      setState(() {
        _userProfile = userProfile;
        _isLoading = false;
      });
    } catch (e) {
      print('Errore nel caricamento del profilo utente: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _generateDietPlan() async {
    if (_userProfile == null) {
      _showErrorSnackBar('Crea prima il tuo profilo');
      return;
    }

    setState(() {
      _isGenerating = true;
    });

    try {
      print('Avvio generazione piano dietetico per l\'utente: ${_userProfile!.age} anni, ${_userProfile!.gender}, ${_userProfile!.height} cm, ${_userProfile!.weight} kg');

      // Inizializza il database degli alimenti prima di generare il piano
      await _foodDatabase.resetDatabase();

      // Verifica che il database contenga alimenti
      final foods = await _foodDatabase.getAllFoods();
      print('Numero di alimenti disponibili: ${foods.length}');

      if (foods.isEmpty) {
        throw Exception('Il database degli alimenti è vuoto. Impossibile generare un piano dietetico.');
      }

      // Genera il piano dietetico utilizzando il generatore specifico consolidato
      final specificGenerator = await _specificDietGeneratorServiceFuture;

      // Pulisci la lista degli alimenti usati di recente per nuovi piani
      SpecificDietGeneratorService.clearRecentlyUsed();

      final generatedPlan = await specificGenerator.generateWeeklyDietPlan(
        _userProfile!,
        weeks: _planDurationWeeks,
      );
      print('Piano dietetico generato con il generatore consolidato per $_planDurationWeeks settimane');

      // Salva il piano generato
      if (_storageService != null) {
        await _storageService!.salvaDietPlan(generatedPlan);
      }

      setState(() {
        _generatedPlan = generatedPlan;
        _isGenerating = false;
      });

      // Mostra un messaggio di successo
      _showSuccessSnackBar('Piano dietetico generato con successo');

      // Naviga alla schermata del piano dietetico
      if (mounted) {
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => DietPlanScreen(dietPlan: generatedPlan),
          ),
        );

        // Se l'utente ha completato la visualizzazione del piano, torna indietro con successo
        if (result == true || mounted) {
          Navigator.pop(context, true); // Restituisce true per indicare successo
        }
      }
    } catch (e) {
      print('Errore nella generazione del piano dietetico: $e');
      setState(() {
        _isGenerating = false;
      });

      // Mostra un messaggio di errore dettagliato
      _showErrorSnackBar('Errore nella generazione del piano dietetico: ${e.toString().split(': ').last}');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _navigateToUserProfileScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UserProfileScreen(
          initialProfile: _userProfile,
          onProfileSaved: (profile) {
            setState(() {
              _userProfile = profile;
            });
          },
        ),
      ),
    );
  }

  void _navigateToDietPlanScreen() async {
    if (_storageService == null) {
      _showErrorSnackBar('Servizio di storage non disponibile');
      return;
    }

    final dietPlan = await _storageService!.caricaDietPlan();

    if (dietPlan == null) {
      _showErrorSnackBar('Nessun piano dietetico disponibile');
      return;
    }

    if (mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => DietPlanScreen(dietPlan: dietPlan),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Generatore Dieta'),
        actions: [
          // Pulsante per le impostazioni di traduzione
          IconButton(
            icon: const Icon(FontAwesomeIcons.language),
            tooltip: 'Impostazioni traduzione',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SettingsScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(FontAwesomeIcons.userGear),
            tooltip: 'Profilo utente',
            onPressed: _navigateToUserProfileScreen,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 600),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Intestazione
                      Text(
                        'Generatore di Piano Dietetico',
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryColor,
                        ),
                      ).animate().fadeIn(duration: 300.ms).slideY(
                        begin: -0.2,
                        end: 0,
                        duration: 300.ms,
                      ),

                      const SizedBox(height: 8),

                      Text(
                        'Crea un piano dietetico personalizzato in base al tuo profilo e ai tuoi obiettivi',
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: AppTheme.textSecondaryColor,
                        ),
                      ).animate().fadeIn(duration: 300.ms, delay: 100.ms),

                      const SizedBox(height: 24),

                      // Profilo utente
                      Card(
                        elevation: 2,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    FontAwesomeIcons.userLarge,
                                    color: AppTheme.primaryColor,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Il tuo profilo',
                                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),

                              const SizedBox(height: 16),

                              if (_userProfile == null)
                                // Nessun profilo
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    const Text(
                                      'Non hai ancora creato un profilo. Crea il tuo profilo per generare un piano dietetico personalizzato.',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: Colors.grey,
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    Center(
                                      child: ElevatedButton.icon(
                                        onPressed: _navigateToUserProfileScreen,
                                        icon: const Icon(FontAwesomeIcons.plus),
                                        label: const Text('Crea profilo'),
                                      ),
                                    ),
                                  ],
                                )
                              else
                                // Mostra il profilo
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Dati biometrici
                                    _buildProfileInfoRow(
                                      'Età',
                                      '${_userProfile!.age} anni',
                                      FontAwesomeIcons.cakeCandles,
                                    ),
                                    _buildProfileInfoRow(
                                      'Sesso',
                                      _userProfile!.gender == Gender.male ? 'Uomo' : 'Donna',
                                      FontAwesomeIcons.venusMars,
                                    ),
                                    _buildProfileInfoRow(
                                      'Altezza',
                                      '${_userProfile!.height} cm',
                                      FontAwesomeIcons.rulerVertical,
                                    ),
                                    _buildProfileInfoRow(
                                      'Peso',
                                      '${_userProfile!.weight} kg',
                                      FontAwesomeIcons.weightScale,
                                    ),
                                    _buildProfileInfoRow(
                                      'BMI',
                                      '${_userProfile!.bmi.toStringAsFixed(1)} (${_userProfile!.bmiCategory})',
                                      FontAwesomeIcons.chartSimple,
                                    ),

                                    const Divider(height: 24),

                                    // Obiettivo
                                    _buildProfileInfoRow(
                                      'Obiettivo',
                                      _getDietGoalText(_userProfile!.goal),
                                      FontAwesomeIcons.bullseye,
                                    ),
                                    if (_userProfile!.goal != Goal.maintenance)
                                      _buildProfileInfoRow(
                                        'Peso target',
                                        '${_userProfile!.targetWeight} kg',
                                        FontAwesomeIcons.weightScale,
                                      ),
                                    if (_userProfile!.goal != Goal.maintenance)
                                      _buildProfileInfoRow(
                                        'Velocità',
                                        '${_userProfile!.weeklyGoal} kg/settimana',
                                        FontAwesomeIcons.gauge,
                                      ),

                                    const Divider(height: 24),

                                    // Dieta
                                    _buildProfileInfoRow(
                                      'Tipo di dieta',
                                      _getDietTypeText(_userProfile!.dietType),
                                      FontAwesomeIcons.leaf,
                                    ),
                                    _buildProfileInfoRow(
                                      'Pasti giornalieri',
                                      '${_userProfile!.mealsPerDay}',
                                      FontAwesomeIcons.utensils,
                                    ),

                                    const SizedBox(height: 16),

                                    // Pulsante modifica
                                    Center(
                                      child: ElevatedButton.icon(
                                        onPressed: _navigateToUserProfileScreen,
                                        icon: const Icon(FontAwesomeIcons.penToSquare),
                                        label: const Text('Modifica profilo'),
                                      ),
                                    ),
                                  ],
                                ),
                            ],
                          ),
                        ),
                      ).animate().fadeIn(duration: 300.ms, delay: 200.ms),

                      const SizedBox(height: 24),

                      // Calcoli nutrizionali
                      if (_userProfile != null)
                        Card(
                          elevation: 2,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Icon(
                                      FontAwesomeIcons.calculator,
                                      color: AppTheme.primaryColor,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Calcoli nutrizionali',
                                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),

                                const SizedBox(height: 16),

                                // BMR
                                _buildNutritionInfoRow(
                                  'Metabolismo basale (BMR)',
                                  '${_userProfile!.calculateBMR().round()} kcal',
                                  FontAwesomeIcons.fire,
                                ),

                                // TDEE
                                _buildNutritionInfoRow(
                                  'Fabbisogno calorico (TDEE)',
                                  '${_userProfile!.calculateTDEE()} kcal',
                                  FontAwesomeIcons.bolt,
                                ),

                                // Obiettivo calorico
                                _buildNutritionInfoRow(
                                  'Obiettivo calorico',
                                  '${_userProfile!.calculateCalorieTarget()} kcal',
                                  FontAwesomeIcons.bullseye,
                                  color: AppTheme.accentColor,
                                ),

                                const Divider(height: 24),

                                // Macronutrienti
                                Text(
                                  'Distribuzione macronutrienti:',
                                  style: Theme.of(context).textTheme.titleMedium,
                                ),

                                const SizedBox(height: 8),

                                // Proteine
                                _buildMacroRow(
                                  'Proteine',
                                  _userProfile!.calculateMacroDistribution()['proteine']! * 100,
                                  _userProfile!.calculateMacroGrams()['proteine']!,
                                  AppTheme.proteinColor,
                                ),

                                // Carboidrati
                                _buildMacroRow(
                                  'Carboidrati',
                                  _userProfile!.calculateMacroDistribution()['carboidrati']! * 100,
                                  _userProfile!.calculateMacroGrams()['carboidrati']!,
                                  AppTheme.carbColor,
                                ),

                                // Grassi
                                _buildMacroRow(
                                  'Grassi',
                                  _userProfile!.calculateMacroDistribution()['grassi']! * 100,
                                  _userProfile!.calculateMacroGrams()['grassi']!,
                                  AppTheme.fatColor,
                                ),
                              ],
                            ),
                          ),
                        ).animate().fadeIn(duration: 300.ms, delay: 300.ms),

                      const SizedBox(height: 32),

                      // Opzioni di generazione
                      if (_userProfile != null)
                        Card(
                          elevation: 2,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Icon(
                                      FontAwesomeIcons.gears,
                                      color: AppTheme.primaryColor,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Opzioni di generazione',
                                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),

                                const SizedBox(height: 16),

                                // Informazioni sull'algoritmo consolidato
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: AppTheme.primaryColor.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: AppTheme.primaryColor.withOpacity(0.3),
                                    ),
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(
                                        FontAwesomeIcons.microchip,
                                        color: AppTheme.primaryColor,
                                        size: 16,
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          'Algoritmo Consolidato: Generazione precisa con varietà intelligente, appropriatezza culturale italiana e target calorici ±50 kcal.',
                                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                            color: AppTheme.primaryColor,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),

                                const SizedBox(height: 16),

                                // Opzione per scegliere la durata del piano
                                Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        'Durata del piano:',
                                        style: Theme.of(context).textTheme.bodyLarge,
                                      ),
                                    ),
                                    DropdownButton<int>(
                                      value: _planDurationWeeks,
                                      onChanged: _isGenerating
                                          ? null
                                          : (value) {
                                              if (value != null) {
                                                setState(() {
                                                  _planDurationWeeks = value;
                                                });
                                              }
                                            },
                                      items: const [
                                        DropdownMenuItem(
                                          value: 1,
                                          child: Text('1 settimana'),
                                        ),
                                        DropdownMenuItem(
                                          value: 2,
                                          child: Text('2 settimane'),
                                        ),
                                        DropdownMenuItem(
                                          value: 4,
                                          child: Text('4 settimane'),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),

                                // Descrizione della durata del piano
                                Text(
                                  'Il piano dietetico verrà generato per $_planDurationWeeks ${_planDurationWeeks == 1 ? 'settimana' : 'settimane'} consecutive.',
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    fontStyle: FontStyle.italic,
                                    color: AppTheme.textSecondaryColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ).animate().fadeIn(duration: 300.ms, delay: 400.ms),

                      const SizedBox(height: 32),

                      // Pulsanti azione
                      Center(
                        child: Column(
                          children: [
                            ElevatedButton.icon(
                              onPressed: _userProfile == null || _isGenerating
                                  ? null
                                  : _generateDietPlan,
                              icon: _isGenerating
                                  ? const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: Colors.white,
                                      ),
                                    )
                                  : const Icon(FontAwesomeIcons.wandMagicSparkles),
                              label: Text(_isGenerating
                                  ? 'Generazione in corso...'
                                  : 'Genera piano dietetico'),
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 32,
                                  vertical: 16,
                                ),
                              ),
                            ),

                            const SizedBox(height: 16),

                            TextButton.icon(
                              onPressed: _navigateToDietPlanScreen,
                              icon: const Icon(FontAwesomeIcons.eye),
                              label: const Text('Visualizza piano esistente'),
                            ),
                          ],
                        ),
                      ).animate().fadeIn(duration: 300.ms, delay: 400.ms),

                      const SizedBox(height: 16),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildProfileInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: Colors.grey,
          ),
          const SizedBox(width: 8),
          Text(
            '$label:',
            style: const TextStyle(
              color: Colors.grey,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNutritionInfoRow(String label, String value, IconData icon, {Color? color}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: color ?? Colors.grey,
          ),
          const SizedBox(width: 8),
          Text(
            '$label:',
            style: const TextStyle(
              color: Colors.grey,
            ),
          ),
          const Spacer(),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMacroRow(String label, double percentage, int grams, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              Text(
                '${percentage.round()}% (${grams}g)',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: percentage / 100,
            backgroundColor: color.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: 8,
            borderRadius: BorderRadius.circular(4),
          ),
        ],
      ),
    );
  }

  String _getDietGoalText(Goal goal) {
    switch (goal) {
      case Goal.weightLoss:
        return 'Perdita di peso';
      case Goal.maintenance:
        return 'Mantenimento del peso';
      case Goal.weightGain:
        return 'Aumento di massa muscolare';
    }
  }

  String _getDietTypeText(DietType dietType) {
    switch (dietType) {
      case DietType.omnivore:
        return 'Onnivoro';
      case DietType.vegetarian:
        return 'Vegetariano';
      case DietType.vegan:
        return 'Vegano';
      case DietType.pescatarian:
        return 'Pescetariano';
      case DietType.keto:
        return 'Keto';
      case DietType.paleo:
        return 'Paleo';
    }
  }
}
