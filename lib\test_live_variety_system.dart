import 'dart:io';
import 'services/specific_diet_generator_service.dart';
import 'services/food_variety_manager.dart';
import 'services/food_database_analyzer.dart';
import 'models/user_profile.dart';
import 'models/food.dart';

/// Test completo per verificare che il sistema di varietà funzioni nel generatore live
Future<void> main() async {
  print('🔍 TEST SISTEMA DI VARIETÀ LIVE - DEBUGGING COMPLETO');
  print('=' * 70);

  try {
    // Test 1: Verifica inizializzazione servizi
    print('\n1️⃣ VERIFICA INIZIALIZZAZIONE SERVIZI');
    print('-' * 50);
    
    final varietyManager = await FoodVarietyManager.getInstance();
    await varietyManager.resetUsageHistory();
    print('✅ FoodVarietyManager inizializzato e resettato');
    
    final specificGenerator = await SpecificDietGeneratorService.getInstance();
    print('✅ SpecificDietGeneratorService inizializzato');
    
    // Test 2: Crea profilo utente di test
    print('\n2️⃣ CREAZIONE PROFILO UTENTE TEST');
    print('-' * 40);
    
    final testProfile = UserProfile(
      id: 'test_variety_user',
      name: 'Test Varietà',
      age: 30,
      gender: Gender.male,
      height: 175,
      weight: 70,
      activityLevel: ActivityLevel.moderate,
      goal: Goal.maintain,
      dietType: DietType.omnivore,
      allergies: [],
      dislikedFoods: [],
      mealsPerDay: 3,
    );
    
    print('✅ Profilo utente creato: ${testProfile.name}');
    print('   - Obiettivo calorico: ${testProfile.calculateCalorieTarget()} kcal');
    print('   - Macronutrienti: ${testProfile.calculateMacroGrams()}');
    
    // Test 3: Genera piani dietetici multipli per verificare la varietà
    print('\n3️⃣ GENERAZIONE PIANI DIETETICI MULTIPLI');
    print('-' * 50);
    
    final allSelectedFoods = <String, List<String>>{};
    final mealsByDay = <String, Map<String, List<String>>>{};
    
    for (int day = 1; day <= 5; day++) {
      print('\n📅 Giorno $day:');
      
      try {
        final weeklyPlan = await specificGenerator.generateWeeklyDietPlan(
          testProfile,
          weeks: 1,
        );
        
        if (weeklyPlan.dailyPlans.isNotEmpty) {
          final dailyPlan = weeklyPlan.dailyPlans.first;
          final dayKey = 'Giorno_$day';
          mealsByDay[dayKey] = {};
          
          for (final meal in dailyPlan.meals) {
            final mealFoods = meal.foods.map((fp) => fp.food.name).toList();
            mealsByDay[dayKey]![meal.type] = mealFoods;
            
            // Aggiungi alla lista globale
            if (!allSelectedFoods.containsKey(meal.type)) {
              allSelectedFoods[meal.type] = [];
            }
            allSelectedFoods[meal.type]!.addAll(mealFoods);
            
            print('   ${_getMealDisplayName(meal.type)}: ${mealFoods.join(', ')}');
          }
        } else {
          print('   ❌ Nessun piano generato per il giorno $day');
        }
      } catch (e) {
        print('   ❌ Errore nella generazione del giorno $day: $e');
      }
    }
    
    // Test 4: Analisi della varietà ottenuta
    print('\n4️⃣ ANALISI VARIETÀ OTTENUTA');
    print('-' * 35);
    
    for (final mealType in ['breakfast', 'lunch', 'dinner']) {
      if (allSelectedFoods.containsKey(mealType)) {
        final foods = allSelectedFoods[mealType]!;
        final uniqueFoods = foods.toSet();
        final varietyRatio = uniqueFoods.length / foods.length;
        
        print('${_getMealDisplayName(mealType)}:');
        print('  - Alimenti totali selezionati: ${foods.length}');
        print('  - Alimenti unici: ${uniqueFoods.length}');
        print('  - Rapporto varietà: ${(varietyRatio * 100).toStringAsFixed(1)}%');
        print('  - Alimenti: ${uniqueFoods.take(5).join(', ')}${uniqueFoods.length > 5 ? '...' : ''}');
      }
    }
    
    // Test 5: Verifica tracking varietà
    print('\n5️⃣ VERIFICA TRACKING VARIETÀ');
    print('-' * 35);
    
    final stats = varietyManager.getUsageStatistics();
    print('📊 Statistiche FoodVarietyManager:');
    print('   - Alimenti tracciati: ${stats['totalTrackedFoods']}');
    print('   - Utilizzi totali: ${stats['totalUsages']}');
    print('   - Utilizzi recenti: ${stats['recentUsages']}');
    
    final recentFoods = varietyManager.getRecentlyUsedFoods();
    print('   - Alimenti utilizzati di recente: ${recentFoods.length}');
    if (recentFoods.isNotEmpty) {
      print('   - Primi 5: ${recentFoods.take(5).join(', ')}');
    }
    
    // Test 6: Verifica differenze tra giorni
    print('\n6️⃣ VERIFICA DIFFERENZE TRA GIORNI');
    print('-' * 40);
    
    for (final mealType in ['breakfast', 'lunch', 'dinner']) {
      print('\n${_getMealDisplayName(mealType)}:');
      
      final mealsByDayForType = <String, List<String>>{};
      for (final dayEntry in mealsByDay.entries) {
        if (dayEntry.value.containsKey(mealType)) {
          mealsByDayForType[dayEntry.key] = dayEntry.value[mealType]!;
        }
      }
      
      // Calcola sovrapposizioni tra giorni
      final allDays = mealsByDayForType.keys.toList();
      for (int i = 0; i < allDays.length - 1; i++) {
        for (int j = i + 1; j < allDays.length; j++) {
          final day1 = allDays[i];
          final day2 = allDays[j];
          final foods1 = mealsByDayForType[day1]?.toSet() ?? {};
          final foods2 = mealsByDayForType[day2]?.toSet() ?? {};
          
          final overlap = foods1.intersection(foods2);
          final overlapPercentage = foods1.isNotEmpty 
              ? (overlap.length / foods1.length * 100) 
              : 0;
          
          print('   $day1 vs $day2: ${overlap.length} alimenti in comune (${overlapPercentage.toStringAsFixed(1)}%)');
        }
      }
    }
    
    // Test 7: Analisi database
    print('\n7️⃣ ANALISI DATABASE UTILIZZO');
    print('-' * 35);
    
    try {
      final analyzer = await FoodDatabaseAnalyzer.create();
      final report = await analyzer.analyzeDatabaseUtilization();
      
      print('📈 Report utilizzo database:');
      print('   - Alimenti totali: ${report.totalFoods}');
      print('   - Tasso utilizzo: ${(report.overallUtilizationRate * 100).toStringAsFixed(1)}%');
      print('   - Alimenti mai utilizzati: ${report.neverUsedFoods.length}');
      print('   - Alimenti ben utilizzati: ${report.wellUtilizedFoods.length}');
      
      if (report.seasonalUnderutilized.isNotEmpty) {
        print('   - Alimenti stagionali sottoutilizzati: ${report.seasonalUnderutilized.length}');
      }
      
      if (report.traditionalUnderutilized.isNotEmpty) {
        print('   - Alimenti tradizionali sottoutilizzati: ${report.traditionalUnderutilized.length}');
      }
    } catch (e) {
      print('⚠️ Analisi database non disponibile: $e');
    }
    
    // Test 8: Valutazione finale
    print('\n8️⃣ VALUTAZIONE FINALE');
    print('-' * 25);
    
    bool varietyWorking = false;
    final issues = <String>[];
    
    // Verifica se ci sono alimenti tracciati
    if (stats['totalTrackedFoods'] as int > 0) {
      print('✅ Sistema di tracking funzionante');
      varietyWorking = true;
    } else {
      issues.add('Nessun alimento tracciato dal FoodVarietyManager');
    }
    
    // Verifica varietà nei pasti
    for (final mealType in ['breakfast', 'lunch', 'dinner']) {
      if (allSelectedFoods.containsKey(mealType)) {
        final foods = allSelectedFoods[mealType]!;
        final uniqueFoods = foods.toSet();
        final varietyRatio = uniqueFoods.length / foods.length;
        
        if (varietyRatio > 0.5) { // Almeno 50% di varietà
          print('✅ Buona varietà per ${_getMealDisplayName(mealType)} (${(varietyRatio * 100).toStringAsFixed(1)}%)');
        } else {
          issues.add('Bassa varietà per ${_getMealDisplayName(mealType)} (${(varietyRatio * 100).toStringAsFixed(1)}%)');
        }
      }
    }
    
    print('\n' + '=' * 70);
    if (varietyWorking && issues.isEmpty) {
      print('🎉 SUCCESSO! Il sistema di varietà sta funzionando correttamente');
      print('✅ Gli utenti vedranno una maggiore varietà nei loro piani dietetici');
    } else {
      print('⚠️ PROBLEMI RILEVATI nel sistema di varietà:');
      for (final issue in issues) {
        print('   - $issue');
      }
      
      if (!varietyWorking) {
        print('\n💡 POSSIBILI CAUSE:');
        print('   - Il PrecisionFoodSelector non viene utilizzato correttamente');
        print('   - Il FoodVarietyManager non sta tracciando gli utilizzi');
        print('   - Problemi di integrazione tra i servizi');
      }
    }
    
  } catch (e, stackTrace) {
    print('\n❌ ERRORE CRITICO DURANTE IL TEST: $e');
    print('Stack trace: $stackTrace');
    exit(1);
  }
}

String _getMealDisplayName(String mealType) {
  switch (mealType) {
    case 'breakfast':
      return 'Colazione';
    case 'lunch':
      return 'Pranzo';
    case 'dinner':
      return 'Cena';
    case 'snack':
      return 'Spuntino';
    default:
      return mealType;
  }
}
