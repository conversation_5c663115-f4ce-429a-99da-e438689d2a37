import 'package:flutter/material.dart';
import '../models/food.dart';
import '../services/food_database_service.dart';
import '../services/food_validation_service.dart';
import '../widgets/food_validation_form.dart';
import '../widgets/food_grid_item.dart';
import 'food_import_screen.dart';
import 'translation_manager_screen.dart';
import 'food_detail_screen.dart';

/// Schermata di amministrazione per la gestione degli alimenti
class FoodAdminScreen extends StatefulWidget {
  const FoodAdminScreen({super.key});

  @override
  State<FoodAdminScreen> createState() => _FoodAdminScreenState();
}

class _FoodAdminScreenState extends State<FoodAdminScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late FoodDatabaseService _databaseService;
  late FoodValidationService _validationService;

  List<Food> _allFoods = [];
  List<Food> _pendingFoods = [];
  List<Food> _validatedFoods = [];
  List<Food> _rejectedFoods = [];

  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initServices();
  }

  Future<void> _initServices() async {
    _databaseService = await FoodDatabaseService.getInstance();
    _validationService = FoodValidationService.getInstance();
    _loadFoods();
  }

  Future<void> _loadFoods() async {
    setState(() {
      _isLoading = true;
    });

    // Carica tutti gli alimenti
    final allFoods = _databaseService.getAllFoods();

    // Filtra gli alimenti in base allo stato di validazione
    final pendingFoods = allFoods.where((food) =>
      food.validationStatus == ValidationStatus.pending).toList();

    final validatedFoods = allFoods.where((food) =>
      food.validationStatus == ValidationStatus.validated).toList();

    final rejectedFoods = allFoods.where((food) =>
      food.validationStatus == ValidationStatus.rejected).toList();

    setState(() {
      _allFoods = allFoods;
      _pendingFoods = pendingFoods;
      _validatedFoods = validatedFoods;
      _rejectedFoods = rejectedFoods;
      _isLoading = false;
    });
  }

  Future<void> _searchFoods(String query) async {
    setState(() {
      _searchQuery = query;
      _isLoading = true;
    });

    if (query.isEmpty) {
      _loadFoods();
      return;
    }

    // Cerca alimenti in tutte le fonti
    final searchResults = await _databaseService.searchFoods(query);

    // Filtra i risultati in base allo stato di validazione
    final pendingFoods = searchResults.where((food) =>
      food.validationStatus == ValidationStatus.pending).toList();

    final validatedFoods = searchResults.where((food) =>
      food.validationStatus == ValidationStatus.validated).toList();

    final rejectedFoods = searchResults.where((food) =>
      food.validationStatus == ValidationStatus.rejected).toList();

    setState(() {
      _allFoods = searchResults;
      _pendingFoods = pendingFoods;
      _validatedFoods = validatedFoods;
      _rejectedFoods = rejectedFoods;
      _isLoading = false;
    });
  }

  Future<void> _validateFood(Food food, ValidationStatus status, String validator) async {
    try {
      await _databaseService.validateFood(
        foodId: food.id,
        status: status,
        validator: validator,
      );

      // Ricarica gli alimenti
      _loadFoods();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Alimento ${status == ValidationStatus.validated ? 'validato' : 'rifiutato'} con successo'),
          backgroundColor: status == ValidationStatus.validated ? Colors.green : Colors.red,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Errore: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _importFood(Food food) async {
    try {
      await _databaseService.importFood(food);

      // Ricarica gli alimenti
      _loadFoods();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Alimento importato con successo'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Errore: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _deleteFood(String foodId) async {
    try {
      final success = await _databaseService.deleteFood(foodId);

      if (success) {
        // Ricarica gli alimenti
        _loadFoods();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Alimento eliminato con successo'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Impossibile eliminare l\'alimento'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Errore: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showValidationForm(Food food) {
    showDialog(
      context: context,
      builder: (context) => FoodValidationForm(
        food: food,
        validationService: _validationService,
        onValidate: (status, validator) {
          _validateFood(food, status, validator);
          Navigator.of(context).pop();
        },
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestione Alimenti'),
        actions: [
          IconButton(
            icon: const Icon(Icons.file_upload),
            tooltip: 'Importazione batch',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const FoodImportScreen(),
                ),
              ).then((_) => _loadFoods());
            },
          ),
          IconButton(
            icon: const Icon(Icons.translate),
            tooltip: 'Gestione traduzioni',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const TranslationManagerScreen(),
                ),
              );
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Tutti'),
            Tab(text: 'In attesa'),
            Tab(text: 'Validati'),
            Tab(text: 'Rifiutati'),
          ],
        ),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              decoration: const InputDecoration(
                labelText: 'Cerca alimenti',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: _searchFoods,
            ),
          ),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : TabBarView(
                    controller: _tabController,
                    children: [
                      _buildFoodList(_allFoods),
                      _buildFoodList(_pendingFoods),
                      _buildFoodList(_validatedFoods),
                      _buildFoodList(_rejectedFoods),
                    ],
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          // Mostra la ricerca di alimenti esterni
          if (_searchQuery.isEmpty) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Inserisci un termine di ricerca per importare alimenti'),
                backgroundColor: Colors.orange,
              ),
            );
            return;
          }

          setState(() {
            _isLoading = true;
          });

          // Cerca alimenti esterni
          final usdaFoods = await _databaseService.searchUsdaFoods(_searchQuery);
          final offFoods = await _databaseService.searchOpenFoodFactsFoods(_searchQuery);

          setState(() {
            _isLoading = false;
          });

          if (!mounted) return;

          // Mostra i risultati in un dialog
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Text('Risultati per "$_searchQuery"'),
              content: SizedBox(
                width: double.maxFinite,
                height: 400,
                child: ListView(
                  children: [
                    if (usdaFoods.isEmpty && offFoods.isEmpty)
                      const ListTile(
                        title: Text('Nessun risultato trovato'),
                      ),
                    if (usdaFoods.isNotEmpty) ...[
                      const ListTile(
                        title: Text('USDA FoodData Central', style: TextStyle(fontWeight: FontWeight.bold)),
                      ),
                      ...usdaFoods.map((food) => ListTile(
                        title: Text(food.name),
                        subtitle: Text('${food.calories} kcal | P: ${food.proteins}g | C: ${food.carbs}g | G: ${food.fats}g'),
                        trailing: IconButton(
                          icon: const Icon(Icons.add),
                          onPressed: () {
                            _importFood(food);
                            Navigator.of(context).pop();
                          },
                        ),
                      )),
                    ],
                    if (offFoods.isNotEmpty) ...[
                      const ListTile(
                        title: Text('Open Food Facts', style: TextStyle(fontWeight: FontWeight.bold)),
                      ),
                      ...offFoods.map((food) => ListTile(
                        title: Text(food.name),
                        subtitle: Text('${food.calories} kcal | P: ${food.proteins}g | C: ${food.carbs}g | G: ${food.fats}g'),
                        trailing: IconButton(
                          icon: const Icon(Icons.add),
                          onPressed: () {
                            _importFood(food);
                            Navigator.of(context).pop();
                          },
                        ),
                      )),
                    ],
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Chiudi'),
                ),
              ],
            ),
          );
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildFoodList(List<Food> foods) {
    if (foods.isEmpty) {
      return const Center(
        child: Text('Nessun alimento trovato'),
      );
    }

    return ListView.builder(
      itemCount: foods.length,
      itemBuilder: (context, index) {
        final food = foods[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: ListTile(
            title: Text(food.name),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('${food.calories} kcal | P: ${food.proteins.toStringAsFixed(1)}g | C: ${food.carbs.toStringAsFixed(1)}g | G: ${food.fats.toStringAsFixed(1)}g'),
                Text('Fonte: ${food.dataSource.toString().split('.').last} | ID: ${food.sourceId}'),
                Text('Stato: ${food.validationStatus.toString().split('.').last}'),
              ],
            ),
            isThreeLine: true,
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.info_outline),
                  color: Colors.blue,
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => FoodDetailScreen(food: food),
                      ),
                    );
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.check_circle),
                  color: Colors.green,
                  onPressed: () => _showValidationForm(food),
                ),
                IconButton(
                  icon: const Icon(Icons.delete),
                  color: Colors.red,
                  onPressed: () => _deleteFood(food.id),
                ),
              ],
            ),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => FoodDetailScreen(food: food),
                ),
              );
            },
          ),
        );
      },
    );
  }
}
