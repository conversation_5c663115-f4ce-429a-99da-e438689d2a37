import 'package:uuid/uuid.dart';
import 'community_user.dart';

/// Tipi di post nella community
enum PostType {
  text('text', 'Testo', '📝'),
  image('image', 'Immagine', '📸'),
  dietPlan('diet_plan', 'Piano Dietetico', '🍽️'),
  progress('progress', 'Progresso', '📈'),
  recipe('recipe', 'Ricetta', '👨‍🍳'),
  achievement('achievement', 'Traguardo', '🏆'),
  question('question', 'Domanda', '❓'),
  tip('tip', 'Consiglio', '💡');

  const PostType(this.id, this.displayName, this.emoji);

  final String id;
  final String displayName;
  final String emoji;
}

/// Livelli di privacy per i post
enum PostPrivacy {
  public('public', 'Pubblico', 'Tutti possono vedere questo post', '🌍'),
  friends('friends', 'Amici', 'Solo i tuoi amici possono vedere', '👥'),
  private('private', 'Solo io', 'Solo tu puoi vedere questo post', '🔒');

  const PostPrivacy(this.id, this.displayName, this.description, this.icon);

  final String id;
  final String displayName;
  final String description;
  final String icon;

  /// Ottieni PostPrivacy da stringa
  static PostPrivacy fromString(String value) {
    return PostPrivacy.values.firstWhere(
      (privacy) => privacy.id == value,
      orElse: () => PostPrivacy.public,
    );
  }
}

/// Modello per i post della community
class CommunityPost {
  final String id;
  final String authorId;
  final CommunityUser? author; // Popolato quando necessario
  final PostType type;
  final String content;
  final List<String> imageUrls;
  final Map<String, dynamic>? attachedData; // Per piani dietetici, progressi, etc.
  final List<String> tags;
  final List<String> taggedUserIds; // IDs degli utenti taggati
  final List<CommunityUser> taggedUsers; // Dati completi degli utenti taggati
  final Map<String, dynamic>? location; // Dati della posizione
  final PostPrivacy privacy; // Visibilità del post
  final DateTime createdAt;
  final DateTime? updatedAt;
  final int likesCount;
  final int commentsCount;
  final int sharesCount;
  final bool isPinned;
  final bool isVerified;
  final String? groupId;
  final Map<String, dynamic> metadata;

  CommunityPost({
    required this.id,
    required this.authorId,
    this.author,
    required this.type,
    required this.content,
    this.imageUrls = const [],
    this.attachedData,
    this.tags = const [],
    this.taggedUserIds = const [],
    this.taggedUsers = const [],
    this.location,
    this.privacy = PostPrivacy.public,
    required this.createdAt,
    this.updatedAt,
    this.likesCount = 0,
    this.commentsCount = 0,
    this.sharesCount = 0,
    this.isPinned = false,
    this.isVerified = false,
    this.groupId,
    this.metadata = const {},
  });

  /// Crea un nuovo post
  factory CommunityPost.create({
    required String authorId,
    required PostType type,
    required String content,
    List<String> imageUrls = const [],
    Map<String, dynamic>? attachedData,
    List<String> tags = const [],
    List<String> taggedUserIds = const [],
    List<CommunityUser> taggedUsers = const [],
    Map<String, dynamic>? location,
    PostPrivacy privacy = PostPrivacy.public,
    String? groupId,
    Map<String, dynamic> metadata = const {},
  }) {
    return CommunityPost(
      id: const Uuid().v4(),
      authorId: authorId,
      type: type,
      content: content,
      imageUrls: imageUrls,
      attachedData: attachedData,
      tags: tags,
      taggedUserIds: taggedUserIds,
      taggedUsers: taggedUsers,
      location: location,
      privacy: privacy,
      createdAt: DateTime.now(),
      groupId: groupId,
      metadata: metadata,
    );
  }

  /// Copia con modifiche
  CommunityPost copyWith({
    CommunityUser? author,
    String? content,
    List<String>? imageUrls,
    Map<String, dynamic>? attachedData,
    List<String>? tags,
    List<String>? taggedUserIds,
    List<CommunityUser>? taggedUsers,
    Map<String, dynamic>? location,
    PostPrivacy? privacy,
    DateTime? updatedAt,
    int? likesCount,
    int? commentsCount,
    int? sharesCount,
    bool? isPinned,
    bool? isVerified,
    String? groupId,
    Map<String, dynamic>? metadata,
  }) {
    return CommunityPost(
      id: id,
      authorId: authorId,
      author: author ?? this.author,
      type: type,
      content: content ?? this.content,
      imageUrls: imageUrls ?? this.imageUrls,
      attachedData: attachedData ?? this.attachedData,
      tags: tags ?? this.tags,
      taggedUserIds: taggedUserIds ?? this.taggedUserIds,
      taggedUsers: taggedUsers ?? this.taggedUsers,
      location: location ?? this.location,
      privacy: privacy ?? this.privacy,
      createdAt: createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      likesCount: likesCount ?? this.likesCount,
      commentsCount: commentsCount ?? this.commentsCount,
      sharesCount: sharesCount ?? this.sharesCount,
      isPinned: isPinned ?? this.isPinned,
      isVerified: isVerified ?? this.isVerified,
      groupId: groupId ?? this.groupId,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Converti in Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'authorId': authorId,
      'type': type.id,
      'content': content,
      'imageUrls': imageUrls,
      'attachedData': attachedData,
      'tags': tags,
      'taggedUserIds': taggedUserIds,
      'location': location,
      'privacy': privacy.id,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'likesCount': likesCount,
      'commentsCount': commentsCount,
      'sharesCount': sharesCount,
      'isPinned': isPinned,
      'isVerified': isVerified,
      'groupId': groupId,
      'metadata': metadata,
    };
  }

  /// Crea da Map
  factory CommunityPost.fromMap(Map<String, dynamic> map) {
    return CommunityPost(
      id: map['id'] ?? '',
      authorId: map['authorId'] ?? '',
      type: PostType.values.firstWhere(
        (type) => type.id == map['type'],
        orElse: () => PostType.text,
      ),
      content: map['content'] ?? '',
      imageUrls: List<String>.from(map['imageUrls'] ?? []),
      attachedData: map['attachedData'] != null
          ? Map<String, dynamic>.from(map['attachedData'])
          : null,
      tags: List<String>.from(map['tags'] ?? []),
      taggedUserIds: List<String>.from(map['taggedUserIds'] ?? []),
      location: map['location'] != null
          ? Map<String, dynamic>.from(map['location'])
          : null,
      privacy: PostPrivacy.fromString(map['privacy'] ?? 'public'),
      createdAt: DateTime.parse(map['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: map['updatedAt'] != null
          ? DateTime.parse(map['updatedAt'])
          : null,
      likesCount: map['likesCount']?.toInt() ?? 0,
      commentsCount: map['commentsCount']?.toInt() ?? 0,
      sharesCount: map['sharesCount']?.toInt() ?? 0,
      isPinned: map['isPinned'] ?? false,
      isVerified: map['isVerified'] ?? false,
      groupId: map['groupId'],
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
    );
  }

  /// Ottieni il tempo trascorso dalla creazione
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}g fa';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h fa';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m fa';
    } else {
      return 'Ora';
    }
  }

  /// Verifica se il post è recente (meno di 24 ore)
  bool get isRecent {
    return DateTime.now().difference(createdAt).inHours < 24;
  }

  /// Verifica se il post è popolare (molti like e commenti)
  bool get isPopular {
    return likesCount >= 10 || commentsCount >= 5;
  }

  /// Ottieni il tasso di engagement
  double get engagementRate {
    final totalEngagement = likesCount + commentsCount + sharesCount;
    return totalEngagement.toDouble();
  }

  /// Verifica se il post ha allegati multimediali
  bool get hasMedia {
    return imageUrls.isNotEmpty || attachedData != null;
  }

  /// Ottieni il colore del tipo di post
  String get typeColor {
    switch (type) {
      case PostType.text:
        return '#6B7280';
      case PostType.image:
        return '#8B5CF6';
      case PostType.dietPlan:
        return '#10B981';
      case PostType.progress:
        return '#3B82F6';
      case PostType.recipe:
        return '#F59E0B';
      case PostType.achievement:
        return '#EF4444';
      case PostType.question:
        return '#06B6D4';
      case PostType.tip:
        return '#84CC16';
    }
  }

  @override
  String toString() {
    return 'CommunityPost(id: $id, type: ${type.displayName}, author: $authorId, likes: $likesCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CommunityPost && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Modello per i commenti ai post
class PostComment {
  final String id;
  final String postId;
  final String authorId;
  final CommunityUser? author;
  final String content;
  final DateTime createdAt;
  final int likesCount;
  final String? parentCommentId; // Per risposte ai commenti

  PostComment({
    required this.id,
    required this.postId,
    required this.authorId,
    this.author,
    required this.content,
    required this.createdAt,
    this.likesCount = 0,
    this.parentCommentId,
  });

  /// Crea un nuovo commento
  factory PostComment.create({
    required String postId,
    required String authorId,
    required String content,
    String? parentCommentId,
  }) {
    return PostComment(
      id: const Uuid().v4(),
      postId: postId,
      authorId: authorId,
      content: content,
      createdAt: DateTime.now(),
      parentCommentId: parentCommentId,
    );
  }

  /// Copia con modifiche
  PostComment copyWith({
    CommunityUser? author,
    String? content,
    DateTime? createdAt,
    int? likesCount,
    String? parentCommentId,
  }) {
    return PostComment(
      id: id,
      postId: postId,
      authorId: authorId,
      author: author ?? this.author,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      likesCount: likesCount ?? this.likesCount,
      parentCommentId: parentCommentId ?? this.parentCommentId,
    );
  }

  /// Converti in Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'postId': postId,
      'authorId': authorId,
      'content': content,
      'createdAt': createdAt.toIso8601String(),
      'likesCount': likesCount,
      'parentCommentId': parentCommentId,
    };
  }

  /// Crea da Map
  factory PostComment.fromMap(Map<String, dynamic> map) {
    return PostComment(
      id: map['id'] ?? '',
      postId: map['postId'] ?? '',
      authorId: map['authorId'] ?? '',
      content: map['content'] ?? '',
      createdAt: DateTime.parse(map['createdAt'] ?? DateTime.now().toIso8601String()),
      likesCount: map['likesCount']?.toInt() ?? 0,
      parentCommentId: map['parentCommentId'],
    );
  }

  /// Ottieni il tempo trascorso dalla creazione
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}g fa';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h fa';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m fa';
    } else {
      return 'Ora';
    }
  }

  /// Verifica se è una risposta a un altro commento
  bool get isReply => parentCommentId != null;
}
