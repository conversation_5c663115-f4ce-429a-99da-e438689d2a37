import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:uuid/uuid.dart';
import '../controllers/advanced_diet_controller.dart';
import '../models/advanced_user_profile.dart';
import '../models/user_profile.dart';
import '../widgets/special_conditions_selector.dart';
import '../theme/app_theme.dart';

/// Schermata per la generazione avanzata di diete personalizzate
class AdvancedDietGeneratorScreen extends StatefulWidget {
  const AdvancedDietGeneratorScreen({Key? key}) : super(key: key);

  @override
  State<AdvancedDietGeneratorScreen> createState() => _AdvancedDietGeneratorScreenState();
}

class _AdvancedDietGeneratorScreenState extends State<AdvancedDietGeneratorScreen> {
  // Stato del profilo utente avanzato
  late UserProfile _baseProfile;
  late AdvancedUserProfile _advancedProfile;

  // Stato della UI
  int _currentStep = 0;
  bool _isGenerating = false;

  @override
  void initState() {
    super.initState();
    _initializeProfiles();
  }

  /// Inizializza i profili utente
  void _initializeProfiles() {
    // Crea un profilo base di esempio
    _baseProfile = UserProfile(
      id: const Uuid().v4(),
      name: 'Utente',
      age: 35,
      gender: Gender.male,
      height: 175,
      weight: 75,
      activityLevel: ActivityLevel.moderatelyActive,
      goal: Goal.maintenance,
      mealsPerDay: 4,
    );

    // Ottieni il controller
    final controller = Provider.of<AdvancedDietController>(context, listen: false);

    // Crea un profilo avanzato basato sul profilo base
    _advancedProfile = controller.createAdvancedProfile(_baseProfile);

    // Imposta il profilo nel controller
    controller.setAdvancedProfile(_advancedProfile);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Generatore Diete Avanzato'),
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: () {
              _showHelpDialog();
            },
          ),
        ],
      ),
      body: Consumer<AdvancedDietController>(
        builder: (context, controller, child) {
          return Stepper(
            type: StepperType.vertical,
            currentStep: _currentStep,
            onStepContinue: () {
              if (_currentStep < 4) {
                setState(() {
                  _currentStep++;
                });
              } else {
                _generateDietPlan();
              }
            },
            onStepCancel: () {
              if (_currentStep > 0) {
                setState(() {
                  _currentStep--;
                });
              }
            },
            controlsBuilder: (context, details) {
              return Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: Row(
                  children: [
                    ElevatedButton(
                      onPressed: details.onStepContinue,
                      child: Text(_currentStep < 4 ? 'Continua' : 'Genera Piano'),
                    ),
                    if (_currentStep > 0) ...[
                      const SizedBox(width: 8),
                      TextButton(
                        onPressed: details.onStepCancel,
                        child: const Text('Indietro'),
                      ),
                    ],
                  ],
                ),
              );
            },
            steps: [
              // Step 1: Profilo base
              Step(
                title: const Text('Profilo Base'),
                subtitle: const Text('Informazioni generali'),
                content: _buildBaseProfileStep(),
                isActive: _currentStep >= 0,
              ),

              // Step 2: Condizioni mediche
              Step(
                title: const Text('Condizioni Mediche'),
                subtitle: const Text('Condizioni che influenzano la dieta'),
                content: _buildMedicalConditionsStep(controller),
                isActive: _currentStep >= 1,
              ),

              // Step 3: Intolleranze alimentari
              Step(
                title: const Text('Intolleranze Alimentari'),
                subtitle: const Text('Alimenti da evitare'),
                content: _buildFoodIntolerancesStep(controller),
                isActive: _currentStep >= 2,
              ),

              // Step 4: Obiettivi di fitness
              Step(
                title: const Text('Obiettivi di Fitness'),
                subtitle: const Text('Cosa vuoi ottenere'),
                content: _buildFitnessGoalsStep(controller),
                isActive: _currentStep >= 3,
              ),

              // Step 5: Attività sportiva
              Step(
                title: const Text('Attività Sportiva'),
                subtitle: const Text('Sport e intensità'),
                content: _buildSportActivityStep(controller),
                isActive: _currentStep >= 4,
              ),
            ],
          );
        },
      ),
    );
  }

  /// Costruisce lo step del profilo base
  Widget _buildBaseProfileStep() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Informazioni Personali',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        // Età
        Row(
          children: [
            const Icon(Icons.cake, color: AppTheme.primaryColor),
            const SizedBox(width: 8),
            Text('Età: ${_baseProfile.age} anni'),
          ],
        ),
        const SizedBox(height: 8),

        // Genere
        Row(
          children: [
            Icon(
              _baseProfile.gender == Gender.male ? Icons.male : Icons.female,
              color: AppTheme.primaryColor,
            ),
            const SizedBox(width: 8),
            Text('Genere: ${_baseProfile.gender == Gender.male ? 'Maschile' : 'Femminile'}'),
          ],
        ),
        const SizedBox(height: 8),

        // Altezza
        Row(
          children: [
            const Icon(Icons.height, color: AppTheme.primaryColor),
            const SizedBox(width: 8),
            Text('Altezza: ${_baseProfile.height} cm'),
          ],
        ),
        const SizedBox(height: 8),

        // Peso
        Row(
          children: [
            const Icon(Icons.monitor_weight, color: AppTheme.primaryColor),
            const SizedBox(width: 8),
            Text('Peso: ${_baseProfile.weight} kg'),
          ],
        ),
        const SizedBox(height: 8),

        // Livello di attività
        Row(
          children: [
            const Icon(Icons.directions_run, color: AppTheme.primaryColor),
            const SizedBox(width: 8),
            Text('Livello di attività: ${_getActivityLevelName(_baseProfile.activityLevel)}'),
          ],
        ),
        const SizedBox(height: 8),

        // Obiettivo
        Row(
          children: [
            const Icon(Icons.flag, color: AppTheme.primaryColor),
            const SizedBox(width: 8),
            Text('Obiettivo: ${_getGoalName(_baseProfile.goal)}'),
          ],
        ),
        const SizedBox(height: 8),

        // Pasti al giorno
        Row(
          children: [
            const Icon(Icons.restaurant, color: AppTheme.primaryColor),
            const SizedBox(width: 8),
            Text('Pasti al giorno: ${_baseProfile.mealsPerDay}'),
          ],
        ),

        const SizedBox(height: 16),
        const Divider(),
        const SizedBox(height: 8),

        // Calorie e macronutrienti calcolati
        Text(
          'Fabbisogno Calorico Base',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text('Calorie giornaliere: ${_baseProfile.calculateCalorieTarget()} kcal'),
        const SizedBox(height: 16),

        // Pulsante per modificare il profilo base
        OutlinedButton.icon(
          onPressed: () {
            // Implementazione per modificare il profilo base
          },
          icon: const Icon(Icons.edit),
          label: const Text('Modifica Profilo Base'),
        ),
      ],
    );
  }

  /// Costruisce lo step delle condizioni mediche
  Widget _buildMedicalConditionsStep(AdvancedDietController controller) {
    return MedicalConditionsSelector(
      selectedConditions: _advancedProfile.medicalConditions,
      onChanged: (conditions) {
        controller.updateMedicalConditions(conditions);
        setState(() {
          _advancedProfile = controller.advancedProfile!;
        });
      },
    );
  }

  /// Costruisce lo step delle intolleranze alimentari
  Widget _buildFoodIntolerancesStep(AdvancedDietController controller) {
    // Converti le intolleranze in enum per il selettore
    List<FoodIntolerance> intolerances = [];

    // Aggiungi le intolleranze selezionate
    for (final intolerance in _advancedProfile.foodIntolerances) {
      switch (intolerance.toLowerCase()) {
        case 'gluten':
          intolerances.add(FoodIntolerance.gluten);
          break;
        case 'lactose':
          intolerances.add(FoodIntolerance.lactose);
          break;
        case 'eggs':
          intolerances.add(FoodIntolerance.eggs);
          break;
        case 'nuts':
          intolerances.add(FoodIntolerance.nuts);
          break;
        case 'shellfish':
          intolerances.add(FoodIntolerance.shellfish);
          break;
        case 'soy':
          intolerances.add(FoodIntolerance.soy);
          break;
        case 'fish':
          intolerances.add(FoodIntolerance.fish);
          break;
        case 'fructose':
          intolerances.add(FoodIntolerance.fructose);
          break;
        case 'histamine':
          intolerances.add(FoodIntolerance.histamine);
          break;
        case 'fodmap':
          intolerances.add(FoodIntolerance.fodmap);
          break;
      }
    }

    // Se non ci sono intolleranze, aggiungi "nessuna"
    if (intolerances.isEmpty) {
      intolerances.add(FoodIntolerance.none);
    }

    return FoodIntolerancesSelector(
      selectedIntolerances: _advancedProfile.foodIntolerances,
      onChanged: (stringIntolerances) {
        // Converti le stringhe in enum FoodIntolerance
        List<FoodIntolerance> foodIntolerances = [];

        for (final intolerance in stringIntolerances) {
          switch (intolerance.toLowerCase()) {
            case 'glutine':
              foodIntolerances.add(FoodIntolerance.gluten);
              break;
            case 'lattosio':
              foodIntolerances.add(FoodIntolerance.lactose);
              break;
            case 'uova':
              foodIntolerances.add(FoodIntolerance.eggs);
              break;
            case 'frutta a guscio':
              foodIntolerances.add(FoodIntolerance.nuts);
              break;
            case 'crostacei':
              foodIntolerances.add(FoodIntolerance.shellfish);
              break;
            case 'soia':
              foodIntolerances.add(FoodIntolerance.soy);
              break;
            case 'pesce':
              foodIntolerances.add(FoodIntolerance.fish);
              break;
          }
        }

        if (foodIntolerances.isEmpty) {
          foodIntolerances.add(FoodIntolerance.none);
        }

        controller.updateFoodIntolerances(foodIntolerances);
        setState(() {
          _advancedProfile = controller.advancedProfile!;
        });
      },
    );
  }

  /// Costruisce lo step degli obiettivi di fitness
  Widget _buildFitnessGoalsStep(AdvancedDietController controller) {
    return FitnessGoalSelector(
      selectedGoal: _advancedProfile.fitnessGoal,
      onChanged: (goal) {
        controller.updateFitnessGoal(goal);
        setState(() {
          _advancedProfile = controller.advancedProfile!;
        });
      },
    );
  }

  /// Costruisce lo step dell'attività sportiva
  Widget _buildSportActivityStep(AdvancedDietController controller) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Attività Sportiva',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16.0),

            // Tipo di sport
            DropdownButtonFormField<SportType>(
              decoration: const InputDecoration(
                labelText: 'Sport Principale',
                border: OutlineInputBorder(),
              ),
              value: _advancedProfile.primarySport,
              items: SportType.values.map((sport) {
                return DropdownMenuItem<SportType>(
                  value: sport,
                  child: Text(_getSportTypeName(sport)),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  controller.updateSportInfo(primarySport: value);
                  setState(() {
                    _advancedProfile = controller.advancedProfile!;
                  });
                }
              },
            ),
            const SizedBox(height: 16.0),

            // Intensità dell'allenamento
            DropdownButtonFormField<TrainingIntensity>(
              decoration: const InputDecoration(
                labelText: 'Intensità dell\'Allenamento',
                border: OutlineInputBorder(),
              ),
              value: _advancedProfile.trainingIntensity,
              items: TrainingIntensity.values.map((intensity) {
                return DropdownMenuItem<TrainingIntensity>(
                  value: intensity,
                  child: Text(_getTrainingIntensityName(intensity)),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  controller.updateSportInfo(trainingIntensity: value);
                  setState(() {
                    _advancedProfile = controller.advancedProfile!;
                  });
                }
              },
            ),
            const SizedBox(height: 16.0),

            // Giorni di allenamento a settimana
            Row(
              children: [
                Expanded(
                  child: Text('Giorni di allenamento a settimana: ${_advancedProfile.trainingDaysPerWeek}'),
                ),
                Slider(
                  value: _advancedProfile.trainingDaysPerWeek.toDouble(),
                  min: 0,
                  max: 7,
                  divisions: 7,
                  label: _advancedProfile.trainingDaysPerWeek.toString(),
                  onChanged: (value) {
                    controller.updateSportInfo(trainingDaysPerWeek: value.round());
                    setState(() {
                      _advancedProfile = controller.advancedProfile!;
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: 16.0),

            // Minuti di allenamento per sessione
            Row(
              children: [
                Expanded(
                  child: Text('Minuti per sessione: ${_advancedProfile.trainingMinutesPerSession}'),
                ),
                Slider(
                  value: _advancedProfile.trainingMinutesPerSession.toDouble(),
                  min: 0,
                  max: 180,
                  divisions: 18,
                  label: _advancedProfile.trainingMinutesPerSession.toString(),
                  onChanged: (value) {
                    controller.updateSportInfo(trainingMinutesPerSession: value.round());
                    setState(() {
                      _advancedProfile = controller.advancedProfile!;
                    });
                  },
                ),
              ],
            ),

            const SizedBox(height: 16.0),
            const Divider(),
            const SizedBox(height: 16.0),

            // Timing dei pasti
            Text(
              'Timing dei Pasti',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8.0),

            Wrap(
              spacing: 8.0,
              runSpacing: 8.0,
              children: MealTiming.values.map((timing) {
                final isSelected = _advancedProfile.mealTiming == timing;

                return ChoiceChip(
                  label: Text(_getMealTimingName(timing)),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      controller.updateMealTiming(timing);
                      setState(() {
                        _advancedProfile = controller.advancedProfile!;
                      });
                    }
                  },
                  backgroundColor: Colors.grey[200],
                  selectedColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  /// Genera il piano dietetico
  void _generateDietPlan() async {
    setState(() {
      _isGenerating = true;
    });

    final controller = Provider.of<AdvancedDietController>(context, listen: false);

    final success = await controller.generateWeeklyDietPlan();

    if (mounted) {
      setState(() {
        _isGenerating = false;
      });

      if (success) {
        // Naviga alla schermata di visualizzazione del piano dietetico
        // Navigator.push(...);

        // Per ora, mostra un dialogo di successo
        _showSuccessDialog();
      } else {
        // Mostra un dialogo di errore
        _showErrorDialog(controller.errorMessage);
      }
    }
  }

  /// Mostra un dialogo di aiuto
  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Generatore Diete Avanzato'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Questo strumento genera piani dietetici personalizzati in base alle tue esigenze specifiche.',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 16),
              Text('Segui i passaggi per creare un piano dietetico adatto a:'),
              SizedBox(height: 8),
              Text('• Condizioni mediche specifiche'),
              Text('• Intolleranze alimentari'),
              Text('• Obiettivi di fitness'),
              Text('• Attività sportiva'),
              SizedBox(height: 16),
              Text(
                'Il piano generato terrà conto di tutte queste informazioni per creare una dieta ottimale per le tue esigenze.',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Chiudi'),
          ),
        ],
      ),
    );
  }

  /// Mostra un dialogo di successo
  void _showSuccessDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Piano Dietetico Generato'),
        content: const Text('Il tuo piano dietetico personalizzato è stato generato con successo!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Chiudi'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Naviga alla schermata di visualizzazione del piano dietetico
              // Navigator.push(...);
            },
            child: const Text('Visualizza Piano'),
          ),
        ],
      ),
    );
  }

  /// Mostra un dialogo di errore
  void _showErrorDialog(String errorMessage) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Errore'),
        content: Text('Si è verificato un errore durante la generazione del piano dietetico: $errorMessage'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Chiudi'),
          ),
        ],
      ),
    );
  }

  /// Ottieni il nome del livello di attività
  String _getActivityLevelName(ActivityLevel level) {
    switch (level) {
      case ActivityLevel.sedentary:
        return 'Sedentario';
      case ActivityLevel.lightlyActive:
        return 'Leggermente attivo';
      case ActivityLevel.moderatelyActive:
        return 'Moderatamente attivo';
      case ActivityLevel.veryActive:
        return 'Molto attivo';
      case ActivityLevel.extremelyActive:
        return 'Estremamente attivo';
    }
  }

  /// Ottieni il nome dell'obiettivo
  String _getGoalName(Goal goal) {
    switch (goal) {
      case Goal.weightLoss:
        return 'Perdita di peso';
      case Goal.maintenance:
        return 'Mantenimento';
      case Goal.weightGain:
        return 'Aumento di peso';
    }
  }

  /// Ottieni il nome del tipo di sport
  String _getSportTypeName(SportType sport) {
    switch (sport) {
      case SportType.none:
        return 'Nessuno sport';
      case SportType.running:
        return 'Corsa';
      case SportType.cycling:
        return 'Ciclismo';
      case SportType.swimming:
        return 'Nuoto';
      case SportType.weightlifting:
        return 'Sollevamento pesi';
      case SportType.yoga:
        return 'Yoga';
      case SportType.pilates:
        return 'Pilates';
      case SportType.martialArts:
        return 'Arti marziali';
      case SportType.teamSports:
        return 'Sport di squadra';
      case SportType.racquetSports:
        return 'Sport con racchetta';
      case SportType.hiking:
        return 'Escursionismo';
      case SportType.climbing:
        return 'Arrampicata';
      case SportType.crossfit:
        return 'CrossFit';
      case SportType.hiit:
        return 'HIIT';
      case SportType.dance:
        return 'Danza';
      case SportType.gymnastics:
        return 'Ginnastica';
    }
  }

  /// Ottieni il nome dell'intensità dell'allenamento
  String _getTrainingIntensityName(TrainingIntensity intensity) {
    switch (intensity) {
      case TrainingIntensity.none:
        return 'Nessun allenamento';
      case TrainingIntensity.light:
        return 'Leggero';
      case TrainingIntensity.moderate:
        return 'Moderato';
      case TrainingIntensity.vigorous:
        return 'Intenso';
      case TrainingIntensity.elite:
        return 'Elite';
    }
  }

  /// Ottieni il nome del timing dei pasti
  String _getMealTimingName(MealTiming timing) {
    switch (timing) {
      case MealTiming.standard:
        return 'Standard';
      case MealTiming.earlyBird:
        return 'Mattiniero';
      case MealTiming.nightOwl:
        return 'Notturno';
      case MealTiming.intermittentFasting:
        return 'Digiuno intermittente';
      case MealTiming.frequentSmallMeals:
        return 'Pasti piccoli e frequenti';
    }
  }
}
