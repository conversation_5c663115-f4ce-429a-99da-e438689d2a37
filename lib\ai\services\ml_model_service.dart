import 'dart:io';
import 'dart:typed_data';
import 'dart:math';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:http/http.dart' as http;

/// Servizio per la gestione dei modelli di machine learning
class MLModelService {
  static final MLModelService _instance = MLModelService._internal();

  /// Etichette del modello
  List<String>? _labels;

  /// Dimensione di input del modello
  List<int> _inputSize = [224, 224];

  /// Flag di inizializzazione
  bool _isInitialized = false;

  /// Nome del file delle etichette
  final String _labelsFileName = 'food101_labels.txt';

  /// Generatore di numeri casuali
  final Random _random = Random();

  factory MLModelService() {
    return _instance;
  }

  MLModelService._internal();

  /// Inizializza il servizio
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Carica le etichette
      await _loadLabels();

      _isInitialized = true;
      print('MLModelService inizializzato con successo');
    } catch (e) {
      print('Errore nell\'inizializzazione di MLModelService: $e');
      _isInitialized = false;
    }
  }

  /// Carica le etichette del modello
  Future<void> _loadLabels() async {
    try {
      final labelsData = await rootBundle.loadString('assets/ml_models/$_labelsFileName');
      _labels = labelsData.split('\n');
      print('Etichette caricate con successo: ${_labels!.length} classi');
    } catch (e) {
      print('Errore nel caricamento delle etichette: $e');
      throw Exception('Impossibile caricare le etichette');
    }
  }

  /// Classifica un'immagine (simulazione realistica)
  Future<List<dynamic>> classifyImage(Uint8List imageBytes) async {
    if (!_isInitialized) {
      await initialize();
    }

    if (_labels == null || _labels!.isEmpty) {
      throw Exception('Etichette non inizializzate');
    }

    // Decodifica l'immagine
    final image = img.decodeImage(imageBytes);
    if (image == null) {
      throw Exception('Impossibile decodificare l\'immagine');
    }

    // Analizza i colori dominanti nell'immagine
    final dominantColors = _analyzeDominantColors(image);

    // Simula il riconoscimento degli alimenti in base ai colori dominanti
    final results = <Map<String, dynamic>>[];

    // Determina quanti alimenti rilevare (1-3)
    final numFoods = 1 + _random.nextInt(2);

    // Mappa dei colori tipici per categoria di alimenti
    final foodColorMap = {
      // Rossi (pomodori, fragole, carne, ecc.)
      0xFF0000: ['pizza', 'strawberry_shortcake', 'beef_carpaccio', 'beef_tartare', 'tomato_sauce'],

      // Verdi (insalate, verdure, ecc.)
      0x00FF00: ['caesar_salad', 'greek_salad', 'seaweed_salad', 'edamame', 'beet_salad'],

      // Marroni (pane, carne, ecc.)
      0x8B4513: ['bread_pudding', 'chocolate_cake', 'steak', 'hamburger', 'french_toast'],

      // Gialli (pasta, patate, ecc.)
      0xFFFF00: ['french_fries', 'pancakes', 'waffles', 'garlic_bread', 'macaroni_and_cheese'],

      // Bianchi (riso, formaggio, ecc.)
      0xFFFFFF: ['risotto', 'ice_cream', 'cheese_plate', 'mashed_potatoes', 'sushi'],
    };

    // Seleziona gli alimenti in base ai colori dominanti
    for (int i = 0; i < numFoods && i < dominantColors.length; i++) {
      final color = dominantColors[i];

      // Trova il colore più simile nella mappa
      int bestMatchColor = 0;
      double bestMatchSimilarity = 0;

      for (final mapColor in foodColorMap.keys) {
        final similarity = _colorSimilarity(color.color, mapColor);
        if (similarity > bestMatchSimilarity) {
          bestMatchSimilarity = similarity;
          bestMatchColor = mapColor;
        }
      }

      // Seleziona un alimento casuale dalla categoria di colore
      final foodOptions = foodColorMap[bestMatchColor] ?? [];
      if (foodOptions.isNotEmpty) {
        final selectedFood = foodOptions[_random.nextInt(foodOptions.length)];

        // Trova l'indice dell'alimento nelle etichette
        final labelIndex = _labels!.indexOf(selectedFood);
        if (labelIndex >= 0) {
          // Genera un punteggio di confidenza realistico
          final baseConfidence = 0.7 + (_random.nextDouble() * 0.25);
          final confidence = baseConfidence * (1.0 - (i * 0.15)); // Diminuisce per alimenti successivi

          results.add({
            'label': selectedFood,
            'confidence': confidence,
          });
        }
      }
    }

    // Se non sono stati trovati alimenti, seleziona alcuni alimenti casuali
    if (results.isEmpty) {
      for (int i = 0; i < numFoods; i++) {
        final randomIndex = _random.nextInt(_labels!.length);
        final randomConfidence = 0.5 + (_random.nextDouble() * 0.3);

        results.add({
          'label': _labels![randomIndex],
          'confidence': randomConfidence,
        });
      }
    }

    return results;
  }

  /// Analizza i colori dominanti in un'immagine
  List<_ColorCount> _analyzeDominantColors(img.Image image) {
    final colorCounts = <int, int>{};

    // Campiona i pixel dell'immagine
    for (int y = 0; y < image.height; y += 10) {
      for (int x = 0; x < image.width; x += 10) {
        final pixel = image.getPixel(x, y);

        // Semplifica il colore per ridurre il numero di colori unici
        final simplifiedColor = _simplifyColor(pixel);

        // Incrementa il conteggio del colore
        colorCounts[simplifiedColor] = (colorCounts[simplifiedColor] ?? 0) + 1;
      }
    }

    // Converti in una lista di _ColorCount
    final colorCountList = colorCounts.entries
        .map((entry) => _ColorCount(entry.key, entry.value))
        .toList();

    // Ordina per conteggio decrescente
    colorCountList.sort((a, b) => b.count.compareTo(a.count));

    // Restituisci i primi 5 colori dominanti
    return colorCountList.take(5).toList();
  }

  /// Semplifica un colore riducendo la precisione
  int _simplifyColor(img.Pixel pixel) {
    final r = (pixel.r.toInt() ~/ 32) * 32;
    final g = (pixel.g.toInt() ~/ 32) * 32;
    final b = (pixel.b.toInt() ~/ 32) * 32;

    return (r << 16) | (g << 8) | b;
  }

  /// Calcola la similarità tra due colori (0-1)
  double _colorSimilarity(int color1, int color2) {
    final r1 = (color1 >> 16) & 0xFF;
    final g1 = (color1 >> 8) & 0xFF;
    final b1 = color1 & 0xFF;

    final r2 = (color2 >> 16) & 0xFF;
    final g2 = (color2 >> 8) & 0xFF;
    final b2 = color2 & 0xFF;

    final rDiff = (r1 - r2).abs();
    final gDiff = (g1 - g2).abs();
    final bDiff = (b1 - b2).abs();

    // Distanza euclidea normalizzata
    final distance = (rDiff * rDiff + gDiff * gDiff + bDiff * bDiff) / (3 * 255 * 255);

    // Converti la distanza in similarità (1 - distanza normalizzata)
    return 1.0 - distance;
  }

  /// Converti un'immagine in un tensore
  Uint8List _imageToByteListUInt8(img.Image image) {
    final result = Uint8List(_inputSize[0] * _inputSize[1] * 3);
    var index = 0;

    for (var y = 0; y < _inputSize[1]; y++) {
      for (var x = 0; x < _inputSize[0]; x++) {
        final pixel = image.getPixel(x, y);

        // Normalizza i valori RGB
        result[index++] = pixel.r.toInt();
        result[index++] = pixel.g.toInt();
        result[index++] = pixel.b.toInt();
      }
    }

    return result;
  }

  /// Verifica se il servizio è inizializzato
  bool isInitialized() {
    return _isInitialized;
  }

  /// Rilascia le risorse
  void dispose() {
    _isInitialized = false;
  }
}

/// Classe di supporto per il conteggio dei colori
class _ColorCount {
  final int color;
  final int count;

  _ColorCount(this.color, this.count);
}
