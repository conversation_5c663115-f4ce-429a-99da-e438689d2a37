import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../ai/models/food_oracle_models.dart';
import '../../theme/app_theme.dart';

/// Widget per visualizzare un alimento rilevato
class DetectedFoodItemWidget extends StatelessWidget {
  /// Alimento rilevato
  final DetectedFood detectedFood;
  
  /// Indice dell'alimento nella lista
  final int index;

  const DetectedFoodItemWidget({
    Key? key,
    required this.detectedFood,
    required this.index,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      elevation: 2.0,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Immagine dell'alimento
            _buildFoodImage(),
            
            const SizedBox(width: 12.0),
            
            // Dettagli dell'alimento
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Nome dell'alimento
                  Text(
                    detectedFood.food.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16.0,
                    ),
                  ),
                  
                  const SizedBox(height: 4.0),
                  
                  // Quantità stimata
                  Text(
                    'Quantità stimata: ${detectedFood.estimatedGrams}g',
                    style: TextStyle(
                      color: Colors.grey[600],
                    ),
                  ),
                  
                  const SizedBox(height: 4.0),
                  
                  // Valori nutrizionali
                  Text(
                    '${detectedFood.nutritionalValues.calories} kcal | '
                    'P: ${detectedFood.nutritionalValues.proteins.toStringAsFixed(1)}g | '
                    'C: ${detectedFood.nutritionalValues.carbs.toStringAsFixed(1)}g | '
                    'G: ${detectedFood.nutritionalValues.fats.toStringAsFixed(1)}g',
                    style: const TextStyle(
                      fontSize: 12.0,
                    ),
                  ),
                  
                  const SizedBox(height: 4.0),
                  
                  // Punteggio di confidenza
                  _buildConfidenceIndicator(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Costruisce l'immagine dell'alimento
  Widget _buildFoodImage() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8.0),
      child: detectedFood.food.imageUrl.isNotEmpty
          ? CachedNetworkImage(
              imageUrl: detectedFood.food.imageUrl,
              width: 80,
              height: 80,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                width: 80,
                height: 80,
                color: Colors.grey[300],
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
              errorWidget: (context, url, error) => Container(
                width: 80,
                height: 80,
                color: Colors.grey[300],
                child: const Icon(Icons.error),
              ),
            )
          : Container(
              width: 80,
              height: 80,
              color: Colors.grey[300],
              child: const Icon(Icons.fastfood),
            ),
    );
  }

  /// Costruisce l'indicatore di confidenza
  Widget _buildConfidenceIndicator() {
    // Determina il colore in base al punteggio di confidenza
    Color color;
    if (detectedFood.confidenceScore >= 0.7) {
      color = Colors.green;
    } else if (detectedFood.confidenceScore >= 0.5) {
      color = Colors.orange;
    } else {
      color = Colors.red;
    }
    
    return Row(
      children: [
        Text(
          'Confidenza: ',
          style: TextStyle(
            fontSize: 12.0,
            color: Colors.grey[600],
          ),
        ),
        Expanded(
          child: LinearProgressIndicator(
            value: detectedFood.confidenceScore,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ),
        const SizedBox(width: 8.0),
        Text(
          '${(detectedFood.confidenceScore * 100).round()}%',
          style: TextStyle(
            fontSize: 12.0,
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
