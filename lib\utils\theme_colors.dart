import 'package:flutter/material.dart';

/// Colori del tema Dr. Staffilano
class ThemeColors {
  // Colori primari Dr. Staffilano
  static const Color primaryGreen = Color(0xFF10B981); // Verde medico
  static const Color primaryGreenLight = Color(0xFF34D399);
  static const Color primaryGreenDark = Color(0xFF059669);
  
  static const Color primaryBlue = Color(0xFF3B82F6); // Blu professionale
  static const Color primaryBlueLight = Color(0xFF60A5FA);
  static const Color primaryBlueDark = Color(0xFF1D4ED8);
  
  static const Color accentGold = Color(0xFFF59E0B); // Oro accento
  static const Color accentGoldLight = Color(0xFFFBBF24);
  static const Color accentGoldDark = Color(0xFFD97706);
  
  // Colori di supporto
  static const Color backgroundLight = Color(0xFFF8FAFC);
  static const Color backgroundWhite = Color(0xFFFFFFFF);
  static const Color backgroundGray = Color(0xFFF1F5F9);
  
  static const Color textPrimary = Color(0xFF1F2937);
  static const Color textSecondary = Color(0xFF6B7280);
  static const Color textLight = Color(0xFF9CA3AF);
  
  static const Color borderLight = Color(0xFFE5E7EB);
  static const Color borderMedium = Color(0xFFD1D5DB);
  
  // Colori di stato
  static const Color success = Color(0xFF10B981);
  static const Color warning = Color(0xFFF59E0B);
  static const Color error = Color(0xFFEF4444);
  static const Color info = Color(0xFF3B82F6);
  
  // Colori per le categorie alimentari
  static const Color categoryFruit = Color(0xFFFF6B6B);
  static const Color categoryVegetable = Color(0xFF4ECDC4);
  static const Color categoryProtein = Color(0xFF45B7D1);
  static const Color categoryGrain = Color(0xFFFFA07A);
  static const Color categoryDairy = Color(0xFF98D8C8);
  static const Color categoryFat = Color(0xFFFFD93D);
  
  // Gradienti
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primaryGreen, primaryGreenLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient blueGradient = LinearGradient(
    colors: [primaryBlue, primaryBlueLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient goldGradient = LinearGradient(
    colors: [accentGold, accentGoldLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient backgroundGradient = LinearGradient(
    colors: [backgroundLight, backgroundWhite],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  // Ombre
  static List<BoxShadow> get lightShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.05),
      blurRadius: 10,
      offset: const Offset(0, 2),
    ),
  ];
  
  static List<BoxShadow> get mediumShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.1),
      blurRadius: 15,
      offset: const Offset(0, 4),
    ),
  ];
  
  static List<BoxShadow> get heavyShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.15),
      blurRadius: 20,
      offset: const Offset(0, 8),
    ),
  ];
  
  // Colori per i livelli di membership
  static const Color membershipBasic = Color(0xFF6B7280);
  static const Color membershipPremium = Color(0xFF3B82F6);
  static const Color membershipVip = Color(0xFFF59E0B);
  static const Color membershipAmbassador = Color(0xFF10B981);
  
  // Colori per le difficoltà delle sfide
  static const Color difficultyEasy = Color(0xFF10B981);
  static const Color difficultyMedium = Color(0xFFF59E0B);
  static const Color difficultyHard = Color(0xFFEF4444);
  static const Color difficultyExpert = Color(0xFF8B5CF6);
  
  // Colori per i tipi di post
  static const Color postTypeText = Color(0xFF6B7280);
  static const Color postTypeImage = Color(0xFF8B5CF6);
  static const Color postTypeDietPlan = Color(0xFF10B981);
  static const Color postTypeProgress = Color(0xFF3B82F6);
  static const Color postTypeRecipe = Color(0xFFF59E0B);
  static const Color postTypeAchievement = Color(0xFFEF4444);
  static const Color postTypeQuestion = Color(0xFF06B6D4);
  static const Color postTypeTip = Color(0xFF84CC16);
  
  // Metodi helper
  static Color getColorWithOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }
  
  static Color getMembershipColor(String membershipLevel) {
    switch (membershipLevel.toLowerCase()) {
      case 'basic':
        return membershipBasic;
      case 'premium':
        return membershipPremium;
      case 'vip':
        return membershipVip;
      case 'ambassador':
        return membershipAmbassador;
      default:
        return membershipBasic;
    }
  }
  
  static Color getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return difficultyEasy;
      case 'medium':
        return difficultyMedium;
      case 'hard':
        return difficultyHard;
      case 'expert':
        return difficultyExpert;
      default:
        return difficultyEasy;
    }
  }
  
  static Color getPostTypeColor(String postType) {
    switch (postType.toLowerCase()) {
      case 'text':
        return postTypeText;
      case 'image':
        return postTypeImage;
      case 'diet_plan':
        return postTypeDietPlan;
      case 'progress':
        return postTypeProgress;
      case 'recipe':
        return postTypeRecipe;
      case 'achievement':
        return postTypeAchievement;
      case 'question':
        return postTypeQuestion;
      case 'tip':
        return postTypeTip;
      default:
        return postTypeText;
    }
  }
  
  // Colori per il tema scuro (per supporto futuro)
  static const Color darkBackground = Color(0xFF1F2937);
  static const Color darkSurface = Color(0xFF374151);
  static const Color darkTextPrimary = Color(0xFFF9FAFB);
  static const Color darkTextSecondary = Color(0xFFD1D5DB);
  
  // Metodo per ottenere il tema appropriato
  static bool isDarkMode(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark;
  }
  
  static Color getBackgroundColor(BuildContext context) {
    return isDarkMode(context) ? darkBackground : backgroundWhite;
  }
  
  static Color getTextColor(BuildContext context) {
    return isDarkMode(context) ? darkTextPrimary : textPrimary;
  }
  
  static Color getSecondaryTextColor(BuildContext context) {
    return isDarkMode(context) ? darkTextSecondary : textSecondary;
  }
}
