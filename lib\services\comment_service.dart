import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/community_comment.dart';
import '../models/community_user.dart';

/// Servizio per gestire i commenti dei post
class CommentService extends ChangeNotifier {
  static const String _commentsKey = 'community_comments';
  static const String _postCommentsCountKey = 'post_comments_count';
  static const String _commentLikesKey = 'comment_likes';
  static const String _commentLikesCountKey = 'comment_likes_count';

  final Map<String, List<CommunityComment>> _postComments = {};
  final Map<String, int> _postCommentsCount = {};
  final Set<String> _likedComments = <String>{};
  final Map<String, int> _commentLikesCount = <String, int>{};

  /// Mappa dei commenti per post ID
  Map<String, List<CommunityComment>> get postComments => Map.unmodifiable(_postComments);

  /// Mappa dei conteggi commenti per post ID
  Map<String, int> get postCommentsCount => Map.unmodifiable(_postCommentsCount);

  /// Set dei commenti con like dall'utente corrente
  Set<String> get likedComments => Set.unmodifiable(_likedComments);

  /// Mappa dei conteggi like per commento ID
  Map<String, int> get commentLikesCount => Map.unmodifiable(_commentLikesCount);

  /// Inizializza il servizio caricando i dati salvati
  Future<void> initialize() async {
    try {
      await _loadComments();
      await _loadCommentsCount();
      await _loadCommentLikes();
      await _loadCommentLikesCount();

      if (kDebugMode) {
        print('✅ CommentService inizializzato');
        print('📝 Post con commenti: ${_postComments.length}');
        print('📊 Commenti totali: ${_getTotalCommentsCount()}');
        print('❤️ Commenti con like: ${_likedComments.length}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore inizializzazione CommentService: $e');
      }
    }
  }

  /// Carica i commenti da SharedPreferences
  Future<void> _loadComments() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final commentsJson = prefs.getString(_commentsKey);

      if (commentsJson != null) {
        final Map<String, dynamic> commentsMap = jsonDecode(commentsJson);
        _postComments.clear();

        commentsMap.forEach((postId, commentsList) {
          final List<dynamic> commentsData = commentsList;
          _postComments[postId] = commentsData
              .map((commentData) => CommunityComment.fromJson(commentData))
              .toList();
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore caricamento commenti: $e');
      }
    }
  }

  /// Carica i conteggi commenti da SharedPreferences
  Future<void> _loadCommentsCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final countJson = prefs.getString(_postCommentsCountKey);

      if (countJson != null) {
        final Map<String, dynamic> countMap = jsonDecode(countJson);
        _postCommentsCount.clear();
        _postCommentsCount.addAll(
          countMap.map((key, value) => MapEntry(key, value as int))
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore caricamento conteggi commenti: $e');
      }
    }
  }

  /// Salva i commenti in SharedPreferences
  Future<void> _saveComments() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final commentsMap = _postComments.map((postId, comments) =>
          MapEntry(postId, comments.map((comment) => comment.toJson()).toList()));
      final commentsJson = jsonEncode(commentsMap);
      await prefs.setString(_commentsKey, commentsJson);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore salvataggio commenti: $e');
      }
    }
  }

  /// Salva i conteggi commenti in SharedPreferences
  Future<void> _saveCommentsCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final countJson = jsonEncode(_postCommentsCount);
      await prefs.setString(_postCommentsCountKey, countJson);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore salvataggio conteggi commenti: $e');
      }
    }
  }

  /// Carica i like dei commenti da SharedPreferences
  Future<void> _loadCommentLikes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final likesJson = prefs.getString(_commentLikesKey);

      if (likesJson != null) {
        final List<dynamic> likesList = jsonDecode(likesJson);
        _likedComments.clear();
        _likedComments.addAll(likesList.cast<String>());
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore caricamento like commenti: $e');
      }
    }
  }

  /// Carica i conteggi like dei commenti da SharedPreferences
  Future<void> _loadCommentLikesCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final countJson = prefs.getString(_commentLikesCountKey);

      if (countJson != null) {
        final Map<String, dynamic> countMap = jsonDecode(countJson);
        _commentLikesCount.clear();
        _commentLikesCount.addAll(
          countMap.map((key, value) => MapEntry(key, value as int))
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore caricamento conteggi like commenti: $e');
      }
    }
  }

  /// Salva i like dei commenti in SharedPreferences
  Future<void> _saveCommentLikes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final likesJson = jsonEncode(_likedComments.toList());
      await prefs.setString(_commentLikesKey, likesJson);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore salvataggio like commenti: $e');
      }
    }
  }

  /// Salva i conteggi like dei commenti in SharedPreferences
  Future<void> _saveCommentLikesCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final countJson = jsonEncode(_commentLikesCount);
      await prefs.setString(_commentLikesCountKey, countJson);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore salvataggio conteggi like commenti: $e');
      }
    }
  }

  /// Ottieni i commenti per un post
  List<CommunityComment> getCommentsForPost(String postId) {
    return _postComments[postId] ?? [];
  }

  /// Ottieni il conteggio commenti per un post
  int getCommentsCount(String postId) {
    return _postCommentsCount[postId] ?? 0;
  }

  /// Verifica se un commento è stato messo like dall'utente corrente
  bool isCommentLiked(String commentId) {
    return _likedComments.contains(commentId);
  }

  /// Ottieni il conteggio like per un commento
  int getCommentLikesCount(String commentId) {
    return _commentLikesCount[commentId] ?? 0;
  }

  /// Toggle del like per un commento
  Future<bool> toggleCommentLike(String commentId, int currentLikesCount) async {
    try {
      final isCurrentlyLiked = _likedComments.contains(commentId);
      final newLikedState = !isCurrentlyLiked;

      if (newLikedState) {
        // Aggiungi like
        _likedComments.add(commentId);
        _commentLikesCount[commentId] = currentLikesCount + 1;
      } else {
        // Rimuovi like
        _likedComments.remove(commentId);
        _commentLikesCount[commentId] = (currentLikesCount - 1).clamp(0, double.infinity).toInt();
      }

      // Salva i cambiamenti
      await _saveCommentLikes();
      await _saveCommentLikesCount();

      // Notifica i listener
      notifyListeners();

      if (kDebugMode) {
        print('${newLikedState ? '❤️' : '💔'} Like commento ${newLikedState ? 'aggiunto' : 'rimosso'} per $commentId');
        print('📊 Nuovo conteggio like: ${_commentLikesCount[commentId]}');
      }

      return newLikedState;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore toggle like commento: $e');
      }
      rethrow;
    }
  }

  /// Inizializza il conteggio like per un commento (se non esiste)
  void initializeCommentLikesCount(String commentId, int initialCount) {
    if (!_commentLikesCount.containsKey(commentId)) {
      _commentLikesCount[commentId] = initialCount;
      _saveCommentLikesCount();
    }
  }

  /// Aggiungi un commento a un post
  Future<CommunityComment> addComment({
    required String postId,
    required CommunityUser author,
    required String content,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // Crea il nuovo commento
      final comment = CommunityComment.create(
        postId: postId,
        author: author,
        content: content,
        metadata: metadata,
      );

      // Aggiungi alla lista dei commenti del post
      if (!_postComments.containsKey(postId)) {
        _postComments[postId] = [];
      }
      _postComments[postId]!.add(comment);

      // Aggiorna il conteggio
      _postCommentsCount[postId] = _postComments[postId]!.length;

      // Salva i dati
      await _saveComments();
      await _saveCommentsCount();

      // Notifica i listener
      notifyListeners();

      if (kDebugMode) {
        print('💬 Commento aggiunto al post $postId');
        print('📊 Nuovo conteggio commenti: ${_postCommentsCount[postId]}');
      }

      return comment;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore aggiunta commento: $e');
      }
      rethrow;
    }
  }

  /// Rimuovi un commento
  Future<bool> removeComment(String postId, String commentId) async {
    try {
      if (!_postComments.containsKey(postId)) return false;

      final comments = _postComments[postId]!;
      final commentIndex = comments.indexWhere((c) => c.id == commentId);

      if (commentIndex == -1) return false;

      // Rimuovi il commento
      comments.removeAt(commentIndex);

      // Aggiorna il conteggio
      _postCommentsCount[postId] = comments.length;

      // Se non ci sono più commenti, rimuovi la chiave
      if (comments.isEmpty) {
        _postComments.remove(postId);
        _postCommentsCount.remove(postId);
      }

      // Salva i dati
      await _saveComments();
      await _saveCommentsCount();

      // Notifica i listener
      notifyListeners();

      if (kDebugMode) {
        print('🗑️ Commento rimosso dal post $postId');
        print('📊 Nuovo conteggio commenti: ${_postCommentsCount[postId] ?? 0}');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore rimozione commento: $e');
      }
      return false;
    }
  }

  /// Modifica un commento
  Future<CommunityComment?> editComment(
    String postId,
    String commentId,
    String newContent,
  ) async {
    try {
      if (!_postComments.containsKey(postId)) return null;

      final comments = _postComments[postId]!;
      final commentIndex = comments.indexWhere((c) => c.id == commentId);

      if (commentIndex == -1) return null;

      // Modifica il commento
      final updatedComment = comments[commentIndex].markAsEdited(newContent);
      comments[commentIndex] = updatedComment;

      // Salva i dati
      await _saveComments();

      // Notifica i listener
      notifyListeners();

      if (kDebugMode) {
        print('✏️ Commento modificato nel post $postId');
      }

      return updatedComment;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore modifica commento: $e');
      }
      return null;
    }
  }

  /// Inizializza il conteggio commenti per un post (se non esiste)
  void initializePostCommentsCount(String postId, int initialCount) {
    if (!_postCommentsCount.containsKey(postId)) {
      _postCommentsCount[postId] = initialCount;
      _saveCommentsCount();
    }
  }

  /// Ottieni il numero totale di commenti
  int _getTotalCommentsCount() {
    return _postComments.values.fold(0, (sum, comments) => sum + comments.length);
  }

  /// Pulisci tutti i commenti
  Future<void> clearAllComments() async {
    try {
      _postComments.clear();
      _postCommentsCount.clear();
      _likedComments.clear();
      _commentLikesCount.clear();

      await _saveComments();
      await _saveCommentsCount();
      await _saveCommentLikes();
      await _saveCommentLikesCount();

      notifyListeners();

      if (kDebugMode) {
        print('🧹 Tutti i commenti e like puliti');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore pulizia commenti: $e');
      }
    }
  }

  /// Ottieni statistiche dei commenti
  Map<String, dynamic> getCommentsStats() {
    final totalComments = _getTotalCommentsCount();
    final postsWithComments = _postComments.length;
    final averageCommentsPerPost = postsWithComments > 0
        ? totalComments / postsWithComments
        : 0.0;

    return {
      'totalComments': totalComments,
      'postsWithComments': postsWithComments,
      'averageCommentsPerPost': averageCommentsPerPost,
      'mostCommentedPostId': _getMostCommentedPostId(),
    };
  }

  /// Ottieni l'ID del post con più commenti
  String? _getMostCommentedPostId() {
    if (_postCommentsCount.isEmpty) return null;

    return _postCommentsCount.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }
}
