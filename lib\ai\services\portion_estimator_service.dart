import 'dart:math';
import 'package:image/image.dart' as img;
import '../../models/food.dart';
import '../models/food_oracle_models.dart';

/// Servizio per la stima delle porzioni degli alimenti
class PortionEstimatorService {
  static final PortionEstimatorService _instance = PortionEstimatorService._internal();

  factory PortionEstimatorService() {
    return _instance;
  }

  PortionEstimatorService._internal();

  /// Stima la porzione di un alimento in base alla sua area nell'immagine
  /// [food] - L'alimento da stimare
  /// [boundingBox] - Il riquadro di delimitazione dell'alimento nell'immagine
  /// [imageWidth] - La larghezza dell'immagine
  /// [imageHeight] - L'altezza dell'immagine
  /// Restituisce la quantità stimata in grammi
  int estimatePortion(
    Food food,
    BoundingBox boundingBox,
    int imageWidth,
    int imageHeight,
  ) {
    // Calcola l'area relativa dell'alimento nell'immagine
    final relativeArea = boundingBox.width * boundingBox.height;
    
    // Stima la porzione in base alla categoria dell'alimento e all'area relativa
    return _estimatePortionByCategory(food, relativeArea);
  }

  /// Stima la porzione in base alla categoria dell'alimento e all'area relativa
  int _estimatePortionByCategory(Food food, double relativeArea) {
    // Porzioni di riferimento per categoria (in grammi)
    final referencePortions = {
      FoodCategory.fruit: 150,
      FoodCategory.vegetable: 100,
      FoodCategory.grain: 80,
      FoodCategory.protein: 120,
      FoodCategory.dairy: 200,
      FoodCategory.fat: 15,
      FoodCategory.beverage: 250,
      FoodCategory.other: 100,
    };
    
    // Fattori di scala per categoria
    final scaleFactors = {
      FoodCategory.fruit: 5.0,
      FoodCategory.vegetable: 4.0,
      FoodCategory.grain: 6.0,
      FoodCategory.protein: 7.0,
      FoodCategory.dairy: 3.0,
      FoodCategory.fat: 10.0,
      FoodCategory.beverage: 2.0,
      FoodCategory.other: 5.0,
    };
    
    // Trova la categoria principale dell'alimento
    FoodCategory mainCategory = FoodCategory.other;
    for (final category in food.categories) {
      if (referencePortions.containsKey(category)) {
        mainCategory = category;
        break;
      }
    }
    
    // Ottieni la porzione di riferimento e il fattore di scala
    final referencePortion = referencePortions[mainCategory] ?? 100;
    final scaleFactor = scaleFactors[mainCategory] ?? 5.0;
    
    // Calcola la porzione stimata
    // Formula: porzione = porzione di riferimento * (area relativa * fattore di scala)
    final estimatedPortion = (referencePortion * relativeArea * scaleFactor).round();
    
    // Limita la porzione a valori ragionevoli per la categoria
    return _clampPortionSize(estimatedPortion, mainCategory);
  }

  /// Limita la porzione a valori ragionevoli per la categoria
  int _clampPortionSize(int portion, FoodCategory category) {
    // Limiti minimi e massimi per categoria (in grammi)
    final minPortions = {
      FoodCategory.fruit: 50,
      FoodCategory.vegetable: 30,
      FoodCategory.grain: 30,
      FoodCategory.protein: 50,
      FoodCategory.dairy: 50,
      FoodCategory.fat: 5,
      FoodCategory.beverage: 100,
      FoodCategory.other: 30,
    };
    
    final maxPortions = {
      FoodCategory.fruit: 300,
      FoodCategory.vegetable: 250,
      FoodCategory.grain: 200,
      FoodCategory.protein: 250,
      FoodCategory.dairy: 400,
      FoodCategory.fat: 50,
      FoodCategory.beverage: 500,
      FoodCategory.other: 300,
    };
    
    // Ottieni i limiti per la categoria
    final minPortion = minPortions[category] ?? 30;
    final maxPortion = maxPortions[category] ?? 300;
    
    // Limita la porzione
    return portion.clamp(minPortion, maxPortion);
  }

  /// Stima la porzione di un alimento in base alla densità e al volume
  /// [food] - L'alimento da stimare
  /// [volumeInCubicCm] - Il volume stimato in centimetri cubi
  /// Restituisce la quantità stimata in grammi
  int estimatePortionByVolume(Food food, double volumeInCubicCm) {
    // Densità tipiche per categoria (g/cm³)
    final typicalDensities = {
      FoodCategory.fruit: 0.8,
      FoodCategory.vegetable: 0.6,
      FoodCategory.grain: 0.7,
      FoodCategory.protein: 1.0,
      FoodCategory.dairy: 1.0,
      FoodCategory.fat: 0.9,
      FoodCategory.beverage: 1.0,
      FoodCategory.other: 0.8,
    };
    
    // Trova la categoria principale dell'alimento
    FoodCategory mainCategory = FoodCategory.other;
    for (final category in food.categories) {
      if (typicalDensities.containsKey(category)) {
        mainCategory = category;
        break;
      }
    }
    
    // Ottieni la densità tipica per la categoria
    final density = typicalDensities[mainCategory] ?? 0.8;
    
    // Calcola la massa stimata (massa = densità * volume)
    final estimatedMass = (density * volumeInCubicCm).round();
    
    // Limita la massa a valori ragionevoli
    return _clampPortionSize(estimatedMass, mainCategory);
  }

  /// Stima il volume di un alimento in base alla sua area nell'immagine
  /// [boundingBox] - Il riquadro di delimitazione dell'alimento nell'immagine
  /// [imageWidth] - La larghezza dell'immagine
  /// [imageHeight] - L'altezza dell'immagine
  /// [depthEstimate] - La stima della profondità (se disponibile)
  /// Restituisce il volume stimato in centimetri cubi
  double estimateVolume(
    BoundingBox boundingBox,
    int imageWidth,
    int imageHeight,
    {double? depthEstimate}
  ) {
    // Calcola l'area in pixel
    final areaInPixels = boundingBox.width * boundingBox.height * imageWidth * imageHeight;
    
    // Converti l'area in centimetri quadrati (assumendo una scala arbitraria)
    final areaInCmSquared = areaInPixels / 10000;
    
    // Stima la profondità se non fornita
    final depth = depthEstimate ?? sqrt(areaInCmSquared) / 2;
    
    // Calcola il volume (area * profondità)
    return areaInCmSquared * depth;
  }

  /// Corregge la stima della porzione in base al feedback dell'utente
  /// [originalEstimate] - La stima originale in grammi
  /// [userEstimate] - La stima dell'utente in grammi
  /// [food] - L'alimento
  /// Restituisce un fattore di correzione per migliorare le stime future
  double calculateCorrectionFactor(
    int originalEstimate,
    int userEstimate,
    Food food
  ) {
    // Calcola il rapporto tra la stima dell'utente e quella originale
    final ratio = userEstimate / originalEstimate;
    
    // Limita il fattore di correzione a valori ragionevoli
    return ratio.clamp(0.5, 2.0);
  }

  /// Stima la porzione di un alimento in base a un riferimento dimensionale
  /// [food] - L'alimento da stimare
  /// [boundingBox] - Il riquadro di delimitazione dell'alimento nell'immagine
  /// [referenceObject] - Il riquadro di delimitazione dell'oggetto di riferimento
  /// [referenceSize] - La dimensione nota dell'oggetto di riferimento in cm
  /// Restituisce la quantità stimata in grammi
  int estimatePortionWithReference(
    Food food,
    BoundingBox boundingBox,
    BoundingBox referenceObject,
    double referenceSize
  ) {
    // Calcola il rapporto di scala tra l'alimento e l'oggetto di riferimento
    final foodArea = boundingBox.width * boundingBox.height;
    final referenceArea = referenceObject.width * referenceObject.height;
    
    final scaleRatio = foodArea / referenceArea;
    
    // Stima il volume dell'alimento
    final estimatedVolume = scaleRatio * (referenceSize * referenceSize * referenceSize);
    
    // Stima la porzione in base al volume
    return estimatePortionByVolume(food, estimatedVolume);
  }
}
