import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/dr_staffilano_theme.dart';
import '../utils/image_placeholder_helper.dart';
import '../controllers/welljourney_controller.dart';

/// Widget per visualizzare un'intestazione con saluto personalizzato all'utente
class UserGreetingHeader extends StatelessWidget {
  final String userName;
  final String? userAvatarUrl;
  final VoidCallback? onAvatarTap;

  const UserGreetingHeader({
    Key? key,
    required this.userName,
    this.userAvatarUrl,
    this.onAvatarTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: DrStaffilanoTheme.primaryGradient,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Ciao,',
                  style: const TextStyle(
                    fontSize: 16,
                    color: DrStaffilanoTheme.textOnPrimary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  userName,
                  style: const TextStyle(
                    fontSize: 24,
                    color: DrStaffilanoTheme.textOnPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: DrStaffilanoTheme.accentGold.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'Dr. Staffilano Nutrition',
                        style: TextStyle(
                          fontSize: 12,
                          color: DrStaffilanoTheme.textOnPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Consumer<WellJourneyController>(
                      builder: (context, controller, child) {
                        final points = controller.userProgress?.totalPoints ?? 0;
                        final level = controller.userProgress?.level ?? 1;

                        return Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.stars_rounded,
                                color: DrStaffilanoTheme.textOnPrimary,
                                size: 14,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '$points',
                                style: const TextStyle(
                                  fontSize: 11,
                                  color: DrStaffilanoTheme.textOnPrimary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: onAvatarTap,
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: DrStaffilanoTheme.backgroundWhite,
                border: Border.all(
                  color: DrStaffilanoTheme.accentGold,
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: DrStaffilanoTheme.shadowMedium,
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  userName.isNotEmpty ? userName[0].toUpperCase() : 'U',
                  style: const TextStyle(
                    fontSize: 24,
                    color: DrStaffilanoTheme.primaryGreen,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
