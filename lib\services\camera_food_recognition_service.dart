import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import 'package:uuid/uuid.dart';
import 'package:path/path.dart' as path;
import '../models/food.dart';
import '../models/food_recognition_result.dart';
import '../data/food_database.dart';
import 'food_safety_service.dart';

/// Servizio per il riconoscimento alimentare tramite fotocamera
/// Implementa l'AI Food Oracle del Dr. Staffilano
class CameraFoodRecognitionService {
  static const String _analysisDirectory = 'food_oracle_analyses';
  final FoodDatabase _foodDatabase = FoodDatabase();
  final Uuid _uuid = const Uuid();

  // Singleton
  static CameraFoodRecognitionService? _instance;

  static CameraFoodRecognitionService getInstance() {
    _instance ??= CameraFoodRecognitionService._();
    return _instance!;
  }

  CameraFoodRecognitionService._();

  /// Analizza un'immagine di cibo e restituisce i risultati del riconoscimento
  Future<FoodRecognitionResult> analyzeFood({
    required File imageFile,
    required String mealType,
    String? userId,
  }) async {
    try {
      print('🔍 Avvio analisi Food Oracle per ${path.basename(imageFile.path)}');

      // Preprocessa l'immagine
      final processedImage = await _preprocessImage(imageFile);

      // Simula il riconoscimento AI (in una implementazione reale useremmo TensorFlow Lite o ML Kit)
      final recognitionResults = await _performFoodRecognition(processedImage);

      // Filtra i risultati per sicurezza alimentare
      final safeResults = _filterSafeResults(recognitionResults);

      // Calcola i valori nutrizionali totali
      final nutritionalSummary = _calculateNutritionalSummary(safeResults);

      // Genera suggerimenti personalizzati
      final suggestions = _generatePersonalizedSuggestions(safeResults, mealType);

      // Salva l'analisi
      final analysisId = _uuid.v4();
      final savedImagePath = await _saveAnalysisImage(processedImage, analysisId, imageFile.path);

      final result = FoodRecognitionResult(
        id: analysisId,
        timestamp: DateTime.now(),
        imagePath: savedImagePath,
        mealType: mealType,
        recognizedFoods: safeResults,
        nutritionalSummary: nutritionalSummary,
        suggestions: suggestions,
        confidenceScore: _calculateOverallConfidence(safeResults),
        userId: userId,
      );

      print('✅ Analisi Food Oracle completata: ${safeResults.length} alimenti riconosciuti');
      return result;

    } catch (e) {
      print('❌ Errore nell\'analisi Food Oracle: $e');
      rethrow;
    }
  }

  /// Preprocessa l'immagine per migliorare il riconoscimento
  Future<img.Image> _preprocessImage(File imageFile) async {
    final bytes = await imageFile.readAsBytes();
    final image = img.decodeImage(bytes);

    if (image == null) {
      throw Exception('Impossibile decodificare l\'immagine');
    }

    // Ridimensiona l'immagine per ottimizzare le prestazioni
    final resized = img.copyResize(image, width: 512, height: 512);

    // Migliora il contrasto e la luminosità
    final enhanced = img.adjustColor(resized,
      contrast: 1.1,
      brightness: 1.05,
      saturation: 1.1
    );

    return enhanced;
  }

  /// Simula il riconoscimento alimentare AI
  Future<List<RecognizedFood>> _performFoodRecognition(img.Image image) async {
    // Simula il tempo di elaborazione dell'AI
    await Future.delayed(const Duration(milliseconds: 1500));

    // Ottieni tutti gli alimenti dal database
    final allFoods = await _foodDatabase.getAllFoods();
    final safeFoods = FoodSafetyService.filterSafeFoods(allFoods);

    // Simula il riconoscimento basato su caratteristiche dell'immagine
    final recognizedFoods = <RecognizedFood>[];

    // Analizza i pixel per determinare i colori dominanti
    final colorAnalysis = _analyzeImageColors(image);

    // Seleziona alimenti basati sui colori rilevati
    for (final colorCategory in colorAnalysis.keys) {
      final confidence = colorAnalysis[colorCategory]!;
      final categoryFoods = _getFoodsByColorCategory(safeFoods, colorCategory);

      if (categoryFoods.isNotEmpty && confidence > 0.3) {
        // Seleziona un alimento rappresentativo dalla categoria
        final selectedFood = categoryFoods.first;

        // Stima la porzione basata sull'area del colore
        final estimatedGrams = _estimatePortionSize(confidence);

        recognizedFoods.add(RecognizedFood(
          food: selectedFood,
          confidence: confidence,
          estimatedGrams: estimatedGrams,
          boundingBox: _generateBoundingBox(confidence),
        ));
      }
    }

    // Assicurati di avere almeno un risultato
    if (recognizedFoods.isEmpty && safeFoods.isNotEmpty) {
      recognizedFoods.add(RecognizedFood(
        food: safeFoods.first,
        confidence: 0.7,
        estimatedGrams: 100,
        boundingBox: const BoundingBox(x: 0.2, y: 0.2, width: 0.6, height: 0.6),
      ));
    }

    return recognizedFoods;
  }

  /// Analizza i colori dominanti nell'immagine
  Map<String, double> _analyzeImageColors(img.Image image) {
    final colorCounts = <String, int>{};
    final totalPixels = image.width * image.height;

    // Campiona i pixel dell'immagine
    for (int y = 0; y < image.height; y += 10) {
      for (int x = 0; x < image.width; x += 10) {
        final pixel = image.getPixel(x, y);
        final red = pixel.r.toInt();
        final green = pixel.g.toInt();
        final blue = pixel.b.toInt();

        final colorCategory = _categorizeColor(red, green, blue);
        colorCounts[colorCategory] = (colorCounts[colorCategory] ?? 0) + 1;
      }
    }

    // Converti in percentuali
    final colorPercentages = <String, double>{};
    colorCounts.forEach((color, count) {
      colorPercentages[color] = count / (totalPixels / 100);
    });

    return colorPercentages;
  }

  /// Categorizza un colore RGB
  String _categorizeColor(int red, int green, int blue) {
    // Verde (verdure)
    if (green > red && green > blue && green > 100) {
      return 'green';
    }
    // Rosso (pomodori, carne)
    if (red > green && red > blue && red > 100) {
      return 'red';
    }
    // Giallo/Arancione (frutta, cereali)
    if ((red + green) > blue * 1.5 && red > 100) {
      return 'yellow_orange';
    }
    // Marrone (carne cotta, cereali)
    if (red > 80 && green > 60 && blue < 80) {
      return 'brown';
    }
    // Bianco (latticini, cereali)
    if (red > 200 && green > 200 && blue > 200) {
      return 'white';
    }

    return 'other';
  }

  /// Ottiene alimenti per categoria di colore
  List<Food> _getFoodsByColorCategory(List<Food> foods, String colorCategory) {
    switch (colorCategory) {
      case 'green':
        return foods.where((f) =>
          f.categories.contains(FoodCategory.vegetable) &&
          (f.tags.contains('verde') || f.tags.contains('foglia') || f.name.toLowerCase().contains('spinaci'))
        ).toList();
      case 'red':
        return foods.where((f) =>
          f.name.toLowerCase().contains('pomodor') ||
          f.name.toLowerCase().contains('peperone') ||
          (f.categories.contains(FoodCategory.protein) && f.tags.contains('carne'))
        ).toList();
      case 'yellow_orange':
        return foods.where((f) =>
          f.categories.contains(FoodCategory.fruit) ||
          f.categories.contains(FoodCategory.grain)
        ).toList();
      case 'brown':
        return foods.where((f) =>
          f.categories.contains(FoodCategory.protein) ||
          f.categories.contains(FoodCategory.grain)
        ).toList();
      case 'white':
        return foods.where((f) =>
          f.categories.contains(FoodCategory.dairy) ||
          f.categories.contains(FoodCategory.grain)
        ).toList();
      default:
        return foods.take(3).toList();
    }
  }

  /// Stima la dimensione della porzione
  int _estimatePortionSize(double confidence) {
    // Porzione base di 100g, modulata dalla confidenza
    final baseGrams = 100;
    final multiplier = 0.5 + (confidence * 1.5); // Range: 0.5 - 2.0
    return (baseGrams * multiplier).round().clamp(50, 300);
  }

  /// Genera un bounding box simulato
  BoundingBox _generateBoundingBox(double confidence) {
    final size = 0.3 + (confidence * 0.4); // Dimensione basata sulla confidenza
    final x = (1.0 - size) / 2; // Centrato
    final y = (1.0 - size) / 2;

    return BoundingBox(x: x, y: y, width: size, height: size);
  }

  /// Filtra i risultati per sicurezza alimentare
  List<RecognizedFood> _filterSafeResults(List<RecognizedFood> results) {
    return results.where((result) =>
      FoodSafetyService.isFoodSafe(result.food)
    ).toList();
  }

  /// Calcola il riepilogo nutrizionale
  NutritionalSummary _calculateNutritionalSummary(List<RecognizedFood> foods) {
    double totalCalories = 0;
    double totalProteins = 0;
    double totalCarbs = 0;
    double totalFats = 0;
    double totalFiber = 0;

    for (final recognizedFood in foods) {
      final factor = recognizedFood.estimatedGrams / 100.0;
      totalCalories += recognizedFood.food.calories * factor;
      totalProteins += recognizedFood.food.proteins * factor;
      totalCarbs += recognizedFood.food.carbs * factor;
      totalFats += recognizedFood.food.fats * factor;
      totalFiber += recognizedFood.food.fiber * factor;
    }

    return NutritionalSummary(
      calories: totalCalories.round(),
      proteins: totalProteins,
      carbs: totalCarbs,
      fats: totalFats,
      fiber: totalFiber,
    );
  }

  /// Genera suggerimenti personalizzati
  List<String> _generatePersonalizedSuggestions(List<RecognizedFood> foods, String mealType) {
    final suggestions = <String>[];

    if (foods.isEmpty) {
      suggestions.add('Non sono riuscito a riconoscere alimenti nell\'immagine. Prova con una foto più chiara.');
      return suggestions;
    }

    // Analizza la composizione del pasto
    final hasProtein = foods.any((f) => f.food.categories.contains(FoodCategory.protein));
    final hasVegetables = foods.any((f) => f.food.categories.contains(FoodCategory.vegetable));
    final hasCarbs = foods.any((f) => f.food.categories.contains(FoodCategory.grain));

    // Suggerimenti basati sulla completezza del pasto
    if (!hasProtein && mealType != 'snack') {
      suggestions.add('💪 Considera di aggiungere una fonte di proteine per un pasto più bilanciato.');
    }

    if (!hasVegetables) {
      suggestions.add('🥬 Aggiungi delle verdure per aumentare l\'apporto di vitamine e fibre.');
    }

    if (!hasCarbs && (mealType == 'breakfast' || mealType == 'lunch')) {
      suggestions.add('🌾 Una fonte di carboidrati complessi potrebbe fornire energia duratura.');
    }

    // Suggerimenti specifici per tipo di pasto
    switch (mealType) {
      case 'breakfast':
        suggestions.add('🌅 Ottima scelta per iniziare la giornata! Ricorda di idratarti bene.');
        break;
      case 'lunch':
        suggestions.add('🍽️ Pasto equilibrato per mantenere l\'energia nel pomeriggio.');
        break;
      case 'dinner':
        suggestions.add('🌙 Perfetto per la cena. Evita pasti troppo pesanti prima di dormire.');
        break;
      case 'snack':
        suggestions.add('🍎 Spuntino sano per mantenere stabile la glicemia.');
        break;
    }

    return suggestions;
  }

  /// Calcola la confidenza complessiva
  double _calculateOverallConfidence(List<RecognizedFood> foods) {
    if (foods.isEmpty) return 0.0;

    final totalConfidence = foods.fold(0.0, (sum, food) => sum + food.confidence);
    return totalConfidence / foods.length;
  }

  /// Salva l'immagine dell'analisi
  Future<String> _saveAnalysisImage(img.Image image, String analysisId, String originalPath) async {
    // Per ora, restituisci sempre il percorso originale dell'immagine
    // In futuro si può implementare il salvataggio su mobile
    return originalPath;
  }
}
