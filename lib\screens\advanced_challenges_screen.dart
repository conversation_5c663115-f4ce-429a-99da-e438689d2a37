import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import '../models/welljourney_models.dart';
import '../controllers/advanced_challenges_controller.dart';
import '../theme/dr_staffilano_theme.dart';
import '../widgets/premium_challenge_card.dart';
import '../widgets/challenge_filter_bar.dart';
import '../widgets/challenge_statistics_widget.dart';
import '../widgets/floating_action_button_dr_staffilano.dart';

/// Schermata avanzata delle sfide Dr. Staffilano con design premium
class AdvancedChallengesScreen extends StatefulWidget {
  const AdvancedChallengesScreen({Key? key}) : super(key: key);

  @override
  State<AdvancedChallengesScreen> createState() => _AdvancedChallengesScreenState();
}

class _AdvancedChallengesScreenState extends State<AdvancedChallengesScreen>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {

  late TabController _tabController;
  late AnimationController _animationController;
  late AnimationController _backgroundAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _showStatistics = false;
  bool _isDarkMode = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Background animation for particle effects
    _backgroundAnimation = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _initializeController();
    _animationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    _backgroundAnimation.dispose();
    super.dispose();
  }

  /// Inizializza il controller
  Future<void> _initializeController() async {
    try {
      final controller = context.read<AdvancedChallengesController>();
      await controller.initialize();
    } catch (e) {
      print('❌ Errore nell\'inizializzazione controller sfide: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Consumer<AdvancedChallengesController>(
      builder: (context, controller, child) {
        if (!controller.isInitialized) {
          return _buildLoadingState();
        }

        if (controller.errorMessage != null) {
          return _buildErrorState(controller.errorMessage!);
        }

        return _buildMainContent(controller);
      },
    );
  }

  /// Stato di caricamento
  Widget _buildLoadingState() {
    return Scaffold(
      backgroundColor: _isDarkMode ? DrStaffilanoTheme.backgroundDark : DrStaffilanoTheme.backgroundLight,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: DrStaffilanoTheme.primaryGradient,
                borderRadius: BorderRadius.circular(40),
              ),
              child: const Center(
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 3,
                ),
              ),
            ).animate(onPlay: (controller) => controller.repeat())
                .shimmer(duration: 1500.ms)
                .scale(begin: const Offset(0.8, 0.8), end: const Offset(1.0, 1.0)),
            const SizedBox(height: 24),
            Text(
              'Caricamento Sfide Dr. Staffilano...',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
              ),
            ).animate().fadeIn(delay: 300.ms),
            const SizedBox(height: 8),
            Text(
              'Preparazione delle tue sfide personalizzate',
              style: TextStyle(
                fontSize: 14,
                color: _isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
              ),
            ).animate().fadeIn(delay: 600.ms),
          ],
        ),
      ),
    );
  }

  /// Stato di errore
  Widget _buildErrorState(String error) {
    return Scaffold(
      backgroundColor: _isDarkMode ? DrStaffilanoTheme.backgroundDark : DrStaffilanoTheme.backgroundLight,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(40),
                ),
                child: const Icon(
                  Icons.error_outline,
                  size: 40,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'Errore nel Caricamento',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                error,
                style: TextStyle(
                  fontSize: 14,
                  color: _isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _initializeController,
                icon: const Icon(Icons.refresh),
                label: const Text('Riprova'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: DrStaffilanoTheme.primaryGreen,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Contenuto principale con collapsing AppBar
  Widget _buildMainContent(AdvancedChallengesController controller) {
    return Scaffold(
      backgroundColor: _isDarkMode ? DrStaffilanoTheme.backgroundDark : DrStaffilanoTheme.backgroundLight,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: NestedScrollView(
            physics: const BouncingScrollPhysics(),
            headerSliverBuilder: (context, innerBoxIsScrolled) => [
              _buildCollapsingSliverAppBar(controller),
            ],
            body: Column(
              children: [
                if (_showStatistics) _buildStatisticsSection(controller),
                _buildFilterBar(controller),
                Expanded(
                  child: _buildTabContent(controller),
                ),
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(controller),
    );
  }

  /// Collapsing SliverAppBar per le sfide
  Widget _buildCollapsingSliverAppBar(AdvancedChallengesController controller) {
    return SliverAppBar(
      backgroundColor: Colors.transparent,
      foregroundColor: Colors.white,
      expandedHeight: 230, // Aumentato per dare più spazio al titolo
      floating: true, // Enables floating behavior
      pinned: false, // Allows complete hiding
      snap: true, // Smooth snap animations
      flexibleSpace: FlexibleSpaceBar(
        centerTitle: true,
        titlePadding: const EdgeInsets.only(bottom: 160), // Increased spacing to avoid overlap with statistics
        title: const Text(
          'Sfide Dr. Staffilano',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
            color: Colors.white,
            shadows: [
              Shadow(
                offset: Offset(0, 1),
                blurRadius: 3,
                color: Colors.black54,
              ),
            ],
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: DrStaffilanoTheme.primaryGradient,
            boxShadow: [
              BoxShadow(
                color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Stack(
            children: [
              // Title-focused particles positioned around the title area
              ...List.generate(12, (index) => _buildChallengesTitleParticle(index)),
              SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Column(
                    children: [
                      const SizedBox(height: 90), // Increased spacing to avoid title overlap with statistics
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Icon(
                              FontAwesomeIcons.trophy,
                              color: Colors.white,
                              size: 18,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              '${controller.activeChallenges.length} attive • ${controller.availableChallenges.length} disponibili',
                              style: TextStyle(
                                fontSize: 13,
                                color: Colors.white.withOpacity(0.9),
                              ),
                            ),
                          ),
                          IconButton(
                            onPressed: () {
                              setState(() {
                                _showStatistics = !_showStatistics;
                              });
                              HapticFeedback.lightImpact();
                            },
                            icon: Icon(
                              _showStatistics ? Icons.analytics : Icons.analytics_outlined,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                          IconButton(
                            onPressed: () {
                              setState(() {
                                _isDarkMode = !_isDarkMode;
                              });
                              HapticFeedback.lightImpact();
                            },
                            icon: Icon(
                              _isDarkMode ? Icons.light_mode : Icons.dark_mode,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      bottom: _buildChallengesTabBar(),
    );
  }

  /// TabBar per le sfide con styling migliorato
  PreferredSizeWidget _buildChallengesTabBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(kToolbarHeight),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              DrStaffilanoTheme.primaryGreen.withOpacity(0.95),
              DrStaffilanoTheme.secondaryBlue.withOpacity(0.85),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: TabBar(
          controller: _tabController,
          indicatorColor: DrStaffilanoTheme.accentGold,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white.withOpacity(0.7),
          indicatorWeight: 3,
          labelStyle: const TextStyle(fontWeight: FontWeight.w600, fontSize: 12),
          tabs: const [
            Tab(icon: Icon(Icons.play_arrow, size: 16), text: 'Attive'),
            Tab(icon: Icon(Icons.explore, size: 16), text: 'Disponibili'),
            Tab(icon: Icon(Icons.history, size: 16), text: 'Completate'),
          ],
        ),
      ),
    );
  }

  /// Particles positioned around the title area for enhanced visual appeal
  Widget _buildChallengesTitleParticle(int index) {
    return AnimatedBuilder(
      animation: _backgroundAnimation,
      builder: (context, child) {
        final offset = (_backgroundAnimation.value + index * 0.15) % 1.0;

        // Calculate positions around the title area (center of header)
        final screenWidth = MediaQuery.of(context).size.width;
        final centerX = screenWidth / 2;
        final titleY = 50; // Spostato ancora più in alto per seguire il titolo

        // Create circular pattern around title
        final angle = (index * 30.0) + (offset * 360.0); // Degrees
        final radius = 75 + (index % 3) * 18; // Varying distances from title
        final radians = angle * (3.14159 / 180);

        final particleX = centerX + (radius * cos(radians));
        final particleY = titleY + (radius * sin(radians)) * 0.5; // Flatten vertically

        return Positioned(
          left: particleX,
          top: particleY,
          child: Opacity(
            opacity: 0.4 + (index % 2) * 0.2, // Varying opacity (0.4-0.6)
            child: Container(
              width: 3 + (index % 3).toDouble(), // Varying sizes (3-6px)
              height: 3 + (index % 3).toDouble(),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.white.withOpacity(0.6),
                    blurRadius: 6,
                    spreadRadius: 1,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// Sezione statistiche
  Widget _buildStatisticsSection(AdvancedChallengesController controller) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: _showStatistics ? 120 : 0,
      child: _showStatistics
          ? ChallengeStatisticsWidget(
              statistics: controller.getStatistics(),
              isDarkMode: _isDarkMode,
            ).animate().slideY(begin: -1, end: 0).fadeIn()
          : null,
    );
  }

  /// Barra filtri
  Widget _buildFilterBar(AdvancedChallengesController controller) {
    return ChallengeFilterBar(
      selectedCategory: controller.selectedCategory,
      filterDifficulty: controller.filterDifficulty,
      sortOrder: controller.sortOrder,
      onCategoryChanged: controller.setSelectedCategory,
      onDifficultyChanged: controller.setFilterDifficulty,
      onSortOrderChanged: controller.setSortOrder,
      isDarkMode: _isDarkMode,
    );
  }

  /// Contenuto delle tab
  Widget _buildTabContent(AdvancedChallengesController controller) {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildActiveChallengesTab(controller),
        _buildAvailableChallengesTab(controller),
        _buildCompletedChallengesTab(controller),
      ],
    );
  }

  /// Tab sfide attive
  Widget _buildActiveChallengesTab(AdvancedChallengesController controller) {
    if (controller.activeChallenges.isEmpty) {
      return _buildEmptyState(
        'Nessuna Sfida Attiva',
        'Inizia una nuova sfida per migliorare le tue abitudini alimentari!',
        FontAwesomeIcons.trophy,
        () => _tabController.animateTo(1),
        'Esplora Sfide',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: controller.activeChallenges.length,
      itemBuilder: (context, index) {
        final challenge = controller.activeChallenges[index];
        final progress = controller.getChallengeProgress(challenge.id);

        return PremiumChallengeCard(
          challenge: challenge,
          progress: progress,
          isActive: true,
          isDarkMode: _isDarkMode,
          onTap: () => _showChallengeDetail(challenge, progress),
          onAction: () => _showLeaveDialog(challenge.id),
        ).animate(delay: (index * 100).ms)
            .slideX(begin: 0.3, end: 0)
            .fadeIn();
      },
    );
  }

  /// Tab sfide disponibili
  Widget _buildAvailableChallengesTab(AdvancedChallengesController controller) {
    final challenges = controller.availableChallenges;

    if (challenges.isEmpty) {
      return _buildEmptyState(
        'Nessuna Sfida Disponibile',
        'Tutte le sfide sono già attive o completate!',
        FontAwesomeIcons.checkCircle,
        null,
        null,
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: challenges.length,
      itemBuilder: (context, index) {
        final challenge = challenges[index];

        return PremiumChallengeCard(
          challenge: challenge,
          progress: null,
          isActive: false,
          isDarkMode: _isDarkMode,
          onTap: () => _showChallengeDetail(challenge, null),
          onAction: () => _showJoinDialog(challenge),
        ).animate(delay: (index * 100).ms)
            .slideX(begin: -0.3, end: 0)
            .fadeIn();
      },
    );
  }

  /// Tab sfide completate
  Widget _buildCompletedChallengesTab(AdvancedChallengesController controller) {
    if (controller.challengeHistory.isEmpty) {
      return _buildEmptyState(
        'Nessuna Sfida Completata',
        'Completa le tue prime sfide per vedere i risultati qui!',
        FontAwesomeIcons.medal,
        () => _tabController.animateTo(1),
        'Inizia Sfide',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: controller.challengeHistory.length,
      itemBuilder: (context, index) {
        final history = controller.challengeHistory[index];

        return _buildHistoryCard(history, index);
      },
    );
  }

  /// Card storico sfida
  Widget _buildHistoryCard(ChallengeHistory history, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: _isDarkMode ? DrStaffilanoTheme.backgroundDark : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: DrStaffilanoTheme.accentGold.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: DrStaffilanoTheme.accentGold.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  FontAwesomeIcons.trophy,
                  color: DrStaffilanoTheme.accentGold,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Sfida Completata',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                      ),
                    ),
                    Text(
                      'Completata il ${_formatDate(history.completedAt)}',
                      style: TextStyle(
                        fontSize: 14,
                        color: _isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '+${history.pointsEarned} punti',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: DrStaffilanoTheme.primaryGreen,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          LinearProgressIndicator(
            value: history.finalProgress,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(DrStaffilanoTheme.accentGold),
            minHeight: 6,
          ),
          const SizedBox(height: 8),
          Text(
            '${(history.finalProgress * 100).round()}% completato',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: _isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
            ),
          ),
        ],
      ),
    ).animate(delay: (index * 100).ms)
        .slideY(begin: 0.3, end: 0)
        .fadeIn();
  }

  /// Stato vuoto
  Widget _buildEmptyState(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback? onAction,
    String? actionText,
  ) {
    return SingleChildScrollView(
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(24), // Ridotto da 32 a 24
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min, // Aggiunto per ridurre l'altezza
            children: [
              Container(
                width: 80, // Ridotto da 100 a 80
                height: 80, // Ridotto da 100 a 80
                decoration: BoxDecoration(
                  color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(40), // Ridotto da 50 a 40
                ),
                child: Icon(
                  icon,
                  size: 40, // Ridotto da 50 a 40
                  color: DrStaffilanoTheme.primaryGreen.withOpacity(0.6),
                ),
              ).animate().scale(delay: 200.ms),
              const SizedBox(height: 16), // Ridotto da 24 a 16
              Text(
                title,
                style: TextStyle(
                  fontSize: 18, // Ridotto da 20 a 18
                  fontWeight: FontWeight.bold,
                  color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                ),
                textAlign: TextAlign.center,
              ).animate().fadeIn(delay: 400.ms),
              const SizedBox(height: 6), // Ridotto da 8 a 6
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 14, // Ridotto da 16 a 14
                  color: _isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
                ),
                textAlign: TextAlign.center,
              ).animate().fadeIn(delay: 600.ms),
              if (onAction != null && actionText != null) ...[
                const SizedBox(height: 16), // Ridotto da 24 a 16
                ElevatedButton.icon(
                  onPressed: onAction,
                  icon: const Icon(Icons.explore, size: 18), // Ridotto da default a 18
                  label: Text(actionText),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: DrStaffilanoTheme.primaryGreen,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10), // Ridotto padding
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10), // Ridotto da 12 a 10
                    ),
                  ),
                ).animate().slideY(begin: 0.3, end: 0, delay: 800.ms),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Floating Action Button
  Widget _buildFloatingActionButton(AdvancedChallengesController controller) {
    return FloatingActionButtonDrStaffilano(
      onPressed: () {
        // Vai alle sfide disponibili
        _tabController.animateTo(1);
        HapticFeedback.mediumImpact();
      },
      icon: FontAwesomeIcons.plus,
      tooltip: 'Nuova Sfida',
    );
  }

  /// Mostra dettaglio sfida
  void _showChallengeDetail(AdvancedChallenge challenge, ChallengeProgress? progress) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildChallengeDetailSheet(challenge, progress),
    );
  }

  /// Mostra dialog partecipazione
  void _showJoinDialog(AdvancedChallenge challenge) {
    showDialog(
      context: context,
      builder: (context) => _buildJoinChallengeDialog(challenge),
    );
  }

  /// Mostra dialog abbandono sfida
  void _showLeaveDialog(String challengeId) {
    final controller = context.read<AdvancedChallengesController>();
    final challenge = controller.activeChallenges.firstWhere((c) => c.id == challengeId);

    showDialog(
      context: context,
      builder: (context) => _buildLeaveChallengeDialog(challenge),
    );
  }

  /// Widget per il dettaglio della sfida
  Widget _buildChallengeDetailSheet(AdvancedChallenge challenge, ChallengeProgress? progress) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: BoxDecoration(
        color: _isDarkMode ? DrStaffilanoTheme.backgroundDark : Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: challenge.category.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    challenge.category.icon,
                    color: challenge.category.primaryColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        challenge.title,
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                        ),
                      ),
                      Text(
                        challenge.category.displayName,
                        style: TextStyle(
                          fontSize: 14,
                          color: challenge.category.primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(
                    Icons.close,
                    color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                  ),
                ),
              ],
            ),
          ),
          // Contenuto scrollabile
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Descrizione
                  Text(
                    'Descrizione',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    challenge.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: _isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
                      height: 1.5,
                    ),
                  ),
                  const SizedBox(height: 20),

                  // Progresso (se attiva)
                  if (progress != null) ...[
                    Text(
                      'Progresso',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 12),
                    LinearProgressIndicator(
                      value: progress.currentProgress,
                      backgroundColor: Colors.grey.shade200,
                      valueColor: AlwaysStoppedAnimation<Color>(challenge.category.primaryColor),
                      minHeight: 8,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${(progress.currentProgress * 100).round()}% completato',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: challenge.category.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 20),
                  ],

                  // Dettagli sfida
                  _buildChallengeDetails(challenge),

                  const SizedBox(height: 20),

                  // Motivazione Dr. Staffilano
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              FontAwesomeIcons.userDoctor,
                              color: DrStaffilanoTheme.primaryGreen,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Dr. Staffilano dice:',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: DrStaffilanoTheme.primaryGreen,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          challenge.drStaffilanoMotivation,
                          style: TextStyle(
                            fontSize: 14,
                            color: _isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
                            fontStyle: FontStyle.italic,
                            height: 1.4,
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 40),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Widget per i dettagli della sfida
  Widget _buildChallengeDetails(AdvancedChallenge challenge) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Dettagli',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        _buildDetailRow('Durata', '${challenge.durationDays} giorni', Icons.schedule),
        _buildDetailRow('Obiettivo', '${challenge.targetValue} ${challenge.unit}', Icons.flag),
        _buildDetailRow('Punti', '${challenge.points} punti WellJourney', Icons.stars),
        _buildDetailRow('Difficoltà', challenge.difficulty.displayName, challenge.difficulty.icon),
        _buildDetailRow('Tracking', challenge.trackingMethod.displayName, challenge.trackingMethod.icon),
      ],
    );
  }

  /// Widget per una riga di dettaglio
  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: _isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
          ),
          const SizedBox(width: 12),
          Text(
            '$label:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: _isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Dialog per partecipare a una sfida
  Widget _buildJoinChallengeDialog(AdvancedChallenge challenge) {
    return AlertDialog(
      backgroundColor: _isDarkMode ? DrStaffilanoTheme.backgroundDark : Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Row(
        children: [
          Icon(
            challenge.category.icon,
            color: challenge.category.primaryColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Partecipa alla Sfida',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
              ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            challenge.title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            challenge.description,
            style: TextStyle(
              fontSize: 14,
              color: _isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: DrStaffilanoTheme.accentGold.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: DrStaffilanoTheme.accentGold.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.stars,
                  color: DrStaffilanoTheme.accentGold,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Ricompensa: ${challenge.points} punti WellJourney',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: DrStaffilanoTheme.accentGold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'Annulla',
            style: TextStyle(
              color: _isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () async {
            Navigator.pop(context);
            final controller = context.read<AdvancedChallengesController>();
            final success = await controller.joinChallenge(challenge.id);

            if (success && mounted) {
              HapticFeedback.mediumImpact();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Sfida "${challenge.title}" iniziata!'),
                  backgroundColor: DrStaffilanoTheme.primaryGreen,
                  behavior: SnackBarBehavior.floating,
                ),
              );
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: challenge.category.primaryColor,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
          child: const Text('Partecipa'),
        ),
      ],
    );
  }

  /// Dialog per abbandonare una sfida
  Widget _buildLeaveChallengeDialog(AdvancedChallenge challenge) {
    return AlertDialog(
      backgroundColor: _isDarkMode ? DrStaffilanoTheme.backgroundDark : Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Row(
        children: [
          Icon(
            Icons.warning_amber_rounded,
            color: Colors.orange,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Abbandona Sfida',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
              ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Sei sicuro di voler abbandonare la sfida "${challenge.title}"?',
            style: TextStyle(
              fontSize: 14,
              color: _isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Colors.orange.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.orange,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Perderai tutti i progressi attuali',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.orange,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'Annulla',
            style: TextStyle(
              color: _isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
            ),
          ),
        ),
        ElevatedButton(
          onPressed: () async {
            Navigator.pop(context);
            final controller = context.read<AdvancedChallengesController>();
            final success = await controller.leaveChallenge(challenge.id);

            if (success && mounted) {
              HapticFeedback.lightImpact();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Sfida "${challenge.title}" abbandonata'),
                  backgroundColor: Colors.orange,
                  behavior: SnackBarBehavior.floating,
                ),
              );
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
          child: const Text('Abbandona'),
        ),
      ],
    );
  }

  /// Formatta data
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
