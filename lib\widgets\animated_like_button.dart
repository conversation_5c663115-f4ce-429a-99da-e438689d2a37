import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/community_post.dart';
import '../services/notification_service.dart';
import '../services/post_interaction_service.dart';
import '../theme/dr_staffilano_theme.dart';

/// Widget per il pulsante Like animato stile Instagram/Facebook
class AnimatedLikeButton extends StatefulWidget {
  final CommunityPost post;
  final bool isLiked;
  final int likesCount;
  final VoidCallback? onLikeChanged;

  const AnimatedLikeButton({
    super.key,
    required this.post,
    required this.isLiked,
    required this.likesCount,
    this.onLikeChanged,
  });

  @override
  State<AnimatedLikeButton> createState() => _AnimatedLikeButtonState();
}

class _AnimatedLikeButtonState extends State<AnimatedLikeButton>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _bounceController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _bounceAnimation;

  bool _isLiked = false;
  int _likesCount = 0;
  bool _isAnimating = false;

  @override
  void initState() {
    super.initState();

    // Inizializza lo stato dal servizio di interazioni
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final interactionService = context.read<PostInteractionService>();
      setState(() {
        _isLiked = interactionService.isPostLiked(widget.post.id);
        _likesCount = interactionService.getPostLikesCount(widget.post.id);

        // Se il conteggio è 0, usa quello del post
        if (_likesCount == 0) {
          _likesCount = widget.likesCount;
          interactionService.initializePostLikesCount(widget.post.id, widget.likesCount);
        }
      });
    });

    // Animazione di scale per il feedback immediato
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));

    // Animazione di bounce per l'effetto Instagram
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _bounceAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _bounceController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(AnimatedLikeButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.isLiked != widget.isLiked) {
      setState(() {
        _isLiked = widget.isLiked;
      });
    }
    if (oldWidget.likesCount != widget.likesCount) {
      setState(() {
        _likesCount = widget.likesCount;
      });
    }
  }

  Future<void> _handleLike() async {
    if (_isAnimating) return;

    setState(() {
      _isAnimating = true;
    });

    try {
      final notificationService = context.read<NotificationService>();
      final interactionService = context.read<PostInteractionService>();

      // Toggle dello stato like usando il servizio
      final newLikedState = await interactionService.toggleLike(widget.post.id, _likesCount);
      final newLikesCount = interactionService.getPostLikesCount(widget.post.id);

      // Aggiorna immediatamente l'UI per feedback rapido
      setState(() {
        _isLiked = newLikedState;
        _likesCount = newLikesCount;
      });

      // Avvia le animazioni
      if (newLikedState) {
        // Like aggiunto - animazione bounce + scale
        _scaleController.forward().then((_) {
          _scaleController.reverse();
        });
        _bounceController.forward().then((_) {
          _bounceController.reverse();
        });

        // Genera notifica per l'autore del post (se non è l'utente corrente)
        if (widget.post.author != null && widget.post.author!.id != 'current_user_id') {
          await notificationService.createLikeNotification(
            postAuthorId: widget.post.author!.id,
            postId: widget.post.id,
            actorUserId: 'current_user_id', // TODO: Ottieni dall'auth service
            actorUserName: 'Dr. Giovanni Staffilano', // TODO: Ottieni dall'auth service
            actorUserAvatar: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=150&h=150&fit=crop&crop=face',
          );
        }

        // Feedback visivo
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('❤️ Mi piace aggiunto!'),
              duration: const Duration(seconds: 1),
              backgroundColor: const Color(0xFFF44336),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              margin: const EdgeInsets.only(bottom: 80, left: 20, right: 20),
            ),
          );
        }
      } else {
        // Like rimosso - solo animazione scale
        _scaleController.forward().then((_) {
          _scaleController.reverse();
        });

        // Feedback visivo
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('💔 Mi piace rimosso'),
              duration: const Duration(seconds: 1),
              backgroundColor: Colors.grey[600],
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              margin: const EdgeInsets.only(bottom: 80, left: 20, right: 20),
            ),
          );
        }
      }

      // TODO: Qui dovresti chiamare l'API per aggiornare il like nel backend
      // await _updateLikeInBackend(widget.post.id, newLikedState);

      // Notifica il parent widget del cambiamento
      widget.onLikeChanged?.call();

    } catch (e) {
      // In caso di errore, ricarica lo stato dal servizio
      final interactionService = context.read<PostInteractionService>();
      setState(() {
        _isLiked = interactionService.isPostLiked(widget.post.id);
        _likesCount = interactionService.getPostLikesCount(widget.post.id);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Errore: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isAnimating = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: _handleLike,
      borderRadius: BorderRadius.circular(20),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icona del cuore con animazioni
            AnimatedBuilder(
              animation: Listenable.merge([_scaleAnimation, _bounceAnimation]),
              builder: (context, child) {
                final scale = _scaleAnimation.value *
                             (_isLiked ? _bounceAnimation.value : 1.0);

                return Transform.scale(
                  scale: scale,
                  child: Icon(
                    _isLiked ? Icons.favorite : Icons.favorite_outline,
                    size: 20,
                    color: _isLiked
                        ? const Color(0xFFF44336) // Rosso per like attivo
                        : Colors.grey[600], // Grigio per like inattivo
                  ),
                );
              },
            ),

            // Contatore likes con animazione
            if (_likesCount > 0) ...[
              const SizedBox(width: 4),
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 200),
                transitionBuilder: (child, animation) {
                  return SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0, 0.5),
                      end: Offset.zero,
                    ).animate(animation),
                    child: FadeTransition(
                      opacity: animation,
                      child: child,
                    ),
                  );
                },
                child: Text(
                  _likesCount.toString(),
                  key: ValueKey(_likesCount),
                  style: TextStyle(
                    fontSize: 12,
                    color: _isLiked
                        ? const Color(0xFFF44336)
                        : Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Estensione per gestire lo stato dei like dei post
extension PostLikeState on CommunityPost {
  /// Verifica se il post è stato messo like dall'utente corrente
  /// Questa proprietà ora viene gestita dal PostInteractionService
  bool get isLikedByCurrentUser {
    // Questa proprietà è ora gestita dinamicamente dal PostInteractionService
    // Il valore effettivo viene ottenuto nel widget AnimatedLikeButton
    return false;
  }
}
