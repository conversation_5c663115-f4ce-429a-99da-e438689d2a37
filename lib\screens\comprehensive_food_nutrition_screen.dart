import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/food.dart';
import '../theme/dr_staffilano_theme.dart';
import '../utils/micronutrients_helper.dart';

/// Schermata completa per visualizzare informazioni nutrizionali dettagliate di un alimento
class ComprehensiveFoodNutritionScreen extends StatefulWidget {
  final Food food;
  final bool isModal;

  const ComprehensiveFoodNutritionScreen({
    Key? key,
    required this.food,
    this.isModal = false,
  }) : super(key: key);

  @override
  State<ComprehensiveFoodNutritionScreen> createState() => _ComprehensiveFoodNutritionScreenState();

  /// Mostra la schermata come modal dialog
  static void showAsModal(BuildContext context, Food food) {
    showDialog(
      context: context,
      builder: (context) => ComprehensiveFoodNutritionScreen(
        food: food,
        isModal: true,
      ),
    );
  }

  /// Naviga alla schermata completa
  static void navigateToFullScreen(BuildContext context, Food food) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ComprehensiveFoodNutritionScreen(
          food: food,
          isModal: false,
        ),
      ),
    );
  }
}

class _ComprehensiveFoodNutritionScreenState extends State<ComprehensiveFoodNutritionScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isModal) {
      return _buildModalContent();
    } else {
      return _buildFullScreenContent();
    }
  }

  Widget _buildModalContent() {
    return Dialog(
      insetPadding: const EdgeInsets.all(16),
      child: Container(
        width: double.maxFinite,
        height: MediaQuery.of(context).size.height * 0.85,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Colors.white,
        ),
        child: Column(
          children: [
            _buildHeader(isModal: true),
            _buildTabBar(),
            Expanded(child: _buildTabContent()),
          ],
        ),
      ),
    );
  }

  Widget _buildFullScreenContent() {
    return Scaffold(
      body: Column(
        children: [
          _buildHeader(isModal: false),
          _buildTabBar(),
          Expanded(child: _buildTabContent()),
        ],
      ),
    );
  }

  Widget _buildHeader({required bool isModal}) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: DrStaffilanoTheme.primaryGradient,
        borderRadius: isModal
            ? const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              )
            : null,
      ),
      child: SafeArea(
        bottom: false,
        child: Row(
          children: [
            if (!isModal)
              IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () => Navigator.of(context).pop(),
              ),
            if (widget.food.imageUrl.isNotEmpty)
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  image: DecorationImage(
                    image: NetworkImage(widget.food.imageUrl),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.food.name,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${widget.food.calories} kcal per 100g',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),
                ],
              ),
            ),
            if (isModal)
              IconButton(
                icon: const Icon(Icons.close, color: Colors.white),
                onPressed: () => Navigator.of(context).pop(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: DrStaffilanoTheme.primaryGreen,
        unselectedLabelColor: DrStaffilanoTheme.textSecondary,
        indicatorColor: DrStaffilanoTheme.primaryGreen,
        tabs: const [
          Tab(
            icon: Icon(FontAwesomeIcons.chartPie),
            text: 'Macronutrienti',
          ),
          Tab(
            icon: Icon(FontAwesomeIcons.table),
            text: 'Tabella Completa',
          ),
          Tab(
            icon: Icon(FontAwesomeIcons.chartBar),
            text: 'Micronutrienti',
          ),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildMacronutrientsTab(),
        _buildCompleteTableTab(),
        _buildMicronutrientsTab(),
      ],
    );
  }

  Widget _buildMacronutrientsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Grafico a torta dei macronutrienti
          const Text(
            'Distribuzione Macronutrienti',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 250,
            child: _buildMacronutrientsPieChart(),
          ),
          const SizedBox(height: 24),

          // Legenda dettagliata
          _buildMacronutrientsLegend(),
          const SizedBox(height: 24),

          // Informazioni caloriche
          _buildCalorieBreakdown(),
        ],
      ),
    );
  }

  Widget _buildMacronutrientsPieChart() {
    final totalGrams = widget.food.proteins + widget.food.carbs + widget.food.fats;

    if (totalGrams == 0) {
      return const Center(
        child: Text(
          'Nessun dato sui macronutrienti disponibile',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    final proteinPercentage = (widget.food.proteins / totalGrams * 100);
    final carbsPercentage = (widget.food.carbs / totalGrams * 100);
    final fatsPercentage = (widget.food.fats / totalGrams * 100);

    return PieChart(
      PieChartData(
        sections: [
          PieChartSectionData(
            color: DrStaffilanoTheme.primaryGreen,
            value: widget.food.proteins,
            title: '${proteinPercentage.toStringAsFixed(1)}%',
            radius: 80,
            titleStyle: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          PieChartSectionData(
            color: DrStaffilanoTheme.secondaryBlue,
            value: widget.food.carbs,
            title: '${carbsPercentage.toStringAsFixed(1)}%',
            radius: 80,
            titleStyle: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          PieChartSectionData(
            color: DrStaffilanoTheme.accentGold,
            value: widget.food.fats,
            title: '${fatsPercentage.toStringAsFixed(1)}%',
            radius: 80,
            titleStyle: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ],
        sectionsSpace: 2,
        centerSpaceRadius: 40,
        startDegreeOffset: 180,
      ),
    );
  }

  Widget _buildMacronutrientsLegend() {
    return Column(
      children: [
        _buildLegendItem(
          'Proteine',
          '${widget.food.proteins.toStringAsFixed(1)}g',
          DrStaffilanoTheme.primaryGreen,
          FontAwesomeIcons.dumbbell,
        ),
        const SizedBox(height: 8),
        _buildLegendItem(
          'Carboidrati',
          '${widget.food.carbs.toStringAsFixed(1)}g',
          DrStaffilanoTheme.secondaryBlue,
          FontAwesomeIcons.wheatAwn,
        ),
        const SizedBox(height: 8),
        _buildLegendItem(
          'Grassi',
          '${widget.food.fats.toStringAsFixed(1)}g',
          DrStaffilanoTheme.accentGold,
          FontAwesomeIcons.droplet,
        ),
        if (widget.food.fiber > 0) ...[
          const SizedBox(height: 8),
          _buildLegendItem(
            'Fibre',
            '${widget.food.fiber.toStringAsFixed(1)}g',
            Colors.green,
            FontAwesomeIcons.leaf,
          ),
        ],
      ],
    );
  }

  Widget _buildLegendItem(String label, String value, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCalorieBreakdown() {
    final proteinCalories = widget.food.proteins * 4;
    final carbCalories = widget.food.carbs * 4;
    final fatCalories = widget.food.fats * 9;
    final totalCalculatedCalories = proteinCalories + carbCalories + fatCalories;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
            DrStaffilanoTheme.secondaryBlue.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.fire,
                color: DrStaffilanoTheme.accentGold,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'Ripartizione Calorica',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildCalorieRow('Da proteine', proteinCalories, DrStaffilanoTheme.primaryGreen),
          _buildCalorieRow('Da carboidrati', carbCalories, DrStaffilanoTheme.secondaryBlue),
          _buildCalorieRow('Da grassi', fatCalories, DrStaffilanoTheme.accentGold),
          const Divider(),
          _buildCalorieRow('Totale calcolato', totalCalculatedCalories, Colors.black, isBold: true),
          _buildCalorieRow('Totale dichiarato', widget.food.calories.toDouble(), Colors.grey),
        ],
      ),
    );
  }

  Widget _buildCalorieRow(String label, double calories, Color color, {bool isBold = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              color: color,
            ),
          ),
          Text(
            '${calories.toStringAsFixed(0)} kcal',
            style: TextStyle(
              fontSize: 14,
              fontWeight: isBold ? FontWeight.bold : FontWeight.w500,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompleteTableTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Valori Nutrizionali Completi',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Valori per 100g di prodotto',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
              fontStyle: FontStyle.italic,
            ),
          ),
          const SizedBox(height: 16),

          // Tabella macronutrienti
          _buildNutritionTable(),
          const SizedBox(height: 24),

          // Informazioni aggiuntive
          if (widget.food.glycemicIndex > 0 || widget.food.glycemicLoad > 0)
            _buildGlycemicInfo(),

          // Proprietà dell'alimento
          _buildFoodProperties(),
        ],
      ),
    );
  }

  Widget _buildNutritionTable() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          _buildTableHeader(),
          _buildTableRow('Energia', '${widget.food.calories}', 'kcal', isHeader: true),
          _buildTableRow('Proteine', widget.food.proteins.toStringAsFixed(1), 'g'),
          _buildTableRow('Carboidrati', widget.food.carbs.toStringAsFixed(1), 'g'),
          _buildTableRow('  di cui zuccheri', widget.food.sugar.toStringAsFixed(1), 'g', isIndented: true),
          _buildTableRow('Grassi', widget.food.fats.toStringAsFixed(1), 'g'),
          if (widget.food.saturatedFats != null)
            _buildTableRow('  di cui saturi', widget.food.saturatedFats!.toStringAsFixed(1), 'g', isIndented: true),
          _buildTableRow('Fibre', widget.food.fiber.toStringAsFixed(1), 'g'),
          if (widget.food.sodium != null)
            _buildTableRow('Sodio', widget.food.sodium!.toStringAsFixed(1), 'mg'),
          if (widget.food.potassium != null)
            _buildTableRow('Potassio', widget.food.potassium!.toStringAsFixed(1), 'mg'),
          if (widget.food.calcium != null)
            _buildTableRow('Calcio', widget.food.calcium!.toStringAsFixed(1), 'mg'),
          if (widget.food.magnesium != null)
            _buildTableRow('Magnesio', widget.food.magnesium!.toStringAsFixed(1), 'mg'),
          if (widget.food.phosphorus != null)
            _buildTableRow('Fosforo', widget.food.phosphorus!.toStringAsFixed(1), 'mg'),
        ],
      ),
    );
  }

  Widget _buildTableHeader() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.primaryGreen,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: const Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              'Nutriente',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'Quantità',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableRow(String nutrient, String value, String unit, {bool isHeader = false, bool isIndented = false}) {
    return Container(
      padding: EdgeInsets.all(isHeader ? 16 : 12),
      decoration: BoxDecoration(
        color: isHeader ? DrStaffilanoTheme.primaryGreen.withOpacity(0.1) : Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Padding(
              padding: EdgeInsets.only(left: isIndented ? 16 : 0),
              child: Text(
                nutrient,
                style: TextStyle(
                  fontSize: isHeader ? 16 : 14,
                  fontWeight: isHeader ? FontWeight.bold : FontWeight.normal,
                  color: isIndented ? Colors.grey.shade600 : Colors.black,
                ),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '$value $unit',
              style: TextStyle(
                fontSize: isHeader ? 16 : 14,
                fontWeight: isHeader ? FontWeight.bold : FontWeight.w500,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGlycemicInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.secondaryBlue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: DrStaffilanoTheme.secondaryBlue.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.chartLine,
                color: DrStaffilanoTheme.secondaryBlue,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'Indici Glicemici',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (widget.food.glycemicIndex > 0)
            _buildGlycemicRow('Indice Glicemico', widget.food.glycemicIndex, 100),
          if (widget.food.glycemicLoad > 0)
            _buildGlycemicRow('Carico Glicemico', widget.food.glycemicLoad, 20),
        ],
      ),
    );
  }

  Widget _buildGlycemicRow(String label, int value, int maxValue) {
    final percentage = (value / maxValue).clamp(0.0, 1.0);
    Color color = Colors.green;
    String level = 'Basso';

    if (label.contains('Indice')) {
      if (value > 70) {
        color = Colors.red;
        level = 'Alto';
      } else if (value > 55) {
        color = Colors.orange;
        level = 'Medio';
      }
    } else {
      if (value > 20) {
        color = Colors.red;
        level = 'Alto';
      } else if (value > 10) {
        color = Colors.orange;
        level = 'Medio';
      }
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '$value ($level)',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: percentage,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
    );
  }

  Widget _buildFoodProperties() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.accentGold.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: DrStaffilanoTheme.accentGold.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.tags,
                color: DrStaffilanoTheme.accentGold,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'Proprietà dell\'Alimento',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              if (widget.food.isVegetarian) _buildPropertyChip('Vegetariano', Colors.green),
              if (widget.food.isVegan) _buildPropertyChip('Vegano', Colors.lightGreen),
              if (widget.food.isGlutenFree) _buildPropertyChip('Senza Glutine', Colors.blue),
              if (widget.food.isDairyFree) _buildPropertyChip('Senza Lattosio', Colors.purple),
              if (widget.food.isSeasonal) _buildPropertyChip('Stagionale', Colors.orange),
              if (widget.food.isRecipe) _buildPropertyChip('Ricetta', Colors.red),
              _buildPropertyChip(widget.food.servingSize, Colors.grey),
            ],
          ),
          if (widget.food.allergens.isNotEmpty) ...[
            const SizedBox(height: 12),
            const Text(
              'Allergeni:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Wrap(
              spacing: 4,
              runSpacing: 4,
              children: widget.food.allergens.map((allergen) =>
                _buildPropertyChip(allergen, Colors.red.shade300)
              ).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPropertyChip(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.5)),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: color.withOpacity(0.8),
        ),
      ),
    );
  }

  Widget _buildMicronutrientsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Micronutrienti e Minerali',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Vitamine, minerali e altri micronutrienti per 100g',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
              fontStyle: FontStyle.italic,
            ),
          ),
          const SizedBox(height: 16),

          // Minerali principali
          if (_hasMainMinerals()) ...[
            _buildMineralsSection(),
            const SizedBox(height: 24),
          ],

          // Grafico a barre dei minerali
          if (_hasMainMinerals()) ...[
            const Text(
              'Distribuzione Minerali Principali',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 250,
              child: _buildMineralsBarChart(),
            ),
            const SizedBox(height: 24),
          ],

          // Micronutrienti aggiuntivi
          if (widget.food.micronutrients.isNotEmpty) ...[
            _buildMicronutrientsSection(),
          ],

          // Messaggio se non ci sono dati
          if (!_hasMainMinerals() && widget.food.micronutrients.isEmpty)
            _buildNoMicronutrientsMessage(),
        ],
      ),
    );
  }

  bool _hasMainMinerals() {
    return widget.food.calcium != null ||
           widget.food.phosphorus != null ||
           widget.food.magnesium != null ||
           widget.food.sodium != null ||
           widget.food.potassium != null;
  }

  Widget _buildMineralsSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.atom,
                color: DrStaffilanoTheme.primaryGreen,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'Minerali Principali',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (widget.food.calcium != null)
            _buildMineralRow('Calcio (Ca)', widget.food.calcium!, 'mg', Icons.local_hospital),
          if (widget.food.phosphorus != null)
            _buildMineralRow('Fosforo (P)', widget.food.phosphorus!, 'mg', Icons.flash_on),
          if (widget.food.magnesium != null)
            _buildMineralRow('Magnesio (Mg)', widget.food.magnesium!, 'mg', Icons.fitness_center),
          if (widget.food.sodium != null)
            _buildMineralRow('Sodio (Na)', widget.food.sodium!, 'mg', Icons.grain),
          if (widget.food.potassium != null)
            _buildMineralRow('Potassio (K)', widget.food.potassium!, 'mg', Icons.favorite),
        ],
      ),
    );
  }

  Widget _buildMineralRow(String name, double value, String unit, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: DrStaffilanoTheme.primaryGreen, size: 18),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              name,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            '${value.toStringAsFixed(1)} $unit',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: DrStaffilanoTheme.primaryGreen,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMineralsBarChart() {
    final minerals = <String, double>{};

    if (widget.food.calcium != null) minerals['Ca'] = widget.food.calcium!;
    if (widget.food.phosphorus != null) minerals['P'] = widget.food.phosphorus!;
    if (widget.food.magnesium != null) minerals['Mg'] = widget.food.magnesium!;
    if (widget.food.sodium != null) minerals['Na'] = widget.food.sodium!;
    if (widget.food.potassium != null) minerals['K'] = widget.food.potassium!;

    if (minerals.isEmpty) {
      return const Center(
        child: Text('Nessun dato sui minerali disponibile'),
      );
    }

    final maxValue = minerals.values.reduce((a, b) => a > b ? a : b);

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: maxValue * 1.1,
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            getTooltipColor: (group) => DrStaffilanoTheme.primaryGreen,
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              final mineral = minerals.keys.elementAt(groupIndex);
              final value = minerals.values.elementAt(groupIndex);
              return BarTooltipItem(
                '$mineral\n${value.toStringAsFixed(1)} mg',
                const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              );
            },
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                final keys = minerals.keys.toList();
                if (value.toInt() < keys.length) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      keys[value.toInt()],
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  );
                }
                return const SizedBox();
              },
              reservedSize: 30,
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 50,
              getTitlesWidget: (value, meta) {
                return Text(
                  '${value.toInt()}',
                  style: const TextStyle(fontSize: 10),
                );
              },
            ),
          ),
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false),
        barGroups: List.generate(minerals.length, (index) {
          final value = minerals.values.elementAt(index);
          return BarChartGroupData(
            x: index,
            barRods: [
              BarChartRodData(
                toY: value,
                color: DrStaffilanoTheme.primaryGreen,
                width: 30,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(6),
                  topRight: Radius.circular(6),
                ),
              ),
            ],
          );
        }),
      ),
    );
  }

  Widget _buildMicronutrientsSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.secondaryBlue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: DrStaffilanoTheme.secondaryBlue.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.microscope,
                color: DrStaffilanoTheme.secondaryBlue,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'Altri Micronutrienti',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...widget.food.micronutrients.entries.map((entry) =>
            _buildMicronutrientRow(entry.key, entry.value)
          ),
        ],
      ),
    );
  }

  Widget _buildMicronutrientRow(String name, double value) {
    final displayName = MicronutrientsHelper.getDisplayName(name);
    final unit = _getMicronutrientUnit(name);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          Icon(
            FontAwesomeIcons.circle,
            color: DrStaffilanoTheme.secondaryBlue,
            size: 8,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              displayName,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            '${value.toStringAsFixed(2)} $unit',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: DrStaffilanoTheme.secondaryBlue,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoMicronutrientsMessage() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          Icon(
            FontAwesomeIcons.circleInfo,
            color: Colors.grey.shade600,
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            'Dati sui micronutrienti non disponibili',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'I dati dettagliati sui micronutrienti per questo alimento non sono attualmente disponibili nel database.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _getMicronutrientUnit(String name) {
    // Simple unit mapping for common micronutrients
    if (name.toLowerCase().contains('vitamin')) {
      return 'μg';
    } else if (name.toLowerCase().contains('mineral') ||
               name.toLowerCase().contains('iron') ||
               name.toLowerCase().contains('zinc')) {
      return 'mg';
    }
    return 'mg'; // Default unit
  }
}
