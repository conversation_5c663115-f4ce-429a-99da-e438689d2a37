import 'package:flutter/material.dart';
import '../../theme/dr_staffilano_theme.dart';

/// Dialog per mostrare il progresso durante la selezione di media
class MediaProgressDialog extends StatefulWidget {
  final String title;
  final String initialMessage;

  const MediaProgressDialog({
    super.key,
    required this.title,
    required this.initialMessage,
  });

  @override
  State<MediaProgressDialog> createState() => _MediaProgressDialogState();
}

class _MediaProgressDialogState extends State<MediaProgressDialog> {
  String _currentMessage = '';

  @override
  void initState() {
    super.initState();
    _currentMessage = widget.initialMessage;
  }

  void updateMessage(String message) {
    if (mounted) {
      setState(() {
        _currentMessage = message;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.photo_library,
                size: 30,
                color: DrStaffilanoTheme.primaryGreen,
              ),
            ),

            const SizedBox(height: 16),

            // Title
            Text(
              widget.title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 12),

            // Progress indicator
            SizedBox(
              width: 30,
              height: 30,
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(
                  DrStaffilanoTheme.primaryGreen,
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Message
            Text(
              _currentMessage,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 20),

            // Cancel button
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey[600],
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              child: const Text('Annulla'),
            ),
          ],
        ),
      ),
    );
  }

  static Future<T?> show<T>(
    BuildContext context, {
    required String title,
    required String initialMessage,
    required Future<T> Function(Function(String) updateMessage) operation,
  }) async {
    late _MediaProgressDialogState dialogState;

    // Mostra il dialog
    final dialogFuture = showDialog<T>(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        final dialog = MediaProgressDialog(
          title: title,
          initialMessage: initialMessage,
        );

        // Ottieni il riferimento allo state per aggiornare i messaggi
        WidgetsBinding.instance.addPostFrameCallback((_) {
          dialogState = dialog.createState() as _MediaProgressDialogState;
        });

        return dialog;
      },
    );

    // Esegui l'operazione
    try {
      final result = await operation((message) {
        if (context.mounted) {
          // Trova il dialog state e aggiorna il messaggio
          final navigator = Navigator.of(context);
          final route = navigator.widget;
          // Per ora usiamo un approccio semplificato
        }
      });

      // Chiudi il dialog e ritorna il risultato
      if (context.mounted) {
        Navigator.of(context).pop(result);
      }

      return result;
    } catch (e) {
      // Chiudi il dialog in caso di errore
      if (context.mounted) {
        Navigator.of(context).pop();
      }
      rethrow;
    }
  }
}

/// Versione semplificata per mostrare solo un loading
class SimpleLoadingDialog extends StatelessWidget {
  final String title;
  final String message;

  const SimpleLoadingDialog({
    super.key,
    required this.title,
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Progress indicator
            SizedBox(
              width: 40,
              height: 40,
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(
                  DrStaffilanoTheme.primaryGreen,
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Title
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 8),

            // Message
            Text(
              message,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  static Future<void> show(BuildContext context, {
    required String title,
    required String message,
  }) async {
    if (!context.mounted) return;

    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => SimpleLoadingDialog(
        title: title,
        message: message,
      ),
    );
  }

  static void hide(BuildContext context) {
    try {
      if (context.mounted) {
        final navigator = Navigator.of(context);
        if (navigator.canPop()) {
          navigator.pop();
        }
      }
    } catch (e) {
      // Ignora errori se il widget è stato smontato
      debugPrint('Errore nella chiusura del dialog: $e');
    }
  }

  static void hideAll(BuildContext context) {
    try {
      if (context.mounted) {
        final navigator = Navigator.of(context);
        while (navigator.canPop()) {
          navigator.pop();
        }
      }
    } catch (e) {
      debugPrint('Errore nella chiusura di tutti i dialog: $e');
    }
  }
}
