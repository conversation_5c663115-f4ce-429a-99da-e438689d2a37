import '../models/food.dart';

/// Servizio per garantire la sicurezza alimentare nelle diete generate
class FoodSafetyService {
  static const List<String> _unsafeFoodNames = [
    // Proteine animali crude
    'crudo',
    'raw',
    'non cotto',
    'non cotta',
    'fresco non cotto',
    'petto di pollo crudo',
    'pollo crudo',
    'maiale crudo',
    'manzo crudo',
    'vitello crudo',
    'agnello crudo',
    'pesce crudo',
    'salmone crudo',
    'tonno crudo',
    'branzino crudo',
    'orata cruda',
    'merluzzo crudo',
    'uova crude',
    'uovo crudo',

    // Verdure che richiedono cottura
    'spinaci crudi',
    'bietole crude',
    'cavolo crudo',
    'cavolfiore crudo',
    'broccoli crudi',
    'carciofi crudi',
    'asparagi crudi',
    'fagiolini crudi',
    'piselli crudi',

    // Legumi e cereali crudi
    'fagioli crudi',
    'lenticchie crude',
    'ceci crudi',
    'farro crudo',
    'orzo crudo',
    'riso crudo',
    'pasta cruda',
    'quinoa cruda',

    // Tuberi crudi
    'patate crude',
    'patata cruda',
    'patate dolci crude',
    'topinambur crudo',

    // Altri alimenti problematici
    'farina cruda',
    'impasto crudo',
    'melanzane crude',
    'funghi crudi',
  ];

  static const List<String> _safeRawFoods = [
    // Salumi e carni conservate
    'prosciutto',
    'bresaola',
    'salame',
    'coppa',
    'pancetta',
    'speck',
    'carpaccio',
    'tartare',
    'sashimi',
    'sushi',
    'crudo di ricciola',
    'crudo di tonno',
    'acciughe',
    'alici',
    'baccalà',
    'bottarga',

    // Frutta (sempre sicura cruda)
    'mela',
    'pera',
    'banana',
    'arancia',
    'limone',
    'fragole',
    'uva',
    'pesca',
    'albicocca',
    'ciliegia',
    'kiwi',
    'ananas',
    'melone',
    'anguria',
    'fichi',
    'prugne',
    'mirtilli',
    'lamponi',
    'more',

    // Verdure sicure crude
    'lattuga',
    'rucola',
    'valeriana',
    'radicchio',
    'indivia',
    'pomodori',
    'cetrioli',
    'carote',
    'sedano',
    'finocchi',
    'peperoni',
    'cipolla',
    'aglio',
    'basilico',
    'prezzemolo',
    'origano',
    'rosmarino',
    'salvia',
    'menta',
    'erba cipollina',

    // Frutta secca e semi
    'noci',
    'mandorle',
    'nocciole',
    'pistacchi',
    'pinoli',
    'semi di girasole',
    'semi di zucca',

    // Formaggi e latticini
    'formaggio',
    'mozzarella',
    'ricotta',
    'yogurt',
    'latte',
    'parmigiano',
    'gorgonzola',
    'pecorino',

    // Oli e condimenti
    'olio',
    'aceto',
    'sale',
    'pepe',
    'spezie',
  ];

  /// Verifica se un alimento è sicuro per il consumo
  static bool isFoodSafe(Food food) {
    final nameLower = food.name.toLowerCase();
    final descriptionLower = food.description.toLowerCase();

    // Se è un alimento tradizionalmente consumato crudo, è sicuro
    if (_safeRawFoods.any((safe) => nameLower.contains(safe))) {
      return true;
    }

    // Se contiene parole che indicano cibo crudo non sicuro, non è sicuro
    if (_unsafeFoodNames.any((unsafe) =>
        nameLower.contains(unsafe) || descriptionLower.contains(unsafe))) {
      return false;
    }

    // Se è marcato come crudo, verifica la sicurezza in base alla categoria
    if (food.foodState == FoodState.raw) {
      final tags = food.tags.map((tag) => tag.toLowerCase()).toList();

      // Proteine animali che richiedono cottura
      if (food.categories.contains(FoodCategory.protein)) {
        final unsafeProteinTags = [
          'pollo', 'chicken', 'carne', 'meat', 'manzo', 'beef',
          'maiale', 'pork', 'vitello', 'veal', 'agnello', 'lamb',
          'pesce', 'fish', 'salmone', 'salmon', 'tonno', 'tuna',
          'branzino', 'sea bass', 'orata', 'sea bream', 'merluzzo', 'cod',
          'uova', 'eggs', 'uovo', 'egg'
        ];

        if (tags.any((tag) => unsafeProteinTags.contains(tag))) {
          return false;
        }
      }

      // Cereali e legumi crudi (sempre non sicuri)
      if (food.categories.contains(FoodCategory.grain)) {
        final unsafeGrainTags = [
          'riso', 'rice', 'pasta', 'farro', 'orzo', 'quinoa',
          'avena', 'miglio', 'grano', 'wheat', 'farina', 'flour'
        ];

        if (tags.any((tag) => unsafeGrainTags.contains(tag))) {
          return false;
        }
      }

      // Verdure che richiedono cottura
      if (food.categories.contains(FoodCategory.vegetable)) {
        final unsafeVegetableTags = [
          'spinaci', 'spinach', 'bietole', 'chard', 'cavolo', 'cabbage',
          'cavolfiore', 'cauliflower', 'broccoli', 'carciofi', 'artichokes',
          'asparagi', 'asparagus', 'fagiolini', 'green beans', 'piselli', 'peas',
          'patate', 'potatoes', 'melanzane', 'eggplant', 'funghi', 'mushrooms',
          'fagioli', 'beans', 'lenticchie', 'lentils', 'ceci', 'chickpeas'
        ];

        if (tags.any((tag) => unsafeVegetableTags.contains(tag))) {
          return false;
        }
      }
    }

    return true;
  }

  /// Filtra una lista di alimenti rimuovendo quelli non sicuri
  static List<Food> filterSafeFoods(List<Food> foods) {
    return foods.where((food) => isFoodSafe(food)).toList();
  }

  /// Ottieni la versione cotta di un alimento crudo
  static Food? getCookedVersion(Food rawFood) {
    if (rawFood.foodState != FoodState.raw) {
      return rawFood; // Già cotto
    }

    // Crea una versione cotta con nome e descrizione appropriati
    final cookedName = _getCookedName(rawFood.name);
    final cookedDescription = _getCookedDescription(rawFood.description, rawFood.name);

    // Calcola i valori nutrizionali per la versione cotta
    final cookedCalories = (rawFood.calories * rawFood.rawToCookedFactor).round();
    final cookedProteins = rawFood.proteins * rawFood.rawToCookedFactor;
    final cookedCarbs = rawFood.carbs * rawFood.rawToCookedFactor;
    final cookedFats = rawFood.fats * rawFood.rawToCookedFactor;
    final cookedFiber = rawFood.fiber * rawFood.rawToCookedFactor;
    final cookedSugar = rawFood.sugar * rawFood.rawToCookedFactor;

    return rawFood.copyWith(
      id: '${rawFood.id}_cooked',
      name: cookedName,
      description: cookedDescription,
      calories: cookedCalories,
      proteins: cookedProteins,
      carbs: cookedCarbs,
      fats: cookedFats,
      fiber: cookedFiber,
      sugar: cookedSugar,
      foodState: FoodState.cooked,
      servingSize: _getCookedServingSize(rawFood.servingSize),
    );
  }

  /// Genera un nome appropriato per la versione cotta
  static String _getCookedName(String rawName) {
    final nameLower = rawName.toLowerCase();

    // PROTEINE ANIMALI
    if (nameLower.contains('petto di pollo')) {
      return 'Petto di pollo alla griglia';
    } else if (nameLower.contains('pollo')) {
      return rawName.replaceAll(RegExp(r'\bcrudo\b', caseSensitive: false), 'alla griglia');
    } else if (nameLower.contains('manzo')) {
      return rawName.replaceAll(RegExp(r'\bcrudo\b', caseSensitive: false), 'alla griglia');
    } else if (nameLower.contains('vitello')) {
      return rawName.replaceAll(RegExp(r'\bcrudo\b', caseSensitive: false), 'ai ferri');
    } else if (nameLower.contains('agnello')) {
      return rawName.replaceAll(RegExp(r'\bcrudo\b', caseSensitive: false), 'arrosto');
    } else if (nameLower.contains('maiale')) {
      return rawName.replaceAll(RegExp(r'\bcrudo\b', caseSensitive: false), 'alla griglia');
    } else if (nameLower.contains('salmone')) {
      return rawName.replaceAll(RegExp(r'\bcrudo\b', caseSensitive: false), 'al vapore');
    } else if (nameLower.contains('tonno')) {
      return rawName.replaceAll(RegExp(r'\bcrudo\b', caseSensitive: false), 'alla griglia');
    } else if (nameLower.contains('branzino')) {
      return rawName.replaceAll(RegExp(r'\bcrudo\b', caseSensitive: false), 'al forno');
    } else if (nameLower.contains('orata')) {
      return rawName.replaceAll(RegExp(r'\bcrudo\b', caseSensitive: false), 'al sale');
    } else if (nameLower.contains('merluzzo')) {
      return rawName.replaceAll(RegExp(r'\bcrudo\b', caseSensitive: false), 'in umido');
    } else if (nameLower.contains('uova') || nameLower.contains('uovo')) {
      return rawName.replaceAll(RegExp(r'\bcrudo\b', caseSensitive: false), 'sode');
    }

    // VERDURE
    else if (nameLower.contains('spinaci')) {
      return rawName.replaceAll(RegExp(r'\bcrudi\b', caseSensitive: false), 'saltati');
    } else if (nameLower.contains('bietole')) {
      return rawName.replaceAll(RegExp(r'\bcrude\b', caseSensitive: false), 'lessate');
    } else if (nameLower.contains('broccoli')) {
      return rawName.replaceAll(RegExp(r'\bcrudi\b', caseSensitive: false), 'al vapore');
    } else if (nameLower.contains('cavolfiore')) {
      return rawName.replaceAll(RegExp(r'\bcrudo\b', caseSensitive: false), 'lessato');
    } else if (nameLower.contains('carciofi')) {
      return rawName.replaceAll(RegExp(r'\bcrudi\b', caseSensitive: false), 'trifolati');
    } else if (nameLower.contains('asparagi')) {
      return rawName.replaceAll(RegExp(r'\bcrudi\b', caseSensitive: false), 'al vapore');
    } else if (nameLower.contains('fagiolini')) {
      return rawName.replaceAll(RegExp(r'\bcrudi\b', caseSensitive: false), 'lessati');
    } else if (nameLower.contains('melanzane')) {
      return rawName.replaceAll(RegExp(r'\bcrude\b', caseSensitive: false), 'grigliate');
    } else if (nameLower.contains('funghi')) {
      return rawName.replaceAll(RegExp(r'\bcrudi\b', caseSensitive: false), 'trifolati');
    } else if (nameLower.contains('patate')) {
      return rawName.replaceAll(RegExp(r'\bcrude\b', caseSensitive: false), 'lesse');
    }

    // CEREALI E LEGUMI
    else if (nameLower.contains('riso')) {
      return rawName.replaceAll(RegExp(r'\bcrudo\b', caseSensitive: false), 'bollito');
    } else if (nameLower.contains('pasta')) {
      return rawName.replaceAll(RegExp(r'\bcruda\b', caseSensitive: false), 'cotta');
    } else if (nameLower.contains('farro')) {
      return rawName.replaceAll(RegExp(r'\bcrudo\b', caseSensitive: false), 'bollito');
    } else if (nameLower.contains('orzo')) {
      return rawName.replaceAll(RegExp(r'\bcrudo\b', caseSensitive: false), 'bollito');
    } else if (nameLower.contains('fagioli')) {
      return rawName.replaceAll(RegExp(r'\bcrudi\b', caseSensitive: false), 'lessati');
    } else if (nameLower.contains('lenticchie')) {
      return rawName.replaceAll(RegExp(r'\bcrude\b', caseSensitive: false), 'lessate');
    } else if (nameLower.contains('ceci')) {
      return rawName.replaceAll(RegExp(r'\bcrudi\b', caseSensitive: false), 'lessati');
    }

    // Fallback generico
    return rawName.replaceAll(RegExp(r'\bcrudo\b|\bcruda\b|\bcrudi\b|\bcrude\b', caseSensitive: false), 'cotto');
  }

  /// Genera una descrizione appropriata per la versione cotta
  static String _getCookedDescription(String rawDescription, String foodName) {
    final nameLower = foodName.toLowerCase();

    if (nameLower.contains('petto di pollo')) {
      return 'Petto di pollo grigliato, senza pelle, cotto';
    } else if (nameLower.contains('pollo')) {
      return rawDescription.replaceAll(RegExp(r'\bcrudo\b', caseSensitive: false), 'grigliato');
    } else if (nameLower.contains('manzo')) {
      return rawDescription.replaceAll(RegExp(r'\bcrudo\b', caseSensitive: false), 'grigliato');
    } else if (nameLower.contains('pesce')) {
      return rawDescription.replaceAll(RegExp(r'\bcrudo\b', caseSensitive: false), 'cotto al vapore');
    }

    return rawDescription.replaceAll(RegExp(r'\bcrudo\b', caseSensitive: false), 'cotto');
  }

  /// Aggiorna la dimensione della porzione per la versione cotta
  static String _getCookedServingSize(String rawServingSize) {
    return rawServingSize.replaceAll(RegExp(r'\bcrudo\b', caseSensitive: false), 'cotto');
  }

  /// Verifica se un alimento richiede cottura
  static bool requiresCooking(Food food) {
    return !isFoodSafe(food) && food.foodState == FoodState.raw;
  }

  /// Ottieni metodi di cottura italiani appropriati per un alimento
  static List<String> getItalianCookingMethods(Food food) {
    final nameLower = food.name.toLowerCase();

    if (nameLower.contains('pollo')) {
      return ['alla griglia', 'al forno', 'in padella', 'arrosto'];
    } else if (nameLower.contains('manzo')) {
      return ['alla griglia', 'brasato', 'arrosto', 'ai ferri'];
    } else if (nameLower.contains('vitello')) {
      return ['ai ferri', 'in padella', 'arrosto', 'scaloppine'];
    } else if (nameLower.contains('agnello')) {
      return ['arrosto', 'alla griglia', 'in umido', 'al forno'];
    } else if (nameLower.contains('maiale')) {
      return ['alla griglia', 'arrosto', 'in padella', 'brasato'];
    } else if (nameLower.contains('pesce')) {
      return ['al vapore', 'al forno', 'alla griglia', 'in umido', 'al sale'];
    } else if (nameLower.contains('uova')) {
      return ['sode', 'in camicia', 'strapazzate', 'alla coque'];
    }

    return ['cotto'];
  }
}
