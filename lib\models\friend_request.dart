/// <PERSON>lo per le richieste di amicizia nella community
class FriendRequest {
  final String id;
  final String fromUserId;
  final String toUserId;
  final String? message;
  final DateTime createdAt;
  final FriendRequestStatus status;

  const FriendRequest({
    required this.id,
    required this.fromUserId,
    required this.toUserId,
    this.message,
    required this.createdAt,
    required this.status,
  });

  /// Crea una nuova richiesta di amicizia
  factory FriendRequest.create({
    required String fromUserId,
    required String toUserId,
    String? message,
  }) {
    return FriendRequest(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      fromUserId: fromUserId,
      toUserId: toUserId,
      message: message,
      createdAt: DateTime.now(),
      status: FriendRequestStatus.pending,
    );
  }

  /// Crea una copia con modifiche
  FriendRequest copyWith({
    String? id,
    String? fromUserId,
    String? toUserId,
    String? message,
    DateTime? createdAt,
    FriendRequestStatus? status,
  }) {
    return FriendRequest(
      id: id ?? this.id,
      fromUserId: fromUserId ?? this.fromUserId,
      toUserId: toUserId ?? this.toUserId,
      message: message ?? this.message,
      createdAt: createdAt ?? this.createdAt,
      status: status ?? this.status,
    );
  }

  /// Converte da JSON
  factory FriendRequest.fromJson(Map<String, dynamic> json) {
    return FriendRequest(
      id: json['id'] as String,
      fromUserId: json['fromUserId'] as String,
      toUserId: json['toUserId'] as String,
      message: json['message'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      status: FriendRequestStatus.values.firstWhere(
        (status) => status.name == json['status'],
        orElse: () => FriendRequestStatus.pending,
      ),
    );
  }

  /// Converte a JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fromUserId': fromUserId,
      'toUserId': toUserId,
      'message': message,
      'createdAt': createdAt.toIso8601String(),
      'status': status.name,
    };
  }

  /// Verifica se la richiesta è in sospeso
  bool get isPending => status == FriendRequestStatus.pending;

  /// Verifica se la richiesta è stata accettata
  bool get isAccepted => status == FriendRequestStatus.accepted;

  /// Verifica se la richiesta è stata rifiutata
  bool get isRejected => status == FriendRequestStatus.rejected;

  /// Verifica se la richiesta è stata cancellata
  bool get isCancelled => status == FriendRequestStatus.cancelled;

  /// Verifica se la richiesta è ancora attiva (pending)
  bool get isActive => isPending;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FriendRequest && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'FriendRequest(id: $id, from: $fromUserId, to: $toUserId, status: $status)';
  }
}

/// Stati possibili per una richiesta di amicizia
enum FriendRequestStatus {
  /// Richiesta in attesa di risposta
  pending,
  
  /// Richiesta accettata
  accepted,
  
  /// Richiesta rifiutata
  rejected,
  
  /// Richiesta cancellata dal mittente
  cancelled,
}

/// Estensioni per FriendRequestStatus
extension FriendRequestStatusExtension on FriendRequestStatus {
  /// Ottiene la descrizione localizzata dello stato
  String get displayName {
    switch (this) {
      case FriendRequestStatus.pending:
        return 'In attesa';
      case FriendRequestStatus.accepted:
        return 'Accettata';
      case FriendRequestStatus.rejected:
        return 'Rifiutata';
      case FriendRequestStatus.cancelled:
        return 'Cancellata';
    }
  }

  /// Ottiene il colore associato allo stato
  String get colorHex {
    switch (this) {
      case FriendRequestStatus.pending:
        return '#FFC107'; // Giallo/Arancione
      case FriendRequestStatus.accepted:
        return '#4CAF50'; // Verde
      case FriendRequestStatus.rejected:
        return '#F44336'; // Rosso
      case FriendRequestStatus.cancelled:
        return '#9E9E9E'; // Grigio
    }
  }

  /// Verifica se lo stato è finale (non può più cambiare)
  bool get isFinal {
    return this == FriendRequestStatus.accepted ||
           this == FriendRequestStatus.rejected ||
           this == FriendRequestStatus.cancelled;
  }
}
