import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/ultra_detailed_profile.dart';
import '../models/user_profile.dart';
import 'user_profile_service.dart';

/// SERVIZIO PER GESTIONE PROFILI ULTRA-DETTAGLIATI
/// Gestisce creazione, salvataggio e caricamento dei profili avanzati
class UltraProfileService {
  static const String _profileKey = 'ultra_detailed_profile';
  static const String _profileHistoryKey = 'ultra_profile_history';

  final UserProfileService _baseProfileService = UserProfileService();

  /// Crea un profilo ultra-dettagliato da zero con wizard guidato
  Future<UltraDetailedProfile> createUltraProfile({
    required String name,
    required int age,
    required Gender gender,
    required double weight,
    required double height,
    required Goal goal,

    // Dati avanzati opzionali
    double? bodyFatPercentage,
    double? leanMass,
    BodyCompositionMethod? bodyCompositionMethod,
    Map<String, double>? circumferences,
    BloodValues? bloodValues,

    // Attività fisica
    required WorkActivity workActivity,
    List<PlannedExercise> plannedExercises = const [],
    required NEATLevel neatLevel,

    // Obiettivi
    required PrimaryGoal primaryGoal,
    List<SecondaryGoal> secondaryGoals = const [],
    WeightLossDetails? weightLossDetails,
    MuscleGainDetails? muscleGainDetails,
    PerformanceGoals? performanceGoals,

    // Preferenze alimentari
    DietaryRegimen? dietaryRegimen,
    List<String> dislikedFoods = const [],
    List<String> preferredFoods = const [],
    BudgetLevel budgetLevel = BudgetLevel.medium,
    CookingTime cookingTime = CookingTime.medium,
    int mealsPerDay = 3,
    MealPrepHabits mealPrepHabits = MealPrepHabits.occasional,
    CookingSkillLevel cookingSkillLevel = CookingSkillLevel.intermediate,

    // Condizioni mediche
    List<String> medicalConditions = const [],
    List<FoodAllergy> foodAllergies = const [],
    List<FoodIntolerance> foodIntolerances = const [],
    bool hasConsultedDoctor = false,

    // Preferenze avanzate
    bool allowCheatMeals = false,
    int cheatMealsPerWeek = 0,
    bool preferOrganicFoods = false,
    bool preferLocalFoods = false,
    bool enableCalorieCycling = false,
    bool enableMealTiming = false,
  }) async {
    print('🔧 Creazione profilo ultra-dettagliato per $name');

    // Crea profilo base
    final baseProfile = UserProfile(
      id: 'user_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      age: age,
      gender: gender,
      weight: weight.round(),
      height: height.round(),
      goal: goal,
      activityLevel: _convertWorkActivityToActivityLevel(workActivity),
      mealsPerDay: mealsPerDay,
    );

    // Calcola massa magra se non fornita ma abbiamo % grasso
    double? calculatedLeanMass = leanMass;
    if (calculatedLeanMass == null && bodyFatPercentage != null) {
      calculatedLeanMass = weight * (1 - bodyFatPercentage / 100);
      print('📊 Massa magra calcolata: ${calculatedLeanMass.toStringAsFixed(1)}kg');
    }

    // Crea profilo ultra-dettagliato
    final ultraProfile = UltraDetailedProfile(
      id: 'ultra_${DateTime.now().millisecondsSinceEpoch}',
      createdAt: DateTime.now(),
      lastUpdated: DateTime.now(),
      baseProfile: baseProfile,
      bodyFatPercentage: bodyFatPercentage,
      leanMass: calculatedLeanMass,
      bodyCompositionMethod: bodyCompositionMethod,
      circumferences: circumferences,
      bloodValues: bloodValues,
      workActivity: workActivity,
      plannedExercises: plannedExercises,
      neatLevel: neatLevel,
      primaryGoal: primaryGoal,
      secondaryGoals: secondaryGoals,
      weightLossDetails: weightLossDetails,
      muscleGainDetails: muscleGainDetails,
      performanceGoals: performanceGoals,
      dietaryRegimen: dietaryRegimen,
      dislikedFoods: dislikedFoods,
      preferredFoods: preferredFoods,
      budgetLevel: budgetLevel,
      cookingTime: cookingTime,
      mealsPerDay: mealsPerDay,
      mealPrepHabits: mealPrepHabits,
      cookingSkillLevel: cookingSkillLevel,
      medicalConditions: medicalConditions,
      foodAllergies: foodAllergies,
      foodIntolerances: foodIntolerances,
      hasConsultedDoctor: hasConsultedDoctor,
      allowCheatMeals: allowCheatMeals,
      cheatMealsPerWeek: cheatMealsPerWeek,
      preferOrganicFoods: preferOrganicFoods,
      preferLocalFoods: preferLocalFoods,
      enableCalorieCycling: enableCalorieCycling,
      enableMealTiming: enableMealTiming,
    );

    // Validazione sicurezza
    final validation = ultraProfile.validateNutritionalSafety();
    if (!validation['isValid']) {
      print('⚠️ Avvertenze profilo: ${validation['warnings']}');
      print('❌ Errori profilo: ${validation['errors']}');
    }

    // Salva profilo
    await saveUltraProfile(ultraProfile);

    print('✅ Profilo ultra-dettagliato creato e salvato');
    return ultraProfile;
  }

  /// Carica il profilo ultra-dettagliato salvato
  Future<UltraDetailedProfile?> loadUltraProfile() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profileJson = prefs.getString(_profileKey);

      if (profileJson != null) {
        final profile = UltraDetailedProfile.fromJson(profileJson);
        print('📱 Profilo ultra-dettagliato caricato: ${profile.baseProfile.name}');
        return profile;
      }
    } catch (e) {
      print('❌ Errore caricamento profilo ultra-dettagliato: $e');
    }

    return null;
  }

  /// Salva il profilo ultra-dettagliato
  Future<void> saveUltraProfile(UltraDetailedProfile profile) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Aggiorna timestamp
      final updatedProfile = UltraDetailedProfile(
        id: profile.id,
        createdAt: profile.createdAt,
        lastUpdated: DateTime.now(),
        baseProfile: profile.baseProfile,
        bodyFatPercentage: profile.bodyFatPercentage,
        leanMass: profile.leanMass,
        bodyCompositionMethod: profile.bodyCompositionMethod,
        circumferences: profile.circumferences,
        weightHistory: profile.weightHistory,
        dietHistory: profile.dietHistory,
        bloodValues: profile.bloodValues,
        preferredBMRFormula: profile.preferredBMRFormula,
        customBMR: profile.customBMR,
        customTDEE: profile.customTDEE,
        workActivity: profile.workActivity,
        plannedExercises: profile.plannedExercises,
        neatLevel: profile.neatLevel,
        primaryGoal: profile.primaryGoal,
        secondaryGoals: profile.secondaryGoals,
        weightLossDetails: profile.weightLossDetails,
        muscleGainDetails: profile.muscleGainDetails,
        performanceGoals: profile.performanceGoals,
        dietaryRegimen: profile.dietaryRegimen,
        dislikedFoods: profile.dislikedFoods,
        preferredFoods: profile.preferredFoods,
        budgetLevel: profile.budgetLevel,
        cookingTime: profile.cookingTime,
        mealsPerDay: profile.mealsPerDay,
        mealPrepHabits: profile.mealPrepHabits,
        cookingSkillLevel: profile.cookingSkillLevel,
        preferredCuisines: profile.preferredCuisines,
        medicalConditions: profile.medicalConditions,
        foodAllergies: profile.foodAllergies,
        foodIntolerances: profile.foodIntolerances,
        hasConsultedDoctor: profile.hasConsultedDoctor,
        allowCheatMeals: profile.allowCheatMeals,
        cheatMealsPerWeek: profile.cheatMealsPerWeek,
        preferOrganicFoods: profile.preferOrganicFoods,
        preferLocalFoods: profile.preferLocalFoods,
        enableCalorieCycling: profile.enableCalorieCycling,
        enableMealTiming: profile.enableMealTiming,
      );

      await prefs.setString(_profileKey, updatedProfile.toJson());

      // Salva anche nella cronologia
      await _saveToHistory(updatedProfile);

      print('💾 Profilo ultra-dettagliato salvato');
    } catch (e) {
      print('❌ Errore salvataggio profilo: $e');
      throw Exception('Impossibile salvare il profilo: $e');
    }
  }

  /// Aggiorna peso nel profilo
  Future<UltraDetailedProfile?> updateWeight(double newWeight, {String? notes}) async {
    final profile = await loadUltraProfile();
    if (profile == null) return null;

    // Aggiungi entry alla storia del peso
    final weightEntry = WeightHistoryEntry(
      date: DateTime.now(),
      weight: newWeight,
      notes: notes,
    );

    // Aggiorna profilo base
    final updatedBaseProfile = UserProfile(
      id: profile.baseProfile.id,
      name: profile.baseProfile.name,
      age: profile.baseProfile.age,
      gender: profile.baseProfile.gender,
      weight: newWeight.round(),
      height: profile.baseProfile.height,
      goal: profile.baseProfile.goal,
      activityLevel: profile.baseProfile.activityLevel,
      mealsPerDay: profile.baseProfile.mealsPerDay,
    );

    // Ricalcola massa magra se abbiamo % grasso
    double? newLeanMass = profile.leanMass;
    if (profile.bodyFatPercentage != null) {
      newLeanMass = newWeight * (1 - profile.bodyFatPercentage! / 100);
    }

    final updatedProfile = UltraDetailedProfile(
      id: profile.id,
      createdAt: profile.createdAt,
      lastUpdated: DateTime.now(),
      baseProfile: updatedBaseProfile,
      bodyFatPercentage: profile.bodyFatPercentage,
      leanMass: newLeanMass,
      bodyCompositionMethod: profile.bodyCompositionMethod,
      circumferences: profile.circumferences,
      weightHistory: [...profile.weightHistory, weightEntry],
      dietHistory: profile.dietHistory,
      bloodValues: profile.bloodValues,
      preferredBMRFormula: profile.preferredBMRFormula,
      customBMR: profile.customBMR,
      customTDEE: profile.customTDEE,
      workActivity: profile.workActivity,
      plannedExercises: profile.plannedExercises,
      neatLevel: profile.neatLevel,
      primaryGoal: profile.primaryGoal,
      secondaryGoals: profile.secondaryGoals,
      weightLossDetails: profile.weightLossDetails,
      muscleGainDetails: profile.muscleGainDetails,
      performanceGoals: profile.performanceGoals,
      dietaryRegimen: profile.dietaryRegimen,
      dislikedFoods: profile.dislikedFoods,
      preferredFoods: profile.preferredFoods,
      budgetLevel: profile.budgetLevel,
      cookingTime: profile.cookingTime,
      mealsPerDay: profile.mealsPerDay,
      mealPrepHabits: profile.mealPrepHabits,
      cookingSkillLevel: profile.cookingSkillLevel,
      preferredCuisines: profile.preferredCuisines,
      medicalConditions: profile.medicalConditions,
      foodAllergies: profile.foodAllergies,
      foodIntolerances: profile.foodIntolerances,
      hasConsultedDoctor: profile.hasConsultedDoctor,
      allowCheatMeals: profile.allowCheatMeals,
      cheatMealsPerWeek: profile.cheatMealsPerWeek,
      preferOrganicFoods: profile.preferOrganicFoods,
      preferLocalFoods: profile.preferLocalFoods,
      enableCalorieCycling: profile.enableCalorieCycling,
      enableMealTiming: profile.enableMealTiming,
    );

    await saveUltraProfile(updatedProfile);
    print('⚖️ Peso aggiornato: ${newWeight}kg');

    return updatedProfile;
  }

  /// Ottieni profilo o crea uno predefinito
  Future<UltraDetailedProfile> getOrCreateUltraProfile() async {
    final existing = await loadUltraProfile();
    if (existing != null) return existing;

    // Crea profilo predefinito
    print('🆕 Creazione profilo ultra-dettagliato predefinito');

    return await createUltraProfile(
      name: 'Utente Demo',
      age: 35,
      gender: Gender.male,
      weight: 75.0,
      height: 175.0,
      goal: Goal.maintenance,
      workActivity: WorkActivity.sedentary,
      neatLevel: NEATLevel.medium,
      primaryGoal: PrimaryGoal.maintenance,
    );
  }

  /// Migra da profilo base a ultra-dettagliato
  Future<UltraDetailedProfile> migrateFromBaseProfile() async {
    final baseProfile = await _baseProfileService.getUserProfile();

    print('🔄 Migrazione da profilo base a ultra-dettagliato');

    return await createUltraProfile(
      name: baseProfile.name,
      age: baseProfile.age,
      gender: baseProfile.gender,
      weight: baseProfile.weight.toDouble(),
      height: baseProfile.height.toDouble(),
      goal: baseProfile.goal,
      workActivity: _convertActivityLevelToWorkActivity(baseProfile.activityLevel),
      neatLevel: NEATLevel.medium,
      primaryGoal: _convertGoalToPrimaryGoal(baseProfile.goal),
      mealsPerDay: baseProfile.mealsPerDay,
    );
  }

  /// Salva nella cronologia
  Future<void> _saveToHistory(UltraDetailedProfile profile) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString(_profileHistoryKey);

      List<Map<String, dynamic>> history = [];
      if (historyJson != null) {
        history = List<Map<String, dynamic>>.from(json.decode(historyJson));
      }

      // Aggiungi snapshot corrente
      history.add({
        'timestamp': DateTime.now().toIso8601String(),
        'weight': profile.baseProfile.weight,
        'bodyFatPercentage': profile.bodyFatPercentage,
        'leanMass': profile.leanMass,
        'primaryGoal': profile.primaryGoal.toString(),
      });

      // Mantieni solo ultimi 50 snapshot
      if (history.length > 50) {
        history = history.sublist(history.length - 50);
      }

      await prefs.setString(_profileHistoryKey, json.encode(history));
    } catch (e) {
      print('⚠️ Errore salvataggio cronologia: $e');
    }
  }

  // METODI DI CONVERSIONE

  ActivityLevel _convertWorkActivityToActivityLevel(WorkActivity workActivity) {
    switch (workActivity) {
      case WorkActivity.sedentary:
        return ActivityLevel.sedentary;
      case WorkActivity.light:
        return ActivityLevel.lightlyActive;
      case WorkActivity.moderate:
        return ActivityLevel.moderatelyActive;
      case WorkActivity.heavy:
        return ActivityLevel.veryActive;
    }
  }

  WorkActivity _convertActivityLevelToWorkActivity(ActivityLevel activityLevel) {
    switch (activityLevel) {
      case ActivityLevel.sedentary:
        return WorkActivity.sedentary;
      case ActivityLevel.lightlyActive:
        return WorkActivity.light;
      case ActivityLevel.moderatelyActive:
        return WorkActivity.moderate;
      case ActivityLevel.veryActive:
        return WorkActivity.heavy;
      case ActivityLevel.extremelyActive:
        return WorkActivity.heavy; // Map extremely active to heavy work activity
    }
  }

  PrimaryGoal _convertGoalToPrimaryGoal(Goal goal) {
    switch (goal) {
      case Goal.weightLoss:
        return PrimaryGoal.weightLoss;
      case Goal.weightGain:
        return PrimaryGoal.weightGain;
      case Goal.maintenance:
        return PrimaryGoal.maintenance;
    }
  }

  /// Elimina profilo ultra-dettagliato
  Future<void> deleteUltraProfile() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_profileKey);
    await prefs.remove(_profileHistoryKey);
    print('🗑️ Profilo ultra-dettagliato eliminato');
  }

  /// Esporta profilo in JSON
  Future<String> exportProfile() async {
    final profile = await loadUltraProfile();
    if (profile == null) throw Exception('Nessun profilo da esportare');

    return profile.toJson();
  }

  /// Importa profilo da JSON
  Future<UltraDetailedProfile> importProfile(String jsonData) async {
    try {
      final profile = UltraDetailedProfile.fromJson(jsonData);
      await saveUltraProfile(profile);
      print('📥 Profilo importato con successo');
      return profile;
    } catch (e) {
      throw Exception('Errore importazione profilo: $e');
    }
  }
}
