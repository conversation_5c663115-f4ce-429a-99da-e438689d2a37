import 'package:flutter/material.dart';
import '../../../models/community_user.dart';
import '../../../utils/theme_colors.dart';

/// Widget per la classifica della community
class CommunityLeaderboardWidget extends StatefulWidget {
  final List<CommunityUser> users;

  const CommunityLeaderboardWidget({
    super.key,
    required this.users,
  });

  @override
  State<CommunityLeaderboardWidget> createState() => _CommunityLeaderboardWidgetState();
}

class _CommunityLeaderboardWidgetState extends State<CommunityLeaderboardWidget> {
  String _selectedPeriod = 'all_time';
  String _selectedCategory = 'points';
  final ScrollController _scrollController = ScrollController();

  /// Ordina gli utenti in base alla categoria selezionata
  List<CommunityUser> get _sortedUsers {
    final users = List<CommunityUser>.from(widget.users);
    
    switch (_selectedCategory) {
      case 'points':
        users.sort((a, b) => b.communityPoints.compareTo(a.communityPoints));
        break;
      case 'posts':
        users.sort((a, b) => b.totalPosts.compareTo(a.totalPosts));
        break;
      case 'likes':
        users.sort((a, b) => b.totalLikes.compareTo(a.totalLikes));
        break;
      case 'followers':
        users.sort((a, b) => b.followersCount.compareTo(a.followersCount));
        break;
    }
    
    return users;
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Filtri
        _buildFilters(),
        
        // Podio (top 3)
        if (_sortedUsers.length >= 3) _buildPodium(),
        
        // Lista classifica
        Expanded(
          child: _buildLeaderboardList(),
        ),
      ],
    );
  }

  /// Costruisce i filtri
  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Filtro periodo
          Row(
            children: [
              const Text(
                'Periodo:',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: SegmentedButton<String>(
                  segments: const [
                    ButtonSegment(
                      value: 'week',
                      label: Text('Settimana', style: TextStyle(fontSize: 12)),
                    ),
                    ButtonSegment(
                      value: 'month',
                      label: Text('Mese', style: TextStyle(fontSize: 12)),
                    ),
                    ButtonSegment(
                      value: 'all_time',
                      label: Text('Sempre', style: TextStyle(fontSize: 12)),
                    ),
                  ],
                  selected: {_selectedPeriod},
                  onSelectionChanged: (Set<String> selection) {
                    setState(() {
                      _selectedPeriod = selection.first;
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // Filtro categoria
          Row(
            children: [
              const Text(
                'Categoria:',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: SegmentedButton<String>(
                  segments: const [
                    ButtonSegment(
                      value: 'points',
                      label: Text('Punti', style: TextStyle(fontSize: 12)),
                    ),
                    ButtonSegment(
                      value: 'posts',
                      label: Text('Post', style: TextStyle(fontSize: 12)),
                    ),
                    ButtonSegment(
                      value: 'likes',
                      label: Text('Like', style: TextStyle(fontSize: 12)),
                    ),
                    ButtonSegment(
                      value: 'followers',
                      label: Text('Follower', style: TextStyle(fontSize: 12)),
                    ),
                  ],
                  selected: {_selectedCategory},
                  onSelectionChanged: (Set<String> selection) {
                    setState(() {
                      _selectedCategory = selection.first;
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Costruisce il podio per i primi 3
  Widget _buildPodium() {
    final top3 = _sortedUsers.take(3).toList();
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // 2° posto
          if (top3.length > 1) _buildPodiumPosition(top3[1], 2, 80),
          
          // 1° posto
          _buildPodiumPosition(top3[0], 1, 100),
          
          // 3° posto
          if (top3.length > 2) _buildPodiumPosition(top3[2], 3, 60),
        ],
      ),
    );
  }

  /// Costruisce una posizione del podio
  Widget _buildPodiumPosition(CommunityUser user, int position, double height) {
    final colors = [
      ThemeColors.accentGold, // 1° posto
      Colors.grey[400]!, // 2° posto
      Colors.brown[300]!, // 3° posto
    ];
    
    final medals = ['🥇', '🥈', '🥉'];
    
    return Column(
      children: [
        // Avatar con corona/medaglia
        Stack(
          alignment: Alignment.center,
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: colors[position - 1].withOpacity(0.1),
              backgroundImage: user.avatarUrl != null
                  ? NetworkImage(user.avatarUrl!)
                  : null,
              child: user.avatarUrl == null
                  ? Text(
                      user.displayName.substring(0, 1).toUpperCase(),
                      style: TextStyle(
                        color: colors[position - 1],
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                    )
                  : null,
            ),
            Positioned(
              top: -5,
              child: Text(
                medals[position - 1],
                style: const TextStyle(fontSize: 20),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        
        // Nome
        Text(
          user.displayName,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
          textAlign: TextAlign.center,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        
        // Valore
        Text(
          _getValueForCategory(user),
          style: TextStyle(
            color: colors[position - 1],
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 8),
        
        // Piedistallo
        Container(
          width: 60,
          height: height,
          decoration: BoxDecoration(
            color: colors[position - 1].withOpacity(0.2),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
            border: Border.all(
              color: colors[position - 1].withOpacity(0.5),
              width: 2,
            ),
          ),
          child: Center(
            child: Text(
              position.toString(),
              style: TextStyle(
                color: colors[position - 1],
                fontWeight: FontWeight.bold,
                fontSize: 24,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Costruisce la lista della classifica
  Widget _buildLeaderboardList() {
    final remainingUsers = _sortedUsers.skip(3).toList();
    
    if (remainingUsers.isEmpty) {
      return const Center(
        child: Text(
          'Non ci sono altri utenti in classifica',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }
    
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: remainingUsers.length,
      itemBuilder: (context, index) {
        final user = remainingUsers[index];
        final position = index + 4; // +4 perché i primi 3 sono nel podio
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: _buildLeaderboardItem(user, position),
        );
      },
    );
  }

  /// Costruisce un elemento della classifica
  Widget _buildLeaderboardItem(CommunityUser user, int position) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Posizione
            Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                color: position <= 10
                    ? ThemeColors.primaryGreen.withOpacity(0.1)
                    : Colors.grey[100],
                borderRadius: BorderRadius.circular(15),
              ),
              child: Center(
                child: Text(
                  position.toString(),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: position <= 10
                        ? ThemeColors.primaryGreen
                        : Colors.grey[600],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            
            // Avatar
            CircleAvatar(
              radius: 20,
              backgroundColor: ThemeColors.primaryGreen.withOpacity(0.1),
              backgroundImage: user.avatarUrl != null
                  ? NetworkImage(user.avatarUrl!)
                  : null,
              child: user.avatarUrl == null
                  ? Text(
                      user.displayName.substring(0, 1).toUpperCase(),
                      style: TextStyle(
                        color: ThemeColors.primaryGreen,
                        fontWeight: FontWeight.bold,
                      ),
                    )
                  : null,
            ),
          ],
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                user.displayName,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            if (user.isVerified)
              Icon(
                Icons.verified,
                size: 16,
                color: ThemeColors.primaryBlue,
              ),
            const SizedBox(width: 4),
            Text(
              user.membershipLevel.icon,
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
        subtitle: Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Color(int.parse('0xFF${user.membershipLevel.colorHex.substring(1)}')).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                user.membershipLevel.displayName,
                style: TextStyle(
                  fontSize: 10,
                  color: Color(int.parse('0xFF${user.membershipLevel.colorHex.substring(1)}')),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            if (user.isMentor) ...[
              const SizedBox(width: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: ThemeColors.accentGold.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '👨‍🏫 Mentor',
                  style: TextStyle(
                    fontSize: 10,
                    color: ThemeColors.accentGold,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              _getValueForCategory(user),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
                color: ThemeColors.primaryGreen,
              ),
            ),
            Text(
              _getCategoryLabel(),
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        onTap: () => _viewUserProfile(user),
      ),
    );
  }

  /// Ottiene il valore per la categoria selezionata
  String _getValueForCategory(CommunityUser user) {
    switch (_selectedCategory) {
      case 'points':
        return _formatNumber(user.communityPoints);
      case 'posts':
        return user.totalPosts.toString();
      case 'likes':
        return _formatNumber(user.totalLikes);
      case 'followers':
        return _formatNumber(user.followersCount);
      default:
        return '0';
    }
  }

  /// Ottiene l'etichetta della categoria
  String _getCategoryLabel() {
    switch (_selectedCategory) {
      case 'points':
        return 'punti';
      case 'posts':
        return 'post';
      case 'likes':
        return 'like';
      case 'followers':
        return 'follower';
      default:
        return '';
    }
  }

  /// Formatta un numero per la visualizzazione
  String _formatNumber(int number) {
    if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    }
    return number.toString();
  }

  /// Visualizza il profilo di un utente
  void _viewUserProfile(CommunityUser user) {
    // Implementa la visualizzazione del profilo
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Visualizzazione profilo: ${user.displayName}'),
        duration: const Duration(seconds: 1),
      ),
    );
  }
}
