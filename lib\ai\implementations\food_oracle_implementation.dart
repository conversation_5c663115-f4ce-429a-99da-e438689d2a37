import 'dart:convert';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../interfaces/food_oracle_interface.dart';
import '../models/food_oracle_models.dart';
import '../services/image_processor_service.dart';
import '../services/food_detector_service.dart';
import '../services/portion_estimator_service.dart';
import '../services/nutrition_calculator_service.dart';
import '../services/suggestion_generator_service.dart';
import '../../models/food.dart';
import '../../models/user_profile.dart';
import '../../services/food_database_service.dart';
import '../../services/storage_service.dart';
import '../../constants/app_constants.dart';
import '../../controllers/badge_controller.dart';

/// Implementazione del sistema Food Oracle
class FoodOracleImplementation implements FoodOracleInterface {
  /// Servizio per l'elaborazione delle immagini
  final ImageProcessorService _imageProcessor = ImageProcessorService();

  /// Servizio per il rilevamento degli alimenti
  final FoodDetectorService _foodDetector = FoodDetectorService();

  /// Servizio per la stima delle porzioni
  final PortionEstimatorService _portionEstimator = PortionEstimatorService();

  /// Servizio per il calcolo dei valori nutrizionali
  final NutritionCalculatorService _nutritionCalculator = NutritionCalculatorService();

  /// Servizio per la generazione di suggerimenti
  final SuggestionGeneratorService _suggestionGenerator = SuggestionGeneratorService();

  /// Servizio per il database degli alimenti
  late FoodDatabaseService _foodDatabaseService;

  /// Servizio per lo storage
  late StorageService _storageService;

  /// Generatore di UUID
  final Uuid _uuid = Uuid();

  /// Flag di inizializzazione
  bool _isInitialized = false;

  /// Statistiche di utilizzo
  final Map<String, dynamic> _stats = {
    'totalAnalyses': 0,
    'successfulAnalyses': 0,
    'averageConfidenceScore': 0.0,
    'totalDetectedFoods': 0,
    'mostDetectedFoods': <String, int>{},
  };

  /// Costruttore
  FoodOracleImplementation({
    required FoodDatabaseService foodDatabaseService,
    required StorageService storageService,
  }) :
    _foodDatabaseService = foodDatabaseService,
    _storageService = storageService;

  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Inizializza i servizi
      await _foodDetector.initialize();
      await _suggestionGenerator.initialize();

      _isInitialized = true;
    } catch (e) {
      print('Errore nell\'inizializzazione di FoodOracleImplementation: $e');
      _isInitialized = false;
    }
  }

  @override
  Future<FoodOracleAnalysisResult> analyzeImage(
    File image,
    UserProfile userProfile,
    {String? mealType}
  ) async {
    if (!_isInitialized) {
      await initialize();
    }

    // Genera un ID univoco per l'analisi
    final analysisId = _uuid.v4();

    // Ottieni la directory temporanea
    final tempDir = await getTemporaryDirectory();
    final processedImagePath = path.join(tempDir.path, 'processed_$analysisId.jpg');

    try {
      // Elabora l'immagine
      final processedImageBytes = await _imageProcessor.processImageAsync(
        image.path,
        targetSize: [640, 480],
      );

      // Salva l'immagine elaborata
      await File(processedImagePath).writeAsBytes(processedImageBytes);

      // Rileva gli alimenti nell'immagine
      final detectedFoods = await _foodDetector.detectFoods(processedImageBytes);

      // Calcola i valori nutrizionali totali
      final totalNutritionalValues = _nutritionCalculator.calculateTotalNutritionalValues(
        detectedFoods,
      );

      // Calcola il punteggio di confidenza medio
      final avgConfidenceScore = detectedFoods.isEmpty
          ? 0.0
          : detectedFoods.fold<double>(
              0,
              (sum, food) => sum + food.confidenceScore,
            ) / detectedFoods.length;

      // Crea il risultato dell'analisi
      final analysisResult = FoodOracleAnalysisResult(
        id: analysisId,
        timestamp: DateTime.now(),
        imagePath: processedImagePath,
        mealType: mealType,
        detectedFoods: detectedFoods,
        totalNutritionalValues: totalNutritionalValues,
        confidenceScore: avgConfidenceScore,
        warnings: _generateWarnings(detectedFoods, avgConfidenceScore),
      );

      // Genera suggerimenti se ci sono alimenti rilevati
      if (detectedFoods.isNotEmpty) {
        final suggestions = await _suggestionGenerator.generateSuggestions(
          analysisResult,
          userProfile,
        );

        // Aggiorna il risultato con i suggerimenti
        final updatedResult = analysisResult.copyWith(
          suggestions: suggestions,
        );

        // Aggiorna le statistiche
        _updateStats(updatedResult);

        // Salva i dati per l'integrazione con le sfide
        await _saveScanDataForChallenges(updatedResult);

        return updatedResult;
      }

      // Aggiorna le statistiche
      _updateStats(analysisResult);

      // Salva i dati per l'integrazione con le sfide
      await _saveScanDataForChallenges(analysisResult);

      return analysisResult;
    } catch (e) {
      print('Errore nell\'analisi dell\'immagine: $e');

      // Crea un risultato di errore
      return FoodOracleAnalysisResult(
        id: analysisId,
        timestamp: DateTime.now(),
        imagePath: image.path,
        mealType: mealType,
        detectedFoods: [],
        totalNutritionalValues: NutritionalValues(
          calories: 0,
          proteins: 0,
          carbs: 0,
          fats: 0,
          fiber: 0,
          sugar: 0,
        ),
        confidenceScore: 0,
        warnings: ['Errore nell\'analisi dell\'immagine: $e'],
      );
    }
  }

  @override
  Future<FoodOracleAnalysisResult> analyzeImageFromPath(
    String imagePath,
    UserProfile userProfile,
    {String? mealType}
  ) async {
    final imageFile = File(imagePath);
    return analyzeImage(imageFile, userProfile, mealType: mealType);
  }

  @override
  Future<FoodOracleAnalysisResult> correctAnalysis(
    FoodOracleAnalysisResult analysisResult,
    FoodOracleUserCorrections userCorrections
  ) async {
    // Crea una copia della lista degli alimenti rilevati
    final correctedFoods = List<DetectedFood>.from(analysisResult.detectedFoods);

    // Rimuovi gli alimenti specificati
    for (final index in userCorrections.foodsToRemove) {
      if (index >= 0 && index < correctedFoods.length) {
        correctedFoods.removeAt(index);
      }
    }

    // Aggiungi i nuovi alimenti
    correctedFoods.addAll(userCorrections.foodsToAdd);

    // Modifica gli alimenti specificati
    userCorrections.foodsToModify.forEach((index, modifiedFood) {
      if (index >= 0 && index < correctedFoods.length) {
        correctedFoods[index] = modifiedFood;
      }
    });

    // Ricalcola i valori nutrizionali totali
    final totalNutritionalValues = _nutritionCalculator.calculateTotalNutritionalValues(
      correctedFoods,
    );

    // Calcola il nuovo punteggio di confidenza medio
    final avgConfidenceScore = correctedFoods.isEmpty
        ? 0.0
        : correctedFoods.fold<double>(
            0,
            (sum, food) => sum + food.confidenceScore,
          ) / correctedFoods.length;

    // Crea il risultato corretto
    final correctedResult = analysisResult.copyWith(
      detectedFoods: correctedFoods,
      totalNutritionalValues: totalNutritionalValues,
      confidenceScore: avgConfidenceScore,
      warnings: _generateWarnings(correctedFoods, avgConfidenceScore),
      // Rimuovi i suggerimenti esistenti, verranno rigenerati se necessario
      suggestions: null,
    );

    return correctedResult;
  }

  @override
  Future<bool> saveAnalysisToFoodDiary(
    FoodOracleAnalysisResult analysisResult,
    String userId,
    {DateTime? date}
  ) async {
    try {
      // Usa la data corrente se non specificata
      final entryDate = date ?? DateTime.now();

      // Crea una voce del diario alimentare per ogni alimento rilevato
      for (final detectedFood in analysisResult.detectedFoods) {
        // Simuliamo l'aggiunta al diario alimentare
        print('Aggiunta al diario alimentare: ${detectedFood.food.name} (${detectedFood.estimatedGrams}g)');
        // In una versione reale, utilizzeremmo un metodo come:
        // await _storageService.addFoodDiaryEntry(
        //   userId: userId,
        //   foodId: detectedFood.food.id,
        //   grams: detectedFood.estimatedGrams,
        //   mealType: analysisResult.mealType ?? 'snack',
        //   date: entryDate,
        //   imageUrl: analysisResult.imagePath,
        // );
      }

      return true;
    } catch (e) {
      print('Errore nel salvataggio dell\'analisi nel diario alimentare: $e');
      return false;
    }
  }

  @override
  Future<List<FoodOracleSuggestion>> getSuggestions(
    FoodOracleAnalysisResult analysisResult,
    UserProfile userProfile
  ) async {
    return _suggestionGenerator.generateSuggestions(
      analysisResult,
      userProfile,
    );
  }

  @override
  bool isInitialized() {
    return _isInitialized;
  }

  @override
  Future<Map<String, dynamic>> getStats() async {
    return {
      ..._stats,
      'isInitialized': _isInitialized,
      'version': '1.0.0',
      'name': AppConstants.foodOracleName,
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }

  /// Genera avvisi in base ai risultati dell'analisi
  List<String> _generateWarnings(List<DetectedFood> detectedFoods, double confidenceScore) {
    final warnings = <String>[];

    // Avviso se non sono stati rilevati alimenti
    if (detectedFoods.isEmpty) {
      warnings.add('Nessun alimento rilevato nell\'immagine. Prova a scattare una foto più chiara o da un\'angolazione diversa.');
    }

    // Avviso se il punteggio di confidenza è basso
    if (confidenceScore < 0.6 && detectedFoods.isNotEmpty) {
      warnings.add('Bassa confidenza nel riconoscimento degli alimenti. I risultati potrebbero non essere accurati.');
    }

    // Avviso se ci sono alimenti con punteggio di confidenza molto basso
    for (final food in detectedFoods) {
      if (food.confidenceScore < 0.4) {
        warnings.add('Bassa confidenza nel riconoscimento di ${food.food.name}. Verifica manualmente.');
      }
    }

    return warnings;
  }

  /// Aggiorna le statistiche di utilizzo
  void _updateStats(FoodOracleAnalysisResult analysisResult) {
    _stats['totalAnalyses'] = (_stats['totalAnalyses'] as int? ?? 0) + 1;

    if (analysisResult.detectedFoods.isNotEmpty) {
      _stats['successfulAnalyses'] = (_stats['successfulAnalyses'] as int? ?? 0) + 1;
    }

    // Aggiorna la media del punteggio di confidenza
    final totalAnalyses = _stats['totalAnalyses'] as int;
    final currentAvg = _stats['averageConfidenceScore'] as double? ?? 0.0;
    _stats['averageConfidenceScore'] = ((currentAvg * (totalAnalyses - 1)) + analysisResult.confidenceScore) / totalAnalyses;

    // Aggiorna il conteggio totale degli alimenti rilevati
    _stats['totalDetectedFoods'] = (_stats['totalDetectedFoods'] as int? ?? 0) + analysisResult.detectedFoods.length;

    // Aggiorna gli alimenti più rilevati
    final mostDetectedFoods = _stats['mostDetectedFoods'] as Map<String, int>? ?? {};
    for (final food in analysisResult.detectedFoods) {
      mostDetectedFoods[food.food.name] = (mostDetectedFoods[food.food.name] ?? 0) + 1;
    }
    _stats['mostDetectedFoods'] = mostDetectedFoods;

    // Integrazione con sistema badge - Notifica scansione completata
    _notifyBadgeSystemForScan(totalAnalyses);
  }

  /// Notifica il sistema badge per una nuova scansione
  Future<void> _notifyBadgeSystemForScan(int totalScans) async {
    try {
      print('📸 Notifica badge system: $totalScans scansioni totali');

      // Aggiorna il contatore globale delle scansioni
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('food_oracle_total_scans', totalScans);

      // Notifica il BadgeController
      final badgeController = BadgeController.instance;
      await badgeController.checkFoodOracleBadges(totalScans);

    } catch (e) {
      print('❌ Errore nella notifica badge system: $e');
    }
  }

  /// Salva i dati della scansione per l'integrazione con le sfide
  Future<void> _saveScanDataForChallenges(FoodOracleAnalysisResult result) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now();
      final todayKey = '${today.year}-${today.month}-${today.day}';

      // Ottieni i dati esistenti di oggi
      final existingDataJson = prefs.getString('food_oracle_scans_$todayKey');
      Map<String, dynamic> todayData = {};

      if (existingDataJson != null) {
        todayData = jsonDecode(existingDataJson) as Map<String, dynamic>;
      }

      // Inizializza la lista se non esiste
      if (!todayData.containsKey('scanned_foods')) {
        todayData['scanned_foods'] = <Map<String, dynamic>>[];
      }

      // Aggiungi i nuovi alimenti scansionati
      final scannedFoods = List<Map<String, dynamic>>.from(todayData['scanned_foods']);

      for (final detectedFood in result.detectedFoods) {
        scannedFoods.add({
          'name': detectedFood.food.name,
          'category': detectedFood.food.categories.isNotEmpty
              ? detectedFood.food.categories.first.toString().split('.').last
              : 'other',
          'grams': detectedFood.estimatedGrams,
          'confidence': detectedFood.confidenceScore,
          'timestamp': result.timestamp.toIso8601String(),
          'meal_type': result.mealType ?? 'snack',
          'calories': detectedFood.food.calories,
          'proteins': detectedFood.food.proteins,
          'carbs': detectedFood.food.carbs,
          'fats': detectedFood.food.fats,
        });
      }

      // Aggiorna i dati
      todayData['scanned_foods'] = scannedFoods;
      todayData['last_updated'] = DateTime.now().toIso8601String();
      todayData['total_scans'] = scannedFoods.length;

      // Salva i dati aggiornati
      await prefs.setString('food_oracle_scans_$todayKey', jsonEncode(todayData));

      print('💾 Dati Food Oracle salvati per le sfide: ${result.detectedFoods.length} alimenti');

      // Notifica il sistema sfide che ci sono nuovi dati
      await _notifyAdvancedChallengesService();

    } catch (e) {
      print('❌ Errore nel salvataggio dati Food Oracle per sfide: $e');
    }
  }

  /// Notifica il servizio sfide avanzate di nuovi dati
  Future<void> _notifyAdvancedChallengesService() async {
    try {
      // TODO: Implementare notifica diretta al servizio sfide
      // Per ora usiamo un flag in SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('food_oracle_data_updated', true);
      await prefs.setString('food_oracle_last_update', DateTime.now().toIso8601String());

      print('🔔 Servizio sfide notificato di nuovi dati Food Oracle');
    } catch (e) {
      print('❌ Errore nella notifica al servizio sfide: $e');
    }
  }
}
