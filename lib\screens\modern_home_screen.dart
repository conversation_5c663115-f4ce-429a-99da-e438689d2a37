import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../theme/dr_staffilano_theme.dart';
import '../widgets/modern_meal_card.dart';
import '../widgets/weekly_calendar.dart';
import '../widgets/meal_card.dart';
import '../widgets/modern_bottom_navigation.dart';
import '../widgets/user_greeting_header.dart';
import '../widgets/section_header.dart';
import '../constants/image_constants.dart';
import '../constants/app_constants.dart';
import '../utils/image_placeholder_helper.dart';
import '../models/meal.dart';
import '../models/meal_plan.dart';
import '../services/storage_service.dart';
import 'meal_detail_screen.dart';
import 'meal_nutrition_detail_screen.dart';
import 'ultra_advanced_diet_screen.dart';
import 'food_oracle_screen.dart';
import 'meal_plan_screen.dart';
import 'community/community_screen.dart';
import 'food_browser_screen.dart';
import 'ai_recommendations_screen.dart';
import 'welljourney_screen.dart';
import 'nutriscore_screen.dart';

class ModernHomeScreen extends StatefulWidget {
  const ModernHomeScreen({Key? key}) : super(key: key);

  @override
  State<ModernHomeScreen> createState() => _ModernHomeScreenState();
}

class _ModernHomeScreenState extends State<ModernHomeScreen> {
  int _currentIndex = 0;
  DateTime _selectedDate = DateTime.now();
  StorageService? _storageService;
  WeeklyMealPlan? _pianoPasti;
  List<Meal> _todayMeals = [];
  bool _isLoading = true;
  bool _hasGeneratedDiet = false;

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  /// Inizializza i servizi necessari
  Future<void> _initializeServices() async {
    try {
      print('🔄 Inizializzazione ModernHomeScreen...');
      _storageService = await StorageService.getInstance();
      print('✅ StorageService inizializzato');
      await _loadMealPlan();
      print('✅ ModernHomeScreen inizializzato con successo');
    } catch (e) {
      print('❌ Errore nell\'inizializzazione dei servizi: $e');
      setState(() {
        _isLoading = false;
        // Imposta valori di default per evitare null
        _pianoPasti = null;
        _todayMeals = [];
        _hasGeneratedDiet = false;
      });
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Ricarica i dati quando la schermata diventa visibile
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadMealPlan();
    });
  }

  /// Carica il piano pasti e i pasti di oggi
  Future<void> _loadMealPlan() async {
    if (_storageService == null) {
      print('StorageService non inizializzato');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Carica il piano pasti settimanale
      final piano = await _storageService!.getPianoSettimanaleCorrente();

      // Carica il piano dietetico per verificare se esiste
      final dietPlan = await _storageService!.caricaDietPlan();

      // Trova i pasti di oggi
      final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
      final todayPlan = piano.dailyPlans.firstWhere(
        (plan) => plan.date == today,
        orElse: () => DailyPlan(date: today, meals: []),
      );

      setState(() {
        _pianoPasti = piano;
        _todayMeals = todayPlan.meals;
        _hasGeneratedDiet = dietPlan != null;
        _isLoading = false;
      });

      print('Caricati ${_todayMeals.length} pasti per oggi: $today');
      for (var meal in _todayMeals) {
        print('- ${meal.nome} (${meal.calorie} kcal)');
      }
    } catch (e) {
      print('❌ Errore nel caricamento del piano pasti: $e');
      setState(() {
        _pianoPasti = null;
        _todayMeals = [];
        _hasGeneratedDiet = false;
        _isLoading = false;
      });
    }
  }

  void _onDateSelected(DateTime date) {
    setState(() {
      _selectedDate = date;
    });
    // Qui potresti caricare i dati specifici per la data selezionata
  }

  /// Aggiorna un pasto specifico
  void _updateMeal(int index, Meal updatedMeal) {
    if (_pianoPasti == null) return;

    setState(() {
      _todayMeals[index] = updatedMeal;
    });

    _saveMealPlan();
  }

  /// Segna un pasto come completato/non completato
  void _toggleMealCompleted(int index) {
    if (index >= _todayMeals.length) return;

    final updatedMeal = _todayMeals[index].copyWith(
      completato: !_todayMeals[index].completato,
    );

    _updateMeal(index, updatedMeal);
  }

  /// Salva il piano pasti aggiornato
  Future<void> _saveMealPlan() async {
    if (_pianoPasti == null || _storageService == null) return;

    try {
      // Aggiorna il piano giornaliero con i pasti modificati
      final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
      final updatedDailyPlan = DailyPlan(
        date: today,
        meals: _todayMeals,
      );

      final updatedWeeklyPlan = _pianoPasti!.updateDailyPlan(updatedDailyPlan);

      setState(() {
        _pianoPasti = updatedWeeklyPlan;
      });

      await _storageService!.salvaPianoPasti(updatedWeeklyPlan);
      print('Piano pasti salvato con successo');
    } catch (e) {
      print('Errore nel salvataggio del piano pasti: $e');
    }
  }

  void _onNavigationTap(int index) {
    setState(() {
      _currentIndex = index;
    });

    // Implementa la navigazione tra le diverse schermate
    switch (index) {
      case 0:
        // Già nella home, non fare nulla
        break;
      case 1:
        // Vai alla schermata della dieta
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const MealPlanScreen(),
          ),
        ).then((_) {
          setState(() => _currentIndex = 0);
          _loadMealPlan(); // Ricarica i dati quando torniamo
        });
        break;
      case 2:
        // Vai al Food Oracle (al centro)
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const FoodOracleScreen(),
          ),
        ).then((_) => setState(() => _currentIndex = 0));
        break;
      case 3:
        // Vai alla schermata della community (Staffilano InnerCircle™)
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const CommunityScreen(),
          ),
        ).then((_) => setState(() => _currentIndex = 0));
        break;
      case 4:
        // Vai alla schermata AI (diet generator) (spostata)
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const UltraAdvancedDietScreen(),
          ),
        ).then((_) {
          setState(() => _currentIndex = 0);
          _loadMealPlan(); // Ricarica i dati quando torniamo
        });
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DrStaffilanoTheme.backgroundLight,
      body: SafeArea(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator(color: DrStaffilanoTheme.primaryGreen))
            : SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const UserGreetingHeader(
                      userName: 'Camille!',
                      userAvatarUrl: null,
                    ).animate().fadeIn(duration: 300.ms).slideY(begin: -0.2, end: 0),

                    _buildFoodOracleButton().animate().fadeIn(duration: 300.ms, delay: 100.ms).slideY(begin: 0.2, end: 0),

                    _buildNutritionInsightsCard().animate().fadeIn(duration: 300.ms, delay: 150.ms).slideY(begin: 0.2, end: 0),

                    _buildWeeklyCalendar().animate().fadeIn(duration: 300.ms, delay: 200.ms),

                    SectionHeader(
                      title: 'Piano Alimentare',
                      subtitle: _hasGeneratedDiet ? 'Piano AI attivo' : null,
                      onSeeAllTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const MealPlanScreen(),
                          ),
                        ).then((_) => _loadMealPlan());
                      },
                    ).animate().fadeIn(duration: 300.ms, delay: 300.ms),

                    _buildMealPlan().animate().fadeIn(duration: 300.ms, delay: 400.ms),

                    SectionHeader(
                      title: 'Database Alimenti',
                      onSeeAllTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const FoodBrowserScreen(),
                          ),
                        );
                      },
                    ).animate().fadeIn(duration: 300.ms, delay: 500.ms),

                    _buildFoodDatabasePreview().animate().fadeIn(duration: 300.ms, delay: 600.ms),

                    SectionHeader(
                      title: 'WellJourney™',
                      subtitle: 'Il tuo percorso di benessere',
                      onSeeAllTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const WellJourneyScreen(),
                          ),
                        );
                      },
                    ).animate().fadeIn(duration: 300.ms, delay: 700.ms),

                    _buildWellJourneyPreview().animate().fadeIn(duration: 300.ms, delay: 800.ms),

                    const SizedBox(height: 20),
                  ],
                ),
              ),
      ),
      bottomNavigationBar: ModernBottomNavigation(
        currentIndex: _currentIndex,
        onTap: _onNavigationTap,
      ),
    );
  }

  Widget _buildNutritionInsightsCard() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          gradient: DrStaffilanoTheme.primaryGradient,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(16),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AIRecommendationsScreen(),
                ),
              );
            },
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      FontAwesomeIcons.heartPulse,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Insight Nutrizionali',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'Scopri consigli personalizzati del Dr. Staffilano',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.white,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWeeklyCalendar() {
    return WeeklyCalendar(
      initialDate: _selectedDate,
      selectedDate: _selectedDate,
      onDateSelected: _onDateSelected,
    );
  }

  Widget _buildMealPlan() {
    if (_todayMeals.isEmpty) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: DrStaffilanoTheme.backgroundWhite,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: DrStaffilanoTheme.primaryGreen.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              Icon(
                FontAwesomeIcons.wandMagicSparkles,
                size: 48,
                color: DrStaffilanoTheme.primaryGreen.withOpacity(0.6),
              ),
              const SizedBox(height: 16),
              const Text(
                'Nessun piano alimentare',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: DrStaffilanoTheme.textPrimary,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Genera un piano personalizzato con l\'AI del Dr. Staffilano',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  color: DrStaffilanoTheme.textSecondary,
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const UltraAdvancedDietScreen(),
                    ),
                  ).then((_) => _loadMealPlan());
                },
                icon: const Icon(FontAwesomeIcons.wandMagicSparkles),
                label: const Text('Genera Piano AI'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: DrStaffilanoTheme.primaryGreen,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: _todayMeals.asMap().entries.map((entry) {
          final index = entry.key;
          final meal = entry.value;

          return MealCard(
            meal: meal,
            onTap: () {
              // Se il pasto ha alimenti dettagliati, usa la schermata nutrizionale avanzata
              // altrimenti usa l'editor semplice
              if (meal.foods.isNotEmpty) {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => MealNutritionDetailScreen(
                      meal: meal,
                      mealTitle: meal.nome,
                    ),
                  ),
                );
              } else {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => MealDetailScreen(
                      pasto: meal,
                      onSave: (updatedMeal) {
                        _updateMeal(index, updatedMeal);
                      },
                    ),
                  ),
                );
              }
            },
            onCompletedChanged: (completed) {
              _toggleMealCompleted(index);
            },
          );
        }).toList(),
      ),
    );
  }

  Widget _buildFoodDatabasePreview() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          _buildFoodCategoryCard(
            'Proteine',
            'Carni, pesce, uova e legumi',
            FontAwesomeIcons.drumstickBite,
            DrStaffilanoTheme.primaryGreen,
          ),
          const SizedBox(height: 12),
          _buildFoodCategoryCard(
            'Carboidrati',
            'Cereali, pasta, riso e pane',
            FontAwesomeIcons.wheatAwn,
            DrStaffilanoTheme.secondaryBlue,
          ),
          const SizedBox(height: 12),
          _buildFoodCategoryCard(
            'Frutta e Verdura',
            'Vitamine e minerali essenziali',
            FontAwesomeIcons.appleWhole,
            DrStaffilanoTheme.accentGold,
          ),
        ],
      ),
    );
  }

  Widget _buildFoodCategoryCard(String title, String subtitle, IconData icon, Color color) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.backgroundWhite,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const FoodBrowserScreen(),
              ),
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: DrStaffilanoTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: const TextStyle(
                          fontSize: 14,
                          color: DrStaffilanoTheme.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: DrStaffilanoTheme.textSecondary,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFoodOracleButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          gradient: DrStaffilanoTheme.goldGradient,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: DrStaffilanoTheme.accentGold.withOpacity(0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(16),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const FoodOracleScreen(),
                ),
              );
            },
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      FontAwesomeIcons.camera,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Food Oracle AI',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'Scansiona il tuo cibo per analisi nutrizionale istantanea',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.white,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWellJourneyPreview() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          _buildWellJourneyCard(
            'Percorsi Guidati',
            'Segui percorsi nutrizionali personalizzati',
            FontAwesomeIcons.route,
            DrStaffilanoTheme.primaryGreen,
            () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const WellJourneyScreen(
                    initialTabIndex: WellJourneyTabs.pathways,
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 12),
          _buildWellJourneyCard(
            'NutriScore Personale',
            'Monitora la qualità della tua alimentazione',
            FontAwesomeIcons.chartLine,
            DrStaffilanoTheme.secondaryBlue,
            () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const WellJourneyScreen(
                    initialTabIndex: WellJourneyTabs.nutriScore,
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 12),
          _buildWellJourneyCard(
            'Sfide',
            'Completa sfide nutrizionali personalizzate',
            FontAwesomeIcons.bullseye,
            DrStaffilanoTheme.accentGold,
            () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const WellJourneyScreen(
                    initialTabIndex: WellJourneyTabs.challenges,
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 12),
          _buildWellJourneyCard(
            'Badge',
            'Colleziona badge per i tuoi successi',
            FontAwesomeIcons.trophy,
            DrStaffilanoTheme.professionalBlue,
            () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const WellJourneyScreen(
                    initialTabIndex: WellJourneyTabs.badges,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildWellJourneyCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.backgroundWhite,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: DrStaffilanoTheme.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: const TextStyle(
                          fontSize: 14,
                          color: DrStaffilanoTheme.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: color,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
