import '../models/food.dart';
import '../models/food_categories_extended.dart';

/// Servizio per la gestione delle categorie alimentari estese
class ExtendedCategoriesService {
  /// Converte una categoria estesa da stringa a enum
  static FoodCategoryExtended? stringToExtendedCategory(String categoryString) {
    try {
      return FoodCategoryExtended.values.firstWhere(
        (cat) => cat.toString().split('.').last == categoryString
      );
    } catch (e) {
      return null;
    }
  }
  
  /// Converte una lista di stringhe di categorie estese in enum
  static List<FoodCategoryExtended> stringsToExtendedCategories(List<String> categoryStrings) {
    List<FoodCategoryExtended> result = [];
    
    for (String catString in categoryStrings) {
      FoodCategoryExtended? category = stringToExtendedCategory(catString);
      if (category != null) {
        result.add(category);
      }
    }
    
    return result;
  }
  
  /// Ottieni le categorie estese di un alimento come enum
  static List<FoodCategoryExtended> getFoodExtendedCategories(Food food) {
    return stringsToExtendedCategories(food.extendedCategories);
  }
  
  /// Ottieni le categorie estese di un alimento come stringhe visualizzabili
  static List<String> getFoodExtendedCategoryNames(Food food) {
    List<FoodCategoryExtended> categories = getFoodExtendedCategories(food);
    return categories.map((cat) => cat.displayName).toList();
  }
  
  /// Verifica se un alimento appartiene a una categoria estesa
  static bool foodBelongsToExtendedCategory(Food food, FoodCategoryExtended category) {
    return food.extendedCategories.contains(category.toString().split('.').last);
  }
  
  /// Suggerisci categorie estese appropriate per un alimento in base alle sue proprietà
  static List<FoodCategoryExtended> suggestExtendedCategories(Food food) {
    List<FoodCategoryExtended> suggestions = [];
    
    // Suggerimenti basati sulla categoria principale
    for (FoodCategory mainCategory in food.categories) {
      switch (mainCategory) {
        case FoodCategory.fruit:
          if (food.isSeasonal) {
            suggestions.add(FoodCategoryExtended.fruitFresh);
          }
          break;
          
        case FoodCategory.vegetable:
          // Verdure a foglia
          if (food.name.toLowerCase().contains('spinac') || 
              food.name.toLowerCase().contains('lattuga') ||
              food.name.toLowerCase().contains('insalata') ||
              food.name.toLowerCase().contains('cavolo')) {
            suggestions.add(FoodCategoryExtended.vegetableLeafy);
          }
          
          // Verdure a radice
          if (food.name.toLowerCase().contains('carota') || 
              food.name.toLowerCase().contains('patata') ||
              food.name.toLowerCase().contains('barbabietola') ||
              food.name.toLowerCase().contains('rapa')) {
            suggestions.add(FoodCategoryExtended.vegetableRoot);
          }
          break;
          
        case FoodCategory.grain:
          // Cereali integrali
          if (food.name.toLowerCase().contains('integral') || 
              food.name.toLowerCase().contains('integrale')) {
            suggestions.add(FoodCategoryExtended.grainWhole);
          } else {
            suggestions.add(FoodCategoryExtended.grainRefined);
          }
          
          // Pasta
          if (food.name.toLowerCase().contains('pasta') || 
              food.name.toLowerCase().contains('spaghetti') ||
              food.name.toLowerCase().contains('penne')) {
            suggestions.add(FoodCategoryExtended.grainPasta);
          }
          
          // Pane
          if (food.name.toLowerCase().contains('pane') || 
              food.name.toLowerCase().contains('focaccia') ||
              food.name.toLowerCase().contains('pizza')) {
            suggestions.add(FoodCategoryExtended.grainBread);
          }
          break;
          
        case FoodCategory.protein:
          // Carni
          if (food.name.toLowerCase().contains('manzo') || 
              food.name.toLowerCase().contains('vitello') ||
              food.name.toLowerCase().contains('maiale')) {
            suggestions.add(FoodCategoryExtended.proteinMeatRed);
          }
          
          if (food.name.toLowerCase().contains('pollo') || 
              food.name.toLowerCase().contains('tacchino') ||
              food.name.toLowerCase().contains('coniglio')) {
            suggestions.add(FoodCategoryExtended.proteinMeatWhite);
          }
          
          // Pesce
          if (food.name.toLowerCase().contains('pesce') || 
              food.name.toLowerCase().contains('tonno') ||
              food.name.toLowerCase().contains('salmone')) {
            suggestions.add(FoodCategoryExtended.proteinFish);
          }
          
          // Legumi
          if (food.name.toLowerCase().contains('fagioli') || 
              food.name.toLowerCase().contains('lenticchie') ||
              food.name.toLowerCase().contains('ceci')) {
            suggestions.add(FoodCategoryExtended.proteinLegumes);
          }
          break;
          
        case FoodCategory.dairy:
          // Latte
          if (food.name.toLowerCase().contains('latte')) {
            suggestions.add(FoodCategoryExtended.dairyMilk);
          }
          
          // Yogurt
          if (food.name.toLowerCase().contains('yogurt')) {
            suggestions.add(FoodCategoryExtended.dairyYogurt);
          }
          
          // Formaggi
          if (food.name.toLowerCase().contains('formaggio')) {
            if (food.name.toLowerCase().contains('fresco') || 
                food.name.toLowerCase().contains('mozzarella') ||
                food.name.toLowerCase().contains('ricotta')) {
              suggestions.add(FoodCategoryExtended.dairyCheeseFresh);
            } else {
              suggestions.add(FoodCategoryExtended.dairyCheeseAged);
            }
          }
          break;
          
        default:
          break;
      }
    }
    
    return suggestions;
  }
  
  /// Aggiorna un alimento con categorie estese suggerite
  static Food enrichFoodWithExtendedCategories(Food food) {
    // Ottieni le categorie estese suggerite
    List<FoodCategoryExtended> suggestedCategories = suggestExtendedCategories(food);
    
    // Converti le categorie in stringhe
    List<String> categoryStrings = suggestedCategories.map(
      (cat) => cat.toString().split('.').last
    ).toList();
    
    // Unisci le categorie esistenti con quelle suggerite, evitando duplicati
    Set<String> combinedCategories = Set.from(food.extendedCategories)..addAll(categoryStrings);
    
    // Aggiorna l'alimento con le nuove categorie
    return food.copyWith(extendedCategories: combinedCategories.toList());
  }
  
  /// Filtra una lista di alimenti per categoria estesa
  static List<Food> filterFoodsByExtendedCategory(List<Food> foods, FoodCategoryExtended category) {
    String categoryString = category.toString().split('.').last;
    return foods.where((food) => food.extendedCategories.contains(categoryString)).toList();
  }
  
  /// Raggruppa gli alimenti per categoria estesa
  static Map<FoodCategoryExtended, List<Food>> groupFoodsByExtendedCategory(List<Food> foods) {
    Map<FoodCategoryExtended, List<Food>> result = {};
    
    // Inizializza il map con liste vuote per tutte le categorie
    for (FoodCategoryExtended category in FoodCategoryExtended.values) {
      result[category] = [];
    }
    
    // Popola il map con gli alimenti
    for (Food food in foods) {
      List<FoodCategoryExtended> categories = getFoodExtendedCategories(food);
      for (FoodCategoryExtended category in categories) {
        result[category]!.add(food);
      }
    }
    
    return result;
  }
}
