import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import '../models/welljourney_models.dart';
import '../services/welljourney_service.dart';
import '../controllers/welljourney_controller.dart';
import '../theme/dr_staffilano_theme.dart';
import '../constants/app_constants.dart';
import '../widgets/dr_staffilano_logo.dart';
import 'pathway_detail_screen.dart';
import 'nutriscore_screen.dart';
import 'challenges_screen.dart';
import 'badges_screen.dart';

/// Indici delle tab per la navigazione diretta
class WellJourneyTabs {
  static const int pathways = 0;
  static const int nutriScore = 1;
  static const int challenges = 2;
  static const int badges = 3;
}

/// Schermata principale del WellJourney™ Dr. Staffilano
class WellJourneyScreen extends StatefulWidget {
  final int? initialTabIndex;

  const WellJourneyScreen({
    Key? key,
    this.initialTabIndex,
  }) : super(key: key);

  @override
  State<WellJourneyScreen> createState() => _WellJourneyScreenState();
}

class _WellJourneyScreenState extends State<WellJourneyScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  WellJourneyProgress? _userProgress;
  List<GuidedPathway> _pathways = [];
  List<AchievementBadge> _badges = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 4,
      vsync: this,
      initialIndex: widget.initialTabIndex ?? 0,
    );
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final service = WellJourneyService.instance;
      _pathways = service.getAvailablePathways();
      _badges = service.getAvailableBadges();

      // Usa il controller per ottenere il progresso
      final controller = context.read<WellJourneyController>();
      _userProgress = controller.userProgress;

      setState(() => _isLoading = false);
    } catch (e) {
      print('Errore nel caricamento dati WellJourney: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DrStaffilanoTheme.backgroundLight,
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingState() : _buildContent(),
    );
  }



  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Row(
        children: [
          const DrStaffilanoLogo.small(),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'WellJourney™',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: DrStaffilanoTheme.textOnPrimary,
                ),
              ),
              Text(
                'Il tuo percorso di benessere',
                style: TextStyle(
                  fontSize: 12,
                  color: DrStaffilanoTheme.textOnPrimary.withOpacity(0.8),
                ),
              ),
            ],
          ),
        ],
      ),
      backgroundColor: DrStaffilanoTheme.primaryGreen,
      elevation: 0,
      bottom: TabBar(
        controller: _tabController,
        indicatorColor: DrStaffilanoTheme.accentGold,
        labelColor: DrStaffilanoTheme.textOnPrimary,
        unselectedLabelColor: DrStaffilanoTheme.textOnPrimary.withOpacity(0.7),
        tabs: const [
          Tab(icon: Icon(Icons.map), text: 'Percorsi'),
          Tab(icon: Icon(Icons.analytics), text: 'NutriScore'),
          Tab(icon: Icon(Icons.emoji_events), text: 'Sfide'),
          Tab(icon: Icon(Icons.stars), text: 'Badge'),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: DrStaffilanoTheme.primaryGreen,
          ),
          SizedBox(height: 16),
          Text(
            'Caricamento del tuo WellJourney™...',
            style: TextStyle(
              fontSize: 16,
              color: DrStaffilanoTheme.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildPathwaysTab(),
        _buildNutriScoreTab(),
        _buildChallengesTab(),
        _buildBadgesTab(),
      ],
    );
  }

  Widget _buildProgressHeader() {
    if (_userProgress == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.fromLTRB(12, 8, 12, 8),
      padding: const EdgeInsets.all(14),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            DrStaffilanoTheme.primaryGreen,
            DrStaffilanoTheme.primaryGreen.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Livello ${_userProgress!.level}',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  Text(
                    '${_userProgress!.totalPoints} punti',
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: DrStaffilanoTheme.accentGold,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.local_fire_department,
                      color: Colors.white,
                      size: 16,
                    ),
                    const SizedBox(width: 3),
                    Text(
                      '${_userProgress!.currentStreak}',
                      style: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          LinearProgressIndicator(
            value: _userProgress!.getLevelProgress(),
            backgroundColor: Colors.white.withOpacity(0.3),
            valueColor: AlwaysStoppedAnimation<Color>(DrStaffilanoTheme.accentGold),
            minHeight: 6,
          ),
          const SizedBox(height: 6),
          Text(
            '${(_userProgress!.getLevelProgress() * 100).round()}% verso il livello ${_userProgress!.level + 1}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.white.withOpacity(0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPathwaysTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Header del progresso WellJourney solo per i percorsi
        _buildProgressHeader(),
        const SizedBox(height: 16),

        Text(
          'Percorsi Guidati',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: DrStaffilanoTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Scegli il tuo percorso di crescita nutrizionale',
          style: TextStyle(
            fontSize: 16,
            color: DrStaffilanoTheme.textSecondary,
          ),
        ),
        const SizedBox(height: 20),
        ..._pathways.map((pathway) => _buildPathwayCard(pathway)).toList(),
      ],
    );
  }

  Widget _buildPathwayCard(GuidedPathway pathway) {
    return Consumer<WellJourneyController>(
      builder: (context, controller, child) {
        final completionPercentage = pathway.getCompletionPercentage(
          controller.completedModules,
        );
        final isStarted = completionPercentage > 0;
        final isCompleted = completionPercentage >= 1.0;
        final isEnrolled = controller.isEnrolledInPathway(pathway.id);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => _openPathway(pathway),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: pathway.primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        pathway.category.icon,
                        color: pathway.primaryColor,
                        size: 30,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  pathway.title,
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: DrStaffilanoTheme.textPrimary,
                                  ),
                                ),
                              ),
                              if (pathway.isPremium)
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: DrStaffilanoTheme.accentGold,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: const Text(
                                    'PRO',
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            pathway.category.displayName,
                            style: TextStyle(
                              fontSize: 14,
                              color: pathway.primaryColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  pathway.description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: DrStaffilanoTheme.textSecondary,
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: 16),
                if (isStarted) ...[
                  Row(
                    children: [
                      Expanded(
                        child: LinearProgressIndicator(
                          value: completionPercentage,
                          backgroundColor: Colors.grey.shade200,
                          valueColor: AlwaysStoppedAnimation<Color>(pathway.primaryColor),
                          minHeight: 6,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        '${(completionPercentage * 100).round()}%',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: pathway.primaryColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                ],
                Row(
                  children: [
                    Icon(
                      Icons.schedule,
                      size: 16,
                      color: DrStaffilanoTheme.textSecondary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${pathway.estimatedDays} giorni',
                      style: TextStyle(
                        fontSize: 12,
                        color: DrStaffilanoTheme.textSecondary,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Icon(
                      Icons.school,
                      size: 16,
                      color: DrStaffilanoTheme.textSecondary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      pathway.difficulty.displayName,
                      style: TextStyle(
                        fontSize: 12,
                        color: DrStaffilanoTheme.textSecondary,
                      ),
                    ),
                    const Spacer(),
                    if (isCompleted)
                      Icon(
                        Icons.check_circle,
                        color: DrStaffilanoTheme.primaryGreen,
                        size: 20,
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
      },
    );
  }

  Widget _buildNutriScoreTab() {
    return const NutriScoreScreen();
  }

  Widget _buildChallengesTab() {
    return const ChallengesScreen();
  }

  Widget _buildBadgesTab() {
    return const BadgesScreen();
  }

  void _openPathway(GuidedPathway pathway) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PathwayDetailScreen(pathway: pathway),
      ),
    );
  }
}
