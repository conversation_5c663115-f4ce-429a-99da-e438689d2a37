import 'package:flutter/material.dart';
import '../theme/new_app_theme.dart';

/// Helper per gestire i placeholder delle immagini quando non sono disponibili
class ImagePlaceholderHelper {
  /// Crea un widget placeholder per un'immagine con un colore di sfondo e un'icona
  static Widget createPlaceholder({
    required double width,
    required double height,
    Color? backgroundColor,
    IconData? icon,
    String? text,
    double borderRadius = NewAppTheme.borderRadius,
  }) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor ?? NewAppTheme.secondaryColor,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (icon != null)
              Icon(
                icon,
                color: NewAppTheme.primaryColor,
                size: 32,
              ),
            if (text != null)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  text,
                  style: NewAppTheme.bodySmall.copyWith(
                    color: NewAppTheme.textSecondaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Crea un widget che simula un'immagine di sfondo per le card
  static Widget createBackgroundPlaceholder({
    required double width,
    required double height,
    required String title,
    Color? backgroundColor,
    double borderRadius = NewAppTheme.borderRadius,
  }) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            backgroundColor ?? NewAppTheme.primaryColor,
            (backgroundColor ?? NewAppTheme.primaryColor).withOpacity(0.7),
          ],
        ),
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: Center(
        child: Text(
          title,
          style: NewAppTheme.subtitleLarge.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  /// Crea un widget che simula un avatar circolare
  static Widget createAvatarPlaceholder({
    required double size,
    String? initials,
    Color? backgroundColor,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: backgroundColor ?? NewAppTheme.primaryColor.withOpacity(0.2),
        border: Border.all(
          color: NewAppTheme.primaryColor,
          width: 2,
        ),
      ),
      child: Center(
        child: initials != null
            ? Text(
                initials,
                style: TextStyle(
                  color: NewAppTheme.primaryColor,
                  fontWeight: FontWeight.bold,
                  fontSize: size / 2.5,
                ),
              )
            : Icon(
                Icons.person,
                color: NewAppTheme.primaryColor,
                size: size / 2,
              ),
      ),
    );
  }
}
