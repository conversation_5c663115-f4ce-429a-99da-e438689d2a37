import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/community_post.dart';
import '../models/community_user.dart';
import '../models/community_comment.dart';
import '../services/comment_service.dart';
import '../services/notification_service.dart';
import '../theme/dr_staffilano_theme.dart';
import 'community/comment_item_widget.dart';

/// Modal centrato per i commenti dei post (stile dialog)
class CenteredCommentsModal extends StatefulWidget {
  final CommunityPost post;

  const CenteredCommentsModal({
    super.key,
    required this.post,
  });

  @override
  State<CenteredCommentsModal> createState() => _CenteredCommentsModalState();
}

class _CenteredCommentsModalState extends State<CenteredCommentsModal>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  final TextEditingController _commentController = TextEditingController();
  final FocusNode _commentFocusNode = FocusNode();
  bool _isSubmitting = false;
  List<CommunityComment> _comments = [];

  // Utente corrente (TODO: ottenere dal servizio di autenticazione)
  late CommunityUser _currentUser;

  @override
  void initState() {
    super.initState();

    // Inizializza l'utente corrente
    _initializeCurrentUser();

    // Carica i commenti esistenti
    _loadComments();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  /// Inizializza l'utente corrente
  void _initializeCurrentUser() {
    // TODO: Ottenere dall'auth service
    _currentUser = CommunityUser(
      id: 'current_user_id',
      displayName: 'Dr. Giovanni Staffilano',
      username: 'dr.staffilano',
      avatarUrl: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=150&h=150&fit=crop&crop=face',
      bio: 'Cardiologo specializzato in prevenzione e benessere',
      joinDate: DateTime.now().subtract(const Duration(days: 365)),
      lastActive: DateTime.now(),
      isVerified: true,
      membershipLevel: MembershipLevel.ambassador,
      communityPoints: 12500,
      followersCount: 1250,
      followingCount: 89,
      totalPosts: 156,
      totalLikes: 3420,
      badges: ['Cardiologo Verificato', 'Mentore Premium', 'Pioniere Community'],
      interests: ['Cardiologia', 'Prevenzione', 'Nutrizione', 'Benessere'],
      isMentor: true,
    );
  }

  /// Carica i commenti dal servizio
  void _loadComments() {
    final commentService = context.read<CommentService>();
    setState(() {
      _comments = commentService.getCommentsForPost(widget.post.id);
    });
  }

  /// Invia un nuovo commento
  Future<void> _submitComment() async {
    final content = _commentController.text.trim();
    if (content.isEmpty || _isSubmitting) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      final commentService = context.read<CommentService>();
      final notificationService = context.read<NotificationService>();

      // Aggiungi il commento
      final newComment = await commentService.addComment(
        postId: widget.post.id,
        author: _currentUser,
        content: content,
      );

      // Aggiorna la lista locale
      setState(() {
        _comments.add(newComment);
        _commentController.clear();
      });

      // Genera notifica per l'autore del post (se non è l'utente corrente)
      if (widget.post.author != null && widget.post.author!.id != _currentUser.id) {
        await notificationService.createCommentNotification(
          postAuthorId: widget.post.author!.id,
          postId: widget.post.id,
          actorUserId: _currentUser.id,
          actorUserName: _currentUser.displayName,
          actorUserAvatar: _currentUser.avatarUrl ?? '',
          commentPreview: content,
        );
      }

      // Mostra feedback di successo
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('💬 Commento pubblicato!'),
            backgroundColor: DrStaffilanoTheme.primaryGreen,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Errore: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _commentController.dispose();
    _commentFocusNode.dispose();
    super.dispose();
  }



  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Opacity(
          opacity: _opacityAnimation.value,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Dialog(
              backgroundColor: Colors.transparent,
              insetPadding: const EdgeInsets.all(20),
              child: Container(
                constraints: const BoxConstraints(
                  maxWidth: 500,
                  maxHeight: 600,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildHeader(),
                    const Divider(height: 1),
                    Flexible(
                      child: _buildCommentsContent(),
                    ),
                    const Divider(height: 1),
                    _buildCommentInput(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Header del modal
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Icon(
            Icons.chat_bubble_outline,
            color: DrStaffilanoTheme.secondaryBlue,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Commenti',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                if (_comments.isNotEmpty)
                  Text(
                    '${_comments.length} ${_comments.length == 1 ? 'commento' : 'commenti'}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close),
            color: Colors.grey[600],
          ),
        ],
      ),
    );
  }

  /// Contenuto dei commenti
  Widget _buildCommentsContent() {
    if (_comments.isEmpty) {
      return const EmptyCommentsWidget();
    }

    return ListView.separated(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: _comments.length,
      separatorBuilder: (context, index) => const Divider(
        height: 1,
        color: Colors.grey,
        indent: 68,
      ),
      itemBuilder: (context, index) {
        final comment = _comments[index];
        return CommentItemWidget(
          comment: comment,
          onLike: null, // Il like è gestito internamente dal widget
          onReply: () => _handleCommentReply(comment),
          onEdit: comment.author.id == _currentUser.id
              ? () => _handleCommentEdit(comment)
              : null,
          onDelete: comment.author.id == _currentUser.id
              ? () => _handleCommentDelete(comment)
              : null,
        );
      },
    );
  }

  /// Gestisce la risposta a un commento
  void _handleCommentReply(CommunityComment comment) {
    _commentController.text = '@${comment.author.username} ';
    _commentFocusNode.requestFocus();
  }

  /// Gestisce la modifica di un commento
  void _handleCommentEdit(CommunityComment comment) {
    _commentController.text = comment.content;
    _commentFocusNode.requestFocus();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('✏️ Modifica commento - Coming soon!'),
        backgroundColor: DrStaffilanoTheme.professionalBlue,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 1),
      ),
    );
  }

  /// Gestisce l'eliminazione di un commento
  void _handleCommentDelete(CommunityComment comment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Elimina commento'),
        content: const Text('Sei sicuro di voler eliminare questo commento?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annulla'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _deleteComment(comment);
            },
            child: const Text('Elimina', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// Elimina un commento
  Future<void> _deleteComment(CommunityComment comment) async {
    try {
      final commentService = context.read<CommentService>();
      final success = await commentService.removeComment(widget.post.id, comment.id);

      if (success) {
        setState(() {
          _comments.removeWhere((c) => c.id == comment.id);
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('🗑️ Commento eliminato'),
            backgroundColor: DrStaffilanoTheme.primaryGreen,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Errore: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Campo di input per i commenti
  Widget _buildCommentInput() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // Avatar dell'utente corrente
          CircleAvatar(
            radius: 18,
            backgroundColor: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
            backgroundImage: const NetworkImage(
              'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=150&h=150&fit=crop&crop=face',
            ),
          ),
          const SizedBox(width: 12),

          // Campo di input
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Colors.grey[300]!,
                  width: 1,
                ),
              ),
              child: TextField(
                controller: _commentController,
                focusNode: _commentFocusNode,
                decoration: const InputDecoration(
                  hintText: 'Scrivi un commento...',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
                maxLines: null,
                textInputAction: TextInputAction.send,
                onSubmitted: (_) => _submitComment(),
              ),
            ),
          ),

          const SizedBox(width: 8),

          // Pulsante invio
          Container(
            decoration: BoxDecoration(
              color: DrStaffilanoTheme.secondaryBlue,
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: _isSubmitting ? null : _submitComment,
              icon: _isSubmitting
                  ? SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(
                      Icons.send,
                      color: Colors.white,
                      size: 18,
                    ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Funzione helper per mostrare il modal commenti
Future<void> showCenteredCommentsModal(BuildContext context, CommunityPost post) {
  return showDialog(
    context: context,
    barrierDismissible: true,
    builder: (context) => CenteredCommentsModal(post: post),
  );
}
