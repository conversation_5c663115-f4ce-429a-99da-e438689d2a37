import '../models/ai_models.dart';
import '../../models/food.dart';
import '../../models/user_profile.dart';
import '../../models/diet_plan.dart';

/// Interfaccia per il sistema di intelligenza artificiale
abstract class AIInterface {
  /// Inizializza il sistema AI
  Future<void> initialize();

  /// Addestra il modello AI con nuovi dati
  Future<void> train(AILearningContext context);

  /// Ottiene raccomandazioni alimentari personalizzate
  Future<AIRecommendationResponse> getRecommendations(AIRecommendationRequest request);

  /// Registra un feedback dell'utente
  Future<void> recordFeedback(UserFeedback feedback);

  /// Analizza un piano dietetico e fornisce suggerimenti di miglioramento
  Future<List<String>> analyzeDietPlan(WeeklyDietPlan dietPlan, UserProfile userProfile);

  /// Genera un piano dietetico personalizzato basato sull'apprendimento
  Future<WeeklyDietPlan> generatePersonalizedDietPlan(
    UserProfile userProfile, 
    {int weeks = 1}
  );

  /// Ottiene alimenti simili a un alimento dato
  Future<List<FoodRecommendation>> getSimilarFoods(Food food, int limit);

  /// Ottiene alimenti complementari a un alimento dato (buone combinazioni)
  Future<List<FoodRecommendation>> getComplementaryFoods(Food food, int limit);

  /// Ottiene alimenti alternativi a un alimento dato (sostituzioni)
  Future<List<FoodRecommendation>> getAlternativeFoods(Food food, int limit);

  /// Ottiene le preferenze apprese per un utente
  Future<List<LearnedPreference>> getLearnedPreferences(String userId);

  /// Ottiene statistiche sull'apprendimento dell'AI
  Future<Map<String, dynamic>> getAIStats();
}
