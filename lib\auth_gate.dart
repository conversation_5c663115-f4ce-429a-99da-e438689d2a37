import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'screens/auth_screen.dart';
import 'screens/modern_home_screen.dart';
import 'theme/dr_staffilano_theme.dart';

/// AuthGate - Gateway di autenticazione robusto
///
/// Gestisce:
/// - Persistenza della sessione al riavvio
/// - Auth state changes per OAuth e deep links
/// - Transizioni fluide tra stati di autenticazione
/// - Loading states appropriati
class AuthGate extends StatefulWidget {
  const AuthGate({Key? key}) : super(key: key);

  @override
  State<AuthGate> createState() => _AuthGateState();
}

class _AuthGateState extends State<AuthGate> {
  bool _isLoading = true;
  User? _user;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeAuth();
  }

  /// Inizializza l'autenticazione e gestisce la sessione iniziale
  Future<void> _initializeAuth() async {
    try {
      print('🔐 AuthGate: Inizializzazione autenticazione...');

      // Controlla se c'è una sessione persistente
      final currentUser = Supabase.instance.client.auth.currentUser;
      print('👤 AuthGate: Utente corrente: ${currentUser?.email ?? 'Nessuno'}');

      setState(() {
        _user = currentUser;
        _isLoading = false;
      });

      // Ascolta i cambiamenti di stato dell'autenticazione
      Supabase.instance.client.auth.onAuthStateChange.listen((data) {
        print('🔄 AuthGate: Cambio stato auth - ${data.event}');

        if (mounted) {
          setState(() {
            _user = data.session?.user;
            _errorMessage = null; // Reset errori precedenti
          });

          // Log dettagliato per debug
          if (data.session?.user != null) {
            print('✅ AuthGate: Utente autenticato - ${data.session!.user.email}');
          } else {
            print('❌ AuthGate: Utente disconnesso');
          }
        }
      });

    } catch (error) {
      print('❌ AuthGate: Errore inizializzazione - $error');

      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Errore di inizializzazione: ${error.toString()}';
        });
      }
    }
  }

  /// Riprova l'inizializzazione in caso di errore
  void _retryInitialization() {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    _initializeAuth();
  }

  @override
  Widget build(BuildContext context) {
    // Schermata di loading durante l'inizializzazione
    if (_isLoading) {
      return _buildLoadingScreen();
    }

    // Schermata di errore se l'inizializzazione è fallita
    if (_errorMessage != null) {
      return _buildErrorScreen();
    }

    // Routing basato sullo stato di autenticazione
    if (_user != null) {
      print('🏠 AuthGate: Mostrando home screen per ${_user!.email}');
      return const ModernHomeScreen();
    } else {
      print('🔐 AuthGate: Mostrando schermata di login');
      return const AuthScreen();
    }
  }

  /// Schermata di caricamento elegante
  Widget _buildLoadingScreen() {
    return Scaffold(
      backgroundColor: DrStaffilanoTheme.primaryGreen,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Logo o icona dell'app
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(60),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: const Icon(
                Icons.favorite,
                size: 60,
                color: DrStaffilanoTheme.primaryGreen,
              ),
            ),
            
            const SizedBox(height: 40),
            
            // Titolo dell'app
            const Text(
              'Dr. Staffilano',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            
            const Text(
              'Nutrition',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w300,
                color: Colors.white70,
              ),
            ),
            
            const SizedBox(height: 60),
            
            // Indicatore di caricamento
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              strokeWidth: 3,
            ),
            
            const SizedBox(height: 20),
            
            const Text(
              'Inizializzazione...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Schermata di errore con possibilità di retry
  Widget _buildErrorScreen() {
    return Scaffold(
      backgroundColor: Colors.red.shade50,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 80,
                color: Colors.red.shade400,
              ),
              
              const SizedBox(height: 24),
              
              const Text(
                'Errore di Inizializzazione',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 16),
              
              Text(
                _errorMessage ?? 'Errore sconosciuto',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.red.shade700,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 32),
              
              ElevatedButton.icon(
                onPressed: _retryInitialization,
                icon: const Icon(Icons.refresh),
                label: const Text('Riprova'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: DrStaffilanoTheme.primaryGreen,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 16,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
