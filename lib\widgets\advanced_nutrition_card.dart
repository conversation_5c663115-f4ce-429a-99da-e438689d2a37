import 'package:flutter/material.dart';
import '../models/food.dart';
import '../services/advanced_nutrition_service.dart';
import '../utils/advanced_nutrition_properties.dart';

/// Widget per visualizzare le proprietà nutrizionali avanzate di un alimento
class AdvancedNutritionCard extends StatelessWidget {
  final Food food;
  final bool showTitle;
  final bool showDescription;
  final bool showAdvice;

  const AdvancedNutritionCard({
    Key? key,
    required this.food,
    this.showTitle = true,
    this.showDescription = true,
    this.showAdvice = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Verifica se l'alimento ha proprietà nutrizionali avanzate
    final hasAdvancedProperties = food.advancedProperties.isNotEmpty;

    if (!hasAdvancedProperties) {
      return Card(
        margin: const EdgeInsets.all(8.0),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Center(
            child: Text(
              'Nessuna proprietà nutrizionale avanzata disponibile per questo alimento.',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ),
      );
    }

    // Ottieni le proprietà disponibili
    final availableProperties = AdvancedNutritionService.getAvailableAdvancedProperties(food);

    return Card(
      margin: const EdgeInsets.all(8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (showTitle) ...[
              Text(
                'Proprietà Nutrizionali Avanzate',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16.0),
            ],

            // Visualizza le proprietà nutrizionali avanzate
            ...availableProperties.map((property) => _buildPropertyRow(context, property)),

            if (showAdvice && availableProperties.isNotEmpty) ...[
              const SizedBox(height: 16.0),
              const Divider(),
              const SizedBox(height: 8.0),
              Text(
                'Consigli Nutrizionali',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8.0),
              ..._buildNutritionalAdvice(context),
            ],
          ],
        ),
      ),
    );
  }

  /// Costruisce una riga per una proprietà nutrizionale
  Widget _buildPropertyRow(BuildContext context, String property) {
    final value = food.advancedProperties[property];
    final formattedValue = AdvancedNutritionProperties.formatPropertyValue(property, value);
    final displayName = AdvancedNutritionProperties.getDisplayName(property);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  displayName,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Text(
                formattedValue,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
          if (showDescription) ...[
            const SizedBox(height: 4.0),
            Text(
              AdvancedNutritionProperties.getDescription(property),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontStyle: FontStyle.italic,
                color: Colors.grey[600],
              ),
            ),
          ],
          const SizedBox(height: 4.0),
          _buildPropertyIndicator(context, property, value),
          const SizedBox(height: 8.0),
        ],
      ),
    );
  }

  /// Costruisce un indicatore visivo per una proprietà nutrizionale
  Widget _buildPropertyIndicator(BuildContext context, String property, dynamic value) {
    // Se la proprietà non è numerica, restituisci un widget vuoto
    if (value is! num) {
      return const SizedBox.shrink();
    }

    // Ottieni l'intervallo di riferimento per questa proprietà
    final referenceRanges = AdvancedNutritionProperties.REFERENCE_RANGES[property];
    if (referenceRanges == null) {
      return const SizedBox.shrink();
    }

    // Calcola il valore normalizzato (0-1)
    final min = referenceRanges['min'] ?? 0.0;
    final max = referenceRanges['max'] ?? 100.0;
    final normalizedValue = (value - min) / (max - min);
    final clampedValue = normalizedValue.clamp(0.0, 1.0);

    // Determina il colore in base al valore
    Color color;
    if (property == AdvancedNutritionProperties.PRAL) {
      // Per PRAL, valori negativi (alcalini) sono verdi, positivi (acidi) sono rossi
      if (value < (referenceRanges['neutral_min'] ?? -5.0)) {
        color = Colors.green;
      } else if (value > (referenceRanges['neutral_max'] ?? 5.0)) {
        color = Colors.red;
      } else {
        color = Colors.amber;
      }
    } else if (property == AdvancedNutritionProperties.INFLAMMATORY_INDEX) {
      // Per indice infiammatorio, valori negativi (anti-infiammatori) sono verdi
      if (value < (referenceRanges['neutral_min'] ?? -1.0)) {
        color = Colors.green;
      } else if (value > (referenceRanges['neutral_max'] ?? 1.0)) {
        color = Colors.red;
      } else {
        color = Colors.amber;
      }
    } else {
      // Per altre proprietà, valori più alti sono generalmente migliori
      if (value < (referenceRanges['low'] ?? 30.0)) {
        color = Colors.red;
      } else if (value > (referenceRanges['high'] ?? 70.0)) {
        color = Colors.green;
      } else {
        color = Colors.amber;
      }
    }

    return Container(
      margin: const EdgeInsets.only(top: 4.0),
      height: 8.0,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(4.0),
        child: LinearProgressIndicator(
          value: clampedValue.toDouble(),
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(color),
        ),
      ),
    );
  }

  /// Costruisce i consigli nutrizionali
  List<Widget> _buildNutritionalAdvice(BuildContext context) {
    final advice = AdvancedNutritionService.getNutritionalAdvice(food);

    if (advice.isEmpty) {
      return [
        Text(
          'Nessun consiglio specifico disponibile per questo alimento.',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
      ];
    }

    return advice.map((tip) => Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(Icons.info_outline, size: 16.0, color: Colors.blue),
          const SizedBox(width: 8.0),
          Expanded(
            child: Text(
              tip,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    )).toList();
  }
}
