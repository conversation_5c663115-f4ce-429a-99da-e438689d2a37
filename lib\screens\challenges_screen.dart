import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/welljourney_models.dart';
import '../services/welljourney_service.dart';
import '../controllers/welljourney_controller.dart';
import '../controllers/advanced_challenges_controller.dart';
import '../theme/dr_staffilano_theme.dart';
import 'advanced_challenges_screen.dart';

/// Schermata delle sfide WellJourney™ - Wrapper per la nuova implementazione avanzata
class ChallengesScreen extends StatefulWidget {
  const ChallengesScreen({Key? key}) : super(key: key);

  @override
  State<ChallengesScreen> createState() => _ChallengesScreenState();
}

class _ChallengesScreenState extends State<ChallengesScreen> {

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => AdvancedChallengesController(),
      child: const AdvancedChallengesScreen(),
    );
  }
}
