import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/food.dart';

/// Servizio per la traduzione dei testi nell'app
class TranslationService {
  static TranslationService? _instance;

  // Singleton
  static Future<TranslationService> getInstance() async {
    if (_instance == null) {
      final prefs = await SharedPreferences.getInstance();
      _instance = TranslationService._(prefs);
      await _instance!._loadTranslations();
    }
    return _instance!;
  }

  final SharedPreferences _prefs;

  // Mappa delle traduzioni (chiave: testo originale, valore: traduzione)
  Map<String, String> _translations = {};

  // Lingua di origine predefinita
  String sourceLanguage = 'en';

  // Lingua di destinazione predefinita
  String targetLanguage = 'it';

  // Chiave API per servizio di traduzione (es. Google Translate, DeepL)
  String? apiKey;

  // Indica se utilizzare l'API online o il database locale
  bool useOnlineApi = false;

  TranslationService._(this._prefs);

  /// Carica le traduzioni salvate dalle preferenze
  Future<void> _loadTranslations() async {
    final translationsJson = _prefs.getString('food_translations');
    if (translationsJson != null) {
      try {
        final Map<String, dynamic> data = json.decode(translationsJson);
        _translations = Map<String, String>.from(data);
      } catch (e) {
        print('Errore nel caricamento delle traduzioni: $e');
        _translations = {};
      }
    }

    // Carica le impostazioni
    sourceLanguage = _prefs.getString('translation_source_language') ?? 'en';
    targetLanguage = _prefs.getString('translation_target_language') ?? 'it';
    apiKey = _prefs.getString('translation_api_key');
    useOnlineApi = _prefs.getBool('translation_use_online_api') ?? false;
  }

  /// Salva le traduzioni nelle preferenze
  Future<void> _saveTranslations() async {
    try {
      final translationsJson = json.encode(_translations);
      await _prefs.setString('food_translations', translationsJson);
    } catch (e) {
      print('Errore nel salvataggio delle traduzioni: $e');
    }
  }

  /// Imposta la lingua di origine
  Future<void> setSourceLanguage(String language) async {
    sourceLanguage = language;
    await _prefs.setString('translation_source_language', language);
  }

  /// Imposta la lingua di destinazione
  Future<void> setTargetLanguage(String language) async {
    targetLanguage = language;
    await _prefs.setString('translation_target_language', language);
  }

  /// Imposta la chiave API per il servizio di traduzione
  Future<void> setApiKey(String newApiKey) async {
    apiKey = newApiKey;
    await _prefs.setString('translation_api_key', newApiKey);
  }

  /// Imposta se utilizzare l'API online
  Future<void> setUseOnlineApi(bool newUseOnlineApi) async {
    useOnlineApi = newUseOnlineApi;
    await _prefs.setBool('translation_use_online_api', newUseOnlineApi);
  }

  /// Ottiene la traduzione di un testo
  /// Se la traduzione non esiste, restituisce il testo originale
  String getTranslation(String text) {
    return _translations[text.toLowerCase()] ?? text;
  }

  /// Aggiunge una traduzione manuale
  Future<void> addTranslation(String original, String translation) async {
    _translations[original.toLowerCase()] = translation;
    await _saveTranslations();
  }

  /// Rimuove una traduzione
  Future<void> removeTranslation(String original) async {
    _translations.remove(original.toLowerCase());
    await _saveTranslations();
  }

  /// Traduce un testo utilizzando l'API online o il database locale
  Future<String> translateText(String text) async {
    // Se il testo è vuoto, restituisci il testo originale
    if (text.isEmpty) {
      return text;
    }

    // Controlla se esiste già una traduzione nel database locale
    final existingTranslation = getTranslation(text);
    if (existingTranslation != text) {
      return existingTranslation;
    }

    // Se non è abilitata l'API online, restituisci il testo originale
    if (!useOnlineApi || apiKey == null || apiKey!.isEmpty) {
      return text;
    }

    try {
      // Utilizza l'API di traduzione (esempio con Google Translate)
      final translation = await _translateWithGoogleApi(text);

      // Salva la traduzione nel database locale
      if (translation != text) {
        await addTranslation(text, translation);
      }

      return translation;
    } catch (e) {
      print('Errore nella traduzione: $e');
      return text;
    }
  }

  /// Traduce un testo utilizzando l'API di Google Translate
  Future<String> _translateWithGoogleApi(String text) async {
    if (apiKey == null || apiKey!.isEmpty) {
      throw Exception('API key non impostata');
    }

    try {
      final url = Uri.parse(
        'https://translation.googleapis.com/language/translate/v2?key=$apiKey'
      );

      final response = await http.post(
        url,
        body: {
          'q': text,
          'source': sourceLanguage,
          'target': targetLanguage,
          'format': 'text',
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        if (data.containsKey('data') &&
            data['data'].containsKey('translations') &&
            data['data']['translations'].isNotEmpty) {
          return data['data']['translations'][0]['translatedText'];
        }
      }

      throw Exception('Errore nella traduzione: ${response.statusCode} - ${response.body}');
    } catch (e) {
      throw Exception('Errore nella traduzione: $e');
    }
  }

  /// Traduce un alimento (nome e descrizione)
  Future<Food> translateFood(Food food) async {
    // Traduci il nome dell'alimento
    final translatedName = await translateText(food.name);

    // Traduci la descrizione dell'alimento
    final translatedDescription = await translateText(food.description);

    // Crea una copia dell'alimento con i campi tradotti
    return food.copyWith(
      name: translatedName,
      description: translatedDescription,
    );
  }

  /// Traduce una lista di alimenti
  Future<List<Food>> translateFoods(List<Food> foods) async {
    final translatedFoods = <Food>[];

    for (final food in foods) {
      translatedFoods.add(await translateFood(food));
    }

    return translatedFoods;
  }

  /// Ottiene tutte le traduzioni
  Map<String, String> getAllTranslations() {
    return Map<String, String>.from(_translations);
  }

  /// Importa traduzioni da un file JSON
  Future<int> importTranslations(String jsonContent) async {
    try {
      final Map<String, dynamic> data = json.decode(jsonContent);
      final Map<String, String> newTranslations = Map<String, String>.from(data);

      // Aggiungi le nuove traduzioni
      _translations.addAll(newTranslations);

      // Salva le traduzioni
      await _saveTranslations();

      return newTranslations.length;
    } catch (e) {
      throw Exception('Errore nell\'importazione delle traduzioni: $e');
    }
  }

  /// Esporta traduzioni in formato JSON
  String exportTranslations() {
    try {
      return json.encode(_translations);
    } catch (e) {
      throw Exception('Errore nell\'esportazione delle traduzioni: $e');
    }
  }

  /// Traduce il nome di un alimento
  String translateFoodName(String foodName) {
    // Prima prova a tradurre l'intero nome
    String translatedName = getTranslation(foodName.toLowerCase());

    // Se non è stato trovato, prova a tradurre parola per parola
    if (translatedName == foodName) {
      final words = foodName.split(' ');
      final translatedWords = <String>[];

      for (final word in words) {
        // Prova prima con il database di traduzioni
        String translatedWord = getTranslation(word.toLowerCase());

        // Se non è stato trovato, prova con il dizionario locale
        if (translatedWord == word) {
          translatedWord = translateFoodTermWithLocalDictionary(word.toLowerCase());
        }

        translatedWords.add(translatedWord);
      }

      translatedName = translatedWords.join(' ');

      // Capitalizza la prima lettera
      if (translatedName.isNotEmpty) {
        translatedName = translatedName[0].toUpperCase() + translatedName.substring(1);
      }
    }

    return translatedName;
  }

  /// Traduce un testo utilizzando un dizionario locale di termini alimentari comuni
  String translateFoodTermWithLocalDictionary(String text) {
    // Dizionario di termini alimentari comuni (inglese -> italiano)
    const Map<String, String> foodDictionary = {
      'apple': 'mela',
      'banana': 'banana',
      'orange': 'arancia',
      'strawberry': 'fragola',
      'blueberry': 'mirtillo',
      'raspberry': 'lampone',
      'blackberry': 'mora',
      'grape': 'uva',
      'watermelon': 'anguria',
      'melon': 'melone',
      'pineapple': 'ananas',
      'peach': 'pesca',
      'pear': 'pera',
      'plum': 'prugna',
      'cherry': 'ciliegia',
      'apricot': 'albicocca',
      'kiwi': 'kiwi',
      'mango': 'mango',
      'papaya': 'papaya',
      'fig': 'fico',
      'date': 'dattero',
      'coconut': 'cocco',
      'avocado': 'avocado',
      'lemon': 'limone',
      'lime': 'lime',
      'grapefruit': 'pompelmo',
      'carrot': 'carota',
      'potato': 'patata',
      'tomato': 'pomodoro',
      'onion': 'cipolla',
      'garlic': 'aglio',
      'lettuce': 'lattuga',
      'spinach': 'spinaci',
      'cabbage': 'cavolo',
      'broccoli': 'broccoli',
      'cauliflower': 'cavolfiore',
      'cucumber': 'cetriolo',
      'pepper': 'peperone',
      'eggplant': 'melanzana',
      'zucchini': 'zucchina',
      'pumpkin': 'zucca',
      'mushroom': 'fungo',
      'corn': 'mais',
      'peas': 'piselli',
      'beans': 'fagioli',
      'lentils': 'lenticchie',
      'chickpeas': 'ceci',
      'rice': 'riso',
      'pasta': 'pasta',
      'bread': 'pane',
      'flour': 'farina',
      'sugar': 'zucchero',
      'salt': 'sale',
      'black_pepper': 'pepe nero',
      'oil': 'olio',
      'butter': 'burro',
      'milk': 'latte',
      'cheese': 'formaggio',
      'yogurt': 'yogurt',
      'cream': 'panna',
      'egg': 'uovo',
      'chicken': 'pollo',
      'beef': 'manzo',
      'pork': 'maiale',
      'lamb': 'agnello',
      'fish': 'pesce',
      'salmon': 'salmone',
      'tuna': 'tonno',
      'shrimp': 'gambero',
      'crab': 'granchio',
      'lobster': 'aragosta',
      'oyster': 'ostrica',
      'mussel': 'cozza',
      'clam': 'vongola',
      'squid': 'calamaro',
      'octopus': 'polpo',
      'honey': 'miele',
      'jam': 'marmellata',
      'chocolate': 'cioccolato',
      'coffee': 'caffè',
      'tea': 'tè',
      'juice': 'succo',
      'water': 'acqua',
      'wine': 'vino',
      'beer': 'birra',
      'breakfast': 'colazione',
      'lunch': 'pranzo',
      'dinner': 'cena',
      'snack': 'spuntino',
      'meal': 'pasto',
      'recipe': 'ricetta',
      'ingredient': 'ingrediente',
      'portion': 'porzione',
      'serving': 'porzione',
      'calories': 'calorie',
      'protein': 'proteine',
      'carbohydrate': 'carboidrati',
      'fat': 'grassi',
      'fiber': 'fibre',
      'vitamin': 'vitamina',
      'mineral': 'minerale',
      'nutrient': 'nutriente',
      'diet': 'dieta',
      'nutrition': 'nutrizione',
      'healthy': 'salutare',
      'organic': 'biologico',
      'fresh': 'fresco',
      'frozen': 'surgelato',
      'canned': 'in scatola',
      'dried': 'secco',
      'raw': 'crudo',
      'cooked': 'cotto',
      'baked': 'al forno',
      'fried': 'fritto',
      'grilled': 'alla griglia',
      'boiled': 'bollito',
      'steamed': 'al vapore',
      'roasted': 'arrosto',
      'sweet': 'dolce',
      'sour': 'acido',
      'bitter': 'amaro',
      'salty': 'salato',
      'spicy': 'piccante',
    };

    // Controlla se il testo è presente nel dizionario
    final lowerText = text.toLowerCase();
    if (foodDictionary.containsKey(lowerText)) {
      return foodDictionary[lowerText]!;
    }

    // Se il testo non è presente nel dizionario, restituisci il testo originale
    return text;
  }
}
