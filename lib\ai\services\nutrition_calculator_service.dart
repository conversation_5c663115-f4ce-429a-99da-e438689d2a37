import '../../models/food.dart';
import '../models/food_oracle_models.dart';

/// Servizio per il calcolo dei valori nutrizionali
class NutritionCalculatorService {
  static final NutritionCalculatorService _instance = NutritionCalculatorService._internal();

  factory NutritionCalculatorService() {
    return _instance;
  }

  NutritionCalculatorService._internal();

  /// Calcola i valori nutrizionali per un alimento in base alla quantità
  /// [food] - L'alimento
  /// [grams] - La quantità in grammi
  /// Restituisce i valori nutrizionali calcolati
  NutritionalValues calculateNutritionalValues(Food food, int grams) {
    // I valori nutrizionali sono per 100g, quindi calcoliamo il fattore di scala
    final factor = grams / 100.0;

    // Calcola i valori nutrizionali scalati
    final calories = (food.calories * factor).round();
    final proteins = food.proteins * factor;
    final carbs = food.carbs * factor;
    final fats = food.fats * factor;
    final fiber = food.fiber * factor;
    final sugar = food.sugar * factor;

    // Calcola i micronutrienti scalati
    final micronutrients = <String, double>{};

    // Aggiungi i micronutrienti principali se disponibili
    if (food.calcium != null) {
      micronutrients['calcium'] = food.calcium! * factor;
    }

    if (food.potassium != null) {
      micronutrients['potassium'] = food.potassium! * factor;
    }

    if (food.magnesium != null) {
      micronutrients['magnesium'] = food.magnesium! * factor;
    }

    if (food.phosphorus != null) {
      micronutrients['phosphorus'] = food.phosphorus! * factor;
    }

    if (food.sodium != null) {
      micronutrients['sodium'] = food.sodium! * factor;
    }

    // Aggiungi altri micronutrienti dalla mappa
    food.micronutrients.forEach((key, value) {
      micronutrients[key] = value * factor;
    });

    return NutritionalValues(
      calories: calories,
      proteins: proteins,
      carbs: carbs,
      fats: fats,
      fiber: fiber,
      sugar: sugar,
      micronutrients: micronutrients,
    );
  }

  /// Calcola i valori nutrizionali totali per un insieme di alimenti
  /// [detectedFoods] - Gli alimenti rilevati con le relative quantità
  /// Restituisce i valori nutrizionali totali
  NutritionalValues calculateTotalNutritionalValues(List<DetectedFood> detectedFoods) {
    // Inizializza i valori nutrizionali totali
    var totalValues = NutritionalValues(
      calories: 0,
      proteins: 0,
      carbs: 0,
      fats: 0,
      fiber: 0,
      sugar: 0,
      micronutrients: {},
    );

    // Somma i valori nutrizionali di tutti gli alimenti
    for (final detectedFood in detectedFoods) {
      totalValues = totalValues + detectedFood.nutritionalValues;
    }

    return totalValues;
  }

  /// Calcola la distribuzione dei macronutrienti in percentuale
  /// [nutritionalValues] - I valori nutrizionali
  /// Restituisce la distribuzione dei macronutrienti (proteine, carboidrati, grassi)
  Map<String, double> calculateMacroDistribution(NutritionalValues nutritionalValues) {
    // Calcola le calorie da ogni macronutriente
    final proteinCalories = nutritionalValues.proteins * 4;
    final carbCalories = nutritionalValues.carbs * 4;
    final fatCalories = nutritionalValues.fats * 9;

    // Calcola il totale delle calorie dai macronutrienti
    final totalMacroCalories = proteinCalories + carbCalories + fatCalories;

    // Calcola le percentuali
    final proteinPercentage = totalMacroCalories > 0 ? (proteinCalories / totalMacroCalories) * 100 : 0;
    final carbPercentage = totalMacroCalories > 0 ? (carbCalories / totalMacroCalories) * 100 : 0;
    final fatPercentage = totalMacroCalories > 0 ? (fatCalories / totalMacroCalories) * 100 : 0;

    return {
      'proteins': proteinPercentage.toDouble(),
      'carbs': carbPercentage.toDouble(),
      'fats': fatPercentage.toDouble(),
    };
  }

  /// Calcola il contributo percentuale di un alimento al totale dei valori nutrizionali
  /// [foodValues] - I valori nutrizionali dell'alimento
  /// [totalValues] - I valori nutrizionali totali
  /// Restituisce il contributo percentuale per ogni nutriente
  Map<String, double> calculateNutrientContribution(
    NutritionalValues foodValues,
    NutritionalValues totalValues
  ) {
    final contribution = <String, double>{};

    // Calcola il contributo percentuale per ogni macronutriente
    if (totalValues.calories > 0) {
      contribution['calories'] = (foodValues.calories / totalValues.calories) * 100;
    }

    if (totalValues.proteins > 0) {
      contribution['proteins'] = (foodValues.proteins / totalValues.proteins) * 100;
    }

    if (totalValues.carbs > 0) {
      contribution['carbs'] = (foodValues.carbs / totalValues.carbs) * 100;
    }

    if (totalValues.fats > 0) {
      contribution['fats'] = (foodValues.fats / totalValues.fats) * 100;
    }

    if (totalValues.fiber > 0) {
      contribution['fiber'] = (foodValues.fiber / totalValues.fiber) * 100;
    }

    if (totalValues.sugar > 0) {
      contribution['sugar'] = (foodValues.sugar / totalValues.sugar) * 100;
    }

    // Calcola il contributo percentuale per i micronutrienti comuni
    for (final entry in totalValues.micronutrients.entries) {
      final nutrient = entry.key;
      final totalValue = entry.value;

      if (totalValue > 0 && foodValues.micronutrients.containsKey(nutrient)) {
        final foodValue = foodValues.micronutrients[nutrient] ?? 0;
        contribution[nutrient] = (foodValue / totalValue) * 100;
      }
    }

    return contribution;
  }

  /// Calcola la densità nutrizionale di un alimento
  /// [food] - L'alimento
  /// [grams] - La quantità in grammi
  /// Restituisce un punteggio di densità nutrizionale (0-100)
  double calculateNutrientDensity(Food food, int grams) {
    // Calcola i valori nutrizionali
    final nutritionalValues = calculateNutritionalValues(food, grams);

    // Calcola la densità calorica (calorie per grammo)
    final caloricDensity = nutritionalValues.calories / grams;

    // Calcola la densità proteica (grammi di proteine per 100 calorie)
    final proteinDensity = nutritionalValues.calories > 0
        ? (nutritionalValues.proteins * 100) / nutritionalValues.calories
        : 0;

    // Calcola la densità di fibre (grammi di fibre per 100 calorie)
    final fiberDensity = nutritionalValues.calories > 0
        ? (nutritionalValues.fiber * 100) / nutritionalValues.calories
        : 0;

    // Calcola la densità di micronutrienti (numero di micronutrienti significativi)
    final significantMicronutrients = nutritionalValues.micronutrients.entries
        .where((entry) => entry.value > 0)
        .length;

    // Calcola il punteggio di densità nutrizionale
    // Formula: punteggio = (proteinDensity * 0.3 + fiberDensity * 0.2 + significantMicronutrients * 2) - (caloricDensity * 0.1)
    final score = (proteinDensity * 0.3 + fiberDensity * 0.2 + significantMicronutrients * 2) - (caloricDensity * 0.1);

    // Normalizza il punteggio a 0-100
    return (score * 10).clamp(0, 100);
  }

  /// Calcola il punteggio di qualità nutrizionale di un pasto
  /// [detectedFoods] - Gli alimenti rilevati con le relative quantità
  /// Restituisce un punteggio di qualità nutrizionale (0-100)
  double calculateMealQualityScore(List<DetectedFood> detectedFoods) {
    // Se non ci sono alimenti, restituisci 0
    if (detectedFoods.isEmpty) {
      return 0;
    }

    // Calcola i valori nutrizionali totali
    final totalValues = calculateTotalNutritionalValues(detectedFoods);

    // Calcola la distribuzione dei macronutrienti
    final macroDistribution = calculateMacroDistribution(totalValues);

    // Calcola il punteggio di equilibrio dei macronutrienti (0-40)
    // Ideale: proteine 20-30%, carboidrati 45-65%, grassi 20-35%
    double macroBalanceScore = 0;

    final proteinPercentage = macroDistribution['proteins'] ?? 0;
    if (proteinPercentage >= 15 && proteinPercentage <= 35) {
      macroBalanceScore += 15;
    } else if (proteinPercentage >= 10 && proteinPercentage <= 40) {
      macroBalanceScore += 10;
    } else {
      macroBalanceScore += 5;
    }

    final carbPercentage = macroDistribution['carbs'] ?? 0;
    if (carbPercentage >= 45 && carbPercentage <= 65) {
      macroBalanceScore += 15;
    } else if (carbPercentage >= 35 && carbPercentage <= 75) {
      macroBalanceScore += 10;
    } else {
      macroBalanceScore += 5;
    }

    final fatPercentage = macroDistribution['fats'] ?? 0;
    if (fatPercentage >= 20 && fatPercentage <= 35) {
      macroBalanceScore += 10;
    } else if (fatPercentage >= 15 && fatPercentage <= 40) {
      macroBalanceScore += 5;
    } else {
      macroBalanceScore += 0;
    }

    // Calcola il punteggio di varietà alimentare (0-20)
    // Basato sul numero di categorie alimentari diverse
    final foodCategories = <FoodCategory>{};
    for (final detectedFood in detectedFoods) {
      foodCategories.addAll(detectedFood.food.categories);
    }

    final varietyScore = (foodCategories.length * 5.0).clamp(0, 20);

    // Calcola il punteggio di densità nutrizionale (0-40)
    double nutrientDensityScore = 0;
    for (final detectedFood in detectedFoods) {
      final density = calculateNutrientDensity(
        detectedFood.food,
        detectedFood.estimatedGrams,
      );
      nutrientDensityScore += density * (detectedFood.estimatedGrams / 100);
    }

    // Normalizza il punteggio di densità nutrizionale
    final totalGrams = detectedFoods.fold<int>(
      0,
      (sum, food) => sum + food.estimatedGrams,
    );

    nutrientDensityScore = (nutrientDensityScore / (totalGrams / 100)).clamp(0, 40);

    // Calcola il punteggio totale
    return macroBalanceScore + varietyScore + nutrientDensityScore;
  }
}
