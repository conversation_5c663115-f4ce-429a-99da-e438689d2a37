import 'package:flutter/material.dart';

/// Modelli per il sistema di quiz WellJourney™

/// Domanda del quiz
class QuizQuestion {
  final String id;
  final String question;
  final List<QuizOption> options;
  final int correctAnswerIndex;
  final String explanation;
  final String drStaffilanoInsight;
  final QuestionDifficulty difficulty;
  final List<String> tags;

  const QuizQuestion({
    required this.id,
    required this.question,
    required this.options,
    required this.correctAnswerIndex,
    required this.explanation,
    required this.drStaffilanoInsight,
    required this.difficulty,
    required this.tags,
  });

  bool isCorrectAnswer(int selectedIndex) {
    return selectedIndex == correctAnswerIndex;
  }

  QuizOption get correctOption => options[correctAnswerIndex];
}

/// Opzione di risposta
class QuizOption {
  final String id;
  final String text;
  final String? additionalInfo;

  const QuizOption({
    required this.id,
    required this.text,
    this.additionalInfo,
  });
}

/// Quiz completo per un modulo
class ModuleQuiz {
  final String id;
  final String moduleId;
  final String title;
  final String description;
  final List<QuizQuestion> questions;
  final int passingScore; // Percentuale minima per passare (es. 80)
  final int timeLimit; // Minuti (0 = nessun limite)
  final bool allowRetakes;
  final int maxAttempts;

  const ModuleQuiz({
    required this.id,
    required this.moduleId,
    required this.title,
    required this.description,
    required this.questions,
    this.passingScore = 80,
    this.timeLimit = 0,
    this.allowRetakes = true,
    this.maxAttempts = 3,
  });

  int calculateScore(List<int> userAnswers) {
    if (userAnswers.length != questions.length) return 0;
    
    int correctAnswers = 0;
    for (int i = 0; i < questions.length; i++) {
      if (questions[i].isCorrectAnswer(userAnswers[i])) {
        correctAnswers++;
      }
    }
    
    return ((correctAnswers / questions.length) * 100).round();
  }

  bool isPassing(int score) {
    return score >= passingScore;
  }
}

/// Tentativo di quiz dell'utente
class QuizAttempt {
  final String id;
  final String quizId;
  final String userId;
  final DateTime startTime;
  final DateTime? endTime;
  final List<int> userAnswers;
  final int score;
  final bool isPassed;
  final int attemptNumber;
  final Duration timeSpent;

  const QuizAttempt({
    required this.id,
    required this.quizId,
    required this.userId,
    required this.startTime,
    this.endTime,
    required this.userAnswers,
    required this.score,
    required this.isPassed,
    required this.attemptNumber,
    required this.timeSpent,
  });

  bool get isCompleted => endTime != null;
}

/// Risultato del quiz con feedback
class QuizResult {
  final QuizAttempt attempt;
  final ModuleQuiz quiz;
  final List<QuestionResult> questionResults;
  final String drStaffilanoFeedback;
  final int bonusPoints;
  final QuizPerformanceLevel performanceLevel;

  const QuizResult({
    required this.attempt,
    required this.quiz,
    required this.questionResults,
    required this.drStaffilanoFeedback,
    required this.bonusPoints,
    required this.performanceLevel,
  });
}

/// Risultato per singola domanda
class QuestionResult {
  final QuizQuestion question;
  final int userAnswerIndex;
  final bool isCorrect;
  final String feedback;

  const QuestionResult({
    required this.question,
    required this.userAnswerIndex,
    required this.isCorrect,
    required this.feedback,
  });
}

/// Statistiche quiz dell'utente
class UserQuizStats {
  final String userId;
  final int totalQuizzesTaken;
  final int totalQuizzesPassed;
  final double averageScore;
  final int totalBonusPoints;
  final Map<String, int> categoryScores; // Punteggi per categoria (cardiologia, nutrizione, etc.)
  final List<String> completedQuizzes;
  final DateTime lastQuizDate;
  final int currentStreak; // Quiz consecutivi passati

  const UserQuizStats({
    required this.userId,
    required this.totalQuizzesTaken,
    required this.totalQuizzesPassed,
    required this.averageScore,
    required this.totalBonusPoints,
    required this.categoryScores,
    required this.completedQuizzes,
    required this.lastQuizDate,
    required this.currentStreak,
  });

  double get passRate => totalQuizzesTaken > 0 ? totalQuizzesPassed / totalQuizzesTaken : 0.0;
}

/// Enumerazioni

enum QuestionDifficulty {
  easy,
  medium,
  hard,
}

enum QuizPerformanceLevel {
  excellent,  // 95-100%
  veryGood,   // 85-94%
  good,       // 75-84%
  passing,    // 65-74%
  needsWork,  // <65%
}

/// Estensioni per le enumerazioni

extension QuestionDifficultyExtension on QuestionDifficulty {
  String get displayName {
    switch (this) {
      case QuestionDifficulty.easy:
        return 'Facile';
      case QuestionDifficulty.medium:
        return 'Medio';
      case QuestionDifficulty.hard:
        return 'Difficile';
    }
  }

  Color get color {
    switch (this) {
      case QuestionDifficulty.easy:
        return Colors.green;
      case QuestionDifficulty.medium:
        return Colors.orange;
      case QuestionDifficulty.hard:
        return Colors.red;
    }
  }

  int get points {
    switch (this) {
      case QuestionDifficulty.easy:
        return 5;
      case QuestionDifficulty.medium:
        return 10;
      case QuestionDifficulty.hard:
        return 15;
    }
  }
}

extension QuizPerformanceLevelExtension on QuizPerformanceLevel {
  String get displayName {
    switch (this) {
      case QuizPerformanceLevel.excellent:
        return 'Eccellente';
      case QuizPerformanceLevel.veryGood:
        return 'Molto Buono';
      case QuizPerformanceLevel.good:
        return 'Buono';
      case QuizPerformanceLevel.passing:
        return 'Sufficiente';
      case QuizPerformanceLevel.needsWork:
        return 'Da Migliorare';
    }
  }

  Color get color {
    switch (this) {
      case QuizPerformanceLevel.excellent:
        return Colors.green;
      case QuizPerformanceLevel.veryGood:
        return Colors.lightGreen;
      case QuizPerformanceLevel.good:
        return Colors.blue;
      case QuizPerformanceLevel.passing:
        return Colors.orange;
      case QuizPerformanceLevel.needsWork:
        return Colors.red;
    }
  }

  String get drStaffilanoMessage {
    switch (this) {
      case QuizPerformanceLevel.excellent:
        return 'Eccellente! La vostra comprensione dei concetti cardiologici è di livello professionale. Continuate così!';
      case QuizPerformanceLevel.veryGood:
        return 'Molto bene! Avete acquisito una solida base di conoscenze. Siete sulla strada giusta per la prevenzione cardiovascolare.';
      case QuizPerformanceLevel.good:
        return 'Buon lavoro! La vostra comprensione è buona, ma c\'è ancora margine per approfondire alcuni concetti.';
      case QuizPerformanceLevel.passing:
        return 'Sufficiente, ma possiamo fare meglio. Vi consiglio di rivedere il materiale e riprovare il quiz.';
      case QuizPerformanceLevel.needsWork:
        return 'È importante rivedere attentamente il materiale. La prevenzione cardiovascolare richiede una comprensione solida dei concetti base.';
    }
  }

  int get bonusPoints {
    switch (this) {
      case QuizPerformanceLevel.excellent:
        return 50;
      case QuizPerformanceLevel.veryGood:
        return 25;
      case QuizPerformanceLevel.good:
        return 15;
      case QuizPerformanceLevel.passing:
        return 5;
      case QuizPerformanceLevel.needsWork:
        return 0;
    }
  }
}

/// Funzioni di utilità

QuizPerformanceLevel getPerformanceLevel(int score) {
  if (score >= 95) return QuizPerformanceLevel.excellent;
  if (score >= 85) return QuizPerformanceLevel.veryGood;
  if (score >= 75) return QuizPerformanceLevel.good;
  if (score >= 65) return QuizPerformanceLevel.passing;
  return QuizPerformanceLevel.needsWork;
}

String generateQuizFeedback(QuizResult result) {
  final level = result.performanceLevel;
  final score = result.attempt.score;
  
  String baseFeedback = level.drStaffilanoMessage;
  
  if (score == 100) {
    baseFeedback += '\n\nPerfetto! Avete risposto correttamente a tutte le domande. La vostra dedizione alla salute cardiovascolare è esemplare.';
  } else if (score >= 90) {
    baseFeedback += '\n\nQuasi perfetto! Solo qualche piccolo dettaglio da rifinire, ma la vostra preparazione è eccellente.';
  } else if (score < 65) {
    baseFeedback += '\n\nNon scoraggiatevi: la medicina è complessa e richiede studio costante. Ogni tentativo è un passo verso la comprensione.';
  }
  
  return baseFeedback;
}
