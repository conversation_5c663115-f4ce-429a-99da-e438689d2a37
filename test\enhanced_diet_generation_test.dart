import 'package:flutter_test/flutter_test.dart';
import '../lib/models/user_profile.dart';
import '../lib/models/food.dart';
import '../lib/services/enhanced_diet_generator_service.dart';
import '../lib/services/meal_appropriateness_validator.dart';
import '../lib/data/expanded_italian_foods.dart';

void main() {
  group('Enhanced Diet Generation Tests', () {
    late EnhancedDietGeneratorService dietGenerator;

    setUpAll(() async {
      dietGenerator = await EnhancedDietGeneratorService.getInstance();
    });

    test('Meal appropriateness validation prevents inappropriate selections', () {
      // Test che mozzarella NON sia appropriata per spuntini
      final mozzarella = Food(
        id: 'test_mozzarella',
        name: 'Mozzarella di bufala',
        description: 'Mozzarella fresca di bufala campana',
        calories: 280,
        proteins: 18.0,
        carbs: 4.9,
        fats: 20.0,
        suitableForMeals: [MealType.lunch, MealType.dinner, MealType.snack], // Erroneamente include snack
        categories: [FoodCategory.dairy],
        servingSize: '125g',
        servingSizeGrams: 125,
      );

      // La validazione deve impedire mozzarella come spuntino
      expect(
        MealAppropriatenessValidator.isAppropriateForMeal(mozzarella, MealType.snack),
        isFalse,
        reason: 'Mozzarella non dovrebbe essere appropriata per spuntini',
      );

      // Ma dovrebbe essere OK per pranzo/cena
      expect(
        MealAppropriatenessValidator.isAppropriateForMeal(mozzarella, MealType.lunch),
        isTrue,
        reason: 'Mozzarella dovrebbe essere appropriata per pranzo',
      );

      print('✅ Validazione appropriatezza: mozzarella correttamente esclusa da spuntini');
    });

    test('Traditional Italian snacks are properly validated', () {
      final traditionalSnacks = ExpandedItalianFoods.getTraditionalSnacks();

      expect(traditionalSnacks.isNotEmpty, isTrue);

      // Verifica che tutti gli spuntini tradizionali siano appropriati
      for (final snack in traditionalSnacks) {
        if (snack.suitableForMeals.contains(MealType.snack)) {
          expect(
            MealAppropriatenessValidator.isAppropriateForMeal(snack, MealType.snack),
            isTrue,
            reason: '${snack.name} dovrebbe essere appropriato per spuntini',
          );

          // Verifica dimensioni porzione appropriate per spuntini
          final caloriesPerServing = (snack.calories * snack.servingSizeGrams / 100).round();
          expect(
            caloriesPerServing,
            lessThanOrEqualTo(200),
            reason: '${snack.name} dovrebbe avere max 200 calorie per porzione',
          );
        }
      }

      print('✅ Spuntini tradizionali italiani validati: ${traditionalSnacks.length} alimenti');
    });

    test('Breakfast foods are culturally appropriate', () {
      final breakfastFoods = ExpandedItalianFoods.getBreakfastFoods();

      expect(breakfastFoods.isNotEmpty, isTrue);

      // Verifica che tutti gli alimenti da colazione siano appropriati
      for (final food in breakfastFoods) {
        expect(
          MealAppropriatenessValidator.isAppropriateForMeal(food, MealType.breakfast),
          isTrue,
          reason: '${food.name} dovrebbe essere appropriato per colazione',
        );

        // Verifica che siano marcati come tradizionali italiani
        expect(
          food.isTraditionalItalian,
          isTrue,
          reason: '${food.name} dovrebbe essere tradizionale italiano',
        );
      }

      // Verifica presenza di categorie tipiche della colazione italiana
      final hasGrains = breakfastFoods.any((f) => f.categories.contains(FoodCategory.grain));
      final hasDairy = breakfastFoods.any((f) => f.categories.contains(FoodCategory.dairy));
      final hasSweets = breakfastFoods.any((f) => f.categories.contains(FoodCategory.sweet));

      expect(hasGrains, isTrue, reason: 'Dovrebbero esserci cereali per colazione');
      expect(hasDairy, isTrue, reason: 'Dovrebbero esserci latticini per colazione');
      expect(hasSweets, isTrue, reason: 'Dovrebbero esserci dolci per colazione italiana');

      print('✅ Alimenti da colazione italiana validati: ${breakfastFoods.length} alimenti');
    });

    test('Enhanced diet generator produces appropriate meal plans', () async {
      final testProfile = UserProfile(
        id: 'test_enhanced',
        name: 'Test Enhanced',
        age: 30,
        gender: Gender.male,
        height: 175,
        weight: 70,
        activityLevel: ActivityLevel.moderatelyActive,
        goal: Goal.maintenance,
        dietType: DietType.omnivore,
        allergies: [],
        dislikedFoods: [],
        mealsPerDay: 4,
      );

      // Genera un piano giornaliero
      final dailyPlan = await dietGenerator.generateDailyDietPlan(testProfile, DateTime.now());

      expect(dailyPlan.meals.length, equals(4));

      // Verifica appropriatezza di ogni pasto
      for (final meal in dailyPlan.meals) {
        final mealType = _getMealTypeFromString(meal.type);
        for (final foodPortion in meal.foods) {
          expect(
            MealAppropriatenessValidator.isAppropriateForMeal(foodPortion.food, mealType),
            isTrue,
            reason: '${foodPortion.food.name} non è appropriato per ${meal.type}',
          );
        }

        print('✅ Pasto ${meal.type}: ${meal.foods.length} alimenti appropriati');
      }

      print('✅ Piano giornaliero generato con appropriatezza garantita');
    });

    test('Snack meals contain only light, appropriate foods', () async {
      final testProfile = UserProfile(
        id: 'test_snacks',
        name: 'Test Snacks',
        age: 25,
        gender: Gender.female,
        height: 165,
        weight: 60,
        activityLevel: ActivityLevel.lightlyActive,
        goal: Goal.maintenance,
        dietType: DietType.omnivore,
        allergies: [],
        dislikedFoods: [],
        mealsPerDay: 5, // Include spuntini
      );

      final dailyPlan = await dietGenerator.generateDailyDietPlan(testProfile, DateTime.now());

      // Trova i pasti di tipo spuntino
      final snackMeals = dailyPlan.meals.where((meal) => meal.type == 'snack').toList();

      expect(snackMeals.isNotEmpty, isTrue);

      for (final snackMeal in snackMeals) {
        // Calcola calorie totali del pasto
        final totalCalories = snackMeal.foods.fold(0, (sum, portion) => sum + portion.totalCalories.round());

        // Verifica che gli spuntini siano leggeri
        expect(
          totalCalories,
          lessThanOrEqualTo(250),
          reason: 'Spuntini dovrebbero essere sotto le 250 calorie',
        );

        // Verifica che non contengano alimenti pesanti
        for (final foodPortion in snackMeal.foods) {
          final food = foodPortion.food;

          // Non dovrebbero esserci formaggi freschi
          expect(
            food.name.toLowerCase().contains('mozzarella'),
            isFalse,
            reason: 'Spuntini non dovrebbero contenere mozzarella',
          );

          expect(
            food.name.toLowerCase().contains('ricotta'),
            isFalse,
            reason: 'Spuntini non dovrebbero contenere ricotta fresca',
          );

          // Non dovrebbero esserci piatti complessi
          if (food.isRecipe) {
            expect(
              food.complexity,
              lessThanOrEqualTo(2),
              reason: 'Spuntini non dovrebbero contenere ricette complesse',
            );
          }

          print('✅ Spuntino appropriato: ${food.name} (${foodPortion.totalCalories.round()} kcal)');
        }
      }

      print('✅ Tutti gli spuntini sono appropriati e leggeri');
    });

    test('Expanded Italian food database provides variety', () {
      final allExpandedFoods = ExpandedItalianFoods.getAllExpandedFoods();

      expect(allExpandedFoods.length, greaterThan(20), reason: 'Database dovrebbe avere almeno 20 alimenti');

      // Verifica varietà per tipo di pasto
      final breakfastCount = allExpandedFoods.where((f) => f.suitableForMeals.contains(MealType.breakfast)).length;
      final lunchCount = allExpandedFoods.where((f) => f.suitableForMeals.contains(MealType.lunch)).length;
      final dinnerCount = allExpandedFoods.where((f) => f.suitableForMeals.contains(MealType.dinner)).length;
      final snackCount = allExpandedFoods.where((f) => f.suitableForMeals.contains(MealType.snack)).length;

      expect(breakfastCount, greaterThan(5), reason: 'Almeno 5 opzioni per colazione');
      expect(lunchCount, greaterThan(3), reason: 'Almeno 3 opzioni per pranzo');
      expect(dinnerCount, greaterThan(3), reason: 'Almeno 3 opzioni per cena');
      expect(snackCount, greaterThan(8), reason: 'Almeno 8 opzioni per spuntini');

      // Verifica autenticità italiana
      final italianFoods = allExpandedFoods.where((f) => f.isTraditionalItalian == true).length;
      expect(
        italianFoods,
        equals(allExpandedFoods.length),
        reason: 'Tutti gli alimenti dovrebbero essere tradizionali italiani',
      );

      print('✅ Database espanso: ${allExpandedFoods.length} alimenti italiani');
      print('   - Colazione: $breakfastCount');
      print('   - Pranzo: $lunchCount');
      print('   - Cena: $dinnerCount');
      print('   - Spuntini: $snackCount');
    });

    test('Meal appropriateness suggestions work correctly', () {
      final inappropriateFood = Food(
        id: 'test_inappropriate',
        name: 'Lasagne alla bolognese',
        description: 'Lasagne tradizionali con ragù',
        calories: 350,
        proteins: 18.0,
        carbs: 25.0,
        fats: 20.0,
        suitableForMeals: [MealType.snack], // Inappropriato!
        categories: [FoodCategory.mixed],
        servingSize: '200g',
        servingSizeGrams: 200,
        isRecipe: true,
        complexity: 4,
        preparationTimeMinutes: 60,
      );

      // Verifica che sia identificato come inappropriato per spuntino
      expect(
        MealAppropriatenessValidator.isAppropriateForMeal(inappropriateFood, MealType.snack),
        isFalse,
      );

      // Verifica che ci siano suggerimenti
      final suggestions = MealAppropriatenessValidator.getSuggestions(inappropriateFood, MealType.snack);
      expect(suggestions.isNotEmpty, isTrue);
      expect(
        suggestions.any((s) => s.contains('troppo pesante')),
        isTrue,
        reason: 'Dovrebbe suggerire che è troppo pesante per spuntino',
      );

      print('✅ Suggerimenti appropriatezza funzionano: ${suggestions.length} suggerimenti');
    });

    test('Weekly diet plan maintains appropriateness across all days', () async {
      final testProfile = UserProfile(
        id: 'test_weekly',
        name: 'Test Weekly',
        age: 35,
        gender: Gender.female,
        height: 160,
        weight: 55,
        activityLevel: ActivityLevel.moderatelyActive,
        goal: Goal.maintenance,
        dietType: DietType.omnivore,
        allergies: [],
        dislikedFoods: [],
        mealsPerDay: 4,
      );

      final weeklyPlan = await dietGenerator.generateWeeklyDietPlan(testProfile);

      expect(weeklyPlan.dailyPlans.length, equals(7));

      int totalInappropriate = 0;
      int totalMeals = 0;

      for (final dailyPlan in weeklyPlan.dailyPlans) {
        for (final meal in dailyPlan.meals) {
          totalMeals++;
          final mealType = _getMealTypeFromString(meal.type);
          for (final foodPortion in meal.foods) {
            if (!MealAppropriatenessValidator.isAppropriateForMeal(foodPortion.food, mealType)) {
              totalInappropriate++;
              print('❌ INAPPROPRIATO: ${foodPortion.food.name} in ${meal.type}');
            }
          }
        }
      }

      expect(
        totalInappropriate,
        equals(0),
        reason: 'Non dovrebbero esserci alimenti inappropriati nel piano settimanale',
      );

      print('✅ Piano settimanale: $totalMeals pasti, 0 inappropriatezze');
    });
  });
}

/// Helper method to convert string to MealType
MealType _getMealTypeFromString(String type) {
  switch (type.toLowerCase()) {
    case 'breakfast':
    case 'colazione':
      return MealType.breakfast;
    case 'lunch':
    case 'pranzo':
      return MealType.lunch;
    case 'dinner':
    case 'cena':
      return MealType.dinner;
    case 'snack':
    case 'spuntino':
      return MealType.snack;
    default:
      return MealType.breakfast; // Default fallback
  }
}
