import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'dart:math' as math;
import '../theme/dr_staffilano_theme.dart';
import '../services/nutriscore_service.dart';

/// Dashboard interattiva premium per NutriScore
class InteractiveDashboard extends StatefulWidget {
  final NutriScoreResult result;
  final bool isDarkMode;

  const InteractiveDashboard({
    Key? key,
    required this.result,
    this.isDarkMode = false,
  }) : super(key: key);

  @override
  State<InteractiveDashboard> createState() => _InteractiveDashboardState();
}

class _InteractiveDashboardState extends State<InteractiveDashboard>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _rotationController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    )..repeat();

    _pulseAnimation = Tween<double>(
      begin: 0.95,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(_rotationController);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: widget.isDarkMode ? [
            Colors.grey[800]!.withOpacity(0.8),
            Colors.grey[900]!.withOpacity(0.9),
          ] : [
            DrStaffilanoTheme.primaryGreen.withOpacity(0.05),
            Colors.white,
            DrStaffilanoTheme.secondaryBlue.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
            blurRadius: 30,
            offset: const Offset(0, 15),
            spreadRadius: 5,
          ),
        ],
      ),
      child: Column(
        children: [
          _buildMainScoreWidget(),
          const SizedBox(height: 32),
          _buildQuickStatsGrid(),
          const SizedBox(height: 24),
          _buildProgressIndicators(),
        ],
      ),
    );
  }

  Widget _buildMainScoreWidget() {
    return Container(
      height: 280,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Cerchi di sfondo animati
          AnimatedBuilder(
            animation: _rotationAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotationAnimation.value,
                child: Container(
                  width: 240,
                  height: 240,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: SweepGradient(
                      colors: [
                        DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                        DrStaffilanoTheme.accentGold.withOpacity(0.1),
                        DrStaffilanoTheme.secondaryBlue.withOpacity(0.1),
                        DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
          // Cerchio principale del punteggio
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        _getScoreColor(widget.result.totalScore),
                        _getScoreColor(widget.result.totalScore).withOpacity(0.8),
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: _getScoreColor(widget.result.totalScore).withOpacity(0.4),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: _buildScoreContent(),
                ),
              );
            },
          ),
          // Indicatori di livello
          ..._buildLevelIndicators(),
        ],
      ),
    );
  }

  Widget _buildScoreContent() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        TweenAnimationBuilder<double>(
          duration: const Duration(milliseconds: 2000),
          tween: Tween(begin: 0, end: widget.result.totalScore),
          curve: Curves.easeOutCubic,
          builder: (context, value, child) {
            return Text(
              value.round().toString(),
              style: const TextStyle(
                fontSize: 48,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                shadows: [
                  Shadow(
                    offset: Offset(0, 2),
                    blurRadius: 4,
                    color: Colors.black26,
                  ),
                ],
              ),
            );
          },
        ),
        const Text(
          '/100',
          style: TextStyle(
            fontSize: 18,
            color: Colors.white70,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
            ),
          ),
          child: Text(
            widget.result.level.displayName,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  List<Widget> _buildLevelIndicators() {
    final levels = [
      {'angle': 0.0, 'label': 'Novizio', 'active': widget.result.totalScore >= 0},
      {'angle': math.pi / 2, 'label': 'Principiante', 'active': widget.result.totalScore >= 40},
      {'angle': math.pi, 'label': 'Intermedio', 'active': widget.result.totalScore >= 60},
      {'angle': 3 * math.pi / 2, 'label': 'Avanzato', 'active': widget.result.totalScore >= 75},
    ];

    return levels.map((level) {
      final angle = level['angle'] as double;
      final isActive = level['active'] as bool;

      return Positioned(
        left: 140 + 110 * math.cos(angle - math.pi / 2),
        top: 140 + 110 * math.sin(angle - math.pi / 2),
        child: Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isActive ? DrStaffilanoTheme.accentGold : Colors.grey.shade300,
            boxShadow: isActive ? [
              BoxShadow(
                color: DrStaffilanoTheme.accentGold.withOpacity(0.5),
                blurRadius: 8,
                spreadRadius: 2,
              ),
            ] : null,
          ),
        ),
      );
    }).toList();
  }

  Widget _buildQuickStatsGrid() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        _buildStatCard(
          'Educativo',
          '${widget.result.educationalScore.round()}%',
          FontAwesomeIcons.graduationCap,
          DrStaffilanoTheme.primaryGreen,
          widget.result.educationalScore / 100,
        ),
        _buildStatCard(
          'Consistenza',
          '${widget.result.consistencyScore.round()}%',
          FontAwesomeIcons.calendar,
          DrStaffilanoTheme.secondaryBlue,
          widget.result.consistencyScore / 100,
        ),
        _buildStatCard(
          'Applicazione',
          '${widget.result.applicationScore.round()}%',
          FontAwesomeIcons.handHoldingHeart,
          DrStaffilanoTheme.accentGold,
          widget.result.applicationScore / 100,
        ),
        _buildStatCard(
          'Expertise',
          '${widget.result.expertiseScore.round()}%',
          FontAwesomeIcons.award,
          Colors.purple,
          widget.result.expertiseScore / 100,
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color, double progress) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.isDarkMode ? Colors.grey[800] : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 16,
                ),
              ),
              const Spacer(),
              Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: widget.isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 1500),
            tween: Tween(begin: 0, end: progress),
            curve: Curves.easeOutCubic,
            builder: (context, animatedProgress, child) {
              return LinearProgressIndicator(
                value: animatedProgress,
                backgroundColor: color.withOpacity(0.1),
                valueColor: AlwaysStoppedAnimation<Color>(color),
                minHeight: 4,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicators() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: widget.isDarkMode ? [
            Colors.grey[800]!.withOpacity(0.8),
            Colors.grey[700]!.withOpacity(0.6),
          ] : [
            DrStaffilanoTheme.secondaryBlue.withOpacity(0.05),
            DrStaffilanoTheme.secondaryBlue.withOpacity(0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: DrStaffilanoTheme.secondaryBlue.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.trophy,
                color: DrStaffilanoTheme.accentGold,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'Prossimo Traguardo',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: widget.isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            widget.result.nextMilestone.title,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: DrStaffilanoTheme.accentGold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.result.nextMilestone.description,
            style: TextStyle(
              fontSize: 14,
              color: widget.isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TweenAnimationBuilder<double>(
                  duration: const Duration(milliseconds: 2000),
                  tween: Tween(
                    begin: 0,
                    end: widget.result.totalScore / widget.result.nextMilestone.targetScore,
                  ),
                  curve: Curves.easeOutCubic,
                  builder: (context, progress, child) {
                    return Container(
                      height: 8,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4),
                        gradient: LinearGradient(
                          colors: [
                            DrStaffilanoTheme.accentGold,
                            DrStaffilanoTheme.accentGold.withOpacity(0.7),
                          ],
                        ),
                      ),
                      child: FractionallySizedBox(
                        alignment: Alignment.centerLeft,
                        widthFactor: progress.clamp(0.0, 1.0),
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4),
                            color: DrStaffilanoTheme.accentGold,
                            boxShadow: [
                              BoxShadow(
                                color: DrStaffilanoTheme.accentGold.withOpacity(0.4),
                                blurRadius: 4,
                                spreadRadius: 1,
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(width: 16),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: DrStaffilanoTheme.accentGold.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${widget.result.nextMilestone.pointsNeeded} punti',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: DrStaffilanoTheme.accentGold,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getScoreColor(double score) {
    if (score >= 90) return const Color(0xFF2E7D32);
    if (score >= 75) return DrStaffilanoTheme.primaryGreen;
    if (score >= 60) return DrStaffilanoTheme.accentGold;
    if (score >= 40) return Colors.orange;
    return Colors.red;
  }
}
