import 'services/specific_diet_generator_service.dart';
import 'services/food_variety_manager.dart';
import 'models/user_profile.dart';

/// Test semplice per verificare l'integrazione del sistema di varietà
Future<void> main() async {
  print('🧪 TEST SEMPLICE INTEGRAZIONE VARIETÀ');
  print('=' * 45);

  try {
    // 1. Inizializza i servizi
    print('\n1️⃣ Inizializzazione servizi...');
    final varietyManager = await FoodVarietyManager.getInstance();
    await varietyManager.resetUsageHistory();
    print('✅ FoodVarietyManager inizializzato');
    
    final specificGenerator = await SpecificDietGeneratorService.getInstance();
    print('✅ SpecificDietGeneratorService inizializzato');
    
    // 2. Crea profilo utente
    print('\n2️⃣ Creazione profilo utente...');
    final testProfile = UserProfile(
      id: 'simple_test_user',
      name: 'Test User',
      age: 30,
      gender: Gender.male,
      height: 175,
      weight: 70,
      activityLevel: ActivityLevel.moderate,
      goal: Goal.maintain,
      dietType: DietType.omnivore,
      allergies: [],
      dislikedFoods: [],
      mealsPerDay: 3,
    );
    print('✅ Profilo creato: ${testProfile.calculateCalorieTarget()} kcal/giorno');
    
    // 3. Genera un piano dietetico
    print('\n3️⃣ Generazione piano dietetico...');
    final weeklyPlan = await specificGenerator.generateWeeklyDietPlan(
      testProfile,
      weeks: 1,
    );
    
    if (weeklyPlan.dailyPlans.isNotEmpty) {
      final dailyPlan = weeklyPlan.dailyPlans.first;
      print('✅ Piano generato con ${dailyPlan.meals.length} pasti');
      
      // Mostra i pasti generati
      for (final meal in dailyPlan.meals) {
        final foodNames = meal.foods.map((fp) => fp.food.name).toList();
        print('   ${meal.name}: ${foodNames.join(', ')}');
      }
    } else {
      print('❌ Nessun piano generato');
    }
    
    // 4. Verifica tracking varietà
    print('\n4️⃣ Verifica tracking varietà...');
    final stats = varietyManager.getUsageStatistics();
    print('📊 Statistiche varietà:');
    print('   - Alimenti tracciati: ${stats['totalTrackedFoods']}');
    print('   - Utilizzi totali: ${stats['totalUsages']}');
    
    if (stats['totalTrackedFoods'] as int > 0) {
      print('✅ Sistema di tracking funzionante!');
    } else {
      print('⚠️ Nessun alimento tracciato - possibile problema di integrazione');
    }
    
    // 5. Test varietà con più generazioni
    print('\n5️⃣ Test varietà con generazioni multiple...');
    final allFoods = <String>[];
    
    for (int i = 1; i <= 3; i++) {
      print('   Generazione $i...');
      final plan = await specificGenerator.generateWeeklyDietPlan(
        testProfile,
        weeks: 1,
      );
      
      if (plan.dailyPlans.isNotEmpty) {
        final foods = plan.dailyPlans.first.meals
            .expand((meal) => meal.foods)
            .map((fp) => fp.food.name)
            .toList();
        allFoods.addAll(foods);
        print('     Alimenti: ${foods.take(3).join(', ')}...');
      }
    }
    
    final uniqueFoods = allFoods.toSet();
    final varietyRatio = uniqueFoods.length / allFoods.length;
    
    print('\n📈 Risultati varietà:');
    print('   - Alimenti totali selezionati: ${allFoods.length}');
    print('   - Alimenti unici: ${uniqueFoods.length}');
    print('   - Rapporto varietà: ${(varietyRatio * 100).toStringAsFixed(1)}%');
    
    if (varietyRatio > 0.3) {
      print('✅ Buona varietà rilevata!');
    } else {
      print('⚠️ Varietà limitata - potrebbero esserci problemi');
    }
    
    print('\n' + '=' * 45);
    print('🎯 TEST COMPLETATO');
    
    if (stats['totalTrackedFoods'] as int > 0 && varietyRatio > 0.3) {
      print('✅ SUCCESSO: Sistema di varietà funzionante');
    } else {
      print('⚠️ ATTENZIONE: Possibili problemi rilevati');
    }
    
  } catch (e) {
    print('\n❌ ERRORE: $e');
  }
}
