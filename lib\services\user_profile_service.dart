import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/user_profile.dart';
import '../models/advanced_user_profile.dart';

/// Servizio per la gestione dei profili utente
class UserProfileService {
  static const String _profileKey = 'user_profile';
  static const String _advancedProfileKey = 'advanced_user_profile';

  /// Salva il profilo utente base
  Future<void> saveProfile(UserProfile profile) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_profileKey, profile.toJson());
  }

  /// Carica il profilo utente base
  Future<UserProfile?> loadProfile() async {
    final prefs = await SharedPreferences.getInstance();
    final profileJson = prefs.getString(_profileKey);

    if (profileJson == null) {
      return null;
    }

    try {
      return UserProfile.fromJson(profileJson);
    } catch (e) {
      print('Errore durante il caricamento del profilo: $e');
      return null;
    }
  }

  /// Salva il profilo utente avanzato
  Future<void> saveAdvancedProfile(AdvancedUserProfile profile) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_advancedProfileKey, profile.toJson());
  }

  /// Carica il profilo utente avanzato
  Future<AdvancedUserProfile?> loadAdvancedProfile() async {
    final prefs = await SharedPreferences.getInstance();
    final profileJson = prefs.getString(_advancedProfileKey);

    if (profileJson == null) {
      return null;
    }

    try {
      return AdvancedUserProfile.fromJson(profileJson);
    } catch (e) {
      print('Errore durante il caricamento del profilo avanzato: $e');
      return null;
    }
  }

  /// Crea un profilo utente predefinito
  UserProfile createDefaultProfile() {
    return UserProfile(
      id: const Uuid().v4(),
      name: 'Utente',
      gender: Gender.male,
      age: 35,
      height: 175,
      weight: 75,
      activityLevel: ActivityLevel.moderatelyActive,
      goal: Goal.maintenance,
      dietType: DietType.omnivore,
      dietaryPreferences: [],
      mealsPerDay: 4,
    );
  }

  /// Crea un profilo utente avanzato predefinito
  AdvancedUserProfile createDefaultAdvancedProfile() {
    final baseProfile = createDefaultProfile();

    return AdvancedUserProfile(
      baseProfile: baseProfile,
      medicalConditions: [MedicalCondition.none],
      foodIntolerances: [],
      fitnessGoal: FitnessGoal.general,
      primarySport: SportType.none,
      trainingIntensity: TrainingIntensity.none,
      trainingDaysPerWeek: 0,
      trainingMinutesPerSession: 0,
      mealTiming: MealTiming.standard,
      isPregnant: false,
      isBreastfeeding: false,
      nutritionalNeeds: {},
      dietaryPreferences: {},
      mealDistribution: {},
    );
  }

  /// Ottieni il profilo utente (carica o crea uno predefinito)
  Future<UserProfile> getUserProfile() async {
    final profile = await loadProfile();
    if (profile != null) {
      return profile;
    }

    // Se non esiste, crea e salva un profilo predefinito
    final defaultProfile = createDefaultProfile();
    await saveProfile(defaultProfile);
    return defaultProfile;
  }

  /// Ottieni il profilo utente avanzato (carica o crea uno predefinito)
  Future<AdvancedUserProfile> getAdvancedUserProfile() async {
    final profile = await loadAdvancedProfile();
    if (profile != null) {
      return profile;
    }

    // Se non esiste, crea e salva un profilo predefinito
    final defaultProfile = createDefaultAdvancedProfile();
    await saveAdvancedProfile(defaultProfile);
    return defaultProfile;
  }
}
