import 'package:flutter/material.dart';
import '../models/food.dart';
import '../models/diet_plan.dart';
import '../services/specific_diet_generator_service.dart';
import '../theme/app_theme.dart';

/// Widget per mostrare le alternative di un alimento nel pasto
class FoodAlternativesWidget extends StatefulWidget {
  final FoodPortion originalPortion;
  final Function(Food) onAlternativeSelected;

  const FoodAlternativesWidget({
    Key? key,
    required this.originalPortion,
    required this.onAlternativeSelected,
  }) : super(key: key);

  @override
  State<FoodAlternativesWidget> createState() => _FoodAlternativesWidgetState();
}

class _FoodAlternativesWidgetState extends State<FoodAlternativesWidget> {
  List<Food> _alternatives = [];
  bool _isLoading = true;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _loadAlternatives();
  }

  Future<void> _loadAlternatives() async {
    try {
      final dietService = await SpecificDietGeneratorService.getInstance();

      // Ottieni tutti gli alimenti dal database
      final allFoods = await dietService.getAllAvailableFoods();

      // Genera le alternative per l'alimento
      final alternatives = dietService.getFoodAlternatives(
        widget.originalPortion.food,
        allFoods,
      );

      setState(() {
        _alternatives = alternatives;
        _isLoading = false;
      });
    } catch (e) {
      print('Errore nel caricamento alternative: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: Column(
        children: [
          // Header con alimento principale
          ListTile(
            leading: CircleAvatar(
              backgroundColor: AppTheme.primaryColor,
              child: Icon(
                _getFoodIcon(widget.originalPortion.food),
                color: Colors.white,
                size: 20,
              ),
            ),
            title: Text(
              widget.originalPortion.food.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(
              '${widget.originalPortion.grams}g • ${widget.originalPortion.calories} kcal',
              style: TextStyle(color: Colors.grey[600]),
            ),
            trailing: _alternatives.isNotEmpty
                ? IconButton(
                    icon: Icon(
                      _isExpanded ? Icons.expand_less : Icons.expand_more,
                      color: AppTheme.primaryColor,
                    ),
                    onPressed: () {
                      setState(() {
                        _isExpanded = !_isExpanded;
                      });
                    },
                  )
                : null,
          ),

          // Alternative espandibili
          if (_isExpanded) ...[
            const Divider(height: 1),
            if (_isLoading)
              const Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(),
              )
            else if (_alternatives.isEmpty)
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'Nessuna alternativa disponibile',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              )
            else
              Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Row(
                      children: [
                        Icon(
                          Icons.swap_horiz,
                          color: AppTheme.accentColor,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Alternative culturalmente appropriate:',
                          style: TextStyle(
                            color: AppTheme.accentColor,
                            fontWeight: FontWeight.w500,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  ...(_alternatives.take(3).map((alternative) => _buildAlternativeItem(alternative))),
                ],
              ),
          ],
        ],
      ),
    );
  }

  Widget _buildAlternativeItem(Food alternative) {
    return ListTile(
      dense: true,
      leading: CircleAvatar(
        radius: 16,
        backgroundColor: AppTheme.accentColor.withOpacity(0.1),
        child: Icon(
          _getFoodIcon(alternative),
          color: AppTheme.accentColor,
          size: 16,
        ),
      ),
      title: Text(
        alternative.name,
        style: const TextStyle(fontSize: 14),
      ),
      subtitle: Text(
        '${alternative.calories} kcal/100g • P:${alternative.proteins.round()}g C:${alternative.carbs.round()}g G:${alternative.fats.round()}g',
        style: TextStyle(
          fontSize: 12,
          color: Colors.grey[600],
        ),
      ),
      trailing: TextButton(
        onPressed: () {
          widget.onAlternativeSelected(alternative);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Sostituito con ${alternative.name}'),
              backgroundColor: AppTheme.primaryColor,
              duration: const Duration(seconds: 2),
            ),
          );
        },
        child: Text(
          'Sostituisci',
          style: TextStyle(
            color: AppTheme.primaryColor,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  IconData _getFoodIcon(Food food) {
    // Determina l'icona in base alle categorie dell'alimento
    if (food.categories.contains(FoodCategory.protein)) {
      return Icons.restaurant;
    } else if (food.categories.contains(FoodCategory.fruit)) {
      return Icons.apple;
    } else if (food.categories.contains(FoodCategory.vegetable)) {
      return Icons.eco;
    } else if (food.categories.contains(FoodCategory.grain)) {
      return Icons.grain;
    } else if (food.categories.contains(FoodCategory.dairy)) {
      return Icons.local_drink;
    } else if (food.categories.contains(FoodCategory.fat)) {
      return Icons.opacity;
    } else {
      return Icons.fastfood;
    }
  }
}

/// Widget semplificato per mostrare solo il numero di alternative disponibili
class FoodAlternativesIndicator extends StatelessWidget {
  final int alternativesCount;
  final VoidCallback onTap;

  const FoodAlternativesIndicator({
    Key? key,
    required this.alternativesCount,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (alternativesCount == 0) return const SizedBox.shrink();

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: AppTheme.accentColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppTheme.accentColor.withOpacity(0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.swap_horiz,
              size: 14,
              color: AppTheme.accentColor,
            ),
            const SizedBox(width: 4),
            Text(
              '$alternativesCount',
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.accentColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
