import 'dart:convert';

/// Modello per le statistiche giornaliere della dieta
class DailyDietStats {
  final String date;
  final int caloriesConsumed;
  final int caloriesTarget;
  final double proteinsConsumed;
  final double proteinsTarget;
  final double carbsConsumed;
  final double carbsTarget;
  final double fatsConsumed;
  final double fatsTarget;
  final int mealsCompleted;
  final int mealsPlanned;
  final double waterIntake; // in litri
  final double waterTarget; // in litri
  final double? weight; // peso del giorno (opzionale)

  DailyDietStats({
    required this.date,
    required this.caloriesConsumed,
    required this.caloriesTarget,
    required this.proteinsConsumed,
    required this.proteinsTarget,
    required this.carbsConsumed,
    required this.carbsTarget,
    required this.fatsConsumed,
    required this.fatsTarget,
    required this.mealsCompleted,
    required this.mealsPlanned,
    this.waterIntake = 0.0,
    this.waterTarget = 2.0,
    this.weight,
  });

  /// Percentuale di calorie consumate rispetto al target
  double get caloriesPercentage => caloriesTarget > 0 ? (caloriesConsumed / caloriesTarget) * 100 : 0;

  /// Percentuale di proteine consumate rispetto al target
  double get proteinsPercentage => proteinsTarget > 0 ? (proteinsConsumed / proteinsTarget) * 100 : 0;

  /// Percentuale di carboidrati consumati rispetto al target
  double get carbsPercentage => carbsTarget > 0 ? (carbsConsumed / carbsTarget) * 100 : 0;

  /// Percentuale di grassi consumati rispetto al target
  double get fatsPercentage => fatsTarget > 0 ? (fatsConsumed / fatsTarget) * 100 : 0;

  /// Percentuale di pasti completati
  double get mealsCompletionPercentage => mealsPlanned > 0 ? (mealsCompleted / mealsPlanned) * 100 : 0;

  /// Percentuale di acqua bevuta rispetto al target
  double get waterPercentage => waterTarget > 0 ? (waterIntake / waterTarget) * 100 : 0;

  /// Indica se l'obiettivo calorico è stato raggiunto (con tolleranza del 5%)
  bool get isCaloriesOnTrack => caloriesPercentage >= 95 && caloriesPercentage <= 105;

  /// Indica se tutti i macronutrienti sono on track (con tolleranza del 10%)
  bool get areMacrosOnTrack => 
      proteinsPercentage >= 90 && proteinsPercentage <= 110 &&
      carbsPercentage >= 90 && carbsPercentage <= 110 &&
      fatsPercentage >= 90 && fatsPercentage <= 110;

  /// Indica se è un giorno di successo generale
  bool get isSuccessfulDay => isCaloriesOnTrack && areMacrosOnTrack && mealsCompletionPercentage >= 80;

  Map<String, dynamic> toMap() {
    return {
      'date': date,
      'caloriesConsumed': caloriesConsumed,
      'caloriesTarget': caloriesTarget,
      'proteinsConsumed': proteinsConsumed,
      'proteinsTarget': proteinsTarget,
      'carbsConsumed': carbsConsumed,
      'carbsTarget': carbsTarget,
      'fatsConsumed': fatsConsumed,
      'fatsTarget': fatsTarget,
      'mealsCompleted': mealsCompleted,
      'mealsPlanned': mealsPlanned,
      'waterIntake': waterIntake,
      'waterTarget': waterTarget,
      'weight': weight,
    };
  }

  factory DailyDietStats.fromMap(Map<String, dynamic> map) {
    return DailyDietStats(
      date: map['date'] as String,
      caloriesConsumed: map['caloriesConsumed'] as int,
      caloriesTarget: map['caloriesTarget'] as int,
      proteinsConsumed: (map['proteinsConsumed'] as num).toDouble(),
      proteinsTarget: (map['proteinsTarget'] as num).toDouble(),
      carbsConsumed: (map['carbsConsumed'] as num).toDouble(),
      carbsTarget: (map['carbsTarget'] as num).toDouble(),
      fatsConsumed: (map['fatsConsumed'] as num).toDouble(),
      fatsTarget: (map['fatsTarget'] as num).toDouble(),
      mealsCompleted: map['mealsCompleted'] as int,
      mealsPlanned: map['mealsPlanned'] as int,
      waterIntake: (map['waterIntake'] as num?)?.toDouble() ?? 0.0,
      waterTarget: (map['waterTarget'] as num?)?.toDouble() ?? 2.0,
      weight: (map['weight'] as num?)?.toDouble(),
    );
  }

  String toJson() => json.encode(toMap());

  factory DailyDietStats.fromJson(String source) => DailyDietStats.fromMap(json.decode(source));

  DailyDietStats copyWith({
    String? date,
    int? caloriesConsumed,
    int? caloriesTarget,
    double? proteinsConsumed,
    double? proteinsTarget,
    double? carbsConsumed,
    double? carbsTarget,
    double? fatsConsumed,
    double? fatsTarget,
    int? mealsCompleted,
    int? mealsPlanned,
    double? waterIntake,
    double? waterTarget,
    double? weight,
  }) {
    return DailyDietStats(
      date: date ?? this.date,
      caloriesConsumed: caloriesConsumed ?? this.caloriesConsumed,
      caloriesTarget: caloriesTarget ?? this.caloriesTarget,
      proteinsConsumed: proteinsConsumed ?? this.proteinsConsumed,
      proteinsTarget: proteinsTarget ?? this.proteinsTarget,
      carbsConsumed: carbsConsumed ?? this.carbsConsumed,
      carbsTarget: carbsTarget ?? this.carbsTarget,
      fatsConsumed: fatsConsumed ?? this.fatsConsumed,
      fatsTarget: fatsTarget ?? this.fatsTarget,
      mealsCompleted: mealsCompleted ?? this.mealsCompleted,
      mealsPlanned: mealsPlanned ?? this.mealsPlanned,
      waterIntake: waterIntake ?? this.waterIntake,
      waterTarget: waterTarget ?? this.waterTarget,
      weight: weight ?? this.weight,
    );
  }
}

/// Modello per le statistiche settimanali aggregate
class WeeklyDietStats {
  final String weekStartDate;
  final List<DailyDietStats> dailyStats;

  WeeklyDietStats({
    required this.weekStartDate,
    required this.dailyStats,
  });

  /// Media delle calorie consumate nella settimana
  double get averageCaloriesConsumed {
    if (dailyStats.isEmpty) return 0;
    return dailyStats.map((s) => s.caloriesConsumed).reduce((a, b) => a + b) / dailyStats.length;
  }

  /// Media delle calorie target nella settimana
  double get averageCaloriesTarget {
    if (dailyStats.isEmpty) return 0;
    return dailyStats.map((s) => s.caloriesTarget).reduce((a, b) => a + b) / dailyStats.length;
  }

  /// Percentuale media di completamento pasti
  double get averageMealsCompletion {
    if (dailyStats.isEmpty) return 0;
    return dailyStats.map((s) => s.mealsCompletionPercentage).reduce((a, b) => a + b) / dailyStats.length;
  }

  /// Numero di giorni di successo nella settimana
  int get successfulDays => dailyStats.where((s) => s.isSuccessfulDay).length;

  /// Percentuale di giorni di successo
  double get successRate => dailyStats.isNotEmpty ? (successfulDays / dailyStats.length) * 100 : 0;

  /// Trend del peso (se disponibile)
  double? get weightTrend {
    final weightsWithData = dailyStats.where((s) => s.weight != null).toList();
    if (weightsWithData.length < 2) return null;
    
    final firstWeight = weightsWithData.first.weight!;
    final lastWeight = weightsWithData.last.weight!;
    return lastWeight - firstWeight;
  }

  /// Media dei macronutrienti consumati
  Map<String, double> get averageMacros {
    if (dailyStats.isEmpty) return {'proteins': 0, 'carbs': 0, 'fats': 0};
    
    return {
      'proteins': dailyStats.map((s) => s.proteinsConsumed).reduce((a, b) => a + b) / dailyStats.length,
      'carbs': dailyStats.map((s) => s.carbsConsumed).reduce((a, b) => a + b) / dailyStats.length,
      'fats': dailyStats.map((s) => s.fatsConsumed).reduce((a, b) => a + b) / dailyStats.length,
    };
  }
}

/// Enum per i tipi di achievement
enum AchievementType {
  caloriesOnTrack,
  macrosBalanced,
  allMealsCompleted,
  weeklyConsistency,
  weightGoalProgress,
  hydrationGoal,
}

/// Modello per gli achievement/badge
class Achievement {
  final AchievementType type;
  final String title;
  final String description;
  final String iconName;
  final DateTime dateEarned;
  final bool isUnlocked;

  Achievement({
    required this.type,
    required this.title,
    required this.description,
    required this.iconName,
    required this.dateEarned,
    this.isUnlocked = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'type': type.toString(),
      'title': title,
      'description': description,
      'iconName': iconName,
      'dateEarned': dateEarned.toIso8601String(),
      'isUnlocked': isUnlocked,
    };
  }

  factory Achievement.fromMap(Map<String, dynamic> map) {
    return Achievement(
      type: AchievementType.values.firstWhere((e) => e.toString() == map['type']),
      title: map['title'] as String,
      description: map['description'] as String,
      iconName: map['iconName'] as String,
      dateEarned: DateTime.parse(map['dateEarned'] as String),
      isUnlocked: map['isUnlocked'] as bool? ?? false,
    );
  }
}
