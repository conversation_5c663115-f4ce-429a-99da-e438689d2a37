import 'package:flutter/material.dart';
import '../utils/responsive_utils.dart';

/// Responsive text widget that adapts to different screen sizes and handles overflow
class ResponsiveText extends StatelessWidget {
  final String text;
  final double baseFontSize;
  final FontWeight? fontWeight;
  final Color? color;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final TextStyle? style;
  final bool adaptToScreenSize;

  const ResponsiveText(
    this.text, {
    super.key,
    required this.baseFontSize,
    this.fontWeight,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.style,
    this.adaptToScreenSize = true,
  });

  @override
  Widget build(BuildContext context) {
    final responsiveFontSize = adaptToScreenSize
        ? ResponsiveUtils.getResponsiveFontSize(context, baseFontSize)
        : baseFontSize;

    final textStyle = (style ?? const TextStyle()).copyWith(
      fontSize: responsiveFontSize,
      fontWeight: fontWeight,
      color: color,
    );

    return Text(
      text,
      style: textStyle,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow ?? TextOverflow.ellipsis,
    );
  }
}

/// Responsive heading widget for consistent typography
class ResponsiveHeading extends StatelessWidget {
  final String text;
  final HeadingLevel level;
  final Color? color;
  final TextAlign? textAlign;
  final int? maxLines;
  final FontWeight? fontWeight;

  const ResponsiveHeading(
    this.text, {
    super.key,
    required this.level,
    this.color,
    this.textAlign,
    this.maxLines,
    this.fontWeight,
  });

  @override
  Widget build(BuildContext context) {
    double baseFontSize;
    FontWeight defaultWeight;

    switch (level) {
      case HeadingLevel.h1:
        baseFontSize = 32;
        defaultWeight = FontWeight.bold;
        break;
      case HeadingLevel.h2:
        baseFontSize = 28;
        defaultWeight = FontWeight.bold;
        break;
      case HeadingLevel.h3:
        baseFontSize = 24;
        defaultWeight = FontWeight.w600;
        break;
      case HeadingLevel.h4:
        baseFontSize = 20;
        defaultWeight = FontWeight.w600;
        break;
      case HeadingLevel.h5:
        baseFontSize = 18;
        defaultWeight = FontWeight.w500;
        break;
      case HeadingLevel.h6:
        baseFontSize = 16;
        defaultWeight = FontWeight.w500;
        break;
    }

    return ResponsiveText(
      text,
      baseFontSize: baseFontSize,
      fontWeight: fontWeight ?? defaultWeight,
      color: color,
      textAlign: textAlign,
      maxLines: maxLines,
    );
  }
}

/// Responsive body text widget
class ResponsiveBodyText extends StatelessWidget {
  final String text;
  final BodyTextSize size;
  final Color? color;
  final TextAlign? textAlign;
  final int? maxLines;
  final FontWeight? fontWeight;

  const ResponsiveBodyText(
    this.text, {
    super.key,
    this.size = BodyTextSize.medium,
    this.color,
    this.textAlign,
    this.maxLines,
    this.fontWeight,
  });

  @override
  Widget build(BuildContext context) {
    double baseFontSize;

    switch (size) {
      case BodyTextSize.small:
        baseFontSize = 12;
        break;
      case BodyTextSize.medium:
        baseFontSize = 14;
        break;
      case BodyTextSize.large:
        baseFontSize = 16;
        break;
    }

    return ResponsiveText(
      text,
      baseFontSize: baseFontSize,
      fontWeight: fontWeight,
      color: color,
      textAlign: textAlign,
      maxLines: maxLines,
    );
  }
}

/// Responsive caption text widget
class ResponsiveCaption extends StatelessWidget {
  final String text;
  final Color? color;
  final TextAlign? textAlign;
  final int? maxLines;
  final FontWeight? fontWeight;

  const ResponsiveCaption(
    this.text, {
    super.key,
    this.color,
    this.textAlign,
    this.maxLines,
    this.fontWeight,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveText(
      text,
      baseFontSize: 10,
      fontWeight: fontWeight ?? FontWeight.w400,
      color: color ?? Colors.grey[600],
      textAlign: textAlign,
      maxLines: maxLines,
    );
  }
}

/// Responsive button text widget
class ResponsiveButtonText extends StatelessWidget {
  final String text;
  final Color? color;
  final FontWeight? fontWeight;

  const ResponsiveButtonText(
    this.text, {
    super.key,
    this.color,
    this.fontWeight,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveText(
      text,
      baseFontSize: 14,
      fontWeight: fontWeight ?? FontWeight.w600,
      color: color,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }
}

/// Heading levels enumeration
enum HeadingLevel { h1, h2, h3, h4, h5, h6 }

/// Body text sizes enumeration
enum BodyTextSize { small, medium, large }

/// Responsive text field widget
class ResponsiveTextField extends StatelessWidget {
  final String? hintText;
  final String? labelText;
  final TextEditingController? controller;
  final ValueChanged<String>? onChanged;
  final bool obscureText;
  final TextInputType? keyboardType;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final String? Function(String?)? validator;

  const ResponsiveTextField({
    super.key,
    this.hintText,
    this.labelText,
    this.controller,
    this.onChanged,
    this.obscureText = false,
    this.keyboardType,
    this.prefixIcon,
    this.suffixIcon,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);

    double fontSize = switch (deviceType) {
      DeviceType.mobile => 16,
      DeviceType.tablet => 18,
      DeviceType.desktop => 20,
    };

    double contentPadding = switch (deviceType) {
      DeviceType.mobile => 16,
      DeviceType.tablet => 20,
      DeviceType.desktop => 24,
    };

    return TextFormField(
      controller: controller,
      onChanged: onChanged,
      obscureText: obscureText,
      keyboardType: keyboardType,
      validator: validator,
      style: TextStyle(fontSize: fontSize),
      decoration: InputDecoration(
        hintText: hintText,
        labelText: labelText,
        prefixIcon: prefixIcon,
        suffixIcon: suffixIcon,
        contentPadding: EdgeInsets.all(contentPadding),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(
            ResponsiveUtils.getResponsiveBorderRadius(context, 8),
          ),
        ),
      ),
    );
  }
}
