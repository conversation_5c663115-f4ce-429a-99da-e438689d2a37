import 'dart:math';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'package:flutter/services.dart';
import '../theme/dr_staffilano_theme.dart';
import '../services/nutriscore_service.dart';
import '../services/welljourney_service.dart';
import '../controllers/welljourney_controller.dart';
import '../widgets/nutriscore_widgets.dart';
import '../widgets/interactive_dashboard.dart';
import '../widgets/advanced_charts.dart';
import '../widgets/celebration_system.dart';
import '../widgets/magical_particles.dart';
import '../models/welljourney_models.dart';

/// Schermata del NutriScore personalizzato Dr. Staffilano
/// Centro di controllo del progresso nutrizionale integrato con WellJourney™
class NutriScoreScreen extends StatefulWidget {
  const NutriScoreScreen({Key? key}) : super(key: key);

  @override
  State<NutriScoreScreen> createState() => _NutriScoreScreenState();
}

class _NutriScoreScreenState extends State<NutriScoreScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _backgroundController;
  late AnimationController _fabController;
  late Animation<double> _backgroundAnimation;
  late Animation<double> _fabAnimation;

  NutriScoreResult? _currentResult;
  bool _isLoading = true;
  bool _isDarkMode = false;
  int _selectedPeriod = 0; // 0: 7 giorni, 1: 30 giorni, 2: 90 giorni
  final List<String> _periods = ['7 giorni', '30 giorni', '90 giorni'];

  // Overlay per celebrazioni
  OverlayEntry? _celebrationOverlay;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _tabController = TabController(length: 4, vsync: this); // Aggiunta tab personalizzazione

    // Ritarda l'inizializzazione per permettere al provider di essere pronto
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _initializeAndLoadNutriScore();
        _setupWellJourneyListener();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _backgroundController.dispose();
    _fabController.dispose();
    _celebrationOverlay?.remove();
    super.dispose();
  }

  /// Configura il listener per aggiornamenti automatici del NutriScore
  void _setupWellJourneyListener() {
    try {
      final controller = context.read<WellJourneyController>();
      controller.addListener(_onWellJourneyUpdate);
      print('✅ Listener WellJourney configurato per aggiornamenti NutriScore');
    } catch (e) {
      print('⚠️ Impossibile configurare listener WellJourney: $e');
    }
  }

  /// Callback per aggiornamenti WellJourney
  void _onWellJourneyUpdate() {
    if (mounted) {
      print('🔄 Aggiornamento NutriScore rilevato da WellJourney');
      _loadNutriScore();
    }
  }

  void _setupAnimations() {
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();

    _fabController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _backgroundAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(_backgroundController);

    _fabAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _fabController,
      curve: Curves.elasticOut,
    ));

    _fabController.forward();
  }

  /// Inizializza il controller e carica il NutriScore in modo robusto
  Future<void> _initializeAndLoadNutriScore() async {
    setState(() {
      _isLoading = true;
    });

    try {
      print('🔄 Inizializzazione robusta NutriScore screen...');

      // Prova a ottenere il controller, ma gestisci il caso in cui non sia disponibile
      WellJourneyController? controller;
      try {
        controller = Provider.of<WellJourneyController>(context, listen: false);
      } catch (e) {
        print('⚠️ Controller non disponibile: $e');
        controller = null;
      }

      // Se il controller è disponibile ma non inizializzato, prova a inizializzarlo
      if (controller != null && controller.userProgress == null) {
        print('⚠️ Controller non inizializzato, inizializzando...');
        try {
          await controller.initialize();
        } catch (e) {
          print('⚠️ Errore inizializzazione controller: $e');
          // Continua comunque, il servizio robusto gestirà il caso
        }
      }

      await _loadNutriScore();
    } catch (e) {
      print('❌ Errore nell\'inizializzazione NutriScore: $e');
      // Anche in caso di errore, prova a caricare con valori di default
      await _loadNutriScore();
    }
  }

  Future<void> _loadNutriScore() async {
    setState(() => _isLoading = true);

    try {
      // Prova a ottenere il controller in modo sicuro
      WellJourneyController? controller;
      try {
        controller = context.read<WellJourneyController>();
      } catch (e) {
        print('⚠️ Impossibile ottenere controller: $e');
        controller = null;
      }

      // Calcola il NutriScore usando il servizio robusto
      final result = NutriScoreService.instance.calculateNutriScore(controller);

      setState(() {
        _currentResult = result;
        _isLoading = false;
      });

      print('✅ NutriScore caricato: ${result.totalScore.toStringAsFixed(1)}');
      print('📊 Dettagli: Educativo=${result.educationalScore.toStringAsFixed(1)}, Consistenza=${result.consistencyScore.toStringAsFixed(1)}');

      // Feedback aptico per il caricamento completato
      try {
        HapticFeedback.lightImpact();
      } catch (e) {
        print('⚠️ Feedback aptico non disponibile: $e');
      }

    } catch (e) {
      print('❌ Errore nel caricamento NutriScore: $e');

      // Anche in caso di errore, crea un risultato di default
      final defaultResult = NutriScoreService.instance.calculateNutriScore(null);

      setState(() {
        _currentResult = defaultResult;
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Caricato NutriScore con valori di default'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    return Theme(
      data: _isDarkMode ? _buildDarkTheme() : _buildLightTheme(),
      child: Scaffold(
        backgroundColor: _isDarkMode
            ? const Color(0xFF121212)
            : DrStaffilanoTheme.backgroundLight,
        body: Stack(
          children: [
            // Sfondo animato
            _buildAnimatedBackground(),
            // Contenuto principale con scroll controller per gestire il comportamento
            NestedScrollView(
              physics: const BouncingScrollPhysics(),
              headerSliverBuilder: (context, innerBoxIsScrolled) => [
                _buildPremiumSliverAppBar(),
              ],
              body: _isLoading
                  ? _buildPremiumLoadingState()
                  : _currentResult != null
                      ? _buildPremiumContent()
                      : _buildPremiumErrorState(),
            ),
            // Floating tab indicator for collapsed state
            _buildFloatingTabIndicator(),
            // Floating Action Buttons Premium
            _buildPremiumFABs(),
          ],
        ),
      ),
    );
  }

  // Metodi per temi
  ThemeData _buildLightTheme() {
    return ThemeData(
      brightness: Brightness.light,
      primarySwatch: Colors.green,
      fontFamily: 'SF Pro Display',
    );
  }

  ThemeData _buildDarkTheme() {
    return ThemeData(
      brightness: Brightness.dark,
      primarySwatch: Colors.green,
      fontFamily: 'SF Pro Display',
      scaffoldBackgroundColor: const Color(0xFF121212),
    );
  }

  Widget _buildAnimatedBackground() {
    return AnimatedBuilder(
      animation: _backgroundAnimation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: _isDarkMode ? [
                const Color(0xFF121212),
                const Color(0xFF1E1E1E),
                const Color(0xFF2D2D2D),
              ] : [
                DrStaffilanoTheme.backgroundLight,
                DrStaffilanoTheme.primaryGreen.withOpacity(0.02),
                DrStaffilanoTheme.secondaryBlue.withOpacity(0.02),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              transform: GradientRotation(_backgroundAnimation.value * 2 * 3.14159),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPremiumSliverAppBar() {
    return SliverAppBar(
      backgroundColor: Colors.transparent,
      foregroundColor: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
      expandedHeight: 220, // Ridotto per un header più compatto
      floating: true, // Enables floating behavior
      pinned: false, // Allows complete hiding
      snap: true, // Smooth snap animations
      flexibleSpace: FlexibleSpaceBar(
        centerTitle: true,
        titlePadding: const EdgeInsets.only(bottom: 130), // Fine-tuned positioning - slightly lower for perfect centering
        title: Text(
          'NutriScore Dr. Staffilano',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
            color: Colors.white,
            shadows: [
              Shadow(
                offset: const Offset(0, 1),
                blurRadius: 3,
                color: Colors.black.withOpacity(0.5),
              ),
            ],
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                DrStaffilanoTheme.primaryGreen,
                DrStaffilanoTheme.primaryGreen.withOpacity(0.8),
                DrStaffilanoTheme.secondaryBlue.withOpacity(0.6),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Stack(
            children: [
              // Title-focused particles positioned around the title area
              ...List.generate(12, (index) => _buildTitleParticle(index)),
            ],
          ),
        ),
      ),
      actions: [
        // Toggle tema
        IconButton(
          onPressed: () {
            setState(() {
              _isDarkMode = !_isDarkMode;
            });
            HapticFeedback.lightImpact();
          },
          icon: Icon(
            _isDarkMode ? FontAwesomeIcons.sun : FontAwesomeIcons.moon,
          ),
        ),
        // Menu opzioni
        PopupMenuButton<String>(
          onSelected: _handleMenuSelection,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  Icon(FontAwesomeIcons.download),
                  SizedBox(width: 12),
                  Text('Esporta Dati'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(FontAwesomeIcons.share),
                  SizedBox(width: 12),
                  Text('Condividi'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: Row(
                children: [
                  Icon(FontAwesomeIcons.gear),
                  SizedBox(width: 12),
                  Text('Impostazioni'),
                ],
              ),
            ),
          ],
        ),
      ],
      bottom: _buildCustomTabBar(),
    );
  }

  /// Enhanced TabBar with better visibility for collapsing behavior
  PreferredSizeWidget _buildCustomTabBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(kToolbarHeight),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              DrStaffilanoTheme.primaryGreen.withOpacity(0.95),
              DrStaffilanoTheme.secondaryBlue.withOpacity(0.85),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: TabBar(
          controller: _tabController,
          indicatorColor: DrStaffilanoTheme.accentGold,
          indicatorWeight: 3,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white.withOpacity(0.7),
          labelStyle: const TextStyle(fontWeight: FontWeight.w600, fontSize: 12),
          tabs: const [
            Tab(icon: Icon(FontAwesomeIcons.trophy, size: 16), text: 'Dashboard'),
            Tab(icon: Icon(FontAwesomeIcons.chartLine, size: 16), text: 'Grafici'),
            Tab(icon: Icon(FontAwesomeIcons.lightbulb, size: 16), text: 'Consigli'),
            Tab(icon: Icon(FontAwesomeIcons.sliders, size: 16), text: 'Personalizza'),
          ],
        ),
      ),
    );
  }

  /// Particles positioned around the title area for enhanced visual appeal
  Widget _buildTitleParticle(int index) {
    return AnimatedBuilder(
      animation: _backgroundAnimation,
      builder: (context, child) {
        final offset = (_backgroundAnimation.value + index * 0.15) % 1.0;

        // Calculate positions around the title area (center of header)
        final screenWidth = MediaQuery.of(context).size.width;
        final centerX = screenWidth / 2;
        final titleY = 55; // Spostato più in basso per un migliore posizionamento delle particelle

        // Create circular pattern around title
        final angle = (index * 30.0) + (offset * 360.0); // Degrees
        final radius = 60 + (index % 3) * 15; // Raggio ridotto per layout più compatto
        final radians = angle * (3.14159 / 180);

        final particleX = centerX + (radius * cos(radians));
        final particleY = titleY + (radius * sin(radians)) * 0.5; // Flatten vertically

        return Positioned(
          left: particleX,
          top: particleY,
          child: Opacity(
            opacity: 0.4 + (index % 2) * 0.2, // Varying opacity
            child: Container(
              width: 3 + (index % 3), // Varying sizes
              height: 3 + (index % 3),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.white.withOpacity(0.6),
                    blurRadius: 6,
                    spreadRadius: 1,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _handleMenuSelection(String value) {
    switch (value) {
      case 'export':
        _exportData();
        break;
      case 'share':
        _shareProgress();
        break;
      case 'settings':
        _showSettings();
        break;
    }
  }

  void _exportData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Esportazione completata con successo!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _shareProgress() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Funzionalità di condivisione in arrivo presto!'),
      ),
    );
  }

  void _showSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Pannello impostazioni in sviluppo'),
      ),
    );
  }

  Widget _buildPremiumLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  DrStaffilanoTheme.primaryGreen,
                  DrStaffilanoTheme.accentGold,
                ],
              ),
              shape: BoxShape.circle,
            ),
            child: const Center(
              child: CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 3,
              ),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Calcolando il tuo NutriScore...',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Analizzando i tuoi progressi nutrizionali',
            style: TextStyle(
              fontSize: 14,
              color: _isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPremiumErrorState() {
    return Center(
      child: Container(
        margin: const EdgeInsets.all(32),
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.red.withOpacity(0.1),
              Colors.orange.withOpacity(0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.orange.withOpacity(0.3),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.2),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                FontAwesomeIcons.exclamationTriangle,
                size: 40,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Errore nel Caricamento',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Non è stato possibile caricare i dati del NutriScore.\nVerifica la connessione e riprova.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: _isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
                height: 1.5,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadNutriScore,
              icon: const Icon(FontAwesomeIcons.arrowsRotate),
              label: const Text('Riprova'),
              style: ElevatedButton.styleFrom(
                backgroundColor: DrStaffilanoTheme.primaryGreen,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPremiumContent() {
    if (_currentResult == null) return const SizedBox.shrink();

    return TabBarView(
      controller: _tabController,
      physics: const BouncingScrollPhysics(),
      children: [
        _buildDashboardTab(),
        _buildChartsTab(),
        _buildAdviceTab(),
        _buildCustomizationTab(),
      ],
    );
  }

  Widget _buildDashboardTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      physics: const BouncingScrollPhysics(),
      child: Column(
        children: [
          // Dashboard interattiva premium
          InteractiveDashboard(
            result: _currentResult!,
            isDarkMode: _isDarkMode,
          ),
          const SizedBox(height: 16),
          // Barra di progresso WellJourney animata
          WellJourneyPointsProgressWidget(
            animate: true,
            height: 65.0,
          ),
          const SizedBox(height: 24),
          // Statistiche WellJourney integration
          _buildWellJourneyIntegrationCard(),
          const SizedBox(height: 100), // Spazio per FAB
        ],
      ),
    );
  }

  Widget _buildChartsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      physics: const BouncingScrollPhysics(),
      child: Column(
        children: [
          // Selettore periodo
          _buildPeriodSelector(),
          const SizedBox(height: 24),
          // Grafico andamento temporale
          AdvancedNutriScoreChart(
            data: _generateChartData(),
            title: 'Andamento NutriScore',
            type: ChartType.line,
            isDarkMode: _isDarkMode,
          ),
          const SizedBox(height: 24),
          // Grafico breakdown categorie
          AdvancedNutriScoreChart(
            data: _generateCategoryData(),
            title: 'Analisi per Categoria',
            type: ChartType.bar,
            isDarkMode: _isDarkMode,
          ),
          const SizedBox(height: 24),
          // Storico progressi
          _buildProgressHistoryCard(),
          const SizedBox(height: 100), // Spazio per FAB
        ],
      ),
    );
  }



  Widget _buildAdviceTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      physics: const BouncingScrollPhysics(),
      child: Column(
        children: [
          // Raccomandazioni Dr. Staffilano
          NutriScoreRecommendationsWidget(result: _currentResult!),
          const SizedBox(height: 24),
          // Prossimi passi
          _buildNextStepsCard(),
          const SizedBox(height: 24),
          // Badge integration
          _buildBadgeIntegrationCard(),
          const SizedBox(height: 100), // Spazio per FAB
        ],
      ),
    );
  }





  Widget _buildCustomizationTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      physics: const BouncingScrollPhysics(),
      child: Column(
        children: [
          _buildCustomizationCard(),
          const SizedBox(height: 24),
          _buildNotificationSettings(),
          const SizedBox(height: 24),
          _buildDataManagement(),
          const SizedBox(height: 100), // Spazio per FAB
        ],
      ),
    );
  }

  Widget _buildPeriodSelector() {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: _isDarkMode ? Colors.grey[800] : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: List.generate(_periods.length, (index) {
          final isSelected = _selectedPeriod == index;
          return Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedPeriod = index;
                });
                HapticFeedback.lightImpact();
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected ? DrStaffilanoTheme.primaryGreen : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _periods[index],
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: isSelected
                        ? Colors.white
                        : (_isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary),
                  ),
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  /// Floating tab indicator that appears when header is collapsed
  Widget _buildFloatingTabIndicator() {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 8,
      left: 16,
      right: 16,
      child: AnimatedOpacity(
        opacity: 0.0, // Will be controlled by scroll position in future enhancement
        duration: const Duration(milliseconds: 300),
        child: Container(
          height: 60,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                DrStaffilanoTheme.primaryGreen.withOpacity(0.9),
                DrStaffilanoTheme.secondaryBlue.withOpacity(0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(30),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildFloatingTabItem(0, FontAwesomeIcons.trophy, 'Dashboard'),
              _buildFloatingTabItem(1, FontAwesomeIcons.chartLine, 'Grafici'),
              _buildFloatingTabItem(2, FontAwesomeIcons.lightbulb, 'Consigli'),
              _buildFloatingTabItem(3, FontAwesomeIcons.sliders, 'Personalizza'),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFloatingTabItem(int index, IconData icon, String label) {
    final isSelected = _tabController.index == index;
    return GestureDetector(
      onTap: () {
        _tabController.animateTo(index);
        HapticFeedback.lightImpact();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.white.withOpacity(0.2) : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? Colors.white : Colors.white.withOpacity(0.7),
            ),
            const SizedBox(height: 2),
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w600,
                color: isSelected ? Colors.white : Colors.white.withOpacity(0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPremiumFABs() {
    return const SizedBox.shrink(); // Rimuovi FAB per evitare problemi
  }

  // Metodi per generare dati dei grafici
  List<Map<String, dynamic>> _generateChartData() {
    return List.generate(7, (index) => {
      'day': index,
      'score': 30 + (index * 8) + (index % 2 == 0 ? 5 : -3),
    });
  }

  List<Map<String, dynamic>> _generateCategoryData() {
    return [
      {'category': 'Educativo', 'score': _currentResult!.educationalScore},
      {'category': 'Consistenza', 'score': _currentResult!.consistencyScore},
      {'category': 'Applicazione', 'score': _currentResult!.applicationScore},
      {'category': 'Expertise', 'score': _currentResult!.expertiseScore},
    ];
  }

  Widget _buildCustomizationCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: _isDarkMode ? Colors.grey[800] : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.palette,
                color: DrStaffilanoTheme.primaryGreen,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'Personalizzazione',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildToggleOption(
            'Tema Scuro',
            'Attiva il tema scuro per un\'esperienza visiva ottimale',
            _isDarkMode,
            (value) {
              setState(() {
                _isDarkMode = value;
              });
              HapticFeedback.lightImpact();
            },
          ),
          const SizedBox(height: 16),
          _buildToggleOption(
            'Animazioni Avanzate',
            'Abilita animazioni fluide e transizioni eleganti',
            true,
            (value) {
              // TODO: Implementare toggle animazioni
              HapticFeedback.lightImpact();
            },
          ),
          const SizedBox(height: 16),
          _buildToggleOption(
            'Feedback Aptico',
            'Vibrazione per confermare le azioni',
            true,
            (value) {
              // TODO: Implementare toggle haptic
              HapticFeedback.lightImpact();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildToggleOption(String title, String description, bool value, Function(bool) onChanged) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(
                  fontSize: 14,
                  color: _isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
          activeColor: DrStaffilanoTheme.primaryGreen,
        ),
      ],
    );
  }

  Widget _buildNotificationSettings() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: _isDarkMode ? Colors.grey[800] : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.secondaryBlue.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.bell,
                color: DrStaffilanoTheme.secondaryBlue,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'Notifiche',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildToggleOption(
            'Celebrazioni Traguardi',
            'Ricevi notifiche quando raggiungi nuovi livelli',
            true,
            (value) => HapticFeedback.lightImpact(),
          ),
          const SizedBox(height: 16),
          _buildToggleOption(
            'Promemoria Giornalieri',
            'Ricorda di completare i moduli WellJourney™',
            true,
            (value) => HapticFeedback.lightImpact(),
          ),
          const SizedBox(height: 16),
          _buildToggleOption(
            'Aggiornamenti Settimanali',
            'Riassunto settimanale dei progressi',
            false,
            (value) => HapticFeedback.lightImpact(),
          ),
        ],
      ),
    );
  }

  Widget _buildDataManagement() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: _isDarkMode ? Colors.grey[800] : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.accentGold.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.database,
                color: DrStaffilanoTheme.accentGold,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'Gestione Dati',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildActionButton(
            'Esporta Progressi',
            'Scarica i tuoi dati in formato PDF',
            FontAwesomeIcons.download,
            DrStaffilanoTheme.primaryGreen,
            _exportData,
          ),
          const SizedBox(height: 12),
          _buildActionButton(
            'Condividi Risultati',
            'Condividi i tuoi successi con altri',
            FontAwesomeIcons.share,
            DrStaffilanoTheme.secondaryBlue,
            _shareProgress,
          ),

        ],
      ),
    );
  }

  Widget _buildActionButton(String title, String description, IconData icon, Color color, VoidCallback onTap) {
    return GestureDetector(
      onTap: () {
        onTap();
        HapticFeedback.lightImpact();
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withOpacity(0.3),
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: color,
                size: 16,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 14,
                      color: _isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              FontAwesomeIcons.chevronRight,
              color: color,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  // Metodi di supporto per le card
  Widget _buildWellJourneyIntegrationCard() {
    final controller = context.read<WellJourneyController>();
    final userProgress = controller.userProgress;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: _isDarkMode ? Colors.grey[800] : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.route,
                color: DrStaffilanoTheme.primaryGreen,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'Integrazione WellJourney™',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildStatItem('Punti Totali', '${userProgress?.totalPoints ?? 0}', FontAwesomeIcons.star),
          _buildStatItem('Badge Ottenuti', '${controller.earnedBadges.length}', FontAwesomeIcons.award),
          _buildStatItem('Percorsi Attivi', '${controller.enrolledPathways.length}', FontAwesomeIcons.road),
          _buildStatItem('Moduli Completati', '${userProgress?.completedModules.length ?? 0}', FontAwesomeIcons.checkCircle),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 16, color: DrStaffilanoTheme.primaryGreen),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: _isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNextStepsCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: _isDarkMode ? Colors.grey[800] : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.secondaryBlue.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.listCheck,
                color: DrStaffilanoTheme.secondaryBlue,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'Prossimi Passi',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildNextStepItem(
            'Completa il percorso "Dieta Mediterranea"',
            'Guadagna +15 punti NutriScore',
            FontAwesomeIcons.route,
          ),
          _buildNextStepItem(
            'Partecipa a 3 sfide settimanali',
            'Migliora la consistenza del +10%',
            FontAwesomeIcons.trophy,
          ),
          _buildNextStepItem(
            'Ottieni il badge "Esperto Cardiovascolare"',
            'Sblocca contenuti avanzati',
            FontAwesomeIcons.award,
          ),
        ],
      ),
    );
  }

  Widget _buildNextStepItem(String title, String benefit, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: DrStaffilanoTheme.secondaryBlue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: DrStaffilanoTheme.secondaryBlue,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  benefit,
                  style: TextStyle(
                    fontSize: 12,
                    color: _isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBadgeIntegrationCard() {
    final controller = context.read<WellJourneyController>();
    final earnedBadges = controller.earnedBadges;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            DrStaffilanoTheme.accentGold.withOpacity(0.1),
            DrStaffilanoTheme.accentGold.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: DrStaffilanoTheme.accentGold.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.medal,
                color: DrStaffilanoTheme.accentGold,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'Badge Nutrizionali',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (earnedBadges.isEmpty)
            Text(
              'Completa i percorsi WellJourney™ per ottenere i tuoi primi badge nutrizionali!',
              style: TextStyle(
                fontSize: 14,
                color: _isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
              ),
            )
          else
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: earnedBadges.take(6).map((badgeId) {
                // Trova il badge dal servizio usando l'ID
                final allBadges = WellJourneyService.instance.getAvailableBadges();
                final badge = allBadges.firstWhere(
                  (b) => b.id == badgeId,
                  orElse: () => AchievementBadge(
                    id: badgeId,
                    title: 'Badge Sconosciuto',
                    description: '',
                    icon: Icons.help,
                    color: Colors.grey,
                    category: BadgeCategory.achievement,
                    pointsRequired: 0,
                    celebrationMessage: '',
                  ),
                );

                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: DrStaffilanoTheme.accentGold.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    badge.title,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: DrStaffilanoTheme.accentGold,
                    ),
                  ),
                );
              }).toList(),
            ),
        ],
      ),
    );
  }

  Widget _buildProgressHistoryCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: _isDarkMode ? Colors.grey[800] : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.accentGold.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.chartLine,
                color: DrStaffilanoTheme.accentGold,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'Storico Progressi',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Il tuo NutriScore è migliorato del 15% nell\'ultimo mese grazie al completamento dei percorsi WellJourney™.',
            style: TextStyle(
              fontSize: 14,
              color: _isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildProgressItem('Questa Settimana', '+5 punti', Colors.green),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildProgressItem('Questo Mese', '+12 punti', DrStaffilanoTheme.primaryGreen),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProgressItem(String period, String change, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Text(
            change,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            period,
            style: TextStyle(
              fontSize: 12,
              color: _isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
            ),
          ),
        ],
      ),
    );
  }
}
