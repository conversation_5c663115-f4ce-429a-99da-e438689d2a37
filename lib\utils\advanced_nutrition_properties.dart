import '../models/food.dart';
import 'micronutrients_helper.dart';

/// Classe per la gestione di proprietà nutrizionali avanzate
class AdvancedNutritionProperties {
  // Costanti per i nomi delle proprietà
  static const String PRAL = 'pral';                     // Potential Renal Acid Load
  static const String INFLAMMATORY_INDEX = 'infl_index';  // Indice infiammatorio
  static const String ANTIOXIDANT_SCORE = 'antiox_score'; // Punteggio antiossidante
  static const String FODMAP_LEVEL = 'fodmap_level';      // Livello FODMAP
  static const String INSULIN_INDEX = 'insulin_index';    // Indice insulinico
  static const String SATIETY_INDEX = 'satiety_index';    // Indice di sazietà
  static const String NUTRIENT_DENSITY = 'nutrient_density'; // Densità nutrizionale
  static const String HISTAMINE_LEVEL = 'histamine_level'; // Livello di istamina
  static const String PURINE_CONTENT = 'purine_content';   // Contenuto di purine
  static const String OXALATE_CONTENT = 'oxalate_content'; // Contenuto di ossalati
  static const String GOITROGEN_CONTENT = 'goitrogen_content'; // Contenuto di goitrogeni
  static const String SALICYLATE_LEVEL = 'salicylate_level'; // Livello di salicilati
  static const String LECTINS_LEVEL = 'lectins_level';     // Livello di lectine
  static const String PHYTATE_CONTENT = 'phytate_content'; // Contenuto di fitati

  // Unità di misura per le proprietà
  static const Map<String, String> UNITS = {
    PRAL: 'mEq/100g',
    INFLAMMATORY_INDEX: 'score',
    ANTIOXIDANT_SCORE: 'score',
    FODMAP_LEVEL: 'level',
    INSULIN_INDEX: 'score',
    SATIETY_INDEX: 'score',
    NUTRIENT_DENSITY: 'score',
    HISTAMINE_LEVEL: 'level',
    PURINE_CONTENT: 'mg/100g',
    OXALATE_CONTENT: 'mg/100g',
    GOITROGEN_CONTENT: 'level',
    SALICYLATE_LEVEL: 'level',
    LECTINS_LEVEL: 'level',
    PHYTATE_CONTENT: 'mg/100g',
  };

  // Nomi visualizzati per le proprietà
  static const Map<String, String> DISPLAY_NAMES = {
    PRAL: 'Carico Acido Renale Potenziale',
    INFLAMMATORY_INDEX: 'Indice Infiammatorio',
    ANTIOXIDANT_SCORE: 'Punteggio Antiossidante',
    FODMAP_LEVEL: 'Livello FODMAP',
    INSULIN_INDEX: 'Indice Insulinico',
    SATIETY_INDEX: 'Indice di Sazietà',
    NUTRIENT_DENSITY: 'Densità Nutrizionale',
    HISTAMINE_LEVEL: 'Livello di Istamina',
    PURINE_CONTENT: 'Contenuto di Purine',
    OXALATE_CONTENT: 'Contenuto di Ossalati',
    GOITROGEN_CONTENT: 'Contenuto di Goitrogeni',
    SALICYLATE_LEVEL: 'Livello di Salicilati',
    LECTINS_LEVEL: 'Livello di Lectine',
    PHYTATE_CONTENT: 'Contenuto di Fitati',
  };

  // Descrizioni delle proprietà
  static const Map<String, String> DESCRIPTIONS = {
    PRAL: 'Misura il potenziale carico acido che un alimento può generare nei reni. Valori negativi indicano alimenti alcalinizzanti, valori positivi indicano alimenti acidificanti.',
    INFLAMMATORY_INDEX: 'Indica il potenziale infiammatorio di un alimento. Valori più bassi indicano alimenti anti-infiammatori, valori più alti indicano alimenti pro-infiammatori.',
    ANTIOXIDANT_SCORE: 'Misura la capacità antiossidante di un alimento. Valori più alti indicano un maggiore potere antiossidante.',
    FODMAP_LEVEL: 'Indica il contenuto di FODMAPs (carboidrati fermentabili) in un alimento. Rilevante per persone con sindrome dell\'intestino irritabile.',
    INSULIN_INDEX: 'Misura la risposta insulinica a un alimento. Può differire dall\'indice glicemico per alcuni alimenti.',
    SATIETY_INDEX: 'Indica quanto un alimento è saziante rispetto a un riferimento standard (pane bianco = 100).',
    NUTRIENT_DENSITY: 'Misura la quantità di nutrienti essenziali per caloria in un alimento.',
    HISTAMINE_LEVEL: 'Indica il contenuto di istamina o il potenziale di rilascio di istamina di un alimento.',
    PURINE_CONTENT: 'Misura il contenuto di purine, rilevante per persone con gotta o iperuricemia.',
    OXALATE_CONTENT: 'Indica il contenuto di ossalati, rilevante per persone con calcoli renali di ossalato di calcio.',
    GOITROGEN_CONTENT: 'Misura il contenuto di goitrogeni, sostanze che possono interferire con la funzione tiroidea.',
    SALICYLATE_LEVEL: 'Indica il livello di salicilati, rilevante per persone con sensibilità ai salicilati.',
    LECTINS_LEVEL: 'Misura il contenuto di lectine, proteine che possono causare problemi digestivi in alcune persone.',
    PHYTATE_CONTENT: 'Indica il contenuto di fitati, che possono ridurre l\'assorbimento di alcuni minerali.',
  };

  // Livelli per proprietà categoriche
  static const Map<String, List<String>> CATEGORICAL_LEVELS = {
    FODMAP_LEVEL: ['Basso', 'Moderato', 'Alto'],
    HISTAMINE_LEVEL: ['Basso', 'Moderato', 'Alto'],
    GOITROGEN_CONTENT: ['Basso', 'Moderato', 'Alto'],
    SALICYLATE_LEVEL: ['Basso', 'Moderato', 'Alto'],
    LECTINS_LEVEL: ['Basso', 'Moderato', 'Alto'],
  };

  // Intervalli di riferimento per proprietà numeriche
  static const Map<String, Map<String, double>> REFERENCE_RANGES = {
    PRAL: {
      'min': -30.0,
      'max': 30.0,
      'neutral_min': -5.0,
      'neutral_max': 5.0,
    },
    INFLAMMATORY_INDEX: {
      'min': -5.0,
      'max': 5.0,
      'neutral_min': -1.0,
      'neutral_max': 1.0,
    },
    ANTIOXIDANT_SCORE: {
      'min': 0.0,
      'max': 100.0,
      'low': 20.0,
      'high': 60.0,
    },
    INSULIN_INDEX: {
      'min': 0.0,
      'max': 120.0,
      'low': 30.0,
      'high': 70.0,
    },
    SATIETY_INDEX: {
      'min': 0.0,
      'max': 250.0,
      'low': 80.0,
      'high': 150.0,
    },
    NUTRIENT_DENSITY: {
      'min': 0.0,
      'max': 100.0,
      'low': 30.0,
      'high': 70.0,
    },
  };

  /// Calcola il PRAL (Potential Renal Acid Load) di un alimento
  /// Formula: PRAL (mEq/100g) = 0.49 × proteina (g/100g) + 0.037 × fosforo (mg/100g) - 0.021 × potassio (mg/100g) - 0.026 × magnesio (mg/100g) - 0.013 × calcio (mg/100g)
  static double calculatePRAL(Food food) {
    // Valori predefiniti se mancano i micronutrienti
    double protein = food.proteins;
    double phosphorus = food.micronutrients[MicronutrientsHelper.PHOSPHORUS] ?? 0.0;
    double potassium = food.micronutrients[MicronutrientsHelper.POTASSIUM] ?? 0.0;
    double magnesium = food.micronutrients[MicronutrientsHelper.MAGNESIUM] ?? 0.0;
    double calcium = food.micronutrients[MicronutrientsHelper.CALCIUM] ?? 0.0;

    return 0.49 * protein + 0.037 * phosphorus - 0.021 * potassium - 0.026 * magnesium - 0.013 * calcium;
  }

  /// Calcola la densità nutrizionale di un alimento
  /// Formula semplificata: somma delle percentuali di RDA di vitamine e minerali essenziali diviso le calorie per 100g
  static double calculateNutrientDensity(Food food) {
    if (food.calories <= 0 || food.micronutrients.isEmpty) {
      return 0.0;
    }

    // Calcola la somma delle percentuali di RDA
    double totalPercentRDA = 0.0;
    int nutrientCount = 0;

    food.micronutrients.forEach((nutrient, amount) {
      // Verifica se abbiamo un valore di riferimento per questo nutriente
      if (nutrient == MicronutrientsHelper.CALCIUM ||
          nutrient == MicronutrientsHelper.IRON ||
          nutrient == MicronutrientsHelper.MAGNESIUM ||
          nutrient == MicronutrientsHelper.ZINC) {

        double rda = 0.0;

        // Assegna valori RDA approssimativi per i nutrienti principali
        if (nutrient == MicronutrientsHelper.CALCIUM) {
          rda = 1000.0;
        } else if (nutrient == MicronutrientsHelper.IRON) {
          rda = 14.0;
        } else if (nutrient == MicronutrientsHelper.MAGNESIUM) {
          rda = 400.0;
        } else if (nutrient == MicronutrientsHelper.ZINC) {
          rda = 10.0;
        }

        if (rda > 0) {
          double percentRDA = (amount / rda) * 100;
          totalPercentRDA += percentRDA;
          nutrientCount++;
        }
      }
    });

    // Se non abbiamo trovato nutrienti con RDA, restituisci 0
    if (nutrientCount == 0) {
      return 0.0;
    }

    // Calcola la densità nutrizionale: media delle percentuali RDA per 100 calorie
    return (totalPercentRDA / nutrientCount) * (100.0 / food.calories);
  }

  /// Crea una mappa di proprietà nutrizionali avanzate
  static Map<String, dynamic> createAdvancedPropertiesMap({
    double? pral,
    double? inflammatoryIndex,
    double? antioxidantScore,
    String? fodmapLevel,
    double? insulinIndex,
    double? satietyIndex,
    double? nutrientDensity,
    String? histamineLevel,
    double? purineContent,
    double? oxalateContent,
    String? goitrogenContent,
    String? salicylateLevel,
    String? lectinsLevel,
    double? phytateContent,
  }) {
    final Map<String, dynamic> properties = {};

    if (pral != null) properties[PRAL] = pral;
    if (inflammatoryIndex != null) properties[INFLAMMATORY_INDEX] = inflammatoryIndex;
    if (antioxidantScore != null) properties[ANTIOXIDANT_SCORE] = antioxidantScore;
    if (fodmapLevel != null) properties[FODMAP_LEVEL] = fodmapLevel;
    if (insulinIndex != null) properties[INSULIN_INDEX] = insulinIndex;
    if (satietyIndex != null) properties[SATIETY_INDEX] = satietyIndex;
    if (nutrientDensity != null) properties[NUTRIENT_DENSITY] = nutrientDensity;
    if (histamineLevel != null) properties[HISTAMINE_LEVEL] = histamineLevel;
    if (purineContent != null) properties[PURINE_CONTENT] = purineContent;
    if (oxalateContent != null) properties[OXALATE_CONTENT] = oxalateContent;
    if (goitrogenContent != null) properties[GOITROGEN_CONTENT] = goitrogenContent;
    if (salicylateLevel != null) properties[SALICYLATE_LEVEL] = salicylateLevel;
    if (lectinsLevel != null) properties[LECTINS_LEVEL] = lectinsLevel;
    if (phytateContent != null) properties[PHYTATE_CONTENT] = phytateContent;

    return properties;
  }

  /// Formatta il valore di una proprietà con l'unità di misura appropriata
  static String formatPropertyValue(String property, dynamic value) {
    if (value == null) return 'N/A';

    if (value is String) {
      return value;
    } else if (value is double) {
      return '${value.toStringAsFixed(1)} ${UNITS[property] ?? ''}';
    } else {
      return '$value ${UNITS[property] ?? ''}';
    }
  }

  /// Ottieni il nome visualizzato di una proprietà
  static String getDisplayName(String property) {
    return DISPLAY_NAMES[property] ?? property;
  }

  /// Ottieni la descrizione di una proprietà
  static String getDescription(String property) {
    return DESCRIPTIONS[property] ?? '';
  }
}
