import 'package:flutter/material.dart';
import '../models/advanced_user_profile.dart';
import '../models/diet_plan.dart';
import '../models/user_profile.dart';
import '../models/food.dart';
import '../services/advanced_diet_generator_service.dart';
import '../services/food_database_service.dart';

/// Controller per la gestione della generazione di diete avanzate
class AdvancedDietController extends ChangeNotifier {
  AdvancedUserProfile? _advancedProfile;
  WeeklyDietPlan? _weeklyPlan;
  bool _isGenerating = false;
  String _errorMessage = '';

  // Servizi
  final AdvancedDietGeneratorService _dietGeneratorService;
  final FoodDatabaseService _foodDatabaseService;

  // Getter
  AdvancedUserProfile? get advancedProfile => _advancedProfile;
  WeeklyDietPlan? get weeklyPlan => _weeklyPlan;
  bool get isGenerating => _isGenerating;
  String get errorMessage => _errorMessage;

  AdvancedDietController({
    required AdvancedDietGeneratorService dietGeneratorService,
    required FoodDatabaseService foodDatabaseService,
  }) : _dietGeneratorService = dietGeneratorService,
       _foodDatabaseService = foodDatabaseService;

  /// Imposta un profilo utente avanzato
  void setAdvancedProfile(AdvancedUserProfile profile) {
    _advancedProfile = profile;
    notifyListeners();
  }

  /// Crea un profilo utente avanzato a partire da un profilo base
  AdvancedUserProfile createAdvancedProfile(UserProfile baseProfile) {
    return AdvancedUserProfile(
      baseProfile: baseProfile,
      medicalConditions: [MedicalCondition.none],
      foodIntolerances: [],
      fitnessGoal: FitnessGoal.general,
      primarySport: SportType.none,
      trainingIntensity: TrainingIntensity.none,
      trainingDaysPerWeek: 0,
      trainingMinutesPerSession: 0,
      mealTiming: MealTiming.standard,
      isPregnant: false,
      isBreastfeeding: false,
    );
  }

  /// Aggiorna le condizioni mediche nel profilo avanzato
  void updateMedicalConditions(List<MedicalCondition> conditions) {
    if (_advancedProfile == null) return;

    _advancedProfile = AdvancedUserProfile(
      baseProfile: _advancedProfile!.baseProfile,
      medicalConditions: conditions,
      foodIntolerances: _advancedProfile!.foodIntolerances,
      fitnessGoal: _advancedProfile!.fitnessGoal,
      primarySport: _advancedProfile!.primarySport,
      trainingIntensity: _advancedProfile!.trainingIntensity,
      trainingDaysPerWeek: _advancedProfile!.trainingDaysPerWeek,
      trainingMinutesPerSession: _advancedProfile!.trainingMinutesPerSession,
      mealTiming: _advancedProfile!.mealTiming,
      isPregnant: _advancedProfile!.isPregnant,
      isBreastfeeding: _advancedProfile!.isBreastfeeding,
      nutritionalNeeds: _advancedProfile!.nutritionalNeeds,
      dietaryPreferences: _advancedProfile!.dietaryPreferences,
      mealDistribution: _advancedProfile!.mealDistribution,
    );

    notifyListeners();
  }

  /// Aggiorna le intolleranze alimentari
  void updateFoodIntolerances(List<FoodIntolerance> intolerances) {
    if (_advancedProfile == null) return;

    // Converti le intolleranze in stringhe
    List<String> intoleranceStrings = intolerances
        .where((i) => i != FoodIntolerance.none)
        .map((i) => i.toString().split('.').last)
        .toList();

    _advancedProfile = AdvancedUserProfile(
      baseProfile: _advancedProfile!.baseProfile,
      medicalConditions: _advancedProfile!.medicalConditions,
      foodIntolerances: intoleranceStrings,
      fitnessGoal: _advancedProfile!.fitnessGoal,
      primarySport: _advancedProfile!.primarySport,
      trainingIntensity: _advancedProfile!.trainingIntensity,
      trainingDaysPerWeek: _advancedProfile!.trainingDaysPerWeek,
      trainingMinutesPerSession: _advancedProfile!.trainingMinutesPerSession,
      mealTiming: _advancedProfile!.mealTiming,
      isPregnant: _advancedProfile!.isPregnant,
      isBreastfeeding: _advancedProfile!.isBreastfeeding,
      nutritionalNeeds: _advancedProfile!.nutritionalNeeds,
      dietaryPreferences: _advancedProfile!.dietaryPreferences,
      mealDistribution: _advancedProfile!.mealDistribution,
    );

    notifyListeners();
  }

  /// Aggiorna l'obiettivo di fitness nel profilo avanzato
  void updateFitnessGoal(FitnessGoal goal) {
    if (_advancedProfile == null) return;

    _advancedProfile = AdvancedUserProfile(
      baseProfile: _advancedProfile!.baseProfile,
      medicalConditions: _advancedProfile!.medicalConditions,
      foodIntolerances: _advancedProfile!.foodIntolerances,
      fitnessGoal: goal,
      primarySport: _advancedProfile!.primarySport,
      trainingIntensity: _advancedProfile!.trainingIntensity,
      trainingDaysPerWeek: _advancedProfile!.trainingDaysPerWeek,
      trainingMinutesPerSession: _advancedProfile!.trainingMinutesPerSession,
      mealTiming: _advancedProfile!.mealTiming,
      isPregnant: _advancedProfile!.isPregnant,
      isBreastfeeding: _advancedProfile!.isBreastfeeding,
      nutritionalNeeds: _advancedProfile!.nutritionalNeeds,
      dietaryPreferences: _advancedProfile!.dietaryPreferences,
      mealDistribution: _advancedProfile!.mealDistribution,
    );

    notifyListeners();
  }

  /// Aggiorna le informazioni sull'attività sportiva nel profilo avanzato
  void updateSportInfo({
    SportType? primarySport,
    TrainingIntensity? trainingIntensity,
    int? trainingDaysPerWeek,
    int? trainingMinutesPerSession,
  }) {
    if (_advancedProfile == null) return;

    _advancedProfile = AdvancedUserProfile(
      baseProfile: _advancedProfile!.baseProfile,
      medicalConditions: _advancedProfile!.medicalConditions,
      foodIntolerances: _advancedProfile!.foodIntolerances,
      fitnessGoal: _advancedProfile!.fitnessGoal,
      primarySport: primarySport ?? _advancedProfile!.primarySport,
      trainingIntensity: trainingIntensity ?? _advancedProfile!.trainingIntensity,
      trainingDaysPerWeek: trainingDaysPerWeek ?? _advancedProfile!.trainingDaysPerWeek,
      trainingMinutesPerSession: trainingMinutesPerSession ?? _advancedProfile!.trainingMinutesPerSession,
      mealTiming: _advancedProfile!.mealTiming,
      isPregnant: _advancedProfile!.isPregnant,
      isBreastfeeding: _advancedProfile!.isBreastfeeding,
      nutritionalNeeds: _advancedProfile!.nutritionalNeeds,
      dietaryPreferences: _advancedProfile!.dietaryPreferences,
      mealDistribution: _advancedProfile!.mealDistribution,
    );

    notifyListeners();
  }

  /// Aggiorna il timing dei pasti nel profilo avanzato
  void updateMealTiming(MealTiming timing) {
    if (_advancedProfile == null) return;

    _advancedProfile = AdvancedUserProfile(
      baseProfile: _advancedProfile!.baseProfile,
      medicalConditions: _advancedProfile!.medicalConditions,
      foodIntolerances: _advancedProfile!.foodIntolerances,
      fitnessGoal: _advancedProfile!.fitnessGoal,
      primarySport: _advancedProfile!.primarySport,
      trainingIntensity: _advancedProfile!.trainingIntensity,
      trainingDaysPerWeek: _advancedProfile!.trainingDaysPerWeek,
      trainingMinutesPerSession: _advancedProfile!.trainingMinutesPerSession,
      mealTiming: timing,
      isPregnant: _advancedProfile!.isPregnant,
      isBreastfeeding: _advancedProfile!.isBreastfeeding,
      nutritionalNeeds: _advancedProfile!.nutritionalNeeds,
      dietaryPreferences: _advancedProfile!.dietaryPreferences,
      mealDistribution: _advancedProfile!.mealDistribution,
    );

    notifyListeners();
  }

  /// Genera un piano dietetico settimanale
  Future<bool> generateWeeklyDietPlan() async {
    if (_advancedProfile == null) {
      _errorMessage = 'Profilo utente non impostato';
      notifyListeners();
      return false;
    }

    _isGenerating = true;
    _errorMessage = '';
    notifyListeners();

    try {
      // Carica gli alimenti dal database
      List<Food> foods = await _foodDatabaseService.getAllFoods();

      if (foods.isEmpty) {
        _errorMessage = 'Nessun alimento disponibile nel database';
        _isGenerating = false;
        notifyListeners();
        return false;
      }

      // Genera il piano dietetico
      _weeklyPlan = await _dietGeneratorService.generateWeeklyDietPlan(
        _advancedProfile!,
        foods,
      );

      _isGenerating = false;
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = 'Errore durante la generazione del piano dietetico: $e';
      _isGenerating = false;
      notifyListeners();
      return false;
    }
  }
}
