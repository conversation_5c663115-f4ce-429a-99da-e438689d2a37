import 'dart:io';
import 'dart:typed_data';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import '../models/food.dart';
import '../models/food_recognition_result.dart';
import '../data/food_database.dart';
import 'food_safety_service.dart';

/// Servizio intelligente di riconoscimento alimentare
/// Combina analisi visiva avanzata con validazione per risultati accurati
class IntelligentFoodRecognitionService {
  // Singleton
  static IntelligentFoodRecognitionService? _instance;
  static IntelligentFoodRecognitionService getInstance() {
    _instance ??= IntelligentFoodRecognitionService._();
    return _instance!;
  }
  IntelligentFoodRecognitionService._();

  FoodDatabase? _foodDatabase;
  final Random _random = Random();
  bool _isInitialized = false;

  // Configurazione
  static const double _confidenceThreshold = 0.6;
  static const int _maxResults = 3;

  /// Inizializza il servizio
  Future<void> _ensureInitialized() async {
    if (_isInitialized && _foodDatabase != null) return;

    try {
      _foodDatabase = FoodDatabase();
      _isInitialized = true;
      print('✅ IntelligentFoodRecognitionService inizializzato');
    } catch (e) {
      print('❌ Errore inizializzazione IntelligentFoodRecognitionService: $e');
      _isInitialized = false;
      rethrow;
    }
  }

  /// Analizza un'immagine e riconosce gli alimenti con alta accuratezza
  Future<FoodRecognitionResult> analyzeFood({
    required File imageFile,
    required String mealType,
    String? userId,
  }) async {
    try {
      // Assicurati che il servizio sia inizializzato
      await _ensureInitialized();

      print('🔍 Avvio analisi intelligente per ${imageFile.path}');

      // Preprocessa l'immagine
      final processedImage = await _preprocessImage(imageFile);

      // Analisi multi-livello
      final visualFeatures = _extractVisualFeatures(processedImage);
      final colorAnalysis = _analyzeColors(processedImage);
      final textureAnalysis = _analyzeTexture(processedImage);
      final shapeAnalysis = _analyzeShapes(processedImage);

      // Riconoscimento intelligente
      final recognizedFoods = await _performIntelligentRecognition(
        visualFeatures, colorAnalysis, textureAnalysis, shapeAnalysis, mealType
      );

      // Validazione e filtri di sicurezza
      final validatedResults = _validateAndFilter(recognizedFoods, visualFeatures);

      // Calcola metriche
      final nutritionalSummary = _calculateNutritionalSummary(validatedResults);
      final suggestions = _generateIntelligentSuggestions(validatedResults, mealType, visualFeatures);
      final overallConfidence = _calculateConfidence(validatedResults, visualFeatures);

      final result = FoodRecognitionResult(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        timestamp: DateTime.now(),
        imagePath: imageFile.path,
        mealType: mealType,
        recognizedFoods: validatedResults,
        nutritionalSummary: nutritionalSummary,
        suggestions: suggestions,
        confidenceScore: overallConfidence,
        userId: userId,
      );

      print('✅ Analisi completata: ${validatedResults.length} alimenti, confidenza ${(overallConfidence * 100).toStringAsFixed(1)}%');

      return result;

    } catch (e) {
      print('❌ Errore nell\'analisi: $e');
      rethrow;
    }
  }

  /// Preprocessa l'immagine per l'analisi con gestione memoria ottimizzata
  Future<img.Image> _preprocessImage(File imageFile) async {
    try {
      print('📷 Inizio preprocessing immagine: ${imageFile.path}');

      // Verifica dimensione file
      final fileSize = await imageFile.length();
      print('📊 Dimensione file: ${fileSize} bytes');

      if (fileSize > 10 * 1024 * 1024) { // 10MB
        throw Exception('File troppo grande: ${fileSize} bytes');
      }

      // Leggi i bytes con gestione memoria
      final bytes = await imageFile.readAsBytes();
      print('📖 Bytes letti: ${bytes.length}');

      // Decodifica con controllo errori
      final image = img.decodeImage(bytes);
      if (image == null) {
        throw Exception('Impossibile decodificare l\'immagine: formato non supportato');
      }

      print('🖼️ Immagine originale: ${image.width}x${image.height}');

      // Ridimensiona in modo più conservativo per evitare problemi di memoria
      final maxSize = 256; // Ridotto da 512 per prestazioni migliori
      img.Image resized;

      if (image.width > maxSize || image.height > maxSize) {
        if (image.width > image.height) {
          resized = img.copyResize(image, width: maxSize);
        } else {
          resized = img.copyResize(image, height: maxSize);
        }
        print('🔄 Immagine ridimensionata: ${resized.width}x${resized.height}');
      } else {
        resized = image;
        print('✅ Immagine già di dimensioni appropriate');
      }

      // Migliora la qualità in modo più leggero
      final enhanced = img.adjustColor(resized,
        contrast: 1.1,  // Ridotto da 1.15
        brightness: 1.05, // Ridotto da 1.1
        saturation: 1.1   // Ridotto da 1.2
      );

      print('✅ Preprocessing completato');
      return enhanced;

    } catch (e, stackTrace) {
      print('❌ Errore nel preprocessing: $e');
      print('Stack trace: $stackTrace');
      rethrow;
    }
  }

  /// Estrae caratteristiche visive avanzate con ottimizzazioni
  Map<String, dynamic> _extractVisualFeatures(img.Image image) {
    try {
      print('🔍 Inizio estrazione caratteristiche visive');
      final features = <String, dynamic>{};

      // Analisi luminosità ottimizzata (campionamento più rado)
      double totalBrightness = 0;
      int pixelCount = 0;
      final step = 10; // Aumentato da 5 per prestazioni migliori

      for (int y = 0; y < image.height; y += step) {
        for (int x = 0; x < image.width; x += step) {
          try {
            final pixel = image.getPixel(x, y);
            final brightness = (pixel.r + pixel.g + pixel.b) / 3;
            totalBrightness += brightness;
            pixelCount++;
          } catch (e) {
            // Ignora pixel fuori bounds
            continue;
          }
        }
      }

      if (pixelCount > 0) {
        features['averageBrightness'] = totalBrightness / pixelCount;
        features['isWellLit'] = features['averageBrightness'] > 100;
      } else {
        features['averageBrightness'] = 128.0; // Valore di default
        features['isWellLit'] = true;
      }

      // Analisi contrasto semplificata
      try {
        features['hasGoodContrast'] = _calculateContrastOptimized(image) > 50;
      } catch (e) {
        print('⚠️ Errore calcolo contrasto: $e');
        features['hasGoodContrast'] = true; // Default sicuro
      }

      // Analisi nitidezza semplificata
      try {
        features['isSharp'] = _calculateSharpnessOptimized(image) > 30;
      } catch (e) {
        print('⚠️ Errore calcolo nitidezza: $e');
        features['isSharp'] = true; // Default sicuro
      }

      print('✅ Caratteristiche visive estratte');
      return features;

    } catch (e, stackTrace) {
      print('❌ Errore estrazione caratteristiche: $e');
      print('Stack trace: $stackTrace');

      // Ritorna valori di default sicuri
      return {
        'averageBrightness': 128.0,
        'isWellLit': true,
        'hasGoodContrast': true,
        'isSharp': true,
      };
    }
  }

  /// Analizza i colori dell'immagine
  Map<String, double> _analyzeColors(img.Image image) {
    final colorCounts = <String, int>{
      'red': 0, 'green': 0, 'blue': 0, 'yellow': 0, 'orange': 0,
      'brown': 0, 'white': 0, 'black': 0, 'purple': 0, 'pink': 0
    };

    int totalPixels = 0;

    for (int y = 0; y < image.height; y += 8) {
      for (int x = 0; x < image.width; x += 8) {
        final pixel = image.getPixel(x, y);
        final color = _categorizeAdvancedColor(pixel.r.toInt(), pixel.g.toInt(), pixel.b.toInt());
        colorCounts[color] = colorCounts[color]! + 1;
        totalPixels++;
      }
    }

    final colorPercentages = <String, double>{};
    colorCounts.forEach((color, count) {
      colorPercentages[color] = count / totalPixels;
    });

    return colorPercentages;
  }

  /// Categorizza i colori in modo avanzato
  String _categorizeAdvancedColor(int r, int g, int b) {
    // Bianco
    if (r > 220 && g > 220 && b > 220) return 'white';

    // Nero
    if (r < 50 && g < 50 && b < 50) return 'black';

    // Rosso
    if (r > g + 40 && r > b + 40 && r > 100) return 'red';

    // Verde
    if (g > r + 40 && g > b + 40 && g > 100) return 'green';

    // Blu
    if (b > r + 40 && b > g + 40 && b > 100) return 'blue';

    // Giallo
    if (r > 150 && g > 150 && b < 100 && (r + g) > 2 * b) return 'yellow';

    // Arancione
    if (r > 150 && g > 80 && g < 150 && b < 100 && r > g) return 'orange';

    // Marrone
    if (r > 80 && r < 160 && g > 50 && g < 120 && b < 80) return 'brown';

    // Rosa
    if (r > 150 && g < 120 && b > 100 && r > b) return 'pink';

    // Viola
    if (r > 100 && b > 100 && g < 80 && (r - b).abs() < 50) return 'purple';

    return 'other';
  }

  /// Analizza la texture dell'immagine
  Map<String, double> _analyzeTexture(img.Image image) {
    final features = <String, double>{};

    // Calcola la varianza dei pixel per determinare la texture
    final pixels = <double>[];
    for (int y = 0; y < image.height; y += 10) {
      for (int x = 0; x < image.width; x += 10) {
        final pixel = image.getPixel(x, y);
        final gray = (pixel.r + pixel.g + pixel.b) / 3;
        pixels.add(gray);
      }
    }

    final mean = pixels.reduce((a, b) => a + b) / pixels.length;
    final variance = pixels.map((p) => pow(p - mean, 2)).reduce((a, b) => a + b) / pixels.length;

    features['variance'] = variance;
    features['isSmooth'] = variance < 500 ? 1.0 : 0.0; // Superficie liscia
    features['isRough'] = variance > 1500 ? 1.0 : 0.0; // Superficie ruvida
    features['isTextured'] = (variance > 800 && variance < 1500) ? 1.0 : 0.0; // Texture media

    return features;
  }

  /// Analizza le forme nell'immagine
  Map<String, bool> _analyzeShapes(img.Image image) {
    final shapes = <String, bool>{};

    // Analisi semplificata delle forme basata sui contorni
    final edges = _detectEdges(image);
    final edgeCount = _countEdges(edges);

    shapes['hasRoundShapes'] = edgeCount < image.width * image.height * 0.1;
    shapes['hasAngularShapes'] = edgeCount > image.width * image.height * 0.2;
    shapes['hasComplexShapes'] = edgeCount > image.width * image.height * 0.15;

    return shapes;
  }

  /// Riconoscimento intelligente basato su tutte le caratteristiche
  Future<List<RecognizedFood>> _performIntelligentRecognition(
    Map<String, dynamic> visualFeatures,
    Map<String, double> colors,
    Map<String, double> texture,
    Map<String, bool> shapes,
    String mealType,
  ) async {
    if (_foodDatabase == null) {
      throw Exception('Database degli alimenti non inizializzato');
    }

    final allFoods = await _foodDatabase!.getAllFoods();
    final safeFoods = FoodSafetyService.filterSafeFoods(allFoods);
    final recognizedFoods = <RecognizedFood>[];

    // Algoritmo di matching intelligente
    for (final food in safeFoods) {
      final confidence = _calculateFoodConfidence(food, colors, texture, shapes, mealType);

      if (confidence > _confidenceThreshold) {
        final estimatedGrams = _estimateRealisticPortion(food, confidence, mealType);

        recognizedFoods.add(RecognizedFood(
          food: food,
          confidence: confidence,
          estimatedGrams: estimatedGrams,
          boundingBox: BoundingBox(x: 0.1, y: 0.1, width: 0.8, height: 0.8),
        ));
      }
    }

    // Ordina per confidenza e prendi i migliori
    recognizedFoods.sort((a, b) => b.confidence.compareTo(a.confidence));
    return recognizedFoods.take(_maxResults).toList();
  }

  /// Calcola la confidenza per un alimento specifico
  double _calculateFoodConfidence(
    Food food,
    Map<String, double> colors,
    Map<String, double> texture,
    Map<String, bool> shapes,
    String mealType,
  ) {
    double confidence = 0.0;
    final foodName = food.name.toLowerCase();

    // Matching basato sui colori - con null safety
    final redValue = colors['red'] ?? 0.0;
    final greenValue = colors['green'] ?? 0.0;
    final yellowValue = colors['yellow'] ?? 0.0;
    final orangeValue = colors['orange'] ?? 0.0;
    final whiteValue = colors['white'] ?? 0.0;
    final brownValue = colors['brown'] ?? 0.0;

    if (foodName.contains('mela')) {
      confidence += redValue * 0.4 + greenValue * 0.3 + yellowValue * 0.2;
    } else if (foodName.contains('spinaci')) {
      confidence += greenValue * 0.6;
    } else if (foodName.contains('pomodoro')) {
      confidence += redValue * 0.5;
    } else if (foodName.contains('banana')) {
      confidence += yellowValue * 0.5;
    } else if (foodName.contains('arancia')) {
      confidence += orangeValue * 0.5;
    } else if (foodName.contains('pollo')) {
      confidence += whiteValue * 0.3 + brownValue * 0.3;
    } else if (foodName.contains('pane')) {
      confidence += brownValue * 0.4 + yellowValue * 0.2;
    } else if (foodName.contains('riso')) {
      confidence += whiteValue * 0.4;
    } else if (foodName.contains('pasta')) {
      confidence += yellowValue * 0.3 + whiteValue * 0.2;
    }

    // Bonus per texture appropriata
    final isSmooth = texture['isSmooth'] ?? 0.0;
    final isRough = texture['isRough'] ?? 0.0;

    if (isSmooth > 0.5 && (foodName.contains('latte') || foodName.contains('yogurt'))) {
      confidence += 0.2;
    }

    if (isRough > 0.5 && (foodName.contains('pane') || foodName.contains('cereali'))) {
      confidence += 0.15;
    }

    // Bonus per contesto del pasto
    if (mealType == 'breakfast' && (foodName.contains('latte') || foodName.contains('cereali'))) {
      confidence += 0.1;
    }

    if (mealType == 'lunch' && food.categories.contains(FoodCategory.protein)) {
      confidence += 0.1;
    }

    // Penalità per combinazioni improbabili
    if (mealType == 'breakfast' && foodName.contains('pizza')) {
      confidence -= 0.3;
    }

    return confidence.clamp(0.0, 1.0);
  }

  /// Stima porzioni realistiche
  int _estimateRealisticPortion(Food food, double confidence, String mealType) {
    final foodName = food.name.toLowerCase();
    int baseGrams = 100;

    // Porzioni tipiche per categoria
    if (food.categories.contains(FoodCategory.fruit)) {
      baseGrams = foodName.contains('mela') ? 150 : 120;
    } else if (food.categories.contains(FoodCategory.vegetable)) {
      baseGrams = 80;
    } else if (food.categories.contains(FoodCategory.protein)) {
      baseGrams = mealType == 'snack' ? 50 : 120;
    } else if (food.categories.contains(FoodCategory.grain)) {
      baseGrams = 80;
    } else if (food.categories.contains(FoodCategory.dairy)) {
      baseGrams = foodName.contains('latte') ? 200 : 100;
    }

    // Modifica basata sulla confidenza
    final confidenceMultiplier = 0.8 + (confidence * 0.4); // 0.8 - 1.2

    return (baseGrams * confidenceMultiplier).round().clamp(30, 300);
  }

  /// Valida e filtra i risultati
  List<RecognizedFood> _validateAndFilter(
    List<RecognizedFood> results,
    Map<String, dynamic> visualFeatures,
  ) {
    final validatedResults = <RecognizedFood>[];

    for (final result in results) {
      // Controlli di coerenza
      if (_isResultRealistic(result, visualFeatures)) {
        validatedResults.add(result);
      } else {
        print('⚠️ Risultato non realistico filtrato: ${result.food.name}');
      }
    }

    return validatedResults;
  }

  /// Verifica se un risultato è realistico
  bool _isResultRealistic(RecognizedFood result, Map<String, dynamic> visualFeatures) {
    // Se l'immagine è troppo scura o sfocata, riduci la confidenza
    final isWellLit = visualFeatures['isWellLit'] as bool? ?? true;
    final isSharp = visualFeatures['isSharp'] as bool? ?? true;

    if (!isWellLit || !isSharp) {
      return result.confidence > 0.8; // Richiedi confidenza più alta
    }

    // Altri controlli di coerenza
    final foodName = result.food.name.toLowerCase();

    // Non riconoscere carne se la confidenza è bassa
    if (foodName.contains('pollo') || foodName.contains('manzo')) {
      return result.confidence > 0.75;
    }

    return true;
  }

  /// Calcola contrasto dell'immagine in modo ottimizzato
  double _calculateContrastOptimized(img.Image image) {
    try {
      double minBrightness = 255;
      double maxBrightness = 0;
      final step = 20; // Aumentato da 10 per prestazioni migliori

      for (int y = 0; y < image.height; y += step) {
        for (int x = 0; x < image.width; x += step) {
          try {
            final pixel = image.getPixel(x, y);
            final brightness = (pixel.r + pixel.g + pixel.b) / 3;
            minBrightness = min(minBrightness, brightness);
            maxBrightness = max(maxBrightness, brightness);
          } catch (e) {
            // Ignora pixel fuori bounds
            continue;
          }
        }
      }

      return maxBrightness - minBrightness;
    } catch (e) {
      print('⚠️ Errore nel calcolo contrasto ottimizzato: $e');
      return 100.0; // Valore di default
    }
  }

  /// Calcola contrasto dell'immagine (metodo legacy)
  double _calculateContrast(img.Image image) {
    return _calculateContrastOptimized(image);
  }

  /// Calcola nitidezza dell'immagine in modo ottimizzato
  double _calculateSharpnessOptimized(img.Image image) {
    try {
      double sharpness = 0;
      int count = 0;
      final step = 20; // Aumentato da 10 per prestazioni migliori

      for (int y = 1; y < image.height - 1; y += step) {
        for (int x = 1; x < image.width - 1; x += step) {
          try {
            final center = image.getPixel(x, y);
            final right = image.getPixel(x + 1, y);
            final bottom = image.getPixel(x, y + 1);

            final centerGray = (center.r + center.g + center.b) / 3;
            final rightGray = (right.r + right.g + right.b) / 3;
            final bottomGray = (bottom.r + bottom.g + bottom.b) / 3;

            sharpness += (centerGray - rightGray).abs() + (centerGray - bottomGray).abs();
            count++;
          } catch (e) {
            // Ignora pixel fuori bounds
            continue;
          }
        }
      }

      return count > 0 ? sharpness / count : 50.0; // Valore di default
    } catch (e) {
      print('⚠️ Errore nel calcolo nitidezza ottimizzato: $e');
      return 50.0; // Valore di default
    }
  }

  /// Calcola nitidezza dell'immagine (metodo legacy)
  double _calculateSharpness(img.Image image) {
    return _calculateSharpnessOptimized(image);
  }

  /// Rileva i bordi nell'immagine
  img.Image _detectEdges(img.Image image) {
    final edges = img.Image(width: image.width, height: image.height);

    for (int y = 1; y < image.height - 1; y++) {
      for (int x = 1; x < image.width - 1; x++) {
        final center = image.getPixel(x, y);
        final right = image.getPixel(x + 1, y);
        final bottom = image.getPixel(x, y + 1);

        final centerGray = (center.r + center.g + center.b) / 3;
        final rightGray = (right.r + right.g + right.b) / 3;
        final bottomGray = (bottom.r + bottom.g + bottom.b) / 3;

        final edgeStrength = (centerGray - rightGray).abs() + (centerGray - bottomGray).abs();
        final edgeValue = edgeStrength > 30 ? 255 : 0;

        edges.setPixel(x, y, img.ColorRgb8(edgeValue.toInt(), edgeValue.toInt(), edgeValue.toInt()));
      }
    }

    return edges;
  }

  /// Conta i bordi nell'immagine
  int _countEdges(img.Image edges) {
    int edgeCount = 0;

    for (int y = 0; y < edges.height; y++) {
      for (int x = 0; x < edges.width; x++) {
        final pixel = edges.getPixel(x, y);
        if (pixel.r > 128) edgeCount++;
      }
    }

    return edgeCount;
  }

  /// Calcola il riepilogo nutrizionale
  NutritionalSummary _calculateNutritionalSummary(List<RecognizedFood> foods) {
    double totalCalories = 0;
    double totalProteins = 0;
    double totalCarbs = 0;
    double totalFats = 0;
    double totalFiber = 0;

    for (final recognizedFood in foods) {
      final factor = recognizedFood.estimatedGrams / 100.0;
      totalCalories += recognizedFood.food.calories * factor;
      totalProteins += recognizedFood.food.proteins * factor;
      totalCarbs += recognizedFood.food.carbs * factor;
      totalFats += recognizedFood.food.fats * factor;
      totalFiber += recognizedFood.food.fiber * factor;
    }

    return NutritionalSummary(
      calories: totalCalories.round(),
      proteins: totalProteins,
      carbs: totalCarbs,
      fats: totalFats,
      fiber: totalFiber,
    );
  }

  /// Genera suggerimenti intelligenti
  List<String> _generateIntelligentSuggestions(
    List<RecognizedFood> foods,
    String mealType,
    Map<String, dynamic> visualFeatures,
  ) {
    final suggestions = <String>[];

    if (foods.isEmpty) {
      final isWellLit = visualFeatures['isWellLit'] as bool? ?? true;
      final isSharp = visualFeatures['isSharp'] as bool? ?? true;

      if (!isWellLit) {
        suggestions.add('💡 L\'immagine sembra poco illuminata. Prova con più luce per un riconoscimento migliore.');
      } else if (!isSharp) {
        suggestions.add('📸 L\'immagine appare sfocata. Assicurati che la fotocamera sia a fuoco.');
      } else {
        suggestions.add('🔍 Non riesco a riconoscere chiaramente gli alimenti. Prova con una foto più ravvicinata.');
      }
      return suggestions;
    }

    // Analisi nutrizionale
    final hasProtein = foods.any((f) => f.food.categories.contains(FoodCategory.protein));
    final hasVegetables = foods.any((f) => f.food.categories.contains(FoodCategory.vegetable));
    final hasCarbs = foods.any((f) => f.food.categories.contains(FoodCategory.grain));

    // Suggerimenti basati sull'analisi
    if (!hasProtein && mealType != 'snack') {
      suggestions.add('💪 Considera di aggiungere una fonte di proteine per un pasto completo.');
    }

    if (!hasVegetables) {
      suggestions.add('🥬 Aggiungi verdure per aumentare vitamine e fibre.');
    }

    if (!hasCarbs && (mealType == 'breakfast' || mealType == 'lunch')) {
      suggestions.add('🌾 Una fonte di carboidrati fornirebbe energia sostenibile.');
    }

    // Suggerimenti specifici per tipo di pasto
    switch (mealType) {
      case 'breakfast':
        suggestions.add('🌅 Ottima colazione! Ricorda di idratarti bene.');
        break;
      case 'lunch':
        suggestions.add('🍽️ Pranzo bilanciato per l\'energia pomeridiana.');
        break;
      case 'dinner':
        suggestions.add('🌙 Cena appropriata. Evita pasti pesanti prima di dormire.');
        break;
      case 'snack':
        suggestions.add('🍎 Spuntino sano per mantenere stabile la glicemia.');
        break;
    }

    return suggestions;
  }

  /// Calcola la confidenza complessiva
  double _calculateConfidence(List<RecognizedFood> foods, Map<String, dynamic> visualFeatures) {
    if (foods.isEmpty) return 0.0;

    double baseConfidence = foods.fold(0.0, (sum, food) => sum + food.confidence) / foods.length;

    // Modifica basata sulla qualità dell'immagine
    final isWellLit = visualFeatures['isWellLit'] as bool? ?? true;
    final isSharp = visualFeatures['isSharp'] as bool? ?? true;
    final hasGoodContrast = visualFeatures['hasGoodContrast'] as bool? ?? true;

    if (!isWellLit) baseConfidence *= 0.8;
    if (!isSharp) baseConfidence *= 0.7;
    if (!hasGoodContrast) baseConfidence *= 0.9;

    return baseConfidence.clamp(0.0, 1.0);
  }
}

// Funzione abs rimossa - usiamo il metodo nativo .abs()
