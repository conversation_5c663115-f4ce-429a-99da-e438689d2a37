import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../constants/app_constants.dart';
import '../theme/app_theme.dart';
import '../widgets/badge_widgets.dart';
import '../controllers/badge_controller.dart';
import 'food_browser_screen.dart';
// import 'user_profile_screen.dart';
import 'diet_generator_screen.dart';
import 'ai_recommendations_screen.dart';
import 'food_oracle_screen.dart';
import 'welljourney_screen.dart';
import 'badges_screen.dart';
import 'nutriscore_screen.dart';
import 'community/community_screen.dart';
import 'diet_plan_test_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    _initializeBadgeSystem();
  }

  /// Inizializza il sistema badge
  Future<void> _initializeBadgeSystem() async {
    final badgeController = BadgeController.instance;
    if (!badgeController.isInitialized) {
      await badgeController.initialize();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppConstants.appName),
        actions: [
          IconButton(
            icon: const Icon(Icons.science_outlined),
            tooltip: 'Test Piano Dietetico',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const DietPlanTestScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings_outlined),
            onPressed: () {
              // TODO: Navigazione alle impostazioni
            },
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header con benvenuto e immagine del Dr. Staffilano
            _buildWelcomeHeader(context),
            const SizedBox(height: 24),

            // Sezione Badge e Community Access
            Row(
              children: [
                Expanded(
                  child: HomeBadgeSection(
                    onViewAllTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const BadgesScreen(),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 16),
                // PULSANTE COMMUNITY PROMINENTE
                _buildCommunityAccessButton(),
              ],
            ).animate().fadeIn(duration: 600.ms, delay: 200.ms).slideY(begin: 0.1, end: 0),
            const SizedBox(height: 24),

            // Tagline dell'app
            Text(
              AppConstants.appTagline,
              style: Theme.of(context).textTheme.headlineMedium,
            ).animate().fadeIn(duration: 600.ms, delay: 300.ms).slideY(begin: 0.2, end: 0),
            const SizedBox(height: 24),

            // Titolo sezione funzionalità
            Text(
              'Funzionalità Premium',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),

            // Griglia di card per le funzionalità principali
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: 0.85, // Card leggermente più alte
              children: [
                // PRIMA RIGA - WellJourney e AI Nutritionist
                _buildFeatureCard(
                  context,
                  'WellJourney™',
                  FontAwesomeIcons.route,
                  Colors.deepPurple,
                  'Percorsi guidati, sfide e gamificazione per il tuo benessere',
                  () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const WellJourneyScreen(
                          initialTabIndex: WellJourneyTabs.pathways,
                        ),
                      ),
                    );
                  },
                ).animate().fadeIn(duration: 400.ms, delay: 100.ms).slideY(begin: 0.1, end: 0),

                _buildFeatureCard(
                  context,
                  AppConstants.aiNutritionistName,
                  FontAwesomeIcons.wandMagicSparkles,
                  AppTheme.accentColor,
                  'Genera piani nutrizionali personalizzati con l\'AI',
                  () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const DietGeneratorScreen(),
                      ),
                    );
                  },
                ).animate().fadeIn(duration: 400.ms, delay: 150.ms).slideY(begin: 0.1, end: 0),

                // SECONDA RIGA - Food Oracle e Food Database
                _buildFeatureCard(
                  context,
                  AppConstants.foodOracleName,
                  FontAwesomeIcons.camera,
                  AppTheme.secondaryColor,
                  'Analizza i tuoi pasti con la visione artificiale',
                  () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const FoodOracleScreen(),
                      ),
                    );
                  },
                ).animate().fadeIn(duration: 400.ms, delay: 200.ms).slideY(begin: 0.1, end: 0),

                _buildFeatureCard(
                  context,
                  'Food Database',
                  FontAwesomeIcons.appleWhole,
                  AppTheme.primaryColor,
                  'Esplora il database alimentare validato dal Dr. Staffilano',
                  () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const FoodBrowserScreen(),
                      ),
                    );
                  },
                ).animate().fadeIn(duration: 400.ms, delay: 250.ms).slideY(begin: 0.1, end: 0),

                // TERZA RIGA - AI Recommendations e COMMUNITY PROMINENTE
                _buildFeatureCard(
                  context,
                  'AI Recommendations',
                  FontAwesomeIcons.brain,
                  Colors.purple,
                  'Scopri raccomandazioni alimentari personalizzate basate sulle tue preferenze',
                  () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const AIRecommendationsScreen(),
                      ),
                    );
                  },
                ).animate().fadeIn(duration: 400.ms, delay: 300.ms).slideY(begin: 0.1, end: 0),

                // COMMUNITY CARD PROMINENTE - BOTTOM RIGHT
                _buildCommunityFeatureCard(context).animate().fadeIn(duration: 400.ms, delay: 350.ms).slideY(begin: 0.1, end: 0).shimmer(duration: 2000.ms, delay: 1000.ms),
              ],
            ),

            const SizedBox(height: 24),

            // Sezione per i consigli del Dr. Staffilano
            Row(
              children: [
                Text(
                  'Consiglio del Dr. Staffilano',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(width: 8),
                const Icon(
                  FontAwesomeIcons.certificate,
                  size: 16,
                  color: AppTheme.accentColor,
                ),
              ],
            ),
            const SizedBox(height: 12),
            Card(
              elevation: 1,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(color: AppTheme.primaryColor.withOpacity(0.2), width: 1),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        CircleAvatar(
                          backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
                          child: Icon(
                            FontAwesomeIcons.lightbulb,
                            color: AppTheme.accentColor,
                            size: 16,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Nutrizione e salute del cuore',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontFamily: 'serif',
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      AppConstants.doctorPhilosophy,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 12),
                    Align(
                      alignment: Alignment.bottomRight,
                      child: Text(
                        '- Dr. Marco Staffilano',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontStyle: FontStyle.italic,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ).animate().fadeIn(duration: 600.ms, delay: 500.ms),

            const SizedBox(height: 24),

            // Sezione per i percorsi WellJourney
            Row(
              children: [
                Text(
                  AppConstants.wellJourneyName,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(width: 8),
                const Icon(
                  FontAwesomeIcons.route,
                  size: 16,
                  color: AppTheme.accentColor,
                ),
              ],
            ),
            const SizedBox(height: 12),
            Card(
              elevation: 1,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Immagine di copertina
                  Container(
                    height: 120,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: AppTheme.secondaryColor,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                    ),
                    child: Center(
                      child: Icon(
                        FontAwesomeIcons.personWalking,
                        size: 48,
                        color: Colors.white.withOpacity(0.8),
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Percorsi nutrizionali personalizzati',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontFamily: 'serif',
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Intraprendi un viaggio nutrizionale guidato con percorsi tematici creati dal Dr. Staffilano. Ogni percorso è progettato per aiutarti a raggiungere obiettivi specifici attraverso piani alimentari personalizzati che integrano la sua esperienza in cardiologia e medicina dello sport.',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          icon: const Icon(FontAwesomeIcons.compass),
                          label: const Text('Inizia il tuo WellJourney™'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.deepPurple,
                            foregroundColor: Colors.white,
                          ),
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const WellJourneyScreen(
                                  initialTabIndex: WellJourneyTabs.pathways,
                                ),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ).animate().fadeIn(duration: 600.ms, delay: 600.ms),

            const SizedBox(height: 32),

            // Disclaimer
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppTheme.dividerColor),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(
                        FontAwesomeIcons.circleInfo,
                        size: 16,
                        color: AppTheme.textSecondaryColor,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Disclaimer',
                        style: Theme.of(context).textTheme.labelLarge?.copyWith(
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    AppConstants.disclaimerText,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  /// Costruisce l'header di benvenuto con l'immagine del Dr. Staffilano
  Widget _buildWelcomeHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        children: [
          // Colonna con il testo di benvenuto
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppConstants.welcomeMessage,
                  style: Theme.of(context).textTheme.displaySmall,
                ).animate().fadeIn(duration: 500.ms).slideX(begin: -0.2, end: 0),
                const SizedBox(height: 8),
                Text(
                  'Con il Dr. ${AppConstants.doctorName}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppTheme.textSecondaryColor,
                    fontStyle: FontStyle.italic,
                  ),
                ).animate().fadeIn(duration: 500.ms, delay: 200.ms).slideX(begin: -0.1, end: 0),
              ],
            ),
          ),

          // Immagine del Dr. Staffilano (placeholder per ora)
          Expanded(
            flex: 2,
            child: Container(
              height: 100,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppTheme.primaryColor.withOpacity(0.1),
                border: Border.all(
                  color: AppTheme.primaryColor.withOpacity(0.3),
                  width: 2,
                ),
              ),
              child: Center(
                child: Icon(
                  FontAwesomeIcons.userDoctor,
                  size: 48,
                  color: AppTheme.primaryColor,
                ),
              ),
            ).animate().fadeIn(duration: 600.ms, delay: 300.ms).scale(begin: const Offset(0.8, 0.8), end: const Offset(1.0, 1.0)),
          ),
        ],
      ),
    );
  }

  /// Costruisce una card per una funzionalità dell'app
  Widget _buildFeatureCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    String description,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 1,
      shadowColor: color.withOpacity(0.3),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Icona in un cerchio colorato
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: 24,
                  color: color,
                ),
              ),
              const SizedBox(height: 16),

              // Titolo della funzionalità
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.left,
              ),
              const SizedBox(height: 8),

              // Descrizione della funzionalità
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.textSecondaryColor,
                  height: 1.4,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Costruisce il pulsante di accesso community prominente
  Widget _buildCommunityAccessButton() {
    return Container(
      width: 140,
      height: 100,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.teal.withOpacity(0.9),
            Colors.teal.withOpacity(0.7),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.teal.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const CommunityScreen(),
              ),
            );
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    FontAwesomeIcons.users,
                    size: 20,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'InnerCircle™',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  'Community',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.white.withOpacity(0.9),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    ).animate().scale(duration: 300.ms, delay: 100.ms).shimmer(duration: 2000.ms, delay: 500.ms);
  }

  /// Costruisce la card community per la griglia (bottom-right)
  Widget _buildCommunityFeatureCard(BuildContext context) {
    return Card(
      elevation: 3,
      shadowColor: Colors.teal.withOpacity(0.4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: Colors.teal.withOpacity(0.3),
          width: 2,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.teal.withOpacity(0.1),
              Colors.teal.withOpacity(0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const CommunityScreen(),
              ),
            );
          },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header con icona e badge NUOVO
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.teal.withOpacity(0.2),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        FontAwesomeIcons.users,
                        size: 24,
                        color: Colors.teal,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.orange.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '🌟 NUOVO',
                        style: TextStyle(
                          fontSize: 8,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange[800],
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Titolo della funzionalità
                Text(
                  'Staffilano InnerCircle™',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.teal[800],
                  ),
                  textAlign: TextAlign.left,
                ),
                const SizedBox(height: 8),

                // Descrizione della funzionalità
                Text(
                  'Community esclusiva per condividere il tuo percorso di benessere con altri membri',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.teal[600],
                    height: 1.4,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
