import 'data/specific_diet_foods.dart';
import 'services/specific_diet_generator_service.dart';
import 'models/user_profile.dart';

/// TEST DATABASE ESPANSO
/// Verifica il numero di alimenti dopo l'espansione
Future<void> main() async {
  print('🚀 TEST DATABASE ESPANSO');
  print('=' * 40);
  print('Obiettivo: Verificare l\'espansione del database alimentare\n');

  try {
    // FASE 1: VERIFICA DATABASE ESPANSO
    print('1️⃣ VERIFICA DATABASE ESPANSO');
    print('-' * 30);
    
    final allFoods = SpecificDietFoods.getAllFoods();
    print('📊 Alimenti totali nel database: ${allFoods.length}');
    
    // Conta per categoria
    final fruits = SpecificDietFoods.getFruits();
    final vegetables = SpecificDietFoods.getVegetables();
    final grains = SpecificDietFoods.getGrains();
    final proteins = SpecificDietFoods.getProteins();
    final dairy = SpecificDietFoods.getDairy();
    final fats = SpecificDietFoods.getFats();
    final sweets = SpecificDietFoods.getSweets();
    final beverages = SpecificDietFoods.getBeverages();
    final breakfast = SpecificDietFoods.getBreakfastItems();
    
    print('\n📈 DISTRIBUZIONE PER CATEGORIA:');
    print('   🍎 Frutta: ${fruits.length} alimenti');
    print('   🥬 Verdure: ${vegetables.length} alimenti');
    print('   🌾 Cereali: ${grains.length} alimenti');
    print('   🍗 Proteine: ${proteins.length} alimenti');
    print('   🥛 Latticini: ${dairy.length} alimenti');
    print('   🫒 Grassi: ${fats.length} alimenti');
    print('   🍯 Dolci: ${sweets.length} alimenti');
    print('   💧 Bevande: ${beverages.length} alimenti');
    print('   🥞 Colazione: ${breakfast.length} alimenti');
    
    final totalCounted = fruits.length + vegetables.length + grains.length + 
                        proteins.length + dairy.length + fats.length + 
                        sweets.length + beverages.length + breakfast.length;
    print('\n   📊 Totale conteggiato: $totalCounted');
    print('   📊 Totale database: ${allFoods.length}');
    
    // FASE 2: VERIFICA ALIMENTI SICURI
    print('\n2️⃣ VERIFICA FILTRO DI SICUREZZA');
    print('-' * 35);
    
    final generator = await SpecificDietGeneratorService.getInstance();
    print('✅ SpecificDietGeneratorService inizializzato');
    
    // Crea profilo test
    final testProfile = UserProfile(
      id: 'test_expanded_${DateTime.now().millisecondsSinceEpoch}',
      name: 'Test Database Espanso',
      age: 30,
      gender: Gender.male,
      height: 175,
      weight: 70,
      activityLevel: ActivityLevel.moderate,
      goal: Goal.maintain,
      dietType: DietType.omnivore,
      allergies: [],
      dislikedFoods: [],
      mealsPerDay: 3,
    );
    
    print('👤 Profilo creato per test');
    
    // FASE 3: TEST GENERAZIONE CON DATABASE ESPANSO
    print('\n3️⃣ TEST GENERAZIONE CON DATABASE ESPANSO');
    print('-' * 45);
    
    print('🔄 Generazione piano dietetico...');
    
    final weeklyPlan = await generator.generateWeeklyDietPlan(
      testProfile,
      weeks: 1,
    );
    
    if (weeklyPlan.dailyPlans.isNotEmpty) {
      final dailyPlan = weeklyPlan.dailyPlans.first;
      final allSelectedFoods = <String>[];
      
      print('\n📋 PIANO GENERATO:');
      for (final meal in dailyPlan.meals) {
        final mealFoods = meal.foods.map((fp) => fp.food.name).toList();
        allSelectedFoods.addAll(mealFoods);
        
        final mealType = _getMealName(meal.type);
        print('   $mealType: ${mealFoods.join(', ')} (${meal.foods.length} alimenti)');
      }
      
      final uniqueSelectedFoods = allSelectedFoods.toSet();
      final varietyRatio = uniqueSelectedFoods.length / allSelectedFoods.length;
      
      print('\n📊 STATISTICHE VARIETÀ:');
      print('   - Alimenti totali selezionati: ${allSelectedFoods.length}');
      print('   - Alimenti unici utilizzati: ${uniqueSelectedFoods.length}');
      print('   - Rapporto varietà: ${(varietyRatio * 100).toStringAsFixed(1)}%');
      print('   - Utilizzo database: ${(uniqueSelectedFoods.length / allFoods.length * 100).toStringAsFixed(1)}%');
      
      // FASE 4: CONFRONTO CON OBIETTIVI
      print('\n4️⃣ CONFRONTO CON OBIETTIVI');
      print('-' * 25);
      
      final targetVariety = 0.7; // 70%
      final targetUniqueFoods = 20;
      final targetDbUtilization = 0.3; // 30%
      
      print('🎯 OBIETTIVI vs RISULTATI:');
      print('   Varietà target: ${(targetVariety * 100).toStringAsFixed(0)}% → Ottenuto: ${(varietyRatio * 100).toStringAsFixed(1)}%');
      print('   Alimenti unici target: $targetUniqueFoods → Ottenuto: ${uniqueSelectedFoods.length}');
      print('   Utilizzo DB target: ${(targetDbUtilization * 100).toStringAsFixed(0)}% → Ottenuto: ${(uniqueSelectedFoods.length / allFoods.length * 100).toStringAsFixed(1)}%');
      
      // Valutazione risultati
      final varietySuccess = varietyRatio >= targetVariety;
      final uniqueFoodsSuccess = uniqueSelectedFoods.length >= targetUniqueFoods;
      final dbUtilizationSuccess = (uniqueSelectedFoods.length / allFoods.length) >= targetDbUtilization;
      
      print('\n📈 VALUTAZIONE:');
      print('   ${varietySuccess ? '✅' : '❌'} Varietà: ${varietySuccess ? 'OBIETTIVO RAGGIUNTO' : 'DA MIGLIORARE'}');
      print('   ${uniqueFoodsSuccess ? '✅' : '❌'} Alimenti unici: ${uniqueFoodsSuccess ? 'OBIETTIVO RAGGIUNTO' : 'DA MIGLIORARE'}');
      print('   ${dbUtilizationSuccess ? '✅' : '❌'} Utilizzo database: ${dbUtilizationSuccess ? 'OBIETTIVO RAGGIUNTO' : 'DA MIGLIORARE'}');
      
      final overallSuccess = varietySuccess && uniqueFoodsSuccess && dbUtilizationSuccess;
      
      print('\n' + '=' * 40);
      if (overallSuccess) {
        print('🎉 SUCCESSO COMPLETO!');
        print('L\'espansione del database ha risolto il problema della varietà!');
        print('Gli utenti vedranno piani dietetici significativamente più vari.');
      } else {
        print('⚠️ MIGLIORAMENTI PARZIALI');
        print('L\'espansione ha migliorato la situazione ma serve ulteriore ottimizzazione.');
        
        if (!varietySuccess) {
          print('💡 Suggerimento: Aggiungere più alimenti o migliorare l\'algoritmo di selezione');
        }
        if (!uniqueFoodsSuccess) {
          print('💡 Suggerimento: Espandere ulteriormente il database');
        }
        if (!dbUtilizationSuccess) {
          print('💡 Suggerimento: Verificare i filtri di sicurezza');
        }
      }
      
      // Mostra alcuni alimenti utilizzati
      print('\n🍽️ ESEMPI ALIMENTI UTILIZZATI:');
      final sortedFoods = uniqueSelectedFoods.toList()..sort();
      for (int i = 0; i < (sortedFoods.length < 10 ? sortedFoods.length : 10); i++) {
        print('   ${i + 1}. ${sortedFoods[i]}');
      }
      
    } else {
      print('❌ Nessun piano generato');
    }
    
  } catch (e, stackTrace) {
    print('\n❌ ERRORE: $e');
    print('Stack trace: $stackTrace');
  }
}

String _getMealName(String mealType) {
  switch (mealType) {
    case 'breakfast': return 'Colazione';
    case 'lunch': return 'Pranzo';
    case 'dinner': return 'Cena';
    case 'snack': return 'Spuntino';
    default: return mealType;
  }
}
