import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../theme/dr_staffilano_theme.dart';
import '../../models/community_post.dart';

/// Opzioni aggiuntive per i post
class PostOptions {
  final PostPrivacy privacy;
  final DateTime? scheduledTime;
  final bool hasPoll;
  final String? backgroundColor;
  final Map<String, dynamic> advancedSettings;

  PostOptions({
    this.privacy = PostPrivacy.public,
    this.scheduledTime,
    this.hasPoll = false,
    this.backgroundColor,
    this.advancedSettings = const {},
  });

  PostOptions copyWith({
    PostPrivacy? privacy,
    DateTime? scheduledTime,
    bool? hasPoll,
    String? backgroundColor,
    Map<String, dynamic>? advancedSettings,
  }) {
    return PostOptions(
      privacy: privacy ?? this.privacy,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      hasPoll: hasPoll ?? this.hasPoll,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      advancedSettings: advancedSettings ?? this.advancedSettings,
    );
  }
}

/// Modal per le opzioni aggiuntive del post
class MoreOptionsModal extends StatefulWidget {
  final PostOptions currentOptions;
  final Function(PostOptions) onOptionsChanged;

  const MoreOptionsModal({
    Key? key,
    required this.currentOptions,
    required this.onOptionsChanged,
  }) : super(key: key);

  static Future<void> show(
    BuildContext context, {
    required PostOptions currentOptions,
    required Function(PostOptions) onOptionsChanged,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: MoreOptionsModal(
          currentOptions: currentOptions,
          onOptionsChanged: onOptionsChanged,
        ),
      ),
    );
  }

  @override
  State<MoreOptionsModal> createState() => _MoreOptionsModalState();
}

class _MoreOptionsModalState extends State<MoreOptionsModal> {
  late PostOptions _options;

  final List<Map<String, dynamic>> _privacyOptions = [
    {
      'privacy': PostPrivacy.public,
      'title': 'Pubblico',
      'subtitle': 'Tutti possono vedere questo post',
      'icon': FontAwesomeIcons.globe,
      'color': DrStaffilanoTheme.primaryGreen,
    },
    {
      'privacy': PostPrivacy.friends,
      'title': 'Amici',
      'subtitle': 'Solo i tuoi amici possono vedere',
      'icon': FontAwesomeIcons.userGroup,
      'color': DrStaffilanoTheme.professionalBlue,
    },
    {
      'privacy': PostPrivacy.private,
      'title': 'Solo io',
      'subtitle': 'Solo tu puoi vedere questo post',
      'icon': FontAwesomeIcons.lock,
      'color': DrStaffilanoTheme.accentGold,
    },
  ];

  final List<Color> _backgroundColors = [
    Colors.transparent,
    DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
    DrStaffilanoTheme.professionalBlue.withOpacity(0.1),
    DrStaffilanoTheme.accentGold.withOpacity(0.1),
    Colors.red.withOpacity(0.1),
    Colors.purple.withOpacity(0.1),
    Colors.orange.withOpacity(0.1),
    Colors.pink.withOpacity(0.1),
  ];

  @override
  void initState() {
    super.initState();
    _options = widget.currentOptions;
  }

  void _updateOptions(PostOptions newOptions) {
    setState(() {
      _options = newOptions;
    });
  }

  void _confirmOptions() {
    HapticFeedback.mediumImpact();
    widget.onOptionsChanged(_options);
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final modalWidth = screenSize.width > 600 ? 500.0 : screenSize.width * 0.85;
    final modalHeight = screenSize.height * 0.7;

    return Container(
      width: modalWidth,
      height: modalHeight,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildPrivacySection(),
                  _buildScheduleSection(),
                  _buildPollSection(),
                  _buildBackgroundSection(),
                  _buildAdvancedSection(),
                ],
              ),
            ),
          ),
          _buildFooter(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.withOpacity(0.2)),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.more_horiz,
            color: DrStaffilanoTheme.professionalBlue,
            size: 20,
          ),
          const SizedBox(width: 12),
          const Text(
            'Opzioni post',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close),
            style: IconButton.styleFrom(
              backgroundColor: Colors.grey.withOpacity(0.1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrivacySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.all(16),
          child: Text(
            'Privacy',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        ..._privacyOptions.map((option) {
          final isSelected = _options.privacy == option['privacy'];
          return ListTile(
            leading: Icon(
              option['icon'],
              color: isSelected ? option['color'] : Colors.grey,
              size: 20,
            ),
            title: Text(
              option['title'],
              style: TextStyle(
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
            subtitle: Text(option['subtitle']),
            trailing: isSelected
                ? Icon(Icons.check, color: option['color'])
                : null,
            onTap: () {
              HapticFeedback.lightImpact();
              _updateOptions(_options.copyWith(privacy: option['privacy']));
            },
          );
        }).toList(),
      ],
    );
  }

  Widget _buildScheduleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.all(16),
          child: Text(
            'Programmazione',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        ListTile(
          leading: Icon(
            Icons.schedule,
            color: _options.scheduledTime != null
                ? DrStaffilanoTheme.professionalBlue
                : Colors.grey,
          ),
          title: Text(
            _options.scheduledTime != null
                ? 'Programmato per ${_formatDateTime(_options.scheduledTime!)}'
                : 'Programma post',
          ),
          subtitle: const Text('Pubblica il post in un momento specifico'),
          trailing: _options.scheduledTime != null
              ? IconButton(
                  onPressed: () {
                    _updateOptions(_options.copyWith(scheduledTime: null));
                  },
                  icon: const Icon(Icons.clear),
                )
              : const Icon(Icons.arrow_forward_ios, size: 16),
          onTap: () => _selectScheduleTime(),
        ),
      ],
    );
  }

  Widget _buildPollSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.all(16),
          child: Text(
            'Interazione',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        SwitchListTile(
          secondary: Icon(
            Icons.poll,
            color: _options.hasPoll
                ? DrStaffilanoTheme.primaryGreen
                : Colors.grey,
          ),
          title: const Text('Aggiungi sondaggio'),
          subtitle: const Text('Permetti agli utenti di votare'),
          value: _options.hasPoll,
          activeColor: DrStaffilanoTheme.primaryGreen,
          onChanged: (value) {
            HapticFeedback.lightImpact();
            _updateOptions(_options.copyWith(hasPoll: value));
          },
        ),
      ],
    );
  }

  Widget _buildBackgroundSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.all(16),
          child: Text(
            'Sfondo',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        Container(
          height: 60,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _backgroundColors.length,
            itemBuilder: (context, index) {
              final color = _backgroundColors[index];
              final isSelected = _options.backgroundColor == color.toString();

              return GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  _updateOptions(_options.copyWith(
                    backgroundColor: color == Colors.transparent
                        ? null
                        : color.toString(),
                  ));
                },
                child: Container(
                  width: 50,
                  height: 50,
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    color: color == Colors.transparent ? Colors.white : color,
                    border: Border.all(
                      color: isSelected
                          ? DrStaffilanoTheme.primaryGreen
                          : Colors.grey.withOpacity(0.3),
                      width: isSelected ? 3 : 1,
                    ),
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: color == Colors.transparent
                      ? const Icon(Icons.format_color_reset, color: Colors.grey)
                      : null,
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAdvancedSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.all(16),
          child: Text(
            'Avanzate',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        ListTile(
          leading: const Icon(Icons.comments_disabled, color: Colors.grey),
          title: const Text('Disabilita commenti'),
          subtitle: const Text('Impedisci agli utenti di commentare'),
          trailing: Switch(
            value: _options.advancedSettings['disableComments'] ?? false,
            activeColor: DrStaffilanoTheme.primaryGreen,
            onChanged: (value) {
              HapticFeedback.lightImpact();
              final newSettings = Map<String, dynamic>.from(_options.advancedSettings);
              newSettings['disableComments'] = value;
              _updateOptions(_options.copyWith(advancedSettings: newSettings));
            },
          ),
        ),
      ],
    );
  }

  Widget _buildFooter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey.withOpacity(0.2)),
        ),
      ),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: _confirmOptions,
          style: ElevatedButton.styleFrom(
            backgroundColor: DrStaffilanoTheme.primaryGreen,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: const Text('Applica opzioni'),
        ),
      ),
    );
  }

  void _selectScheduleTime() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(hours: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );

    if (date != null && mounted) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.now(),
      );

      if (time != null && mounted) {
        final scheduledTime = DateTime(
          date.year,
          date.month,
          date.day,
          time.hour,
          time.minute,
        );
        _updateOptions(_options.copyWith(scheduledTime: scheduledTime));
      }
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} alle ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
