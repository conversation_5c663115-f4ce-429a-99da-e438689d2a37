import 'dart:convert';
import 'meal.dart';

class DailyPlan {
  final String date;
  final List<Meal> meals;
  
  DailyPlan({
    required this.date,
    required this.meals,
  });
  
  DailyPlan copyWith({
    String? date,
    List<Meal>? meals,
  }) {
    return DailyPlan(
      date: date ?? this.date,
      meals: meals ?? this.meals,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'date': date,
      'meals': meals.map((meal) => meal.toJson()).toList(),
    };
  }
  
  factory DailyPlan.fromJson(Map<String, dynamic> json) {
    return DailyPlan(
      date: json['date'],
      meals: (json['meals'] as List)
          .map((mealJson) => Meal.fromJson(mealJson))
          .toList(),
    );
  }
  
  // Calcola le calorie totali per il giorno
  int get totalCalories {
    return meals.fold(0, (sum, meal) => sum + (meal.completato ? meal.calorie : 0));
  }
  
  // Calcola i macronutrienti totali per il giorno
  Map<String, double> get totalMacros {
    double proteine = 0.0;
    double carboidrati = 0.0;
    double grassi = 0.0;
    
    for (var meal in meals) {
      if (meal.completato) {
        proteine += meal.proteine;
        carboidrati += meal.carboidrati;
        grassi += meal.grassi;
      }
    }
    
    return {
      'proteine': proteine,
      'carboidrati': carboidrati,
      'grassi': grassi,
    };
  }
}

class WeeklyMealPlan {
  final String startDate;
  final List<DailyPlan> dailyPlans;
  
  WeeklyMealPlan({
    required this.startDate,
    required this.dailyPlans,
  });
  
  WeeklyMealPlan copyWith({
    String? startDate,
    List<DailyPlan>? dailyPlans,
  }) {
    return WeeklyMealPlan(
      startDate: startDate ?? this.startDate,
      dailyPlans: dailyPlans ?? this.dailyPlans,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'startDate': startDate,
      'dailyPlans': dailyPlans.map((plan) => plan.toJson()).toList(),
    };
  }
  
  factory WeeklyMealPlan.fromJson(Map<String, dynamic> json) {
    return WeeklyMealPlan(
      startDate: json['startDate'],
      dailyPlans: (json['dailyPlans'] as List)
          .map((planJson) => DailyPlan.fromJson(planJson))
          .toList(),
    );
  }
  
  // Ottieni un piano giornaliero per una data specifica
  DailyPlan? getPlanForDate(String date) {
    try {
      return dailyPlans.firstWhere((plan) => plan.date == date);
    } catch (e) {
      return null;
    }
  }
  
  // Aggiorna o aggiungi un piano giornaliero
  WeeklyMealPlan updateDailyPlan(DailyPlan newPlan) {
    final existingIndex = dailyPlans.indexWhere((plan) => plan.date == newPlan.date);
    
    if (existingIndex >= 0) {
      // Aggiorna il piano esistente
      final updatedPlans = List<DailyPlan>.from(dailyPlans);
      updatedPlans[existingIndex] = newPlan;
      return copyWith(dailyPlans: updatedPlans);
    } else {
      // Aggiungi un nuovo piano
      final updatedPlans = List<DailyPlan>.from(dailyPlans)..add(newPlan);
      // Ordina i piani per data
      updatedPlans.sort((a, b) => a.date.compareTo(b.date));
      return copyWith(dailyPlans: updatedPlans);
    }
  }
  
  // Crea un piano settimanale vuoto a partire da una data
  static WeeklyMealPlan createEmptyPlan(DateTime startDate) {
    final dailyPlans = <DailyPlan>[];
    
    for (int i = 0; i < 7; i++) {
      final date = startDate.add(Duration(days: i));
      final dateString = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      
      dailyPlans.add(DailyPlan(
        date: dateString,
        meals: [],
      ));
    }
    
    return WeeklyMealPlan(
      startDate: '${startDate.year}-${startDate.month.toString().padLeft(2, '0')}-${startDate.day.toString().padLeft(2, '0')}',
      dailyPlans: dailyPlans,
    );
  }
}
