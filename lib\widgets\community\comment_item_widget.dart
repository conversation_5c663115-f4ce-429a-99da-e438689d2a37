import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/community_comment.dart';
import '../../services/comment_service.dart';
import '../../theme/dr_staffilano_theme.dart';
import 'animated_comment_like_button.dart';

/// Widget per visualizzare un singolo commento
class CommentItemWidget extends StatelessWidget {
  final CommunityComment comment;
  final VoidCallback? onLike;
  final VoidCallback? onReply;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool showActions;

  const CommentItemWidget({
    super.key,
    required this.comment,
    this.onLike,
    this.onReply,
    this.onEdit,
    this.onDelete,
    this.showActions = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Avatar dell'autore
          _buildAuthorAvatar(),
          const SizedBox(width: 12),

          // Contenuto del commento
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header con nome e timestamp
                _buildCommentHeader(context),
                const SizedBox(height: 4),

                // Contenuto del commento
                _buildCommentContent(context),
                const SizedBox(height: 8),

                // Azioni (like, rispondi, ecc.)
                if (showActions) _buildCommentActions(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Costruisce l'avatar dell'autore
  Widget _buildAuthorAvatar() {
    return Container(
      width: 36,
      height: 36,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: ClipOval(
        child: comment.author.avatarUrl != null
            ? Image.network(
                comment.author.avatarUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildDefaultAvatar(),
              )
            : _buildDefaultAvatar(),
      ),
    );
  }

  /// Costruisce l'avatar di default
  Widget _buildDefaultAvatar() {
    return Container(
      color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
      child: Icon(
        Icons.person,
        color: DrStaffilanoTheme.primaryGreen,
        size: 20,
      ),
    );
  }

  /// Costruisce l'header del commento (nome + timestamp)
  Widget _buildCommentHeader(BuildContext context) {
    return Row(
      children: [
        // Nome dell'autore
        Text(
          comment.author.displayName,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
            color: Colors.black87,
          ),
        ),

        // Badge verificato (se applicabile)
        if (comment.author.isVerified) ...[
          const SizedBox(width: 4),
          Icon(
            Icons.verified,
            size: 14,
            color: DrStaffilanoTheme.professionalBlue,
          ),
        ],

        const SizedBox(width: 8),

        // Timestamp
        Text(
          comment.timeAgo,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),

        // Indicatore di modifica
        if (comment.isEdited) ...[
          const SizedBox(width: 4),
          Text(
            '• modificato',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[500],
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ],
    );
  }

  /// Costruisce il contenuto del commento
  Widget _buildCommentContent(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Text(
        comment.content,
        style: const TextStyle(
          fontSize: 14,
          color: Colors.black87,
          height: 1.4,
        ),
      ),
    );
  }

  /// Costruisce le azioni del commento
  Widget _buildCommentActions(BuildContext context) {
    return Row(
      children: [
        // Like con animazione
        AnimatedCommentLikeButton(
          comment: comment,
          onLikeChanged: () {
            // Callback per aggiornamenti futuri se necessario
          },
        ),

        const SizedBox(width: 16),

        // Rispondi
        _buildActionButton(
          icon: Icons.reply,
          label: 'Rispondi',
          color: Colors.grey[600]!,
          onTap: onReply,
        ),

        const Spacer(),

        // Menu azioni (modifica, elimina)
        _buildMoreActionsMenu(context),
      ],
    );
  }

  /// Costruisce un pulsante di azione
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: color,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Costruisce il menu delle azioni aggiuntive
  Widget _buildMoreActionsMenu(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Icon(
        Icons.more_horiz,
        size: 16,
        color: Colors.grey[600],
      ),
      iconSize: 16,
      padding: EdgeInsets.zero,
      itemBuilder: (context) => [
        if (onEdit != null)
          PopupMenuItem<String>(
            value: 'edit',
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.edit,
                  size: 16,
                  color: DrStaffilanoTheme.professionalBlue,
                ),
                const SizedBox(width: 8),
                const Text('Modifica'),
              ],
            ),
          ),
        if (onDelete != null)
          PopupMenuItem<String>(
            value: 'delete',
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.delete,
                  size: 16,
                  color: Colors.red,
                ),
                const SizedBox(width: 8),
                const Text('Elimina'),
              ],
            ),
          ),
      ],
      onSelected: (value) {
        switch (value) {
          case 'edit':
            onEdit?.call();
            break;
          case 'delete':
            onDelete?.call();
            break;
        }
      },
    );
  }
}

/// Widget per lo stato vuoto dei commenti
class EmptyCommentsWidget extends StatelessWidget {
  const EmptyCommentsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.chat_bubble_outline,
                size: 40,
                color: DrStaffilanoTheme.primaryGreen.withOpacity(0.6),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Nessun commento ancora',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Sii il primo a commentare!',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
