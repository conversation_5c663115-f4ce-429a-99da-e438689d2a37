import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';
import '../models/supabase_models.dart';
import '../models/ultra_detailed_profile.dart';
import '../models/diet_plan.dart';
import '../models/meal.dart';

/// Servizio principale per le operazioni database con Supabase
class SupabaseDatabaseService {
  static final SupabaseDatabaseService _instance = SupabaseDatabaseService._internal();
  factory SupabaseDatabaseService() => _instance;
  SupabaseDatabaseService._internal();

  final SupabaseClient _client = SupabaseConfig.client;
  
  String? get _currentUserId => _client.auth.currentUser?.id;
  bool get _isLoggedIn => _currentUserId != null;

  // ==================== PROFILI UTENTE ====================

  /// Salva/aggiorna i dati utente completi
  Future<bool> saveDatiUtente({
    required String obiettivo,
    required String sesso,
    required int eta,
    required int altezzaCm,
    required double pesoKg,
    required String livelloAttivita,
    List<String>? allergieAlimentari,
    List<String>? preferenzeDietetiche,
    UltraDetailedProfile? profiloUltraDettagliato,
  }) async {
    try {
      if (!_isLoggedIn) return false;

      final data = {
        'obiettivo': obiettivo,
        'sesso': sesso,
        'eta': eta,
        'altezza_cm': altezzaCm,
        'peso_kg': pesoKg,
        'livello_attivita': livelloAttivita,
        'allergie_alimentari': allergieAlimentari,
        'preferenze_dietetiche': preferenzeDietetiche,
        'profilo_ultra_dettagliato': profiloUltraDettagliato?.toMap(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      await _client
          .from('dati_utente')
          .upsert(data..['id'] = _currentUserId!);

      print('✅ Dati utente salvati con successo');
      return true;
    } catch (e) {
      print('❌ Errore salvataggio dati utente: $e');
      return false;
    }
  }

  /// Carica i dati utente
  Future<SupabaseDatiUtente?> loadDatiUtente() async {
    try {
      if (!_isLoggedIn) return null;

      final response = await _client
          .from('dati_utente')
          .select()
          .eq('id', _currentUserId!)
          .maybeSingle();

      if (response == null) return null;

      return SupabaseDatiUtente.fromJson(response);
    } catch (e) {
      print('❌ Errore caricamento dati utente: $e');
      return null;
    }
  }

  // ==================== PIANI DIETETICI ====================

  /// Salva un piano dietetico
  Future<int?> savePianoDietetico({
    required String nome,
    String? descrizione,
    required DailyDietPlan pianoCompleto,
    required int calorieTarget,
    required int durataGiorni,
    required String tipoPiano,
    bool isAttivo = false,
  }) async {
    try {
      if (!_isLoggedIn) return null;

      final data = {
        'user_id': _currentUserId!,
        'nome': nome,
        'descrizione': descrizione,
        'piano_completo': pianoCompleto.toMap(),
        'calorie_target': calorieTarget,
        'durata_giorni': durataGiorni,
        'tipo_piano': tipoPiano,
        'is_attivo': isAttivo,
        'created_at': DateTime.now().toIso8601String(),
      };

      final response = await _client
          .from('piani_dietetici')
          .insert(data)
          .select('id')
          .single();

      print('✅ Piano dietetico salvato con ID: ${response['id']}');
      return response['id'] as int;
    } catch (e) {
      print('❌ Errore salvataggio piano dietetico: $e');
      return null;
    }
  }

  /// Carica tutti i piani dietetici dell'utente
  Future<List<SupabasePianoDietetico>> loadPianiDietetici() async {
    try {
      if (!_isLoggedIn) return [];

      final response = await _client
          .from('piani_dietetici')
          .select()
          .eq('user_id', _currentUserId!)
          .order('created_at', ascending: false);

      return response
          .map((json) => SupabasePianoDietetico.fromJson(json))
          .toList();
    } catch (e) {
      print('❌ Errore caricamento piani dietetici: $e');
      return [];
    }
  }

  /// Attiva un piano dietetico (disattiva gli altri)
  Future<bool> attivaPianoDietetico(int pianoId) async {
    try {
      if (!_isLoggedIn) return false;

      // Disattiva tutti i piani
      await _client
          .from('piani_dietetici')
          .update({'is_attivo': false})
          .eq('user_id', _currentUserId!);

      // Attiva il piano selezionato
      await _client
          .from('piani_dietetici')
          .update({'is_attivo': true})
          .eq('id', pianoId);

      print('✅ Piano dietetico $pianoId attivato');
      return true;
    } catch (e) {
      print('❌ Errore attivazione piano dietetico: $e');
      return false;
    }
  }

  /// Elimina un piano dietetico
  Future<bool> deletePianoDietetico(int pianoId) async {
    try {
      if (!_isLoggedIn) return false;

      await _client
          .from('piani_dietetici')
          .delete()
          .eq('id', pianoId)
          .eq('user_id', _currentUserId!);

      print('✅ Piano dietetico $pianoId eliminato');
      return true;
    } catch (e) {
      print('❌ Errore eliminazione piano dietetico: $e');
      return false;
    }
  }

  // ==================== DIARIO ALIMENTARE ====================

  /// Salva un pasto nel diario
  Future<bool> savePastoInDiario({
    required DateTime data,
    required String nomePasto,
    required Meal meal,
  }) async {
    try {
      if (!_isLoggedIn) return false;

      final alimenti = {
        'nome': meal.nome,
        'calorie': meal.calorie,
        'proteine': meal.proteine,
        'carboidrati': meal.carboidrati,
        'grassi': meal.grassi,
        'fibre': meal.fibre,
        'zuccheri': meal.zuccheri,
        'sodio': meal.sodio,
        'ingredienti': meal.ingredienti,
      };

      final data_pasto = {
        'user_id': _currentUserId!,
        'data': data.toIso8601String().split('T')[0], // Solo la data
        'nome_pasto': nomePasto,
        'alimenti': alimenti,
        'calorie_totali': meal.calorie,
        'proteine_g': meal.proteine,
        'carboidrati_g': meal.carboidrati,
        'grassi_g': meal.grassi,
        'completato': meal.completato,
        'created_at': DateTime.now().toIso8601String(),
      };

      await _client.from('diari_alimentari').insert(data_pasto);

      print('✅ Pasto salvato nel diario');
      return true;
    } catch (e) {
      print('❌ Errore salvataggio pasto: $e');
      return false;
    }
  }

  /// Carica i pasti di una data specifica
  Future<List<SupabaseDiarioAlimentare>> loadPastiGiorno(DateTime data) async {
    try {
      if (!_isLoggedIn) return [];

      final dataString = data.toIso8601String().split('T')[0];

      final response = await _client
          .from('diari_alimentari')
          .select()
          .eq('user_id', _currentUserId!)
          .eq('data', dataString)
          .order('created_at', ascending: true);

      return response
          .map((json) => SupabaseDiarioAlimentare.fromJson(json))
          .toList();
    } catch (e) {
      print('❌ Errore caricamento pasti: $e');
      return [];
    }
  }

  /// Aggiorna lo stato di completamento di un pasto
  Future<bool> updateCompletamentoPasto(int pastoId, bool completato) async {
    try {
      if (!_isLoggedIn) return false;

      await _client
          .from('diari_alimentari')
          .update({'completato': completato})
          .eq('id', pastoId)
          .eq('user_id', _currentUserId!);

      return true;
    } catch (e) {
      print('❌ Errore aggiornamento completamento pasto: $e');
      return false;
    }
  }

  /// Elimina un pasto dal diario
  Future<bool> deletePastoDiario(int pastoId) async {
    try {
      if (!_isLoggedIn) return false;

      await _client
          .from('diari_alimentari')
          .delete()
          .eq('id', pastoId)
          .eq('user_id', _currentUserId!);

      return true;
    } catch (e) {
      print('❌ Errore eliminazione pasto: $e');
      return false;
    }
  }

  // ==================== PROGRESSI UTENTE ====================

  /// Salva un progresso utente
  Future<bool> saveProgressoUtente({
    required DateTime data,
    double? pesoKg,
    double? grassoCorporeoPercentuale,
    double? massaMagraKg,
    Map<String, dynamic>? circonferenze,
    String? fotoProgressoUrl,
    String? note,
  }) async {
    try {
      if (!_isLoggedIn) return false;

      final progressoData = {
        'user_id': _currentUserId!,
        'data': data.toIso8601String().split('T')[0],
        'peso_kg': pesoKg,
        'grasso_corporeo_percentuale': grassoCorporeoPercentuale,
        'massa_magra_kg': massaMagraKg,
        'circonferenze': circonferenze,
        'foto_progresso_url': fotoProgressoUrl,
        'note': note,
        'created_at': DateTime.now().toIso8601String(),
      };

      await _client.from('progressi_utente').insert(progressoData);

      print('✅ Progresso salvato');
      return true;
    } catch (e) {
      print('❌ Errore salvataggio progresso: $e');
      return false;
    }
  }

  /// Carica tutti i progressi dell'utente
  Future<List<SupabaseProgressoUtente>> loadProgressiUtente({
    DateTime? dataInizio,
    DateTime? dataFine,
  }) async {
    try {
      if (!_isLoggedIn) return [];

      var query = _client
          .from('progressi_utente')
          .select()
          .eq('user_id', _currentUserId!);

      if (dataInizio != null) {
        query = query.gte('data', dataInizio.toIso8601String().split('T')[0]);
      }

      if (dataFine != null) {
        query = query.lte('data', dataFine.toIso8601String().split('T')[0]);
      }

      final response = await query.order('data', ascending: false);

      return response
          .map((json) => SupabaseProgressoUtente.fromJson(json))
          .toList();
    } catch (e) {
      print('❌ Errore caricamento progressi: $e');
      return [];
    }
  }

  // ==================== STATISTICHE ====================

  /// Ottieni statistiche complete dell'utente
  Future<Map<String, dynamic>> getStatisticheUtente() async {
    try {
      if (!_isLoggedIn) return {};

      // Conta i piani dietetici
      final pianiCount = await _client
          .from('piani_dietetici')
          .select('id', const FetchOptions(count: CountOption.exact))
          .eq('user_id', _currentUserId!);

      // Conta i pasti registrati
      final pastiCount = await _client
          .from('diari_alimentari')
          .select('id', const FetchOptions(count: CountOption.exact))
          .eq('user_id', _currentUserId!);

      // Conta i progressi registrati
      final progressiCount = await _client
          .from('progressi_utente')
          .select('id', const FetchOptions(count: CountOption.exact))
          .eq('user_id', _currentUserId!);

      // Ultimo peso registrato
      final ultimoPeso = await _client
          .from('progressi_utente')
          .select('peso_kg, data')
          .eq('user_id', _currentUserId!)
          .not('peso_kg', 'is', null)
          .order('data', ascending: false)
          .limit(1)
          .maybeSingle();

      return {
        'piani_dietetici_count': pianiCount.count ?? 0,
        'pasti_registrati_count': pastiCount.count ?? 0,
        'progressi_count': progressiCount.count ?? 0,
        'ultimo_peso': ultimoPeso?['peso_kg'],
        'data_ultimo_peso': ultimoPeso?['data'],
      };
    } catch (e) {
      print('❌ Errore caricamento statistiche: $e');
      return {};
    }
  }

  // ==================== UTILITY ====================

  /// Sincronizza i dati offline con Supabase
  Future<bool> syncOfflineData() async {
    try {
      // Qui implementeresti la logica per sincronizzare
      // i dati salvati offline con Supabase
      print('🔄 Sincronizzazione dati offline...');
      
      // TODO: Implementare logica di sincronizzazione
      
      return true;
    } catch (e) {
      print('❌ Errore sincronizzazione: $e');
      return false;
    }
  }

  /// Esporta tutti i dati dell'utente
  Future<Map<String, dynamic>?> exportUserData() async {
    try {
      if (!_isLoggedIn) return null;

      final datiUtente = await loadDatiUtente();
      final piani = await loadPianiDietetici();
      final progressi = await loadProgressiUtente();
      
      return {
        'dati_utente': datiUtente?.toJson(),
        'piani_dietetici': piani.map((p) => p.toJson()).toList(),
        'progressi': progressi.map((p) => p.toJson()).toList(),
        'export_date': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('❌ Errore export dati: $e');
      return null;
    }
  }
}
