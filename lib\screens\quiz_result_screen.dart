import 'package:flutter/material.dart';
import 'package:confetti/confetti.dart';
import '../models/quiz_models.dart';
import '../theme/dr_staffilano_theme.dart';
import '../widgets/dr_staffilano_logo.dart';
import '../widgets/quiz_points_feedback_widget.dart';

/// Schermata dei risultati del quiz
class QuizResultScreen extends StatefulWidget {
  final QuizResult result;

  const QuizResultScreen({
    Key? key,
    required this.result,
  }) : super(key: key);

  @override
  State<QuizResultScreen> createState() => _QuizResultScreenState();
}

class _QuizResultScreenState extends State<QuizResultScreen>
    with TickerProviderStateMixin {
  late ConfettiController _confettiController;
  late AnimationController _scoreController;
  late Animation<double> _scoreAnimation;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _confettiController = ConfettiController(duration: const Duration(seconds: 3));

    _scoreController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _scoreAnimation = Tween<double>(
      begin: 0.0,
      end: widget.result.attempt.score.toDouble(),
    ).animate(CurvedAnimation(
      parent: _scoreController,
      curve: Curves.easeOutCubic,
    ));

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));

    // Avvia le animazioni
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 500), () {
      _scoreController.forward();
    });

    // Mostra i coriandoli se il punteggio è alto
    if (widget.result.attempt.score >= 90) {
      Future.delayed(const Duration(milliseconds: 1500), () {
        _confettiController.play();
      });
    }
  }

  @override
  void dispose() {
    _confettiController.dispose();
    _scoreController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DrStaffilanoTheme.backgroundLight,
      appBar: _buildAppBar(),
      body: Stack(
        children: [
          _buildContent(),
          Align(
            alignment: Alignment.topCenter,
            child: ConfettiWidget(
              confettiController: _confettiController,
              blastDirectionality: BlastDirectionality.explosive,
              shouldLoop: false,
              colors: [
                DrStaffilanoTheme.primaryGreen,
                DrStaffilanoTheme.secondaryBlue,
                DrStaffilanoTheme.accentGold,
                Colors.red,
                Colors.purple,
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomActions(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Row(
        children: [
          const DrStaffilanoLogo.small(),
          const SizedBox(width: 12),
          const Text(
            'Risultati Quiz',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ),
      backgroundColor: widget.result.performanceLevel.color,
      elevation: 0,
    );
  }

  Widget _buildContent() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            _buildScoreCard(),
            const SizedBox(height: 20),

            // Feedback punti guadagnati
            QuizPointsFeedbackWidget(
              pointsEarned: _calculateTotalPoints(),
              bonusPoints: widget.result.bonusPoints,
              percentage: widget.result.attempt.score,
              performanceLevel: widget.result.performanceLevel.displayName,
              onAnimationComplete: () {
                // Opzionale: azioni dopo l'animazione
                print('🎉 Animazione feedback completata');
              },
            ),
            const SizedBox(height: 20),

            _buildPerformanceCard(),
            const SizedBox(height: 20),
            _buildDrStaffilanoFeedback(),
            const SizedBox(height: 20),
            _buildQuestionReview(),
            const SizedBox(height: 100), // Spazio per i pulsanti in basso
          ],
        ),
      ),
    );
  }

  Widget _buildScoreCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            widget.result.performanceLevel.color,
            widget.result.performanceLevel.color.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: widget.result.performanceLevel.color.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            widget.result.attempt.isPassed ? Icons.check_circle : Icons.info,
            color: Colors.white,
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            widget.result.attempt.isPassed ? 'Quiz Superato!' : 'Quiz Non Superato',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.result.performanceLevel.displayName,
            style: TextStyle(
              fontSize: 16,
              color: Colors.white.withOpacity(0.9),
            ),
          ),
          const SizedBox(height: 24),
          AnimatedBuilder(
            animation: _scoreAnimation,
            builder: (context, child) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  Text(
                    _scoreAnimation.value.round().toString(),
                    style: const TextStyle(
                      fontSize: 72,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const Text(
                    '%',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: 8),
          Text(
            '${widget.result.questionResults.where((r) => r.isCorrect).length} di ${widget.result.quiz.questions.length} risposte corrette',
            style: TextStyle(
              fontSize: 14,
              color: Colors.white.withOpacity(0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Dettagli Performance',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: DrStaffilanoTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          _buildStatRow('Punteggio Minimo', '${widget.result.quiz.passingScore}%'),
          _buildStatRow('Il Tuo Punteggio', '${widget.result.attempt.score}%'),
          _buildStatRow('Tempo Impiegato', _formatDuration(widget.result.attempt.timeSpent)),
          _buildStatRow('Tentativo Numero', '${widget.result.attempt.attemptNumber}'),
          if (widget.result.bonusPoints > 0)
            _buildStatRow('Punti Bonus', '+${widget.result.bonusPoints}', isBonus: true),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value, {bool isBonus = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: DrStaffilanoTheme.textSecondary,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: isBonus ? DrStaffilanoTheme.accentGold : DrStaffilanoTheme.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrStaffilanoFeedback() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.secondaryBlue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: DrStaffilanoTheme.secondaryBlue.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: DrStaffilanoTheme.secondaryBlue,
                child: const Icon(
                  Icons.person,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Feedback del Dr. Staffilano',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: DrStaffilanoTheme.secondaryBlue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            widget.result.drStaffilanoFeedback,
            style: const TextStyle(
              fontSize: 16,
              height: 1.5,
              color: DrStaffilanoTheme.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionReview() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Revisione Domande',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: DrStaffilanoTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          ...widget.result.questionResults.asMap().entries.map((entry) {
            final index = entry.key;
            final questionResult = entry.value;
            return _buildQuestionResultItem(index + 1, questionResult);
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildQuestionResultItem(int questionNumber, QuestionResult result) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: result.isCorrect ? Colors.green.shade50 : Colors.red.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: result.isCorrect ? Colors.green : Colors.red,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: result.isCorrect ? Colors.green : Colors.red,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    questionNumber.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Icon(
                result.isCorrect ? Icons.check_circle : Icons.cancel,
                color: result.isCorrect ? Colors.green : Colors.red,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  result.isCorrect ? 'Risposta Corretta' : 'Risposta Errata',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: result.isCorrect ? Colors.green.shade700 : Colors.red.shade700,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            result.question.question,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: DrStaffilanoTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tua risposta: ${result.question.options[result.userAnswerIndex].text}',
            style: TextStyle(
              fontSize: 13,
              color: result.isCorrect ? Colors.green.shade700 : Colors.red.shade700,
            ),
          ),
          if (!result.isCorrect) ...[
            const SizedBox(height: 4),
            Text(
              'Risposta corretta: ${result.question.correctOption.text}',
              style: TextStyle(
                fontSize: 13,
                color: Colors.green.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (!widget.result.attempt.isPassed) ...[
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _retakeQuiz,
                icon: const Icon(Icons.refresh),
                label: const Text('Ripeti Quiz'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: DrStaffilanoTheme.primaryGreen,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
            const SizedBox(height: 12),
          ],
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _backToModule,
              icon: const Icon(Icons.arrow_back),
              label: const Text('Torna al Modulo'),
              style: OutlinedButton.styleFrom(
                foregroundColor: DrStaffilanoTheme.primaryGreen,
                side: BorderSide(color: DrStaffilanoTheme.primaryGreen),
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes}m ${seconds}s';
  }

  /// Calcola i punti totali guadagnati dal quiz
  int _calculateTotalPoints() {
    final score = widget.result.attempt.score;
    int basePoints = 0;

    if (score >= 95) {
      basePoints = 150; // Punteggio perfetto/quasi perfetto
    } else if (score >= 85) {
      basePoints = 125; // Punteggio eccellente
    } else if (score >= 75) {
      basePoints = 100; // Punteggio molto buono
    } else if (score >= 65) {
      basePoints = 75;  // Punteggio buono
    } else if (score >= 50) {
      basePoints = 50;  // Punteggio sufficiente
    } else {
      basePoints = 25;  // Punteggio minimo per il tentativo
    }

    return basePoints + widget.result.bonusPoints;
  }

  void _retakeQuiz() {
    Navigator.pop(context);
    Navigator.pop(context); // Torna alla schermata del modulo
    // Il modulo dovrebbe permettere di rifare il quiz
  }

  void _backToModule() {
    Navigator.pop(context);
    Navigator.pop(context); // Torna alla schermata del modulo
  }
}
