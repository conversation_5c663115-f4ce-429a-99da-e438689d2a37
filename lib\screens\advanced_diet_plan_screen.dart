import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import '../controllers/advanced_diet_controller.dart';
import '../models/diet_plan.dart';
import '../models/food.dart';
import '../theme/app_theme.dart';

/// Schermata per visualizzare un piano dietetico avanzato
class AdvancedDietPlanScreen extends StatefulWidget {
  final WeeklyDietPlan? weeklyPlan;
  
  const AdvancedDietPlanScreen({
    Key? key,
    this.weeklyPlan,
  }) : super(key: key);

  @override
  State<AdvancedDietPlanScreen> createState() => _AdvancedDietPlanScreenState();
}

class _AdvancedDietPlanScreenState extends State<AdvancedDietPlanScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _selectedDayIndex = 0;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 7, vsync: this);
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        setState(() {
          _selectedDayIndex = _tabController.index;
        });
      }
    });
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Piano Dietetico Avanzato'),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: _buildDayTabs(),
        ),
      ),
      body: Consumer<AdvancedDietController>(
        builder: (context, controller, child) {
          final weeklyPlan = widget.weeklyPlan ?? controller.weeklyPlan;
          
          if (weeklyPlan == null) {
            return const Center(
              child: Text('Nessun piano dietetico disponibile'),
            );
          }
          
          if (_selectedDayIndex >= weeklyPlan.dailyPlans.length) {
            return const Center(
              child: Text('Giorno non disponibile'),
            );
          }
          
          final dailyPlan = weeklyPlan.dailyPlans[_selectedDayIndex];
          
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Riepilogo giornaliero
                _buildDailySummaryCard(dailyPlan),
                const SizedBox(height: 16.0),
                
                // Grafico macronutrienti
                _buildMacronutrientsChart(dailyPlan),
                const SizedBox(height: 24.0),
                
                // Pasti
                ...dailyPlan.meals.map((meal) => _buildMealCard(meal)),
              ],
            ),
          );
        },
      ),
    );
  }
  
  /// Costruisce le tab per i giorni della settimana
  List<Widget> _buildDayTabs() {
    final daysOfWeek = ['Lunedì', 'Martedì', 'Mercoledì', 'Giovedì', 'Venerdì', 'Sabato', 'Domenica'];
    
    return List.generate(7, (index) {
      return Tab(
        text: daysOfWeek[index],
      );
    });
  }
  
  /// Costruisce la card di riepilogo giornaliero
  Widget _buildDailySummaryCard(DailyDietPlan dailyPlan) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Riepilogo Giornaliero',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16.0),
            
            // Calorie
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Calorie totali:'),
                Text(
                  '${dailyPlan.getTotalCalories()} / ${dailyPlan.calorieTarget} kcal',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8.0),
            
            // Barra di progresso calorie
            LinearProgressIndicator(
              value: dailyPlan.getTotalCalories() / dailyPlan.calorieTarget,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                dailyPlan.getTotalCalories() > dailyPlan.calorieTarget * 1.05
                    ? Colors.red
                    : dailyPlan.getTotalCalories() < dailyPlan.calorieTarget * 0.95
                        ? Colors.orange
                        : Colors.green,
              ),
            ),
            const SizedBox(height: 16.0),
            
            // Macronutrienti
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Proteine:'),
                Text(
                  '${dailyPlan.getTotalMacro('proteins').round()} / ${dailyPlan.macroTargets['proteins']} g',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8.0),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Carboidrati:'),
                Text(
                  '${dailyPlan.getTotalMacro('carbs').round()} / ${dailyPlan.macroTargets['carbs']} g',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8.0),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Grassi:'),
                Text(
                  '${dailyPlan.getTotalMacro('fats').round()} / ${dailyPlan.macroTargets['fats']} g',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8.0),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Fibre:'),
                Text(
                  '${dailyPlan.getTotalMacro('fiber').round()} g',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16.0),
            
            // Precisione del piano
            _buildPlanAccuracyIndicator(dailyPlan),
          ],
        ),
      ),
    );
  }
  
  /// Costruisce l'indicatore di precisione del piano
  Widget _buildPlanAccuracyIndicator(DailyDietPlan dailyPlan) {
    // Calcola la deviazione percentuale per calorie e macronutrienti
    final calorieDeviation = ((dailyPlan.getTotalCalories() - dailyPlan.calorieTarget) / dailyPlan.calorieTarget).abs() * 100;
    
    final proteinDeviation = ((dailyPlan.getTotalMacro('proteins') - dailyPlan.macroTargets['proteins']!) / dailyPlan.macroTargets['proteins']!).abs() * 100;
    final carbDeviation = ((dailyPlan.getTotalMacro('carbs') - dailyPlan.macroTargets['carbs']!) / dailyPlan.macroTargets['carbs']!).abs() * 100;
    final fatDeviation = ((dailyPlan.getTotalMacro('fats') - dailyPlan.macroTargets['fats']!) / dailyPlan.macroTargets['fats']!).abs() * 100;
    
    // Calcola la precisione media
    final averageDeviation = (calorieDeviation + proteinDeviation + carbDeviation + fatDeviation) / 4;
    
    // Determina il livello di precisione
    String accuracyLevel;
    Color accuracyColor;
    
    if (averageDeviation < 3.0) {
      accuracyLevel = 'Eccellente';
      accuracyColor = Colors.green;
    } else if (averageDeviation < 5.0) {
      accuracyLevel = 'Molto buona';
      accuracyColor = Colors.green.shade300;
    } else if (averageDeviation < 10.0) {
      accuracyLevel = 'Buona';
      accuracyColor = Colors.amber;
    } else if (averageDeviation < 15.0) {
      accuracyLevel = 'Discreta';
      accuracyColor = Colors.orange;
    } else {
      accuracyLevel = 'Migliorabile';
      accuracyColor = Colors.red;
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text('Precisione del piano:'),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: accuracyColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: accuracyColor),
              ),
              child: Text(
                accuracyLevel,
                style: TextStyle(color: accuracyColor, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8.0),
        Text(
          'Deviazione media: ${averageDeviation.toStringAsFixed(1)}%',
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }
  
  /// Costruisce il grafico dei macronutrienti
  Widget _buildMacronutrientsChart(DailyDietPlan dailyPlan) {
    final proteins = dailyPlan.getTotalMacro('proteins');
    final carbs = dailyPlan.getTotalMacro('carbs');
    final fats = dailyPlan.getTotalMacro('fats');
    
    final totalCalories = proteins * 4 + carbs * 4 + fats * 9;
    
    final proteinPercentage = proteins * 4 / totalCalories;
    final carbPercentage = carbs * 4 / totalCalories;
    final fatPercentage = fats * 9 / totalCalories;
    
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Distribuzione Macronutrienti',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16.0),
            
            SizedBox(
              height: 200,
              child: Row(
                children: [
                  // Grafico a torta
                  Expanded(
                    flex: 3,
                    child: PieChart(
                      PieChartData(
                        sections: [
                          PieChartSectionData(
                            value: proteinPercentage,
                            title: '${(proteinPercentage * 100).round()}%',
                            color: Colors.red,
                            radius: 60,
                          ),
                          PieChartSectionData(
                            value: carbPercentage,
                            title: '${(carbPercentage * 100).round()}%',
                            color: Colors.blue,
                            radius: 60,
                          ),
                          PieChartSectionData(
                            value: fatPercentage,
                            title: '${(fatPercentage * 100).round()}%',
                            color: Colors.yellow,
                            radius: 60,
                          ),
                        ],
                        sectionsSpace: 0,
                        centerSpaceRadius: 40,
                      ),
                    ),
                  ),
                  
                  // Legenda
                  Expanded(
                    flex: 2,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildLegendItem('Proteine', Colors.red, '${proteins.round()} g'),
                        const SizedBox(height: 8.0),
                        _buildLegendItem('Carboidrati', Colors.blue, '${carbs.round()} g'),
                        const SizedBox(height: 8.0),
                        _buildLegendItem('Grassi', Colors.yellow, '${fats.round()} g'),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// Costruisce un elemento della legenda
  Widget _buildLegendItem(String label, Color color, String value) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          color: color,
        ),
        const SizedBox(width: 8.0),
        Expanded(
          child: Text(label),
        ),
        Text(
          value,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }
  
  /// Costruisce la card di un pasto
  Widget _buildMealCard(PlannedMeal meal) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Intestazione del pasto
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  meal.name,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  meal.time,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8.0),
            
            // Riepilogo del pasto
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Calorie: ${meal.totalCalories} kcal'),
                Text(
                  'P: ${meal.getMacroValue('proteins').round()} g | C: ${meal.getMacroValue('carbs').round()} g | G: ${meal.getMacroValue('fats').round()} g',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const Divider(),
            
            // Alimenti
            ...meal.foods.map((foodPortion) => _buildFoodPortionItem(foodPortion)),
          ],
        ),
      ),
    );
  }
  
  /// Costruisce un elemento per una porzione di alimento
  Widget _buildFoodPortionItem(FoodPortion foodPortion) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Immagine dell'alimento
          if (foodPortion.food.imageUrl.isNotEmpty)
            ClipRRect(
              borderRadius: BorderRadius.circular(8.0),
              child: Image.network(
                foodPortion.food.imageUrl,
                width: 60,
                height: 60,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 60,
                    height: 60,
                    color: Colors.grey[300],
                    child: const Icon(Icons.restaurant, color: Colors.grey),
                  );
                },
              ),
            )
          else
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: const Icon(Icons.restaurant, color: Colors.grey),
            ),
          const SizedBox(width: 16.0),
          
          // Informazioni sull'alimento
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  foodPortion.food.name,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4.0),
                Text('${foodPortion.grams} g'),
                const SizedBox(height: 4.0),
                Text(
                  '${foodPortion.calories} kcal | P: ${foodPortion.proteins.round()} g | C: ${foodPortion.carbs.round()} g | G: ${foodPortion.fats.round()} g',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
