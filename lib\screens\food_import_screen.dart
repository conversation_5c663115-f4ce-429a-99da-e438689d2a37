import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:convert';
import '../models/food.dart';
import '../services/food_database_service.dart';
import '../services/food_validation_service.dart';
import '../services/food_import_service.dart';
import '../services/translation_service.dart';
import '../theme/app_theme.dart';
import 'translation_manager_screen.dart';

/// Schermata per l'importazione di alimenti da fonti esterne
class FoodImportScreen extends StatefulWidget {
  const FoodImportScreen({super.key});

  @override
  State<FoodImportScreen> createState() => _FoodImportScreenState();
}

class _FoodImportScreenState extends State<FoodImportScreen> {
  late FoodDatabaseService _databaseService;
  late FoodValidationService _validationService;
  late FoodImportService _importService;
  TranslationService? _translationService;

  final TextEditingController _urlController = TextEditingController();

  bool _isLoading = false;
  String _importMethod = 'file'; // 'file', 'url', 'paste'
  String _fileType = 'csv'; // 'csv', 'json'
  String _pastedContent = '';
  bool _autoTranslate = false;

  List<Food> _previewFoods = [];
  bool _showPreview = false;

  // Mappa delle colonne per CSV
  final Map<String, int> _columnMap = {
    'name': 0,
    'calories': 1,
    'proteins': 2,
    'carbs': 3,
    'fats': 4,
    'fiber': 5,
    'sugar': 6,
    'description': 7,
    'source': 8,
  };

  @override
  void initState() {
    super.initState();
    _initServices();
  }

  Future<void> _initServices() async {
    _databaseService = await FoodDatabaseService.getInstance();
    _validationService = FoodValidationService.getInstance();

    try {
      _translationService = await TranslationService.getInstance();
    } catch (e) {
      print('Errore nell\'inizializzazione del servizio di traduzione: $e');
      _translationService = null;
    }

    _importService = FoodImportService(
      databaseService: _databaseService,
      validationService: _validationService,
      translationService: _translationService,
      autoTranslate: _autoTranslate,
    );
  }

  @override
  void dispose() {
    _urlController.dispose();
    super.dispose();
  }

  Future<void> _pickAndImportFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['csv', 'json'],
      );

      if (result == null || result.files.isEmpty) {
        return;
      }

      setState(() {
        _isLoading = true;
        _showPreview = false;
        _previewFoods = [];
      });

      final file = result.files.first;
      final fileExtension = file.extension?.toLowerCase() ?? '';

      if (fileExtension != 'csv' && fileExtension != 'json') {
        _showErrorSnackBar('Formato file non supportato. Usa CSV o JSON.');
        setState(() {
          _isLoading = false;
        });
        return;
      }

      setState(() {
        _fileType = fileExtension;
      });

      String content = '';
      if (file.bytes != null) {
        // Web
        content = String.fromCharCodes(file.bytes!);
      } else if (file.path != null) {
        // Mobile/Desktop
        content = await rootBundle.loadString(file.path!);
      } else {
        _showErrorSnackBar('Impossibile leggere il file.');
        setState(() {
          _isLoading = false;
        });
        return;
      }

      List<Food> importedFoods = [];

      if (_fileType == 'csv') {
        importedFoods = await _importService.importFromCsv(
          csvContent: content,
          columnMap: _columnMap,
        );
      } else {
        importedFoods = await _importService.importFromJson(content);
      }

      setState(() {
        _isLoading = false;
        _showPreview = true;
        _previewFoods = importedFoods;
      });

      _showSuccessSnackBar('${importedFoods.length} alimenti importati con successo. Controlla l\'anteprima.');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('Errore nell\'importazione: $e');
    }
  }

  Future<void> _importFromUrl() async {
    final url = _urlController.text.trim();

    if (url.isEmpty) {
      _showErrorSnackBar('Inserisci un URL valido.');
      return;
    }

    setState(() {
      _isLoading = true;
      _showPreview = false;
      _previewFoods = [];
    });

    try {
      final importedFoods = await _importService.importFromUrl(
        url: url,
        fileType: _fileType,
        columnMap: _fileType == 'csv' ? _columnMap : null,
      );

      setState(() {
        _isLoading = false;
        _showPreview = true;
        _previewFoods = importedFoods;
      });

      _showSuccessSnackBar('${importedFoods.length} alimenti importati con successo. Controlla l\'anteprima.');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('Errore nell\'importazione: $e');
    }
  }

  Future<void> _importFromPastedContent() async {
    if (_pastedContent.isEmpty) {
      _showErrorSnackBar('Incolla il contenuto prima di importare.');
      return;
    }

    setState(() {
      _isLoading = true;
      _showPreview = false;
      _previewFoods = [];
    });

    try {
      List<Food> importedFoods = [];

      if (_fileType == 'csv') {
        importedFoods = await _importService.importFromCsv(
          csvContent: _pastedContent,
          columnMap: _columnMap,
        );
      } else {
        importedFoods = await _importService.importFromJson(_pastedContent);
      }

      setState(() {
        _isLoading = false;
        _showPreview = true;
        _previewFoods = importedFoods;
      });

      _showSuccessSnackBar('${importedFoods.length} alimenti importati con successo. Controlla l\'anteprima.');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('Errore nell\'importazione: $e');
    }
  }

  Future<void> _saveImportedFoods() async {
    if (_previewFoods.isEmpty) {
      _showErrorSnackBar('Nessun alimento da salvare.');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final savedCount = await _importService.saveImportedFoods(_previewFoods);

      setState(() {
        _isLoading = false;
        _showPreview = false;
        _previewFoods = [];
      });

      _showSuccessSnackBar('$savedCount alimenti salvati nel database.');

      // Torna alla schermata precedente
      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('Errore nel salvataggio: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Importa Alimenti'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _showPreview
              ? _buildPreviewList()
              : _buildImportForm(),
      floatingActionButton: _showPreview
          ? FloatingActionButton.extended(
              onPressed: _saveImportedFoods,
              icon: const Icon(Icons.save),
              label: const Text('Salva nel Database'),
            )
          : null,
    );
  }

  Widget _buildImportForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Metodo di importazione
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Metodo di importazione',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildImportMethodSelector(),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Tipo di file
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Tipo di file',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildFileTypeSelector(),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Opzioni di traduzione
          if (_translationService != null)
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Opzioni di traduzione',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text('Traduzione automatica'),
                      subtitle: const Text('Traduce automaticamente i nomi degli alimenti in italiano'),
                      value: _autoTranslate,
                      onChanged: (value) {
                        setState(() {
                          _autoTranslate = value;
                          _importService.autoTranslate = value;
                        });
                      },
                    ),
                    TextButton.icon(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const TranslationManagerScreen(),
                          ),
                        );
                      },
                      icon: const Icon(Icons.settings),
                      label: const Text('Gestisci traduzioni'),
                    ),
                  ],
                ),
              ),
            ),

          const SizedBox(height: 16),

          // Form di importazione specifico per il metodo selezionato
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _importMethod == 'file'
                        ? 'Seleziona file'
                        : _importMethod == 'url'
                            ? 'Importa da URL'
                            : 'Incolla contenuto',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildImportMethodForm(),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Informazioni sul formato
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Formato atteso',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _fileType == 'csv'
                      ? _buildCsvFormatInfo()
                      : _buildJsonFormatInfo(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImportMethodSelector() {
    return Row(
      children: [
        Expanded(
          child: RadioListTile<String>(
            title: const Text('File'),
            value: 'file',
            groupValue: _importMethod,
            onChanged: (value) {
              setState(() {
                _importMethod = value!;
              });
            },
          ),
        ),
        Expanded(
          child: RadioListTile<String>(
            title: const Text('URL'),
            value: 'url',
            groupValue: _importMethod,
            onChanged: (value) {
              setState(() {
                _importMethod = value!;
              });
            },
          ),
        ),
        Expanded(
          child: RadioListTile<String>(
            title: const Text('Incolla'),
            value: 'paste',
            groupValue: _importMethod,
            onChanged: (value) {
              setState(() {
                _importMethod = value!;
              });
            },
          ),
        ),
      ],
    );
  }

  Widget _buildFileTypeSelector() {
    return Row(
      children: [
        Expanded(
          child: RadioListTile<String>(
            title: const Text('CSV'),
            value: 'csv',
            groupValue: _fileType,
            onChanged: (value) {
              setState(() {
                _fileType = value!;
              });
            },
          ),
        ),
        Expanded(
          child: RadioListTile<String>(
            title: const Text('JSON'),
            value: 'json',
            groupValue: _fileType,
            onChanged: (value) {
              setState(() {
                _fileType = value!;
              });
            },
          ),
        ),
      ],
    );
  }

  Widget _buildImportMethodForm() {
    switch (_importMethod) {
      case 'file':
        return ElevatedButton.icon(
          onPressed: _pickAndImportFile,
          icon: const Icon(Icons.file_upload),
          label: Text('Seleziona file ${_fileType.toUpperCase()}'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
          ),
        );
      case 'url':
        return Column(
          children: [
            TextField(
              controller: _urlController,
              decoration: const InputDecoration(
                labelText: 'URL del file',
                hintText: 'https://example.com/foods.csv',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _importFromUrl,
              icon: const Icon(Icons.cloud_download),
              label: const Text('Importa da URL'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
              ),
            ),
          ],
        );
      case 'paste':
        return Column(
          children: [
            TextField(
              maxLines: 10,
              decoration: const InputDecoration(
                labelText: 'Incolla il contenuto',
                hintText: 'Incolla qui il contenuto CSV o JSON...',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _pastedContent = value;
                });
              },
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _importFromPastedContent,
              icon: const Icon(Icons.content_paste),
              label: const Text('Importa contenuto'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
              ),
            ),
          ],
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildCsvFormatInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Il file CSV deve avere le seguenti colonne:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        const Text(
          '1. Nome (obbligatorio)\n'
          '2. Calorie per 100g (obbligatorio)\n'
          '3. Proteine per 100g (obbligatorio)\n'
          '4. Carboidrati per 100g (obbligatorio)\n'
          '5. Grassi per 100g (obbligatorio)\n'
          '6. Fibre per 100g (opzionale)\n'
          '7. Zuccheri per 100g (opzionale)\n'
          '8. Descrizione (opzionale)\n'
          '9. Fonte (opzionale)',
        ),
        const SizedBox(height: 8),
        const Text(
          'Esempio:',
          style: TextStyle(fontStyle: FontStyle.italic),
        ),
        const Text(
          'Mela,52,0.3,14,0.2,2.4,10.4,Mela fresca,USDA',
          style: TextStyle(fontFamily: 'monospace'),
        ),
      ],
    );
  }

  Widget _buildJsonFormatInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Il file JSON deve essere un array di oggetti con la seguente struttura:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Text(
            '[\n'
            '  {\n'
            '    "name": "Mela",\n'
            '    "calories": 52,\n'
            '    "proteins": 0.3,\n'
            '    "carbs": 14,\n'
            '    "fats": 0.2,\n'
            '    "fiber": 2.4,\n'
            '    "sugar": 10.4,\n'
            '    "description": "Mela fresca",\n'
            '    "dataSource": "usda"\n'
            '  },\n'
            '  ...\n'
            ']',
            style: TextStyle(fontFamily: 'monospace'),
          ),
        ),
      ],
    );
  }

  Widget _buildPreviewList() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              const Icon(Icons.info_outline, color: Colors.blue),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Anteprima di ${_previewFoods.length} alimenti. Controlla i dati e premi "Salva nel Database" per completare l\'importazione.',
                  style: const TextStyle(color: Colors.blue),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: _previewFoods.length,
            itemBuilder: (context, index) {
              final food = _previewFoods[index];
              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                child: ListTile(
                  title: Text(food.name),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('${food.calories} kcal | P: ${food.proteins.toStringAsFixed(1)}g | C: ${food.carbs.toStringAsFixed(1)}g | G: ${food.fats.toStringAsFixed(1)}g'),
                      Text('Fonte: ${food.dataSource.toString().split('.').last}'),
                    ],
                  ),
                  isThreeLine: true,
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
