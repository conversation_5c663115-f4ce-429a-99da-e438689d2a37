import 'dart:io';
import '../interfaces/food_oracle_interface.dart';
import '../implementations/food_oracle_implementation.dart';
import '../models/food_oracle_models.dart';
import '../../models/user_profile.dart';
import '../../services/food_database_service.dart';
import '../../services/storage_service.dart';
import '../../constants/app_constants.dart';

/// Servizio principale per il Food Oracle
class FoodOracleService {
  static final FoodOracleService _instance = FoodOracleService._internal();
  
  /// Implementazione dell'interfaccia Food Oracle
  late FoodOracleInterface _foodOracleImplementation;
  
  /// Flag di inizializzazione
  bool _isInitialized = false;

  factory FoodOracleService() {
    return _instance;
  }

  FoodOracleService._internal();

  /// Ottiene l'istanza singleton del servizio
  static Future<FoodOracleService> getInstance() async {
    if (!_instance._isInitialized) {
      await _instance._initialize();
    }
    return _instance;
  }

  /// Inizializza il servizio
  Future<void> _initialize() async {
    if (_isInitialized) return;

    print('Inizializzazione del servizio ${AppConstants.foodOracleName}...');

    // Ottieni le dipendenze necessarie
    final foodDatabaseService = await FoodDatabaseService.getInstance();
    final storageService = await StorageService.getInstance();

    // Crea l'implementazione del Food Oracle
    _foodOracleImplementation = FoodOracleImplementation(
      foodDatabaseService: foodDatabaseService,
      storageService: storageService,
    );

    // Inizializza l'implementazione
    await _foodOracleImplementation.initialize();

    _isInitialized = true;
    print('Servizio ${AppConstants.foodOracleName} inizializzato con successo');
  }

  /// Analizza un'immagine di cibo
  Future<FoodOracleAnalysisResult> analyzeImage(
    File image, 
    UserProfile userProfile, 
    {String? mealType}
  ) async {
    if (!_isInitialized) {
      await _initialize();
    }
    
    return _foodOracleImplementation.analyzeImage(
      image,
      userProfile,
      mealType: mealType,
    );
  }

  /// Analizza un'immagine di cibo già presente nel dispositivo
  Future<FoodOracleAnalysisResult> analyzeImageFromPath(
    String imagePath, 
    UserProfile userProfile, 
    {String? mealType}
  ) async {
    if (!_isInitialized) {
      await _initialize();
    }
    
    return _foodOracleImplementation.analyzeImageFromPath(
      imagePath,
      userProfile,
      mealType: mealType,
    );
  }

  /// Corregge i risultati dell'analisi con input dell'utente
  Future<FoodOracleAnalysisResult> correctAnalysis(
    FoodOracleAnalysisResult analysisResult,
    FoodOracleUserCorrections userCorrections
  ) async {
    if (!_isInitialized) {
      await _initialize();
    }
    
    return _foodOracleImplementation.correctAnalysis(
      analysisResult,
      userCorrections,
    );
  }

  /// Salva i risultati dell'analisi nel diario alimentare dell'utente
  Future<bool> saveAnalysisToFoodDiary(
    FoodOracleAnalysisResult analysisResult,
    String userId,
    {DateTime? date}
  ) async {
    if (!_isInitialized) {
      await _initialize();
    }
    
    return _foodOracleImplementation.saveAnalysisToFoodDiary(
      analysisResult,
      userId,
      date: date,
    );
  }

  /// Ottiene suggerimenti per migliorare il pasto analizzato
  Future<List<FoodOracleSuggestion>> getSuggestions(
    FoodOracleAnalysisResult analysisResult,
    UserProfile userProfile
  ) async {
    if (!_isInitialized) {
      await _initialize();
    }
    
    return _foodOracleImplementation.getSuggestions(
      analysisResult,
      userProfile,
    );
  }

  /// Verifica se il servizio è inizializzato
  bool isInitialized() {
    return _isInitialized;
  }

  /// Ottiene statistiche sul sistema Food Oracle
  Future<Map<String, dynamic>> getStats() async {
    if (!_isInitialized) {
      await _initialize();
    }
    
    return _foodOracleImplementation.getStats();
  }
}
