import '../models/food.dart';
import '../utils/advanced_nutrition_properties.dart';

/// Servizio per la gestione delle proprietà nutrizionali avanzate
class AdvancedNutritionService {
  /// Calcola e aggiunge proprietà nutrizionali avanzate a un alimento
  static Food enrichFoodWithAdvancedProperties(Food food) {
    // Crea una mappa per le proprietà avanzate
    Map<String, dynamic> advancedProps = Map.from(food.advancedProperties);

    // Calcola il PRAL se non è già presente
    if (!advancedProps.containsKey(AdvancedNutritionProperties.PRAL)) {
      double pral = AdvancedNutritionProperties.calculatePRAL(food);
      advancedProps[AdvancedNutritionProperties.PRAL] = pral;
    }

    // Calcola la densità nutrizionale se non è già presente
    if (!advancedProps.containsKey(AdvancedNutritionProperties.NUTRIENT_DENSITY)) {
      double nutrientDensity = AdvancedNutritionProperties.calculateNutrientDensity(food);
      advancedProps[AdvancedNutritionProperties.NUTRIENT_DENSITY] = nutrientDensity;
    }

    // Aggiorna l'alimento con le nuove proprietà
    return food.copyWith(advancedProperties: advancedProps);
  }

  /// Ottieni il valore di una proprietà nutrizionale avanzata
  static dynamic getAdvancedProperty(Food food, String property) {
    return food.advancedProperties[property];
  }

  /// Ottieni il valore formattato di una proprietà nutrizionale avanzata
  static String getFormattedAdvancedProperty(Food food, String property) {
    dynamic value = getAdvancedProperty(food, property);
    return AdvancedNutritionProperties.formatPropertyValue(property, value);
  }

  /// Verifica se un alimento ha una proprietà nutrizionale avanzata
  static bool hasAdvancedProperty(Food food, String property) {
    return food.advancedProperties.containsKey(property) &&
           food.advancedProperties[property] != null;
  }

  /// Ottieni tutte le proprietà nutrizionali avanzate disponibili per un alimento
  static List<String> getAvailableAdvancedProperties(Food food) {
    return food.advancedProperties.keys.toList();
  }

  /// Ottieni una mappa di tutte le proprietà nutrizionali avanzate con i loro valori formattati
  static Map<String, String> getAllFormattedAdvancedProperties(Food food) {
    Map<String, String> formattedProps = {};

    food.advancedProperties.forEach((key, value) {
      formattedProps[AdvancedNutritionProperties.getDisplayName(key)] =
          AdvancedNutritionProperties.formatPropertyValue(key, value);
    });

    return formattedProps;
  }

  /// Ottieni una descrizione testuale di una proprietà nutrizionale avanzata
  static String getAdvancedPropertyDescription(String property) {
    return AdvancedNutritionProperties.getDescription(property);
  }

  /// Valuta se un alimento è acido o alcalino in base al PRAL
  static String evaluatePRAL(Food food) {
    if (!hasAdvancedProperty(food, AdvancedNutritionProperties.PRAL)) {
      return 'Non disponibile';
    }

    double pral = getAdvancedProperty(food, AdvancedNutritionProperties.PRAL);

    if (pral < -5.0) {
      return 'Fortemente alcalinizzante';
    } else if (pral < -2.0) {
      return 'Moderatamente alcalinizzante';
    } else if (pral < 2.0) {
      return 'Neutro';
    } else if (pral < 5.0) {
      return 'Moderatamente acidificante';
    } else {
      return 'Fortemente acidificante';
    }
  }

  /// Valuta la densità nutrizionale di un alimento
  static String evaluateNutrientDensity(Food food) {
    if (!hasAdvancedProperty(food, AdvancedNutritionProperties.NUTRIENT_DENSITY)) {
      return 'Non disponibile';
    }

    double density = getAdvancedProperty(food, AdvancedNutritionProperties.NUTRIENT_DENSITY);

    if (density > 10.0) {
      return 'Eccellente';
    } else if (density > 5.0) {
      return 'Molto buona';
    } else if (density > 2.0) {
      return 'Buona';
    } else if (density > 1.0) {
      return 'Moderata';
    } else {
      return 'Bassa';
    }
  }

  /// Ottieni consigli nutrizionali basati sulle proprietà avanzate
  static List<String> getNutritionalAdvice(Food food) {
    List<String> advice = [];

    // Consigli basati sul PRAL
    if (hasAdvancedProperty(food, AdvancedNutritionProperties.PRAL)) {
      double pral = getAdvancedProperty(food, AdvancedNutritionProperties.PRAL);

      if (pral > 5.0) {
        advice.add('Questo alimento ha un alto potenziale acidificante. Considera di bilanciarlo con alimenti alcalinizzanti come verdure a foglia verde.');
      } else if (pral < -5.0) {
        advice.add('Questo alimento ha un buon potenziale alcalinizzante, utile per bilanciare una dieta ricca di proteine animali e cereali.');
      }
    }

    // Consigli basati sulla densità nutrizionale
    if (hasAdvancedProperty(food, AdvancedNutritionProperties.NUTRIENT_DENSITY)) {
      double density = getAdvancedProperty(food, AdvancedNutritionProperties.NUTRIENT_DENSITY);

      if (density > 5.0) {
        advice.add('Questo alimento ha un\'alta densità nutrizionale, fornisce molti nutrienti essenziali per caloria.');
      } else if (density < 1.0) {
        advice.add('Questo alimento ha una bassa densità nutrizionale. Considera di abbinarlo con alimenti più nutrienti.');
      }
    }

    // Consigli basati sul livello FODMAP
    if (hasAdvancedProperty(food, AdvancedNutritionProperties.FODMAP_LEVEL)) {
      String fodmapLevel = getAdvancedProperty(food, AdvancedNutritionProperties.FODMAP_LEVEL);

      if (fodmapLevel == 'Alto') {
        advice.add('Questo alimento è alto in FODMAPs, che potrebbero causare sintomi in persone con sindrome dell\'intestino irritabile.');
      }
    }

    return advice;
  }
}
