import 'package:flutter/material.dart';
import '../theme/dr_staffilano_theme.dart';
import 'package:intl/intl.dart';

/// Widget per visualizzare un calendario settimanale minimalista
class WeeklyCalendar extends StatefulWidget {
  final DateTime initialDate;
  final Function(DateTime) onDateSelected;
  final DateTime? selectedDate;

  const WeeklyCalendar({
    Key? key,
    required this.initialDate,
    required this.onDateSelected,
    this.selectedDate,
  }) : super(key: key);

  @override
  State<WeeklyCalendar> createState() => _WeeklyCalendarState();
}

class _WeeklyCalendarState extends State<WeeklyCalendar> {
  late DateTime _selectedDate;
  late List<DateTime> _weekDays;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.selectedDate ?? widget.initialDate;
    _generateWeekDays();
  }

  @override
  void didUpdateWidget(WeeklyCalendar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedDate != null &&
        widget.selectedDate != oldWidget.selectedDate) {
      _selectedDate = widget.selectedDate!;
      _generateWeekDays();
    }
  }

  void _generateWeekDays() {
    // Trova il lunedì della settimana corrente
    DateTime monday = _selectedDate.subtract(
      Duration(days: _selectedDate.weekday - 1),
    );

    // Genera i giorni della settimana
    _weekDays = List.generate(
      7,
      (index) => monday.add(Duration(days: index)),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.backgroundWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: List.generate(
          7,
          (index) => _buildDayItem(_weekDays[index]),
        ),
      ),
    );
  }

  Widget _buildDayItem(DateTime date) {
    final isSelected = _isSameDay(date, _selectedDate);
    final isToday = _isSameDay(date, DateTime.now());

    // Nomi dei giorni in italiano
    final dayNames = ['Lun', 'Mar', 'Mer', 'Gio', 'Ven', 'Sab', 'Dom'];
    final dayName = dayNames[date.weekday - 1];
    final dayNumber = date.day.toString();

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedDate = date;
        });
        widget.onDateSelected(date);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: 40,
        height: 60,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: isSelected
              ? DrStaffilanoTheme.primaryGreen
              : Colors.transparent,
          border: isToday && !isSelected
              ? Border.all(color: DrStaffilanoTheme.primaryGreen, width: 2)
              : null,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              dayName,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: isSelected
                    ? DrStaffilanoTheme.textOnPrimary
                    : DrStaffilanoTheme.textSecondary,
              ),
            ),
            const SizedBox(height: 4),
            Container(
              width: 28,
              height: 28,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isSelected
                    ? DrStaffilanoTheme.accentGold.withOpacity(0.2)
                    : isToday
                        ? DrStaffilanoTheme.primaryGreenLight.withOpacity(0.1)
                        : Colors.transparent,
              ),
              child: Center(
                child: Text(
                  dayNumber,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: isSelected || isToday
                        ? FontWeight.bold
                        : FontWeight.w500,
                    color: isSelected
                        ? DrStaffilanoTheme.textOnPrimary
                        : isToday
                            ? DrStaffilanoTheme.primaryGreen
                            : DrStaffilanoTheme.textPrimary,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }
}
