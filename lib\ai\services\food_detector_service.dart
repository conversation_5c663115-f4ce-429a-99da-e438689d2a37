import 'dart:io';
import 'dart:typed_data';
import 'dart:math';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;
import '../../models/food.dart';
import '../../services/food_database_service.dart';
import '../models/food_oracle_models.dart';
import 'ml_model_service.dart';

/// Servizio per il rilevamento degli alimenti nelle immagini
class FoodDetectorService {
  static final FoodDetectorService _instance = FoodDetectorService._internal();

  /// Servizio per il modello ML
  final MLModelService _mlModelService = MLModelService();

  /// Servizio per il database degli alimenti
  late FoodDatabaseService _foodDatabaseService;

  /// Generatore di numeri casuali
  final Random _random = Random();

  /// Flag di inizializzazione
  bool _isInitialized = false;

  factory FoodDetectorService() {
    return _instance;
  }

  FoodDetectorService._internal();

  /// Inizializza il servizio
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Inizializza il servizio del modello ML
      await _mlModelService.initialize();

      // Inizializza il servizio del database degli alimenti
      _foodDatabaseService = await FoodDatabaseService.getInstance();

      _isInitialized = true;
      print('FoodDetectorService inizializzato con successo');
    } catch (e) {
      print('Errore nell\'inizializzazione di FoodDetectorService: $e');
      _isInitialized = false;
    }
  }

  /// Rileva gli alimenti in un'immagine
  /// [imageBytes] - I byte dell'immagine da analizzare
  /// Restituisce una lista di alimenti rilevati con le relative informazioni
  Future<List<DetectedFood>> detectFoods(Uint8List imageBytes) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // Decodifica l'immagine
      final img.Image? image = img.decodeImage(imageBytes);
      if (image == null) {
        throw Exception('Impossibile decodificare l\'immagine');
      }

      // Classifica l'immagine utilizzando il modello ML
      final results = await _mlModelService.classifyImage(imageBytes);

      if (results.isEmpty) {
        // Se non sono stati rilevati alimenti, utilizza il rilevamento basato sui colori
        print('Nessun alimento rilevato dal modello ML, utilizzo il rilevamento basato sui colori');
        return _detectFoodsByColor(image);
      }

      // Converti i risultati in oggetti DetectedFood
      final detectedFoods = <DetectedFood>[];

      for (final result in results) {
        final label = result['label'] as String;
        final confidence = result['confidence'] as double;

        // Cerca l'alimento nel database
        final foods = await _searchFoodByName(label);

        if (foods.isNotEmpty) {
          // Utilizza il primo alimento trovato
          final food = foods.first;

          // Stima la quantità
          final estimatedGrams = _estimatePortionSize(food, confidence);

          // Calcola i valori nutrizionali
          final nutritionalValues = _calculateNutritionalValues(food, estimatedGrams);

          // Crea un bounding box simulato
          final boundingBox = _createBoundingBox(detectedFoods.length);

          // Aggiungi l'alimento rilevato
          detectedFoods.add(DetectedFood(
            food: food,
            estimatedGrams: estimatedGrams,
            confidenceScore: confidence,
            boundingBox: boundingBox,
            nutritionalValues: nutritionalValues,
          ));
        }
      }

      // Se non sono stati trovati alimenti nel database, utilizza il rilevamento basato sui colori
      if (detectedFoods.isEmpty) {
        print('Nessun alimento trovato nel database, utilizzo il rilevamento basato sui colori');
        return _detectFoodsByColor(image);
      }

      return detectedFoods;
    } catch (e) {
      print('Errore nel rilevamento degli alimenti: $e');

      // In caso di errore, utilizza il rilevamento basato sui colori
      final img.Image? image = img.decodeImage(imageBytes);
      if (image == null) {
        throw Exception('Impossibile decodificare l\'immagine');
      }

      return _detectFoodsByColor(image);
    }
  }

  /// Rileva gli alimenti in base ai colori dominanti nell'immagine
  /// [image] - L'immagine da analizzare
  /// Restituisce una lista di alimenti rilevati con le relative informazioni
  Future<List<DetectedFood>> _detectFoodsByColor(img.Image image) async {
    // Ottieni alcuni alimenti casuali dal database
    final allFoods = await _foodDatabaseService.getAllFoods();

    // Analizza i colori dominanti nell'immagine per simulare il rilevamento
    final dominantColors = _analyzeDominantColors(image);

    // Seleziona gli alimenti in base ai colori dominanti
    final detectedFoods = <DetectedFood>[];

    // Simula il rilevamento di 1-3 alimenti
    final numFoods = 1 + (dominantColors.length % 3);

    for (int i = 0; i < numFoods && i < dominantColors.length; i++) {
      final color = dominantColors[i];

      // Seleziona un alimento in base al colore
      final matchingFoods = _findFoodsByColor(allFoods, color);

      if (matchingFoods.isNotEmpty) {
        // Seleziona il primo alimento corrispondente
        final food = matchingFoods.first;

        // Stima la quantità in base alla dimensione della regione di colore
        final estimatedGrams = _estimatePortionSize(food, color.count / (image.width * image.height));

        // Calcola i valori nutrizionali in base alla quantità
        final nutritionalValues = _calculateNutritionalValues(food, estimatedGrams);

        // Crea un bounding box simulato
        final boundingBox = _createBoundingBox(i);

        // Aggiungi l'alimento rilevato
        detectedFoods.add(DetectedFood(
          food: food,
          estimatedGrams: estimatedGrams,
          confidenceScore: 0.7 + (i * 0.1),
          boundingBox: boundingBox,
          nutritionalValues: nutritionalValues,
        ));
      }
    }

    return detectedFoods;
  }

  /// Cerca un alimento nel database per nome
  Future<List<Food>> _searchFoodByName(String name) async {
    try {
      // Cerca l'alimento nel database
      final foods = await _foodDatabaseService.searchFoods(name);

      if (foods.isNotEmpty) {
        return foods;
      }

      // Se non è stato trovato, cerca alimenti con nomi simili
      final allFoods = await _foodDatabaseService.getAllFoods();

      // Converti il nome in minuscolo per il confronto
      final lowerName = name.toLowerCase();

      // Cerca alimenti con nomi simili
      final similarFoods = allFoods.where((food) {
        final lowerFoodName = food.name.toLowerCase();

        // Verifica se il nome dell'alimento contiene il nome cercato o viceversa
        return lowerFoodName.contains(lowerName) || lowerName.contains(lowerFoodName);
      }).toList();

      if (similarFoods.isNotEmpty) {
        return similarFoods;
      }

      // Se non sono stati trovati alimenti simili, restituisci un alimento casuale
      if (allFoods.isNotEmpty) {
        final randomIndex = _random.nextInt(allFoods.length);
        return [allFoods[randomIndex]];
      }

      return [];
    } catch (e) {
      print('Errore nella ricerca dell\'alimento: $e');
      return [];
    }
  }

  /// Crea un bounding box simulato
  BoundingBox _createBoundingBox(int index) {
    return BoundingBox(
      x: 0.1 + (index * 0.3),
      y: 0.2 + (index * 0.2),
      width: 0.3,
      height: 0.3,
    );
  }

  /// Analizza i colori dominanti in un'immagine
  /// [image] - L'immagine da analizzare
  /// Restituisce una lista di colori dominanti con il loro conteggio
  List<_ColorCount> _analyzeDominantColors(img.Image image) {
    final colorCounts = <int, int>{};

    // Campiona i pixel dell'immagine
    for (int y = 0; y < image.height; y += 10) {
      for (int x = 0; x < image.width; x += 10) {
        final pixel = image.getPixel(x, y);

        // Semplifica il colore per ridurre il numero di colori unici
        final simplifiedColor = _simplifyColor(pixel);

        // Incrementa il conteggio del colore
        colorCounts[simplifiedColor] = (colorCounts[simplifiedColor] ?? 0) + 1;
      }
    }

    // Converti in una lista di _ColorCount
    final colorCountList = colorCounts.entries
        .map((entry) => _ColorCount(entry.key, entry.value))
        .toList();

    // Ordina per conteggio decrescente
    colorCountList.sort((a, b) => b.count.compareTo(a.count));

    // Restituisci i primi 5 colori dominanti
    return colorCountList.take(5).toList();
  }

  /// Semplifica un colore riducendo la precisione
  int _simplifyColor(img.Pixel pixel) {
    final r = (pixel.r ~/ 32) * 32;
    final g = (pixel.g ~/ 32) * 32;
    final b = (pixel.b ~/ 32) * 32;

    return (r << 16) | (g << 8) | b;
  }

  /// Trova alimenti in base al colore
  List<Food> _findFoodsByColor(List<Food> foods, _ColorCount colorCount) {
    // Mappa dei colori tipici per categoria di alimenti
    final categoryColors = {
      FoodCategory.fruit: [0xFF0000, 0xFF7F00, 0xFFFF00, 0x00FF00], // Rosso, arancione, giallo, verde
      FoodCategory.vegetable: [0x00FF00, 0x007F00], // Verde
      FoodCategory.grain: [0xFFD700, 0xDAA520], // Oro, marrone chiaro
      FoodCategory.protein: [0xA52A2A, 0x8B4513], // Marrone
      FoodCategory.dairy: [0xFFFFFF, 0xFFFACD], // Bianco, crema
    };

    // Trova la categoria più probabile in base al colore
    FoodCategory? mostLikelyCategory;
    double bestMatch = 0;

    for (final entry in categoryColors.entries) {
      final category = entry.key;
      final colors = entry.value;

      for (final categoryColor in colors) {
        final similarity = _colorSimilarity(colorCount.color, categoryColor);
        if (similarity > bestMatch) {
          bestMatch = similarity;
          mostLikelyCategory = category;
        }
      }
    }

    // Filtra gli alimenti per la categoria più probabile
    if (mostLikelyCategory != null) {
      final matchingFoods = foods.where((food) =>
        food.categories.contains(mostLikelyCategory)).toList();

      if (matchingFoods.isNotEmpty) {
        return matchingFoods;
      }
    }

    // Se non troviamo corrispondenze, restituisci alcuni alimenti casuali
    return foods.take(3).toList();
  }

  /// Calcola la similarità tra due colori (0-1)
  double _colorSimilarity(int color1, int color2) {
    final r1 = (color1 >> 16) & 0xFF;
    final g1 = (color1 >> 8) & 0xFF;
    final b1 = color1 & 0xFF;

    final r2 = (color2 >> 16) & 0xFF;
    final g2 = (color2 >> 8) & 0xFF;
    final b2 = color2 & 0xFF;

    final rDiff = (r1 - r2).abs();
    final gDiff = (g1 - g2).abs();
    final bDiff = (b1 - b2).abs();

    // Distanza euclidea normalizzata
    final distance = (rDiff * rDiff + gDiff * gDiff + bDiff * bDiff) / (3 * 255 * 255);

    // Converti la distanza in similarità (1 - distanza normalizzata)
    return 1.0 - distance;
  }

  /// Stima la dimensione della porzione in base alla categoria dell'alimento e alla confidenza
  int _estimatePortionSize(Food food, double confidence) {
    // Porzioni tipiche per categoria
    final typicalPortions = {
      FoodCategory.fruit: 150,
      FoodCategory.vegetable: 100,
      FoodCategory.grain: 80,
      FoodCategory.protein: 120,
      FoodCategory.dairy: 200,
      FoodCategory.fat: 15,
      FoodCategory.beverage: 250,
    };

    // Trova la categoria principale dell'alimento
    FoodCategory mainCategory = FoodCategory.other;
    for (final category in food.categories) {
      if (typicalPortions.containsKey(category)) {
        mainCategory = category;
        break;
      }
    }

    // Ottieni la porzione tipica per la categoria
    final typicalPortion = typicalPortions[mainCategory] ?? 100;

    // Adatta la porzione in base alla confidenza
    // Usiamo una formula che tiene conto della confidenza del rilevamento
    // Più alta è la confidenza, più vicina sarà la porzione a quella tipica
    final confidenceFactor = 0.5 + (confidence * 0.5); // Varia da 0.5 a 1.0
    final estimatedGrams = (typicalPortion * confidenceFactor).round();

    // Aggiungi una variazione casuale per rendere più realistiche le stime
    final variation = (estimatedGrams * 0.1 * (_random.nextDouble() - 0.5)).round();
    final finalEstimate = estimatedGrams + variation;

    // Limita la porzione a valori ragionevoli per la categoria
    final minPortion = typicalPortion ~/ 2;
    final maxPortion = typicalPortion * 2;

    return finalEstimate.clamp(minPortion, maxPortion);
  }

  /// Calcola i valori nutrizionali in base alla quantità
  NutritionalValues _calculateNutritionalValues(Food food, int grams) {
    final factor = grams / 100.0; // I valori nutrizionali sono per 100g

    // Calcola i valori nutrizionali scalati
    final calories = (food.calories * factor).round();
    final proteins = food.proteins * factor;
    final carbs = food.carbs * factor;
    final fats = food.fats * factor;
    final fiber = food.fiber * factor;
    final sugar = food.sugar * factor;

    // Calcola i micronutrienti scalati
    final micronutrients = <String, double>{};
    food.micronutrients.forEach((key, value) {
      micronutrients[key] = value * factor;
    });

    return NutritionalValues(
      calories: calories,
      proteins: proteins,
      carbs: carbs,
      fats: fats,
      fiber: fiber,
      sugar: sugar,
      micronutrients: micronutrients,
    );
  }

  /// Verifica se il servizio è inizializzato
  bool isInitialized() {
    return _isInitialized;
  }
}

/// Classe di supporto per il conteggio dei colori
class _ColorCount {
  final int color;
  final int count;

  _ColorCount(this.color, this.count);
}
