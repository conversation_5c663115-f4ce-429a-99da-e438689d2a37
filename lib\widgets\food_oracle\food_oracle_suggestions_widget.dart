import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../ai/models/food_oracle_models.dart';
import '../../theme/app_theme.dart';

/// Widget per visualizzare i suggerimenti del Food Oracle
class FoodOracleSuggestionsWidget extends StatelessWidget {
  /// Lista dei suggerimenti
  final List<FoodOracleSuggestion> suggestions;

  const FoodOracleSuggestionsWidget({
    Key? key,
    required this.suggestions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Tito<PERSON> della sezione
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: AppTheme.accentColor,
              ),
              const SizedBox(width: 8.0),
              Text(
                'Suggerimenti',
                style: TextStyle(
                  fontSize: 18.0,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.accentColor,
                ),
              ),
            ],
          ),
        ),
        
        // Lista dei suggerimenti
        Expanded(
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: suggestions.length,
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            itemBuilder: (context, index) {
              return _buildSuggestionCard(suggestions[index]);
            },
          ),
        ),
      ],
    );
  }

  /// Costruisce una card per un suggerimento
  Widget _buildSuggestionCard(FoodOracleSuggestion suggestion) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      elevation: 2.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
        side: BorderSide(
          color: _getSuggestionColor(suggestion.type).withOpacity(0.5),
          width: 1.0,
        ),
      ),
      child: Container(
        width: 250,
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Icona e tipo di suggerimento
            Row(
              children: [
                Icon(
                  _getSuggestionIcon(suggestion.type),
                  color: _getSuggestionColor(suggestion.type),
                  size: 16.0,
                ),
                const SizedBox(width: 8.0),
                Text(
                  _getSuggestionTypeLabel(suggestion.type),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: _getSuggestionColor(suggestion.type),
                    fontSize: 12.0,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8.0),
            
            // Messaggio del suggerimento
            Expanded(
              child: Text(
                suggestion.message,
                style: const TextStyle(fontSize: 14.0),
              ),
            ),
            
            // Alimento suggerito (se presente)
            if (suggestion.suggestedFood != null)
              Row(
                children: [
                  const Icon(
                    Icons.add_circle_outline,
                    size: 14.0,
                    color: Colors.green,
                  ),
                  const SizedBox(width: 4.0),
                  Text(
                    suggestion.suggestedFood!.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 12.0,
                      color: Colors.green,
                    ),
                  ),
                  if (suggestion.suggestedGrams != null)
                    Text(
                      ' (${suggestion.suggestedGrams}g)',
                      style: TextStyle(
                        fontSize: 12.0,
                        color: Colors.grey[600],
                      ),
                    ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  /// Ottiene l'icona per un tipo di suggerimento
  IconData _getSuggestionIcon(SuggestionType type) {
    switch (type) {
      case SuggestionType.general:
        return Icons.info_outline;
      case SuggestionType.addFood:
        return Icons.add_circle_outline;
      case SuggestionType.removeFood:
        return Icons.remove_circle_outline;
      case SuggestionType.replaceFood:
        return Icons.swap_horiz;
      case SuggestionType.adjustPortion:
        return Icons.scale;
      case SuggestionType.balanceMacros:
        return FontAwesomeIcons.balanceScale;
      case SuggestionType.improveMicronutrients:
        return FontAwesomeIcons.vial;
    }
  }

  /// Ottiene il colore per un tipo di suggerimento
  Color _getSuggestionColor(SuggestionType type) {
    switch (type) {
      case SuggestionType.general:
        return Colors.blue;
      case SuggestionType.addFood:
        return Colors.green;
      case SuggestionType.removeFood:
        return Colors.red;
      case SuggestionType.replaceFood:
        return Colors.orange;
      case SuggestionType.adjustPortion:
        return Colors.purple;
      case SuggestionType.balanceMacros:
        return Colors.teal;
      case SuggestionType.improveMicronutrients:
        return Colors.amber;
    }
  }

  /// Ottiene l'etichetta per un tipo di suggerimento
  String _getSuggestionTypeLabel(SuggestionType type) {
    switch (type) {
      case SuggestionType.general:
        return 'Suggerimento generale';
      case SuggestionType.addFood:
        return 'Aggiungi alimento';
      case SuggestionType.removeFood:
        return 'Rimuovi alimento';
      case SuggestionType.replaceFood:
        return 'Sostituisci alimento';
      case SuggestionType.adjustPortion:
        return 'Modifica porzione';
      case SuggestionType.balanceMacros:
        return 'Bilancia macronutrienti';
      case SuggestionType.improveMicronutrients:
        return 'Migliora micronutrienti';
    }
  }
}
