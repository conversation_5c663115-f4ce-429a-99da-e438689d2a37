import '../models/food.dart';

/// Database espanso di alimenti italiani con categorizzazione appropriata per tipo di pasto
class ExpandedItalianFoods {

  /// Alimenti per colazione italiana tradizionale
  static List<Food> getBreakfastFoods() {
    return [
      // CORNETTI E DOLCI DA FORNO
      Food(
        id: 'breakfast_cornetto_1',
        name: 'Cornetto semplice',
        description: 'Cornetto italiano tradizionale senza ripieno',
        calories: 180,
        proteins: 5.2,
        carbs: 22.0,
        fats: 8.5,
        fiber: 1.2,
        sugar: 3.0,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.grain, FoodCategory.sweet],
        isVegetarian: true,
        allergens: ['glutine', 'uova', 'latticini'],
        servingSize: '1 cornetto (50g)',
        servingSizeGrams: 50,
        foodState: FoodState.prepared,
        isTraditionalItalian: true,
      ),

      Food(
        id: 'breakfast_cornetto_2',
        name: 'Cornetto alla crema',
        description: 'Cornetto ripieno di crema pasticcera',
        calories: 220,
        proteins: 6.0,
        carbs: 26.0,
        fats: 11.0,
        fiber: 1.0,
        sugar: 8.0,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.grain, FoodCategory.sweet],
        isVegetarian: true,
        allergens: ['glutine', 'uova', 'latticini'],
        servingSize: '1 cornetto (60g)',
        servingSizeGrams: 60,
        foodState: FoodState.prepared,
        isTraditionalItalian: true,
      ),

      Food(
        id: 'breakfast_brioche_1',
        name: 'Brioche siciliana',
        description: 'Brioche siciliana con tuppo, tradizionale',
        calories: 195,
        proteins: 5.8,
        carbs: 24.0,
        fats: 9.2,
        fiber: 1.1,
        sugar: 4.5,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.grain, FoodCategory.sweet],
        isVegetarian: true,
        allergens: ['glutine', 'uova', 'latticini'],
        servingSize: '1 brioche (55g)',
        servingSizeGrams: 55,
        foodState: FoodState.prepared,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.sicilia],
      ),

      // BISCOTTI E FETTE BISCOTTATE
      Food(
        id: 'breakfast_fette_1',
        name: 'Fette biscottate integrali',
        description: 'Fette biscottate integrali italiane',
        calories: 410,
        proteins: 11.0,
        carbs: 72.0,
        fats: 7.5,
        fiber: 6.5,
        sugar: 2.8,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        allergens: ['glutine'],
        servingSize: '4 fette (30g)',
        servingSizeGrams: 30,
        foodState: FoodState.processed,
        isTraditionalItalian: true,
      ),

      Food(
        id: 'breakfast_biscotti_1',
        name: 'Biscotti digestive',
        description: 'Biscotti digestive italiani per colazione',
        calories: 480,
        proteins: 7.0,
        carbs: 65.0,
        fats: 20.0,
        fiber: 3.5,
        sugar: 16.0,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.grain, FoodCategory.sweet],
        isVegetarian: true,
        allergens: ['glutine', 'latticini'],
        servingSize: '4 biscotti (25g)',
        servingSizeGrams: 25,
        foodState: FoodState.processed,
        isTraditionalItalian: true,
      ),

      // CEREALI E MUESLI
      Food(
        id: 'breakfast_cereali_1',
        name: 'Cereali integrali italiani',
        description: 'Mix di cereali integrali per colazione',
        calories: 350,
        proteins: 12.0,
        carbs: 65.0,
        fats: 6.0,
        fiber: 8.0,
        sugar: 5.0,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '40g',
        servingSizeGrams: 40,
        foodState: FoodState.processed,
        isTraditionalItalian: true,
      ),

      // MARMELLATE E CREME SPALMABILI
      Food(
        id: 'breakfast_marmellata_1',
        name: 'Marmellata di arance siciliane',
        description: 'Marmellata di arance rosse di Sicilia',
        calories: 250,
        proteins: 0.5,
        carbs: 62.0,
        fats: 0.1,
        fiber: 1.2,
        sugar: 60.0,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.sweet, FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '20g (1 cucchiaio)',
        servingSizeGrams: 20,
        foodState: FoodState.processed,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.sicilia],
      ),

      Food(
        id: 'breakfast_miele_1',
        name: 'Miele di acacia italiano',
        description: 'Miele di acacia delle colline italiane',
        calories: 320,
        proteins: 0.3,
        carbs: 80.0,
        fats: 0.0,
        fiber: 0.0,
        sugar: 80.0,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.sweet],
        isVegetarian: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '15g (1 cucchiaio)',
        servingSizeGrams: 15,
        foodState: FoodState.raw,
        isTraditionalItalian: true,
      ),
    ];
  }

  /// Spuntini italiani tradizionali appropriati
  static List<Food> getTraditionalSnacks() {
    return [
      // FRUTTA FRESCA ITALIANA
      Food(
        id: 'snack_pesca_1',
        name: 'Pesca italiana',
        description: 'Pesca fresca italiana di stagione',
        calories: 39,
        proteins: 0.9,
        carbs: 9.5,
        fats: 0.3,
        fiber: 1.5,
        sugar: 8.4,
        suitableForMeals: [MealType.breakfast, MealType.snack],
        categories: [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '1 pesca media (150g)',
        servingSizeGrams: 150,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: [6, 7, 8, 9],
        isTraditionalItalian: true,
      ),

      Food(
        id: 'snack_albicocca_1',
        name: 'Albicocche italiane',
        description: 'Albicocche fresche italiane',
        calories: 48,
        proteins: 1.4,
        carbs: 11.0,
        fats: 0.4,
        fiber: 2.0,
        sugar: 9.2,
        suitableForMeals: [MealType.breakfast, MealType.snack],
        categories: [FoodCategory.fruit],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '3 albicocche (120g)',
        servingSizeGrams: 120,
        foodState: FoodState.raw,
        isSeasonal: true,
        seasonalMonths: [6, 7, 8],
        isTraditionalItalian: true,
      ),

      // FRUTTA SECCA E NOCI
      Food(
        id: 'snack_mandorle_1',
        name: 'Mandorle siciliane',
        description: 'Mandorle siciliane sgusciate, naturali',
        calories: 579,
        proteins: 21.0,
        carbs: 22.0,
        fats: 49.0,
        fiber: 12.0,
        sugar: 4.4,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.protein, FoodCategory.fat],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: ['frutta a guscio'],
        servingSize: '20g (15-20 mandorle)',
        servingSizeGrams: 20,
        foodState: FoodState.raw,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.sicilia],
      ),

      Food(
        id: 'snack_noci_1',
        name: 'Noci italiane',
        description: 'Noci italiane sgusciate, naturali',
        calories: 654,
        proteins: 15.0,
        carbs: 14.0,
        fats: 65.0,
        fiber: 6.7,
        sugar: 2.6,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.protein, FoodCategory.fat],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: ['frutta a guscio'],
        servingSize: '15g (6-8 gherigli)',
        servingSizeGrams: 15,
        foodState: FoodState.raw,
        isTraditionalItalian: true,
      ),

      // PRODOTTI DA FORNO LEGGERI
      Food(
        id: 'snack_grissini_1',
        name: 'Grissini torinesi',
        description: 'Grissini tradizionali di Torino',
        calories: 433,
        proteins: 12.0,
        carbs: 68.0,
        fats: 12.0,
        fiber: 3.2,
        sugar: 2.0,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        allergens: ['glutine'],
        servingSize: '4 grissini (20g)',
        servingSizeGrams: 20,
        foodState: FoodState.processed,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.piemonte],
      ),

      Food(
        id: 'snack_taralli_1',
        name: 'Taralli pugliesi',
        description: 'Taralli pugliesi tradizionali all\'olio',
        calories: 450,
        proteins: 11.0,
        carbs: 65.0,
        fats: 16.0,
        fiber: 2.8,
        sugar: 1.5,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.grain],
        isVegetarian: true,
        isVegan: true,
        allergens: ['glutine'],
        servingSize: '3 taralli (25g)',
        servingSizeGrams: 25,
        foodState: FoodState.processed,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.puglia],
      ),

      // FORMAGGI STAGIONATI (PORZIONI PICCOLE)
      Food(
        id: 'snack_parmigiano_1',
        name: 'Parmigiano Reggiano DOP (spuntino)',
        description: 'Parmigiano Reggiano DOP 24 mesi, porzione spuntino',
        calories: 392,
        proteins: 33.0,
        carbs: 0.0,
        fats: 28.0,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.dairy, FoodCategory.protein],
        isVegetarian: true,
        isGlutenFree: true,
        allergens: ['latticini'],
        servingSize: '20g',
        servingSizeGrams: 20,
        foodState: FoodState.processed,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.emiliaRomagna],
      ),

      // YOGURT ITALIANO
      Food(
        id: 'snack_yogurt_1',
        name: 'Yogurt greco italiano',
        description: 'Yogurt greco prodotto in Italia, naturale',
        calories: 97,
        proteins: 9.0,
        carbs: 4.0,
        fats: 5.0,
        fiber: 0.0,
        sugar: 4.0,
        suitableForMeals: [MealType.breakfast, MealType.snack],
        categories: [FoodCategory.dairy, FoodCategory.protein],
        isVegetarian: true,
        isGlutenFree: true,
        allergens: ['latticini'],
        servingSize: '125g',
        servingSizeGrams: 125,
        foodState: FoodState.prepared,
        isTraditionalItalian: true,
      ),
    ];
  }

  /// Primi piatti italiani tradizionali
  static List<Food> getPrimiPiatti() {
    return [
      // PASTA
      Food(
        id: 'primo_spaghetti_1',
        name: 'Spaghetti al pomodoro e basilico',
        description: 'Spaghetti con sugo di pomodoro fresco e basilico',
        calories: 180,
        proteins: 6.5,
        carbs: 35.0,
        fats: 2.5,
        fiber: 2.8,
        sugar: 4.0,
        suitableForMeals: [MealType.lunch, MealType.dinner],
        categories: [FoodCategory.grain, FoodCategory.mixed],
        isVegetarian: true,
        isVegan: true,
        allergens: ['glutine'],
        servingSize: '100g pasta + sugo',
        servingSizeGrams: 120,
        foodState: FoodState.cooked,
        preparationTimeMinutes: 20,
        isRecipe: true,
        complexity: 2,
        isTraditionalItalian: true,
        ingredients: ['spaghetti', 'pomodori', 'basilico', 'aglio', 'olio extravergine'],
      ),

      Food(
        id: 'primo_penne_1',
        name: 'Penne all\'arrabbiata',
        description: 'Penne con sugo piccante di pomodoro, aglio e peperoncino',
        calories: 185,
        proteins: 6.8,
        carbs: 36.0,
        fats: 3.0,
        fiber: 3.0,
        sugar: 4.5,
        suitableForMeals: [MealType.lunch, MealType.dinner],
        categories: [FoodCategory.grain, FoodCategory.mixed],
        isVegetarian: true,
        isVegan: true,
        allergens: ['glutine'],
        servingSize: '100g pasta + sugo',
        servingSizeGrams: 120,
        foodState: FoodState.cooked,
        preparationTimeMinutes: 25,
        isRecipe: true,
        complexity: 2,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.lazio],
        ingredients: ['penne', 'pomodori', 'aglio', 'peperoncino', 'olio extravergine'],
      ),

      // RISOTTI
      Food(
        id: 'primo_risotto_1',
        name: 'Risotto alla milanese',
        description: 'Risotto con zafferano, tradizione lombarda',
        calories: 220,
        proteins: 5.5,
        carbs: 42.0,
        fats: 4.5,
        fiber: 1.2,
        sugar: 1.0,
        suitableForMeals: [MealType.lunch, MealType.dinner],
        categories: [FoodCategory.grain, FoodCategory.mixed],
        isVegetarian: true,
        isGlutenFree: true,
        allergens: ['latticini'],
        servingSize: '150g',
        servingSizeGrams: 150,
        foodState: FoodState.cooked,
        preparationTimeMinutes: 35,
        isRecipe: true,
        complexity: 3,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.lombardia],
        ingredients: ['riso carnaroli', 'zafferano', 'brodo', 'parmigiano', 'burro'],
      ),

      // ZUPPE E MINESTRE
      Food(
        id: 'primo_minestrone_1',
        name: 'Minestrone di verdure',
        description: 'Minestrone tradizionale con verdure di stagione',
        calories: 85,
        proteins: 3.5,
        carbs: 15.0,
        fats: 2.0,
        fiber: 4.5,
        sugar: 6.0,
        suitableForMeals: [MealType.lunch, MealType.dinner],
        categories: [FoodCategory.vegetable, FoodCategory.mixed],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '250ml',
        servingSizeGrams: 250,
        foodState: FoodState.cooked,
        preparationTimeMinutes: 45,
        isRecipe: true,
        complexity: 2,
        isTraditionalItalian: true,
        ingredients: ['verdure miste', 'pomodori', 'fagioli', 'olio extravergine'],
      ),
    ];
  }

  /// Secondi piatti italiani tradizionali
  static List<Food> getSecondiPiatti() {
    return [
      // PESCE
      Food(
        id: 'secondo_branzino_1',
        name: 'Branzino al sale',
        description: 'Branzino cotto in crosta di sale, ricetta mediterranea',
        calories: 97,
        proteins: 20.0,
        carbs: 0.0,
        fats: 1.5,
        fiber: 0.0,
        sugar: 0.0,
        suitableForMeals: [MealType.lunch, MealType.dinner],
        categories: [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: ['pesce'],
        servingSize: '150g',
        servingSizeGrams: 150,
        foodState: FoodState.cooked,
        preparationTimeMinutes: 40,
        isRecipe: true,
        complexity: 3,
        isTraditionalItalian: true,
        ingredients: ['branzino', 'sale grosso', 'rosmarino', 'limone'],
      ),

      // CARNE
      Food(
        id: 'secondo_pollo_1',
        name: 'Pollo alla cacciatora',
        description: 'Pollo in umido con pomodori, olive e rosmarino',
        calories: 165,
        proteins: 25.0,
        carbs: 3.0,
        fats: 6.0,
        fiber: 1.0,
        sugar: 2.5,
        suitableForMeals: [MealType.lunch, MealType.dinner],
        categories: [FoodCategory.protein],
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '150g',
        servingSizeGrams: 150,
        foodState: FoodState.cooked,
        preparationTimeMinutes: 50,
        isRecipe: true,
        complexity: 3,
        isTraditionalItalian: true,
        ingredients: ['pollo', 'pomodori', 'olive', 'rosmarino', 'vino bianco'],
      ),
    ];
  }

  /// Contorni italiani tradizionali
  static List<Food> getContorni() {
    return [
      Food(
        id: 'contorno_spinaci_1',
        name: 'Spinaci saltati',
        description: 'Spinaci saltati in padella con aglio e olio',
        calories: 35,
        proteins: 3.0,
        carbs: 3.5,
        fats: 1.5,
        fiber: 2.2,
        sugar: 0.4,
        suitableForMeals: [MealType.lunch, MealType.dinner],
        categories: [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '150g',
        servingSizeGrams: 150,
        foodState: FoodState.cooked,
        preparationTimeMinutes: 10,
        isRecipe: true,
        complexity: 1,
        isTraditionalItalian: true,
        ingredients: ['spinaci', 'aglio', 'olio extravergine'],
      ),

      Food(
        id: 'contorno_zucchine_1',
        name: 'Zucchine grigliate',
        description: 'Zucchine grigliate con erbe aromatiche',
        calories: 25,
        proteins: 2.0,
        carbs: 4.0,
        fats: 0.5,
        fiber: 1.5,
        sugar: 3.0,
        suitableForMeals: [MealType.lunch, MealType.dinner],
        categories: [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '150g',
        servingSizeGrams: 150,
        foodState: FoodState.cooked,
        preparationTimeMinutes: 15,
        isRecipe: true,
        complexity: 1,
        isTraditionalItalian: true,
        ingredients: ['zucchine', 'olio extravergine', 'erbe aromatiche'],
      ),
    ];
  }

  /// Ottieni tutti gli alimenti espansi
  static List<Food> getAllExpandedFoods() {
    return [
      ...getBreakfastFoods(),
      ...getTraditionalSnacks(),
      ...getPrimiPiatti(),
      ...getSecondiPiatti(),
      ...getContorni(),
    ];
  }
}
