import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/meal.dart';
import '../models/meal_plan.dart';
import '../models/user_profile.dart';
import '../models/diet_plan.dart';

class StorageService {
  static StorageService? _instance;

  static const String _pastiKey = 'pasti';
  static const String _calorieGiornaliereKey = 'calorie_giornaliere';
  static const String _storicoKey = 'storico_calorie';
  static const String _macroNutrientiKey = 'macro_nutrienti';
  static const String _acquaKey = 'acqua';
  static const String _obiettivoBicchieriKey = 'obiettivo_bicchieri';
  static const String _pianoPastiKey = 'piano_pasti';
  static const String _userProfileKey = 'user_profile';
  static const String _dietPlanKey = 'diet_plan';
  static const String _lastIntegratedDietPlanIdKey = 'last_integrated_diet_plan_id';
  static const String _calorieConsumateKey = 'calorie_consumate';
  static const String _macroConsumatiKey = 'macro_consumati';

  // Costruttore privato per il pattern singleton
  StorageService._();

  /// Ottiene l'istanza singleton del servizio di storage
  static Future<StorageService> getInstance() async {
    if (_instance == null) {
      _instance = StorageService._();
    }
    return _instance!;
  }

  // Salva la lista dei pasti
  Future<void> salvaPasti(List<Meal> pasti) async {
    final prefs = await SharedPreferences.getInstance();
    final pastiMap = pasti.map((pasto) => pasto.toMap()).toList();
    final pastiJson = jsonEncode(pastiMap);
    await prefs.setString(_pastiKey, pastiJson);
  }

  // Carica la lista dei pasti
  Future<List<Meal>> caricaPasti() async {
    final prefs = await SharedPreferences.getInstance();
    final pastiJson = prefs.getString(_pastiKey);

    if (pastiJson == null) {
      return [];
    }

    final List<dynamic> decoded = jsonDecode(pastiJson);
    return decoded.map((item) => Meal.fromMap(Map<String, dynamic>.from(item))).toList();
  }

  // Salva l'obiettivo calorico giornaliero
  Future<void> salvaCalorieGiornaliere(int calorie) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_calorieGiornaliereKey, calorie);
  }

  // Carica l'obiettivo calorico giornaliero
  Future<int> caricaCalorieGiornaliere() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_calorieGiornaliereKey) ?? 2000; // Default a 2000 calorie
  }

  // Aggiunge un record allo storico delle calorie
  Future<void> aggiungiAlloStorico(String data, int calorieConsumate) async {
    final prefs = await SharedPreferences.getInstance();
    final storicoJson = prefs.getString(_storicoKey);

    Map<String, int> storico = {};
    if (storicoJson != null) {
      final Map<String, dynamic> decoded = jsonDecode(storicoJson);
      storico = decoded.map((key, value) => MapEntry(key, value as int));
    }

    storico[data] = calorieConsumate;
    await prefs.setString(_storicoKey, jsonEncode(storico));
  }

  // Carica lo storico delle calorie
  Future<Map<String, int>> caricaStorico() async {
    final prefs = await SharedPreferences.getInstance();
    final storicoJson = prefs.getString(_storicoKey);

    if (storicoJson == null) {
      return {};
    }

    final Map<String, dynamic> decoded = jsonDecode(storicoJson);
    return decoded.map((key, value) => MapEntry(key, value as int));
  }

  // Salva gli obiettivi di macronutrienti
  Future<void> salvaMacroNutrienti(Map<String, double> macroNutrienti) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_macroNutrientiKey, jsonEncode(macroNutrienti));
  }

  // Carica gli obiettivi di macronutrienti
  Future<Map<String, double>> caricaMacroNutrienti() async {
    final prefs = await SharedPreferences.getInstance();
    final macroJson = prefs.getString(_macroNutrientiKey);

    if (macroJson == null) {
      // Valori predefiniti: 30% proteine, 40% carboidrati, 30% grassi
      return {
        'proteine': 30.0,
        'carboidrati': 40.0,
        'grassi': 30.0,
      };
    }

    final Map<String, dynamic> decoded = jsonDecode(macroJson);
    return decoded.map((key, value) => MapEntry(key, value as double));
  }

  // Salva i bicchieri d'acqua
  Future<void> salvaBicchieriAcqua(int bicchieri) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_acquaKey, bicchieri);
  }

  // Carica i bicchieri d'acqua
  Future<int> caricaBicchieriAcqua() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_acquaKey) ?? 0;
  }

  // Salva l'obiettivo di bicchieri d'acqua
  Future<void> salvaObiettivoBicchieri(int obiettivo) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_obiettivoBicchieriKey, obiettivo);
  }

  // Carica l'obiettivo di bicchieri d'acqua
  Future<int> caricaObiettivoBicchieri() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_obiettivoBicchieriKey) ?? 8; // Default a 8 bicchieri
  }

  // Salva il piano settimanale dei pasti
  Future<void> salvaPianoPasti(WeeklyMealPlan piano) async {
    final prefs = await SharedPreferences.getInstance();
    final pianoJson = jsonEncode(piano.toJson());
    await prefs.setString(_pianoPastiKey, pianoJson);
  }

  // Carica il piano settimanale dei pasti
  Future<WeeklyMealPlan?> caricaPianoPasti() async {
    final prefs = await SharedPreferences.getInstance();
    final pianoJson = prefs.getString(_pianoPastiKey);

    if (pianoJson == null) {
      return null;
    }

    try {
      final Map<String, dynamic> decoded = jsonDecode(pianoJson);
      return WeeklyMealPlan.fromJson(decoded);
    } catch (e) {
      print('Errore nel caricamento del piano pasti: $e');
      return null;
    }
  }

  // Ottieni il piano per la settimana corrente o crea uno nuovo
  Future<WeeklyMealPlan> getPianoSettimanaleCorrente() async {
    // Trova l'inizio della settimana corrente (lunedì)
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));

    // Carica il piano esistente
    final pianoPasti = await caricaPianoPasti();

    if (pianoPasti != null) {
      // Controlla se il piano è per la settimana corrente
      final startDate = DateTime.parse(pianoPasti.startDate);
      final startOfWeekString = '${startOfWeek.year}-${startOfWeek.month.toString().padLeft(2, '0')}-${startOfWeek.day.toString().padLeft(2, '0')}';

      if (pianoPasti.startDate == startOfWeekString) {
        return pianoPasti;
      }
    }

    // Crea un nuovo piano per la settimana corrente
    return WeeklyMealPlan.createEmptyPlan(startOfWeek);
  }

  // Aggiorna un piano giornaliero nel piano settimanale
  Future<void> aggiornaPianoGiornaliero(DailyPlan pianoDiario) async {
    final piano = await getPianoSettimanaleCorrente();
    final pianoAggiornato = piano.updateDailyPlan(pianoDiario);
    await salvaPianoPasti(pianoAggiornato);
  }

  // Salva il profilo utente
  Future<void> salvaUserProfile(UserProfile userProfile) async {
    final prefs = await SharedPreferences.getInstance();
    final userProfileJson = jsonEncode(userProfile.toMap());
    await prefs.setString(_userProfileKey, userProfileJson);
  }

  // Carica il profilo utente
  Future<UserProfile?> caricaUserProfile() async {
    final prefs = await SharedPreferences.getInstance();
    final userProfileJson = prefs.getString(_userProfileKey);

    if (userProfileJson == null) {
      return null;
    }

    try {
      final Map<String, dynamic> decoded = jsonDecode(userProfileJson);
      return UserProfile.fromMap(decoded);
    } catch (e) {
      print('Errore nel caricamento del profilo utente: $e');
      return null;
    }
  }

  // Salva il piano dietetico settimanale
  Future<void> salvaDietPlan(WeeklyDietPlan dietPlan) async {
    final prefs = await SharedPreferences.getInstance();
    final dietPlanJson = jsonEncode(dietPlan.toMap());
    await prefs.setString(_dietPlanKey, dietPlanJson);
  }

  // Carica il piano dietetico settimanale
  Future<WeeklyDietPlan?> caricaDietPlan() async {
    final prefs = await SharedPreferences.getInstance();
    final dietPlanJson = prefs.getString(_dietPlanKey);

    if (dietPlanJson == null) {
      return null;
    }

    try {
      final Map<String, dynamic> decoded = jsonDecode(dietPlanJson);
      return WeeklyDietPlan.fromMap(decoded);
    } catch (e) {
      print('Errore nel caricamento del piano dietetico: $e');
      return null;
    }
  }

  // Converti un piano dietetico in pasti per l'app
  Future<List<Meal>> convertDietPlanToMeals(DailyDietPlan dailyPlan) async {
    final meals = <Meal>[];

    for (var plannedMeal in dailyPlan.meals) {
      meals.add(plannedMeal.toMeal());
    }

    return meals;
  }

  // Applica il piano dietetico giornaliero ai pasti dell'app
  Future<void> applicaDietPlanGiornaliero(String date) async {
    final dietPlan = await caricaDietPlan();
    if (dietPlan == null) return;

    final dailyPlan = dietPlan.getPlanForDate(date);
    if (dailyPlan == null) return;

    final meals = await convertDietPlanToMeals(dailyPlan);
    await salvaPasti(meals);
  }

  // Salva le calorie consumate per il giorno corrente
  Future<void> salvaCalorieConsumate(int calorie) async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now().toIso8601String().split('T')[0];
    await prefs.setInt('${_calorieConsumateKey}_$today', calorie);
  }

  // Carica le calorie consumate per il giorno corrente
  Future<int> caricaCalorieConsumate() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now().toIso8601String().split('T')[0];
    return prefs.getInt('${_calorieConsumateKey}_$today') ?? 0;
  }

  // Salva i macronutrienti consumati per il giorno corrente
  Future<void> salvaMacroConsumati(Map<String, double> macroConsumati) async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now().toIso8601String().split('T')[0];
    await prefs.setString('${_macroConsumatiKey}_$today', jsonEncode(macroConsumati));
  }

  // Carica i macronutrienti consumati per il giorno corrente
  Future<Map<String, double>> caricaMacroConsumati() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now().toIso8601String().split('T')[0];
    final macroJson = prefs.getString('${_macroConsumatiKey}_$today');

    if (macroJson == null) {
      return {
        'proteine': 0.0,
        'carboidrati': 0.0,
        'grassi': 0.0,
      };
    }

    final Map<String, dynamic> decoded = jsonDecode(macroJson);
    return decoded.map((key, value) => MapEntry(key, value as double));
  }

  // Salva l'ID dell'ultimo piano dietetico integrato
  Future<void> salvaLastIntegratedDietPlanId(String dietPlanId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastIntegratedDietPlanIdKey, dietPlanId);
  }

  // Carica l'ID dell'ultimo piano dietetico integrato
  Future<String?> caricaLastIntegratedDietPlanId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_lastIntegratedDietPlanIdKey);
  }

  // Metodi generici per il supporto delle sfide avanzate

  /// Salva una stringa
  Future<void> setString(String key, String value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(key, value);
  }

  /// Carica una stringa
  Future<String?> getString(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(key);
  }

  /// Salva un intero
  Future<void> setInt(String key, int value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(key, value);
  }

  /// Carica un intero
  Future<int?> getInt(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(key);
  }

  /// Salva un booleano
  Future<void> setBool(String key, bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(key, value);
  }

  /// Carica un booleano
  Future<bool?> getBool(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(key);
  }

  /// Salva un double
  Future<void> setDouble(String key, double value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble(key, value);
  }

  /// Carica un double
  Future<double?> getDouble(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getDouble(key);
  }

  /// Rimuove una chiave
  Future<void> remove(String key) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(key);
  }

  /// Controlla se una chiave esiste
  Future<bool> containsKey(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.containsKey(key);
  }

  /// Pulisce tutto lo storage
  Future<void> clear() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }
}
