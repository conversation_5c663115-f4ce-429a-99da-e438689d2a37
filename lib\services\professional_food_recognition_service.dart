import 'dart:io';
import 'dart:typed_data';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;
// import 'package:tflite_flutter/tflite_flutter.dart'; // Temporaneamente disabilitato
// import 'package:tflite_flutter_helper/tflite_flutter_helper.dart'; // Rimosso per conflitto versioni
import '../models/food.dart';
import '../models/food_recognition_result.dart';
import '../data/food_database.dart';
import 'food_safety_service.dart';

/// Servizio professionale di riconoscimento alimentare
/// Utilizza TensorFlow Lite con modelli pre-addestrati per accuratezza elevata
class ProfessionalFoodRecognitionService {
  static const String _modelPath = 'assets/ml_models/food_classifier.tflite';
  static const String _labelsPath = 'assets/ml_models/food101_labels.txt';

  // Singleton
  static ProfessionalFoodRecognitionService? _instance;
  static ProfessionalFoodRecognitionService getInstance() {
    _instance ??= ProfessionalFoodRecognitionService._();
    return _instance!;
  }
  ProfessionalFoodRecognitionService._();

  // Componenti ML
  Interpreter? _interpreter;
  List<String>? _labels;
  bool _isInitialized = false;

  // Database alimentare
  final FoodDatabase _foodDatabase = FoodDatabase();

  // Mappatura classi Food-101 -> alimenti italiani
  late Map<String, List<String>> _classToItalianFoods;

  // Configurazione modello
  static const int _inputSize = 224;
  static const int _numChannels = 3;
  static const double _confidenceThreshold = 0.3;
  static const int _maxResults = 5;

  /// Inizializza il servizio
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      print('🔧 Inizializzazione Professional Food Recognition Service...');

      // Carica il modello TensorFlow Lite
      await _loadModel();

      // Carica le etichette
      await _loadLabels();

      // Inizializza la mappatura classi -> alimenti italiani
      _initializeClassMapping();

      _isInitialized = true;
      print('✅ Professional Food Recognition Service inizializzato con successo');

    } catch (e) {
      print('❌ Errore nell\'inizializzazione: $e');
      _isInitialized = false;
      rethrow;
    }
  }

  /// Carica il modello TensorFlow Lite
  Future<void> _loadModel() async {
    try {
      // Carica il modello dai assets
      final options = InterpreterOptions();

      // Abilita accelerazione hardware se disponibile
      if (!kIsWeb) {
        options.addDelegate(XNNPackDelegate());
      }

      _interpreter = await Interpreter.fromAsset(_modelPath, options: options);

      print('📱 Modello TensorFlow Lite caricato: ${_interpreter!.getInputTensors().length} input, ${_interpreter!.getOutputTensors().length} output');

    } catch (e) {
      print('❌ Errore nel caricamento del modello: $e');
      throw Exception('Impossibile caricare il modello di riconoscimento alimentare');
    }
  }

  /// Carica le etichette delle classi
  Future<void> _loadLabels() async {
    try {
      final labelsData = await rootBundle.loadString(_labelsPath);
      _labels = labelsData.split('\n')
          .map((line) => line.trim())
          .where((line) => line.isNotEmpty)
          .toList();

      print('🏷️ Caricate ${_labels!.length} etichette di classificazione');

    } catch (e) {
      print('❌ Errore nel caricamento delle etichette: $e');
      throw Exception('Impossibile caricare le etichette di classificazione');
    }
  }

  /// Inizializza la mappatura tra classi Food-101 e alimenti italiani
  void _initializeClassMapping() {
    _classToItalianFoods = {
      // Frutta
      'apple_pie': ['mela', 'torta di mele'],
      'apple': ['mela', 'mela fuji', 'mela golden'],
      'orange': ['arancia', 'arancia rossa'],
      'banana': ['banana'],
      'strawberry': ['fragola', 'fragole'],
      'grapes': ['uva', 'uva bianca', 'uva nera'],

      // Verdure
      'broccoli': ['broccoli', 'broccoli cotti'],
      'carrot_cake': ['carota', 'torta di carote'],
      'spinach': ['spinaci', 'spinaci cotti'],
      'tomato': ['pomodoro', 'pomodori'],
      'lettuce': ['lattuga', 'insalata'],

      // Proteine
      'chicken_breast': ['petto di pollo', 'pollo cotto'],
      'chicken_wings': ['ali di pollo', 'pollo cotto'],
      'beef_steak': ['bistecca', 'manzo cotto'],
      'pork_chop': ['braciola di maiale', 'maiale cotto'],
      'salmon': ['salmone', 'salmone cotto'],
      'tuna': ['tonno', 'tonno cotto'],
      'eggs': ['uova', 'uova cotte'],

      // Carboidrati
      'bread': ['pane', 'pane integrale'],
      'pasta': ['pasta', 'pasta cotta'],
      'rice': ['riso', 'riso basmati cotto'],
      'pizza': ['pizza', 'pizza margherita'],

      // Latticini
      'cheese': ['formaggio', 'parmigiano reggiano'],
      'milk': ['latte', 'latte parzialmente scremato'],
      'yogurt': ['yogurt', 'yogurt greco'],

      // Legumi
      'beans': ['fagioli', 'fagioli cannellini cotti'],
      'lentils': ['lenticchie', 'lenticchie cotte'],

      // Noci e semi
      'nuts': ['noci', 'mandorle', 'nocciole'],
      'almonds': ['mandorle'],

      // Dolci (limitati)
      'chocolate_cake': ['torta al cioccolato'],
      'ice_cream': ['gelato'],
    };
  }

  /// Analizza un'immagine e riconosce gli alimenti
  Future<FoodRecognitionResult> analyzeFood({
    required File imageFile,
    required String mealType,
    String? userId,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      print('🔍 Avvio analisi professionale per ${imageFile.path}');

      // Preprocessa l'immagine
      final preprocessedImage = await _preprocessImage(imageFile);

      // Esegui la classificazione
      final classificationResults = await _classifyImage(preprocessedImage);

      // Converti i risultati in alimenti riconosciuti
      final recognizedFoods = await _convertToRecognizedFoods(classificationResults);

      // Filtra per sicurezza alimentare
      final safeResults = _filterSafeResults(recognizedFoods);

      // Valida i risultati per coerenza
      final validatedResults = _validateResults(safeResults, preprocessedImage);

      // Calcola riepilogo nutrizionale
      final nutritionalSummary = _calculateNutritionalSummary(validatedResults);

      // Genera suggerimenti
      final suggestions = _generateSuggestions(validatedResults, mealType);

      // Calcola confidenza complessiva
      final overallConfidence = _calculateOverallConfidence(validatedResults);

      final result = FoodRecognitionResult(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        timestamp: DateTime.now(),
        imagePath: imageFile.path,
        mealType: mealType,
        recognizedFoods: validatedResults,
        nutritionalSummary: nutritionalSummary,
        suggestions: suggestions,
        confidenceScore: overallConfidence,
        userId: userId,
      );

      print('✅ Analisi completata: ${validatedResults.length} alimenti riconosciuti con confidenza ${(overallConfidence * 100).toStringAsFixed(1)}%');

      return result;

    } catch (e) {
      print('❌ Errore nell\'analisi: $e');
      rethrow;
    }
  }

  /// Preprocessa l'immagine per il modello
  Future<img.Image> _preprocessImage(File imageFile) async {
    final bytes = await imageFile.readAsBytes();
    final image = img.decodeImage(bytes);

    if (image == null) {
      throw Exception('Impossibile decodificare l\'immagine');
    }

    // Ridimensiona a 224x224 (input standard per modelli di classificazione)
    final resized = img.copyResize(image, width: _inputSize, height: _inputSize);

    // Migliora la qualità dell'immagine
    final enhanced = img.adjustColor(resized,
      contrast: 1.1,
      brightness: 1.05,
      saturation: 1.1
    );

    return enhanced;
  }

  /// Classifica l'immagine usando TensorFlow Lite
  Future<List<ClassificationResult>> _classifyImage(img.Image image) async {
    if (_interpreter == null || _labels == null) {
      throw Exception('Modello non inizializzato');
    }

    // Prepara l'input tensor
    final input = _imageToByteListFloat32(image);
    final output = List.filled(_labels!.length, 0.0).reshape([1, _labels!.length]);

    // Esegui l'inferenza
    _interpreter!.run(input, output);

    // Processa i risultati
    final results = <ClassificationResult>[];
    final outputList = output[0] as List<double>;

    for (int i = 0; i < outputList.length; i++) {
      final confidence = outputList[i];
      if (confidence > _confidenceThreshold) {
        results.add(ClassificationResult(
          label: _labels![i],
          confidence: confidence,
          index: i,
        ));
      }
    }

    // Ordina per confidenza decrescente e prendi i migliori risultati
    results.sort((a, b) => b.confidence.compareTo(a.confidence));
    return results.take(_maxResults).toList();
  }

  /// Converte l'immagine in input tensor
  Float32List _imageToByteListFloat32(img.Image image) {
    final convertedBytes = Float32List(1 * _inputSize * _inputSize * _numChannels);
    final buffer = Float32List.view(convertedBytes.buffer);
    int pixelIndex = 0;

    for (int i = 0; i < _inputSize; i++) {
      for (int j = 0; j < _inputSize; j++) {
        final pixel = image.getPixel(j, i);

        // Normalizza i valori RGB a [0, 1]
        buffer[pixelIndex++] = pixel.r / 255.0;
        buffer[pixelIndex++] = pixel.g / 255.0;
        buffer[pixelIndex++] = pixel.b / 255.0;
      }
    }

    return convertedBytes.reshape([1, _inputSize, _inputSize, _numChannels]);
  }

  /// Converte i risultati di classificazione in alimenti riconosciuti
  Future<List<RecognizedFood>> _convertToRecognizedFoods(List<ClassificationResult> results) async {
    final recognizedFoods = <RecognizedFood>[];
    final allFoods = await _foodDatabase.getAllFoods();

    for (final result in results) {
      final italianFoodNames = _classToItalianFoods[result.label] ?? [];

      for (final foodName in italianFoodNames) {
        // Trova l'alimento nel database
        final matchingFood = allFoods.firstWhere(
          (food) => food.name.toLowerCase().contains(foodName.toLowerCase()),
          orElse: () => Food.empty(),
        );

        if (matchingFood.id.isNotEmpty) {
          // Stima la porzione basata sulla confidenza
          final estimatedGrams = _estimatePortionSize(result.confidence);

          recognizedFoods.add(RecognizedFood(
            food: matchingFood,
            confidence: result.confidence,
            estimatedGrams: estimatedGrams,
            boundingBox: BoundingBox(
              x: 0.1,
              y: 0.1,
              width: 0.8,
              height: 0.8,
            ),
          ));
          break; // Prendi solo il primo match per evitare duplicati
        }
      }
    }

    return recognizedFoods;
  }

  /// Stima la dimensione della porzione basata sulla confidenza
  int _estimatePortionSize(double confidence) {
    // Porzioni realistiche basate sulla confidenza
    if (confidence > 0.8) {
      return 100 + Random().nextInt(50); // 100-150g per alta confidenza
    } else if (confidence > 0.6) {
      return 80 + Random().nextInt(40); // 80-120g per media confidenza
    } else {
      return 60 + Random().nextInt(40); // 60-100g per bassa confidenza
    }
  }

  /// Filtra i risultati per sicurezza alimentare
  List<RecognizedFood> _filterSafeResults(List<RecognizedFood> results) {
    return results.where((result) =>
      FoodSafetyService.isFoodSafe(result.food)
    ).toList();
  }

  /// Valida i risultati per coerenza (evita errori grossolani)
  List<RecognizedFood> _validateResults(List<RecognizedFood> results, img.Image image) {
    if (results.isEmpty) return results;

    // Analizza i colori dominanti dell'immagine per validazione
    final dominantColors = _analyzeDominantColors(image);
    final validatedResults = <RecognizedFood>[];

    for (final result in results) {
      if (_isResultConsistent(result, dominantColors)) {
        validatedResults.add(result);
      } else {
        print('⚠️ Risultato inconsistente filtrato: ${result.food.name}');
      }
    }

    return validatedResults;
  }

  /// Verifica se un risultato è consistente con i colori dell'immagine
  bool _isResultConsistent(RecognizedFood result, Map<String, double> dominantColors) {
    final foodName = result.food.name.toLowerCase();

    // Regole di validazione basate sui colori
    if (foodName.contains('mela') && dominantColors['red']! < 0.1 && dominantColors['green']! < 0.1) {
      return false; // Mela senza rosso o verde
    }

    if (foodName.contains('spinaci') && dominantColors['green']! < 0.2) {
      return false; // Spinaci senza verde
    }

    if (foodName.contains('pomodoro') && dominantColors['red']! < 0.15) {
      return false; // Pomodoro senza rosso
    }

    if (foodName.contains('pollo') && dominantColors['white']! < 0.1 && dominantColors['brown']! < 0.1) {
      return false; // Pollo senza bianco o marrone
    }

    return true; // Passa la validazione
  }

  /// Analizza i colori dominanti nell'immagine
  Map<String, double> _analyzeDominantColors(img.Image image) {
    final colorCounts = <String, int>{
      'red': 0,
      'green': 0,
      'blue': 0,
      'white': 0,
      'brown': 0,
      'yellow': 0,
    };

    int totalPixels = 0;

    // Campiona i pixel (ogni 10 pixel per performance)
    for (int y = 0; y < image.height; y += 10) {
      for (int x = 0; x < image.width; x += 10) {
        final pixel = image.getPixel(x, y);
        final r = pixel.r.toInt();
        final g = pixel.g.toInt();
        final b = pixel.b.toInt();

        final colorCategory = _categorizePixelColor(r, g, b);
        colorCounts[colorCategory] = colorCounts[colorCategory]! + 1;
        totalPixels++;
      }
    }

    // Converti in percentuali
    final colorPercentages = <String, double>{};
    colorCounts.forEach((color, count) {
      colorPercentages[color] = count / totalPixels;
    });

    return colorPercentages;
  }

  /// Categorizza il colore di un pixel
  String _categorizePixelColor(int r, int g, int b) {
    // Bianco
    if (r > 200 && g > 200 && b > 200) return 'white';

    // Rosso
    if (r > g + 30 && r > b + 30) return 'red';

    // Verde
    if (g > r + 30 && g > b + 30) return 'green';

    // Blu
    if (b > r + 30 && b > g + 30) return 'blue';

    // Giallo
    if (r > 150 && g > 150 && b < 100) return 'yellow';

    // Marrone
    if (r > 100 && g > 60 && b < 80 && r > g) return 'brown';

    return 'other';
  }

  /// Calcola il riepilogo nutrizionale
  NutritionalSummary _calculateNutritionalSummary(List<RecognizedFood> foods) {
    double totalCalories = 0;
    double totalProteins = 0;
    double totalCarbs = 0;
    double totalFats = 0;
    double totalFiber = 0;

    for (final recognizedFood in foods) {
      final factor = recognizedFood.estimatedGrams / 100.0;
      totalCalories += recognizedFood.food.calories * factor;
      totalProteins += recognizedFood.food.proteins * factor;
      totalCarbs += recognizedFood.food.carbs * factor;
      totalFats += recognizedFood.food.fats * factor;
      totalFiber += recognizedFood.food.fiber * factor;
    }

    return NutritionalSummary(
      calories: totalCalories.round(),
      proteins: totalProteins,
      carbs: totalCarbs,
      fats: totalFats,
      fiber: totalFiber,
    );
  }

  /// Genera suggerimenti personalizzati
  List<String> _generateSuggestions(List<RecognizedFood> foods, String mealType) {
    final suggestions = <String>[];

    if (foods.isEmpty) {
      suggestions.add('Non sono riuscito a riconoscere alimenti chiari nell\'immagine. Prova con una foto più nitida e ben illuminata.');
      return suggestions;
    }

    // Analizza la composizione nutrizionale
    final hasProtein = foods.any((f) => f.food.categories.contains(FoodCategory.protein));
    final hasVegetables = foods.any((f) => f.food.categories.contains(FoodCategory.vegetable));
    final hasCarbs = foods.any((f) => f.food.categories.contains(FoodCategory.grain));

    // Suggerimenti basati sull'analisi
    if (!hasProtein && mealType != 'snack') {
      suggestions.add('💪 Considera di aggiungere una fonte di proteine per un pasto più completo.');
    }

    if (!hasVegetables) {
      suggestions.add('🥬 Aggiungi delle verdure per aumentare vitamine, minerali e fibre.');
    }

    if (!hasCarbs && (mealType == 'breakfast' || mealType == 'lunch')) {
      suggestions.add('🌾 Una fonte di carboidrati complessi fornirebbe energia sostenibile.');
    }

    // Suggerimenti specifici per tipo di pasto
    switch (mealType) {
      case 'breakfast':
        suggestions.add('🌅 Ottima colazione! Ricorda di idratarti bene per iniziare la giornata.');
        break;
      case 'lunch':
        suggestions.add('🍽️ Pranzo bilanciato per mantenere l\'energia nel pomeriggio.');
        break;
      case 'dinner':
        suggestions.add('🌙 Cena appropriata. Evita pasti troppo pesanti prima di dormire.');
        break;
      case 'snack':
        suggestions.add('🍎 Spuntino sano per mantenere stabile la glicemia tra i pasti.');
        break;
    }

    return suggestions;
  }

  /// Calcola la confidenza complessiva
  double _calculateOverallConfidence(List<RecognizedFood> foods) {
    if (foods.isEmpty) return 0.0;

    final totalConfidence = foods.fold(0.0, (sum, food) => sum + food.confidence);
    return totalConfidence / foods.length;
  }

  /// Rilascia le risorse
  void dispose() {
    _interpreter?.close();
    _interpreter = null;
    _isInitialized = false;
  }
}

/// Risultato di classificazione
class ClassificationResult {
  final String label;
  final double confidence;
  final int index;

  const ClassificationResult({
    required this.label,
    required this.confidence,
    required this.index,
  });
}
