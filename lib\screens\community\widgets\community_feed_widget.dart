import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../models/community_post.dart';
import '../../../models/community_user.dart';
import '../../../services/notification_service.dart';
import '../../../services/comment_service.dart';
import '../../../widgets/animated_like_button.dart';
import '../../../widgets/centered_comments_modal.dart';
import '../../../utils/theme_colors.dart';

/// Widget per il feed della community
class CommunityFeedWidget extends StatefulWidget {
  final List<CommunityPost> posts;

  const CommunityFeedWidget({
    super.key,
    required this.posts,
  });

  @override
  State<CommunityFeedWidget> createState() => _CommunityFeedWidgetState();
}

class _CommunityFeedWidgetState extends State<CommunityFeedWidget> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.posts.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: () async {
        // Implementa il refresh del feed
        await Future.delayed(const Duration(seconds: 1));
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        itemCount: widget.posts.length + 1, // +1 per il loading indicator
        itemBuilder: (context, index) {
          if (index == widget.posts.length) {
            return _buildLoadMoreIndicator();
          }

          final post = widget.posts[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: _buildPostCard(post),
          );
        },
      ),
    );
  }

  /// Costruisce lo stato vuoto
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.article_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Nessun post nel feed',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Inizia a seguire altri utenti o unisciti ai gruppi\nper vedere i loro post qui',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // Naviga ai gruppi suggeriti
            },
            icon: const Icon(Icons.group_add),
            label: const Text('Esplora Gruppi'),
            style: ElevatedButton.styleFrom(
              backgroundColor: ThemeColors.primaryGreen,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  /// Costruisce l'indicatore di caricamento per altri post
  Widget _buildLoadMoreIndicator() {
    return const Padding(
      padding: EdgeInsets.all(16),
      child: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  /// Costruisce la card di un post
  Widget _buildPostCard(CommunityPost post) {
    return Card(
      elevation: 1,
      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header del post
          _buildPostHeader(post),

          // Contenuto del post
          if (post.content.isNotEmpty) _buildPostContent(post),

          // Immagini del post
          if (post.imageUrls.isNotEmpty) _buildPostImages(post),

          // Dati allegati (piano dietetico, progresso, etc.)
          if (post.attachedData != null) _buildAttachedData(post),

          // Footer con azioni
          _buildPostFooter(post),
        ],
      ),
    );
  }

  /// Costruisce l'header del post
  Widget _buildPostHeader(CommunityPost post) {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          // Avatar dell'autore
          CircleAvatar(
            radius: 18,
            backgroundColor: ThemeColors.primaryGreen.withOpacity(0.1),
            backgroundImage: post.author?.avatarUrl != null
                ? NetworkImage(post.author!.avatarUrl!)
                : null,
            child: post.author?.avatarUrl == null
                ? Text(
                    post.author?.displayName.substring(0, 1).toUpperCase() ?? '?',
                    style: TextStyle(
                      color: ThemeColors.primaryGreen,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  )
                : null,
          ),
          const SizedBox(width: 10),

          // Info autore
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      post.author?.displayName ?? 'Utente Sconosciuto',
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                    if (post.author?.isVerified == true) ...[
                      const SizedBox(width: 4),
                      Icon(
                        Icons.verified,
                        size: 16,
                        color: ThemeColors.primaryBlue,
                      ),
                    ],
                    if (post.author?.membershipLevel != null) ...[
                      const SizedBox(width: 4),
                      Text(
                        post.author!.membershipLevel.icon,
                        style: const TextStyle(fontSize: 12),
                      ),
                    ],
                  ],
                ),
                Row(
                  children: [
                    Text(
                      post.timeAgo,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(width: 8),

                    // Privacy indicator
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                      decoration: BoxDecoration(
                        color: _getPrivacyColor(post.privacy).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            post.privacy.icon,
                            style: const TextStyle(fontSize: 8),
                          ),
                          const SizedBox(width: 2),
                          Text(
                            post.privacy.displayName,
                            style: TextStyle(
                              fontSize: 9,
                              color: _getPrivacyColor(post.privacy),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 8),

                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Color(int.parse('0xFF${post.typeColor.substring(1)}')).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '${post.type.emoji} ${post.type.displayName}',
                        style: TextStyle(
                          fontSize: 10,
                          color: Color(int.parse('0xFF${post.typeColor.substring(1)}')),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Menu del post
          PopupMenuButton<String>(
            icon: Icon(Icons.more_vert, color: Colors.grey[600]),
            onSelected: (value) => _handlePostAction(post, value),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'save',
                child: Row(
                  children: [
                    Icon(Icons.bookmark_outline),
                    SizedBox(width: 8),
                    Text('Salva'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'share',
                child: Row(
                  children: [
                    Icon(Icons.share),
                    SizedBox(width: 8),
                    Text('Condividi'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'report',
                child: Row(
                  children: [
                    Icon(Icons.flag_outlined),
                    SizedBox(width: 8),
                    Text('Segnala'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Costruisce il contenuto del post
  Widget _buildPostContent(CommunityPost post) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Testo del post
          if (post.content.isNotEmpty)
            Text(
              post.content,
              style: const TextStyle(
                fontSize: 14,
                height: 1.4,
              ),
            ),

          // Tag degli amici
          if (post.taggedUsers.isNotEmpty) ...[
            const SizedBox(height: 8),
            _buildTaggedFriends(post),
          ],

          // Posizione del post
          if (post.location != null) ...[
            const SizedBox(height: 8),
            _buildPostLocation(post),
          ],
        ],
      ),
    );
  }

  /// Costruisce le immagini del post
  Widget _buildPostImages(CommunityPost post) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 12),
      height: 200,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: post.imageUrls.length,
        itemBuilder: (context, index) {
          return Container(
            margin: EdgeInsets.only(right: index < post.imageUrls.length - 1 ? 8 : 0),
            width: 200,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: DecorationImage(
                image: NetworkImage(post.imageUrls[index]),
                fit: BoxFit.cover,
              ),
            ),
          );
        },
      ),
    );
  }

  /// Costruisce i dati allegati
  Widget _buildAttachedData(CommunityPost post) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: ThemeColors.primaryGreen.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: ThemeColors.primaryGreen.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.attachment,
            color: ThemeColors.primaryGreen,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            'Contenuto allegato',
            style: TextStyle(
              color: ThemeColors.primaryGreen,
              fontWeight: FontWeight.w500,
            ),
          ),
          const Spacer(),
          Icon(
            Icons.arrow_forward_ios,
            color: ThemeColors.primaryGreen,
            size: 16,
          ),
        ],
      ),
    );
  }

  /// Costruisce il footer del post
  Widget _buildPostFooter(CommunityPost post) {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          // Like semplice
          _buildActionButton(
            icon: Icons.favorite_outline,
            activeIcon: Icons.favorite,
            count: post.likesCount,
            isActive: false, // TODO: Implementare logica di like
            onTap: () => _handleLike(post),
            color: Colors.red,
          ),
          const SizedBox(width: 16),

          // Commenti con conteggio aggiornato
          Consumer<CommentService>(
            builder: (context, commentService, child) {
              final commentsCount = commentService.getCommentsCount(post.id);
              return _buildActionButton(
                icon: Icons.chat_bubble_outline,
                count: commentsCount > 0 ? commentsCount : post.commentsCount,
                onTap: () => _showComments(post),
                color: ThemeColors.primaryBlue,
              );
            },
          ),
          const SizedBox(width: 16),

          // Condivisioni
          _buildActionButton(
            icon: Icons.share_outlined,
            count: post.sharesCount,
            onTap: () => _handleShare(post),
            color: ThemeColors.primaryGreen,
          ),

          const Spacer(),

          // Engagement rate
          if (post.engagementRate > 0)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${post.engagementRate.toStringAsFixed(1)} engagement',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey[600],
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Costruisce un pulsante di azione
  Widget _buildActionButton({
    required IconData icon,
    IconData? activeIcon,
    required int count,
    bool isActive = false,
    required VoidCallback onTap,
    required Color color,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isActive && activeIcon != null ? activeIcon : icon,
              size: 20,
              color: isActive ? color : Colors.grey[600],
            ),
            if (count > 0) ...[
              const SizedBox(width: 4),
              Text(
                count.toString(),
                style: TextStyle(
                  fontSize: 12,
                  color: isActive ? color : Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Gestisce le azioni del post
  void _handlePostAction(CommunityPost post, String action) {
    switch (action) {
      case 'save':
        // Implementa il salvataggio
        break;
      case 'share':
        _handleShare(post);
        break;
      case 'report':
        // Implementa la segnalazione
        break;
    }
  }



  /// Mostra i commenti del post
  void _showComments(CommunityPost post) {
    showCenteredCommentsModal(context, post);
  }



  /// Gestisce la condivisione del post
  void _handleShare(CommunityPost post) async {
    try {
      final notificationService = context.read<NotificationService>();

      // TODO: Implementa la logica di condivisione nel backend

      // Genera notifica per l'autore del post (se non è l'utente corrente)
      if (post.author != null) {
        await notificationService.createShareNotification(
          postAuthorId: post.author!.id,
          postId: post.id,
          actorUserId: 'current_user_id', // TODO: Ottieni dall'auth service
          actorUserName: 'Dr. Giovanni Staffilano', // TODO: Ottieni dall'auth service
          actorUserAvatar: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=150&h=150&fit=crop&crop=face',
        );
      }

      // Mostra feedback visivo
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('🔄 Post condiviso!'),
          duration: const Duration(seconds: 1),
          backgroundColor: ThemeColors.primaryGreen,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Errore: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Costruisce la visualizzazione degli amici taggati
  Widget _buildTaggedFriends(CommunityPost post) {
    if (post.taggedUsers.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      decoration: BoxDecoration(
        color: ThemeColors.primaryBlue.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: ThemeColors.primaryBlue.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.person_pin,
            size: 14,
            color: ThemeColors.primaryBlue,
          ),
          const SizedBox(width: 6),
          Text(
            'Con ',
            style: TextStyle(
              fontSize: 12,
              color: ThemeColors.primaryBlue,
              fontWeight: FontWeight.w500,
            ),
          ),
          Expanded(
            child: Wrap(
              spacing: 4,
              runSpacing: 2,
              children: post.taggedUsers.asMap().entries.map((entry) {
                final index = entry.key;
                final user = entry.value;
                final isLast = index == post.taggedUsers.length - 1;

                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    GestureDetector(
                      onTap: () {
                        // TODO: Naviga al profilo dell'utente
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Profilo di ${user.displayName}'),
                            duration: const Duration(seconds: 1),
                          ),
                        );
                      },
                      child: Text(
                        '@${user.username}',
                        style: TextStyle(
                          fontSize: 12,
                          color: ThemeColors.primaryBlue,
                          fontWeight: FontWeight.w600,
                          decoration: TextDecoration.none,
                        ),
                      ),
                    ),
                    if (!isLast)
                      Text(
                        ', ',
                        style: TextStyle(
                          fontSize: 12,
                          color: ThemeColors.primaryBlue,
                        ),
                      ),
                  ],
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  /// Costruisce la visualizzazione della posizione del post
  Widget _buildPostLocation(CommunityPost post) {
    if (post.location == null) return const SizedBox.shrink();

    final locationName = post.location!['name'] as String? ?? 'Posizione sconosciuta';
    final locationAddress = post.location!['address'] as String? ?? '';

    return GestureDetector(
      onTap: () {
        // TODO: Apri mappa o dettagli posizione
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('📍 Posizione: $locationName'),
            duration: const Duration(seconds: 2),
            action: SnackBarAction(
              label: 'Mappa',
              onPressed: () {
                // TODO: Implementa apertura mappa
              },
            ),
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        decoration: BoxDecoration(
          color: const Color(0xFFF5533D).withOpacity(0.05),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: const Color(0xFFF5533D).withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.location_on,
              size: 14,
              color: const Color(0xFFF5533D),
            ),
            const SizedBox(width: 6),
            Flexible(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    locationName,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFFF5533D),
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (locationAddress.isNotEmpty)
                    Text(
                      locationAddress,
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.grey[600],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Ottieni il colore per l'indicatore di privacy
  Color _getPrivacyColor(PostPrivacy privacy) {
    switch (privacy) {
      case PostPrivacy.public:
        return ThemeColors.primaryGreen;
      case PostPrivacy.friends:
        return ThemeColors.primaryBlue;
      case PostPrivacy.private:
        return const Color(0xFFF5533D); // Rosso per privato
    }
  }

  /// Gestisce il like di un post
  void _handleLike(CommunityPost post) {
    // TODO: Implementare logica di like
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('❤️ Like - Coming soon!'),
        duration: Duration(seconds: 1),
      ),
    );
  }
}
