import 'dart:math';
import '../models/food.dart';
import '../models/diet_plan.dart';
import '../models/user_profile.dart';

/// Servizio per il calcolo preciso delle porzioni secondo la logica imperativa
class PortionCalculatorService {
  static const double _calorieTolerancePercent = 0.05; // ±5% tolleranza calorica

  /// A. Distribuzione Calorica e Macronutrienti tra i Pasti
  static Map<String, double> getMealCalorieDistribution(int mealsPerDay, {String? preference}) {
    switch (mealsPerDay) {
      case 3:
        return _getThreeMealDistribution(preference);
      case 4:
        return _getFourMealDistribution(preference);
      case 5:
        return _getFiveMealDistribution(preference);
      case 6:
        return _getSixMealDistribution(preference);
      default:
        return _getThreeMealDistribution(preference);
    }
  }

  static Map<String, double> _getThreeMealDistribution(String? preference) {
    switch (preference) {
      case 'colazione_abbondante':
        return {'breakfast': 0.30, 'lunch': 0.35, 'dinner': 0.35};
      case 'cena_leggera':
        return {'breakfast': 0.25, 'lunch': 0.50, 'dinner': 0.25};
      default:
        return {'breakfast': 0.25, 'lunch': 0.35, 'dinner': 0.30};
    }
  }

  static Map<String, double> _getFourMealDistribution(String? preference) {
    switch (preference) {
      case 'colazione_abbondante':
        return {'breakfast': 0.30, 'snack': 0.10, 'lunch': 0.30, 'dinner': 0.30};
      default:
        return {'breakfast': 0.20, 'snack': 0.10, 'lunch': 0.35, 'dinner': 0.25};
    }
  }

  static Map<String, double> _getFiveMealDistribution(String? preference) {
    return {
      'breakfast': 0.20,
      'morning_snack': 0.10,
      'lunch': 0.30,
      'afternoon_snack': 0.10,
      'dinner': 0.30
    };
  }

  static Map<String, double> _getSixMealDistribution(String? preference) {
    return {
      'breakfast': 0.20,
      'morning_snack': 0.08,
      'lunch': 0.30,
      'afternoon_snack': 0.08,
      'dinner': 0.25,
      'evening_snack': 0.09
    };
  }

  /// Distribuzione prioritaria dei macronutrienti per tipo di pasto
  static Map<String, double> getMacroDistributionForMeal(String mealType, Goal userGoal) {
    switch (mealType) {
      case 'breakfast':
        return _getBreakfastMacroDistribution(userGoal);
      case 'lunch':
        return _getLunchMacroDistribution(userGoal);
      case 'dinner':
        return _getDinnerMacroDistribution(userGoal);
      case 'snack':
      case 'morning_snack':
      case 'afternoon_snack':
      case 'evening_snack':
        return _getSnackMacroDistribution(userGoal);
      default:
        return {'proteins': 0.25, 'carbs': 0.45, 'fats': 0.30};
    }
  }

  static Map<String, double> _getBreakfastMacroDistribution(Goal goal) {
    switch (goal) {
      case Goal.weightLoss:
        return {'proteins': 0.30, 'carbs': 0.40, 'fats': 0.30}; // Più proteine per sazietà
      case Goal.weightGain:
        return {'proteins': 0.25, 'carbs': 0.50, 'fats': 0.25}; // Più carboidrati per energia
      default:
        return {'proteins': 0.25, 'carbs': 0.45, 'fats': 0.30};
    }
  }

  static Map<String, double> _getLunchMacroDistribution(Goal goal) {
    // Pranzo: distribuzione bilanciata con focus su energia sostenuta
    return {'proteins': 0.25, 'carbs': 0.45, 'fats': 0.30};
  }

  static Map<String, double> _getDinnerMacroDistribution(Goal goal) {
    switch (goal) {
      case Goal.weightLoss:
        return {'proteins': 0.35, 'carbs': 0.30, 'fats': 0.35}; // Meno carboidrati la sera
      default:
        return {'proteins': 0.30, 'carbs': 0.35, 'fats': 0.35};
    }
  }

  static Map<String, double> _getSnackMacroDistribution(Goal goal) {
    // Spuntini: focus su proteine e grassi sani per sazietà
    return {'proteins': 0.30, 'carbs': 0.35, 'fats': 0.35};
  }

  /// B. Logica di Porzionamento Intra-Pasto ("Piatto Intelligente Quantificato")
  static List<FoodPortion> calculateOptimalPortions({
    required List<Food> selectedFoods,
    required int targetCalories,
    required Map<String, int> targetMacros,
    required String mealType,
  }) {
    print('🧮 CALCOLO PORZIONI OTTIMALI per $mealType');
    print('   Target: ${targetCalories}kcal, P:${targetMacros['proteins']}g, C:${targetMacros['carbs']}g, G:${targetMacros['fats']}g');

    if (selectedFoods.isEmpty) return [];

    // Classifica gli alimenti per ruolo nutrizionale
    final proteinSources = <Food>[];
    final carbSources = <Food>[];
    final vegetableSources = <Food>[];
    final fatSources = <Food>[];

    for (final food in selectedFoods) {
      final role = _classifyFoodRole(food);
      switch (role) {
        case 'protein':
          proteinSources.add(food);
          break;
        case 'carb':
          carbSources.add(food);
          break;
        case 'vegetable':
          vegetableSources.add(food);
          break;
        case 'fat':
          fatSources.add(food);
          break;
      }
    }

    final portions = <FoodPortion>[];

    // STEP 1: Calcola porzione proteica
    if (proteinSources.isNotEmpty) {
      final proteinPortion = _calculateProteinPortion(
        proteinSources.first,
        targetMacros['proteins']!,
        mealType,
      );
      portions.add(proteinPortion);
      print('   ✅ Proteine: ${proteinPortion.food.name} ${proteinPortion.grams}g (${proteinPortion.proteins.round()}g P)');
    }

    // STEP 2: Calcola porzione di carboidrati
    if (carbSources.isNotEmpty) {
      final currentCarbs = portions.fold(0.0, (sum, p) => sum + p.carbs);
      final remainingCarbs = max(0, targetMacros['carbs']! - currentCarbs);
      
      final carbPortion = _calculateCarbPortion(
        carbSources.first,
        remainingCarbs.round(),
        mealType,
      );
      portions.add(carbPortion);
      print('   ✅ Carboidrati: ${carbPortion.food.name} ${carbPortion.grams}g (${carbPortion.carbs.round()}g C)');
    }

    // STEP 3: Calcola porzione di verdure (abbondante)
    if (vegetableSources.isNotEmpty) {
      final vegetablePortion = _calculateVegetablePortion(
        vegetableSources.first,
        mealType,
      );
      portions.add(vegetablePortion);
      print('   ✅ Verdure: ${vegetablePortion.food.name} ${vegetablePortion.grams}g');
    }

    // STEP 4: Calcola grassi aggiunti
    if (fatSources.isNotEmpty) {
      final currentFats = portions.fold(0.0, (sum, p) => sum + p.fats);
      final remainingFats = max(0, targetMacros['fats']! - currentFats);
      
      final fatPortion = _calculateFatPortion(
        fatSources.first,
        remainingFats.round(),
        mealType,
      );
      portions.add(fatPortion);
      print('   ✅ Grassi: ${fatPortion.food.name} ${fatPortion.grams}g (${fatPortion.fats.round()}g G)');
    }

    // STEP 5: Bilanciamento finale
    final adjustedPortions = _balancePortions(portions, targetCalories, targetMacros);

    // Verifica finale
    final totalCalories = adjustedPortions.fold(0, (sum, p) => sum + p.calories);
    final totalProteins = adjustedPortions.fold(0.0, (sum, p) => sum + p.proteins);
    final totalCarbs = adjustedPortions.fold(0.0, (sum, p) => sum + p.carbs);
    final totalFats = adjustedPortions.fold(0.0, (sum, p) => sum + p.fats);

    print('   📊 RISULTATO: ${totalCalories}kcal, P:${totalProteins.round()}g, C:${totalCarbs.round()}g, G:${totalFats.round()}g');
    print('   📈 Scostamento: ${((totalCalories - targetCalories) / targetCalories * 100).round()}%');

    return adjustedPortions;
  }

  /// Classifica il ruolo nutrizionale di un alimento
  static String _classifyFoodRole(Food food) {
    // Proteine: >15g proteine per 100g
    if (food.proteins > 15) return 'protein';
    
    // Carboidrati: >50g carboidrati per 100g
    if (food.carbs > 50) return 'carb';
    
    // Verdure: <50 kcal per 100g e ricche di fibre
    if (food.calories < 50 && food.fiber > 2) return 'vegetable';
    
    // Grassi: >70% calorie da grassi
    final fatCalories = food.fats * 9;
    if (fatCalories / food.calories > 0.7) return 'fat';
    
    // Default: considera come fonte mista
    if (food.proteins > food.carbs && food.proteins > food.fats) return 'protein';
    if (food.carbs > food.proteins && food.carbs > food.fats) return 'carb';
    return 'fat';
  }

  /// Calcola porzione proteica
  static FoodPortion _calculateProteinPortion(Food food, int targetProteins, String mealType) {
    // Calcola grammi necessari per raggiungere target proteico
    int grams = food.proteins > 0 ? (targetProteins / food.proteins * 100).round() : 100;
    
    // Applica limiti realistici per tipo di alimento
    grams = _applyProteinPortionLimits(food, grams, mealType);
    
    return FoodPortion(food: food, grams: grams);
  }

  /// Calcola porzione di carboidrati
  static FoodPortion _calculateCarbPortion(Food food, int targetCarbs, String mealType) {
    int grams = food.carbs > 0 ? (targetCarbs / food.carbs * 100).round() : 80;
    
    // Limiti per cereali e carboidrati
    if (food.name.toLowerCase().contains('pasta') || 
        food.name.toLowerCase().contains('riso')) {
      grams = grams.clamp(60, 120); // Peso a crudo
    } else if (food.name.toLowerCase().contains('pane')) {
      grams = grams.clamp(40, 100);
    } else {
      grams = grams.clamp(50, 150);
    }
    
    return FoodPortion(food: food, grams: grams);
  }

  /// Calcola porzione di verdure (abbondante)
  static FoodPortion _calculateVegetablePortion(Food food, String mealType) {
    // Verdure: porzioni abbondanti (150-250g minimo)
    int grams = 200; // Base generosa
    
    // Aggiusta in base al tipo di verdura
    if (food.name.toLowerCase().contains('spinaci') ||
        food.name.toLowerCase().contains('lattuga')) {
      grams = 150; // Verdure a foglia: meno dense
    } else if (food.name.toLowerCase().contains('broccoli') ||
               food.name.toLowerCase().contains('cavolfiore')) {
      grams = 200; // Crucifere: porzione standard
    } else if (food.name.toLowerCase().contains('carote') ||
               food.name.toLowerCase().contains('zucchine')) {
      grams = 250; // Verdure dolci: porzione più abbondante
    }
    
    return FoodPortion(food: food, grams: grams);
  }

  /// Calcola porzione di grassi
  static FoodPortion _calculateFatPortion(Food food, int targetFats, String mealType) {
    int grams = food.fats > 0 ? (targetFats / food.fats * 100).round() : 15;
    
    // Limiti molto specifici per i grassi
    if (food.name.toLowerCase().contains('olio')) {
      grams = grams.clamp(5, 20); // Olio: 5-20g (1-4 cucchiaini)
    } else if (food.name.toLowerCase().contains('mandorle') ||
               food.name.toLowerCase().contains('noci')) {
      grams = grams.clamp(15, 30); // Frutta secca: 15-30g
    } else if (food.name.toLowerCase().contains('avocado')) {
      grams = grams.clamp(50, 100); // Avocado: 1/2-1 frutto
    } else {
      grams = grams.clamp(10, 25); // Altri grassi
    }
    
    return FoodPortion(food: food, grams: grams);
  }

  /// Applica limiti realistici per porzioni proteiche
  static int _applyProteinPortionLimits(Food food, int grams, String mealType) {
    final foodName = food.name.toLowerCase();
    
    if (foodName.contains('uovo')) {
      return grams.clamp(50, 120); // 1-2 uova (50-60g per uovo)
    } else if (foodName.contains('pollo') || foodName.contains('tacchino')) {
      return grams.clamp(80, 150); // Carne bianca
    } else if (foodName.contains('manzo') || foodName.contains('maiale')) {
      return grams.clamp(80, 120); // Carne rossa: porzioni più piccole
    } else if (foodName.contains('pesce') || foodName.contains('salmone')) {
      return grams.clamp(100, 180); // Pesce: porzioni generose
    } else if (foodName.contains('lenticchie') || 
               foodName.contains('ceci') || 
               foodName.contains('fagioli')) {
      return grams.clamp(80, 150); // Legumi secchi
    } else if (foodName.contains('yogurt')) {
      return grams.clamp(125, 250); // 1-2 vasetti
    } else if (foodName.contains('formaggio')) {
      return grams.clamp(30, 80); // Formaggi: porzioni piccole
    } else {
      return grams.clamp(80, 150); // Default proteine
    }
  }

  /// Bilanciamento finale delle porzioni
  static List<FoodPortion> _balancePortions(
    List<FoodPortion> portions,
    int targetCalories,
    Map<String, int> targetMacros,
  ) {
    final adjustedPortions = List<FoodPortion>.from(portions);
    
    // Calcola totali attuali
    int currentCalories = adjustedPortions.fold(0, (sum, p) => sum + p.calories);
    
    // Se lo scostamento è entro la tolleranza, non modificare
    final calorieDeviation = (currentCalories - targetCalories).abs() / targetCalories;
    if (calorieDeviation <= _calorieTolerancePercent) {
      return adjustedPortions;
    }
    
    // Aggiustamento proporzionale
    final adjustmentFactor = targetCalories / currentCalories;
    
    for (int i = 0; i < adjustedPortions.length; i++) {
      final currentPortion = adjustedPortions[i];
      final newGrams = (currentPortion.grams * adjustmentFactor).round();
      
      // Applica limiti minimi e massimi
      final minGrams = _getMinimumPortion(currentPortion.food);
      final maxGrams = _getMaximumPortion(currentPortion.food);
      final clampedGrams = newGrams.clamp(minGrams, maxGrams);
      
      adjustedPortions[i] = FoodPortion(
        food: currentPortion.food,
        grams: clampedGrams,
      );
    }
    
    return adjustedPortions;
  }

  /// Porzione minima ragionevole per alimento
  static int _getMinimumPortion(Food food) {
    final foodName = food.name.toLowerCase();
    
    if (foodName.contains('olio')) return 5;
    if (foodName.contains('uovo')) return 50;
    if (foodName.contains('verdura') || foodName.contains('spinaci')) return 100;
    return 30; // Default
  }

  /// Porzione massima ragionevole per alimento
  static int _getMaximumPortion(Food food) {
    final foodName = food.name.toLowerCase();
    
    if (foodName.contains('olio')) return 25;
    if (foodName.contains('formaggio')) return 100;
    if (foodName.contains('carne') || foodName.contains('pesce')) return 200;
    if (foodName.contains('verdura')) return 400;
    return 200; // Default
  }

  /// E. Arrotondamento a valori pratici
  static int roundToPracticalValue(int grams, Food food) {
    final foodName = food.name.toLowerCase();
    
    // Arrotondamenti specifici per tipo di alimento
    if (foodName.contains('olio')) {
      // Olio: multipli di 5g (1 cucchiaino ≈ 5g)
      return ((grams / 5).round() * 5).clamp(5, 25);
    } else if (foodName.contains('uovo')) {
      // Uova: multipli di 50-60g (1 uovo)
      final numEggs = (grams / 55).round().clamp(1, 2);
      return numEggs * 55;
    } else if (foodName.contains('pasta') || foodName.contains('riso')) {
      // Cereali: multipli di 10g
      return ((grams / 10).round() * 10).clamp(60, 120);
    } else if (foodName.contains('yogurt')) {
      // Yogurt: multipli di 125g (1 vasetto)
      final numCups = (grams / 125).round().clamp(1, 2);
      return numCups * 125;
    } else {
      // Altri alimenti: multipli di 5g
      return ((grams / 5).round() * 5);
    }
  }
}
