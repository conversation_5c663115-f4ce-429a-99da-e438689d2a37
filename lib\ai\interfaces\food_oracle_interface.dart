import 'dart:io';
import '../models/food_oracle_models.dart';
import '../../models/food.dart';
import '../../models/user_profile.dart';

/// Interfaccia per il sistema Food Oracle che analizza le immagini di alimenti
abstract class FoodOracleInterface {
  /// Inizializza il sistema Food Oracle
  Future<void> initialize();

  /// Analizza un'immagine di cibo e restituisce i risultati
  /// [image] - L'immagine da analizzare
  /// [userProfile] - Il profilo dell'utente per personalizzare l'analisi
  /// [mealType] - Il tipo di pasto (colazione, pranzo, cena, spuntino)
  Future<FoodOracleAnalysisResult> analyzeImage(
    File image, 
    UserProfile userProfile, 
    {String? mealType}
  );

  /// Analizza un'immagine di cibo già presente nel dispositivo
  /// [imagePath] - Il percorso dell'immagine da analizzare
  /// [userProfile] - Il profilo dell'utente per personalizzare l'analisi
  /// [mealType] - Il tipo di pasto (colazione, pranzo, cena, spuntino)
  Future<FoodOracleAnalysisResult> analyzeImageFromPath(
    String imagePath, 
    UserProfile userProfile, 
    {String? mealType}
  );

  /// Corregge i risultati dell'analisi con input dell'utente
  /// [analysisResult] - I risultati originali dell'analisi
  /// [userCorrections] - Le correzioni fornite dall'utente
  Future<FoodOracleAnalysisResult> correctAnalysis(
    FoodOracleAnalysisResult analysisResult,
    FoodOracleUserCorrections userCorrections
  );

  /// Salva i risultati dell'analisi nel diario alimentare dell'utente
  /// [analysisResult] - I risultati dell'analisi da salvare
  /// [userId] - L'ID dell'utente
  /// [date] - La data del pasto (default: oggi)
  Future<bool> saveAnalysisToFoodDiary(
    FoodOracleAnalysisResult analysisResult,
    String userId,
    {DateTime? date}
  );

  /// Ottiene suggerimenti per migliorare il pasto analizzato
  /// [analysisResult] - I risultati dell'analisi
  /// [userProfile] - Il profilo dell'utente
  Future<List<FoodOracleSuggestion>> getSuggestions(
    FoodOracleAnalysisResult analysisResult,
    UserProfile userProfile
  );

  /// Verifica se il sistema è inizializzato e pronto per l'uso
  bool isInitialized();

  /// Ottiene statistiche sul sistema Food Oracle
  Future<Map<String, dynamic>> getStats();
}
