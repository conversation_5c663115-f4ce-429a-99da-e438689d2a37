# ⚡ CHECKLIST RAPIDO TEST SUPABASE

## 🔍 VERIFICA IMMEDIATA

### **1. Controlla URL nel Codice**
```dart
// In lib/config/supabase_config.dart
static const String supabaseUrl = 'https://rnunzfuibfjpritvcfmj.supabase.co';
```
✅ **CORRETTO** - URL reale presente

### **2. Controlla Anon Key**
```dart
static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';
```
✅ **CORRETTO** - Anon key presente

## 🌐 VERIFICA SUPABASE DASHBOARD

### **Authentication > URL Configuration**
- [ ] **Site URL**: `https://localhost:3000`
- [ ] **Redirect URLs**:
  - [ ] `https://localhost:3000/auth/callback`
  - [ ] `https://rnunzfuibfjpritvcfmj.supabase.co/auth/v1/callback`
  - [ ] `io.supabase.nutriplan://login-callback/`

### **Authentication > Providers**
- [ ] **Email**: Abilitato
- [ ] **Google**: Abilitato con Client ID/Secret

### **Table Editor**
- [ ] **Tabella `profiles`**: Esiste
- [ ] **RLS**: Abilitato su `profiles`
- [ ] **Policies**: Create per SELECT, INSERT, UPDATE

## 🔧 GOOGLE CLOUD CONSOLE

### **APIs & Services > Credentials**
- [ ] **OAuth 2.0 Client ID**: Configurato
- [ ] **Authorized redirect URIs**:
  - [ ] `https://rnunzfuibfjpritvcfmj.supabase.co/auth/v1/callback`

## 📱 TEST RAPIDO

### **1. Test Connessione**
```bash
# Testa se l'URL Supabase risponde
curl https://rnunzfuibfjpritvcfmj.supabase.co/rest/v1/
```

### **2. Test Registrazione**
1. Apri app
2. Vai a registrazione
3. Inserisci: `<EMAIL>` / `password123`
4. Controlla log per errori

### **3. Test Login Google**
1. Clicca "Continua con Google"
2. Dovrebbe aprire browser/webview
3. Controlla se redirect funziona

## 🚨 ERRORI COMUNI

### **"DNS_PROBE_FINISHED_NXDOMAIN"**
- ❌ **Causa**: URL sbagliato nel codice
- ✅ **Soluzione**: Verifica `supabaseUrl` in config

### **"SocketException: Failed host lookup"**
- ❌ **Causa**: Problema connessione/URL
- ✅ **Soluzione**: Controlla internet + URL

### **"Invalid redirect URL"**
- ❌ **Causa**: Redirect URL non configurato
- ✅ **Soluzione**: Aggiungi URL in Supabase Dashboard

## 🎯 PRIORITÀ AZIONI

### **ALTA PRIORITÀ**
1. ✅ Verifica URL in `supabase_config.dart`
2. 🔄 Configura redirect URLs in Supabase
3. 🔄 Configura Google OAuth

### **MEDIA PRIORITÀ**
4. 🔄 Crea tabella `profiles`
5. 🔄 Configura RLS policies

### **BASSA PRIORITÀ**
6. 🔄 Test funzionalità avanzate
7. 🔄 Ottimizzazioni performance

## 📞 SE HAI PROBLEMI

### **Condividi questi dettagli:**
1. **Screenshot** errori specifici
2. **Log completi** dall'app
3. **Configurazione** Supabase Dashboard
4. **URL** che stai usando

### **Informazioni utili:**
- **Progetto Supabase**: `rnunzfuibfjpritvcfmj`
- **URL**: `https://rnunzfuibfjpritvcfmj.supabase.co`
- **Anon Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
