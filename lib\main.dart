import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'screens/add_meal_screen.dart';
import 'screens/meal_detail_screen.dart';
import 'screens/stats_screen.dart';
import 'screens/meal_plan_screen.dart';
import 'screens/food_admin_screen.dart';
import 'screens/home_screen.dart';
import 'screens/modern_home_screen.dart';
import 'screens/advanced_diet_generator_screen.dart';
import 'screens/ultra_advanced_diet_screen.dart';
import 'services/storage_service.dart';
import 'services/food_database_service.dart';
import 'services/service_locator.dart';
import 'services/italian_food_database_initializer.dart';
import 'models/meal.dart';
import 'models/food.dart';
import 'controllers/advanced_diet_controller.dart';
import 'controllers/welljourney_controller.dart';
import 'services/notification_service.dart';
import 'services/post_interaction_service.dart';
import 'services/comment_service.dart';
import 'services/friendship_service.dart';
import 'services/community_profile_service.dart';
import 'config/supabase_config.dart';
import 'theme/app_theme.dart';
import 'theme/new_app_theme.dart';
import 'theme/dr_staffilano_theme.dart';
import 'widgets/meal_card.dart';
import 'widgets/calories_summary_card.dart';
import 'widgets/water_tracking_card.dart';
import 'widgets/motivational_card.dart';
import 'constants/image_constants.dart';
import 'constants/app_constants.dart';
import 'widgets/auth_wrapper.dart';
import 'debug/supabase_debug_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    print('🚀 Inizializzazione app Dr. Staffilano...');

    // Inizializza Supabase
    print('🔗 Inizializzazione Supabase...');
    await SupabaseConfig.initialize();
    print('✅ Supabase inizializzato con successo');

    // Inizializza i servizi in ordine sequenziale
    await setupServiceLocator();
    print('✅ Service Locator inizializzato');

    // Inizializza il database con alimenti italiani
    await ItalianFoodDatabaseInitializer.initializeItalianFoods();
    print('✅ Database alimenti inizializzato');

    runApp(
      MultiProvider(
        providers: [
          ChangeNotifierProvider(
            create: (_) => serviceLocator<AdvancedDietController>(),
          ),
          ChangeNotifierProvider(
            create: (_) {
              try {
                final controller = serviceLocator<WellJourneyController>();
                // Inizializza il controller in modo asincrono e sicuro
                Future.microtask(() async {
                  try {
                    await controller.initialize();
                    print('✅ WellJourneyController inizializzato con successo');
                  } catch (e) {
                    print('⚠️ Errore inizializzazione WellJourneyController: $e');
                    // Il controller continuerà a funzionare con valori di default
                  }
                });
                return controller;
              } catch (e) {
                print('❌ Errore critico nella creazione WellJourneyController: $e');
                // Crea un controller di fallback
                final fallbackController = WellJourneyController();
                Future.microtask(() async {
                  try {
                    await fallbackController.initialize();
                  } catch (e) {
                    print('⚠️ Anche il controller di fallback ha avuto problemi: $e');
                  }
                });
                return fallbackController;
              }
            },
          ),
          ChangeNotifierProvider(
            create: (_) {
              final notificationService = NotificationService();
              // Inizializza il servizio notifiche in modo asincrono
              Future.microtask(() async {
                try {
                  await notificationService.initialize();
                  print('✅ NotificationService inizializzato con successo');
                } catch (e) {
                  print('⚠️ Errore inizializzazione NotificationService: $e');
                }
              });
              return notificationService;
            },
          ),
          ChangeNotifierProvider(
            create: (_) {
              final interactionService = PostInteractionService();
              // Inizializza il servizio interazioni in modo asincrono
              Future.microtask(() async {
                try {
                  await interactionService.initialize();
                  print('✅ PostInteractionService inizializzato con successo');
                } catch (e) {
                  print('⚠️ Errore inizializzazione PostInteractionService: $e');
                }
              });
              return interactionService;
            },
          ),
          ChangeNotifierProvider(
            create: (_) {
              final commentService = CommentService();
              // Inizializza il servizio commenti in modo asincrono
              Future.microtask(() async {
                try {
                  await commentService.initialize();
                  print('✅ CommentService inizializzato con successo');
                } catch (e) {
                  print('⚠️ Errore inizializzazione CommentService: $e');
                }
              });
              return commentService;
            },
          ),
          ChangeNotifierProvider(
            create: (_) {
              final friendshipService = FriendshipService();
              // Inizializza il servizio amicizie in modo asincrono
              Future.microtask(() async {
                try {
                  await friendshipService.initialize();
                  print('✅ FriendshipService inizializzato con successo');
                } catch (e) {
                  print('⚠️ Errore inizializzazione FriendshipService: $e');
                }
              });
              return friendshipService;
            },
          ),
          ChangeNotifierProvider(
            create: (_) {
              final profileService = CommunityProfileService();
              // Inizializza il servizio profili in modo asincrono
              Future.microtask(() async {
                try {
                  await profileService.initialize();
                  print('✅ CommunityProfileService inizializzato con successo');
                } catch (e) {
                  print('⚠️ Errore inizializzazione CommunityProfileService: $e');
                }
              });
              return profileService;
            },
          ),
        ],
        child: const MyApp(),
      ),
    );

    print('✅ App avviata con successo');
  } catch (e) {
    print('❌ Errore durante l\'inizializzazione: $e');
    // Avvia l'app comunque con configurazione di fallback
    runApp(const MyApp());
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Inizializza il supporto per la localizzazione delle date
    initializeDateFormatting('it_IT', null);

    return MaterialApp(
      title: AppConstants.appName,
      theme: DrStaffilanoTheme.themeData,
      darkTheme: DrStaffilanoTheme.themeData, // Use light theme for now
      themeMode: ThemeMode.light,
      home: const SupabaseDebugScreen(), // Temporaneo per debug
      debugShowCheckedModeBanner: false,
      // Enable responsive text scaling
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaleFactor: MediaQuery.of(context).textScaleFactor.clamp(0.8, 1.4),
          ),
          child: child!,
        );
      },
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  StorageService? _storageService;

  // Lista dei pasti
  List<Meal> _pasti = [];

  int _calorieGiornaliere = 2000;
  int _calorieConsumate = 0;
  bool _isLoading = true;

  // Obiettivi macronutrienti (percentuali)
  Map<String, double> _macroNutrientiTarget = {
    'proteine': 30.0,
    'carboidrati': 40.0,
    'grassi': 30.0,
  };

  // Monitoraggio acqua
  int _bicchieriAcqua = 0;
  int _obiettivoBicchieri = 8;

  // Suggerimento del giorno
  late String _suggerimentoDelGiorno;

  @override
  void initState() {
    super.initState();
    _suggerimentoDelGiorno = MotivationalCard.getRandomTip();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    try {
      _storageService = await StorageService.getInstance();
      await _caricaDati();
    } catch (e) {
      print('Errore nell\'inizializzazione dei servizi: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _caricaDati() async {
    if (_storageService == null) return;

    setState(() {
      _isLoading = true;
    });

    // Carica i pasti salvati
    final pasti = await _storageService!.caricaPasti();

    // Se non ci sono pasti salvati, crea alcuni pasti di esempio
    if (pasti.isEmpty) {
      pasti.addAll([
        Meal(
          nome: 'Colazione',
          orario: '8:00',
          calorie: 350,
          proteine: 15.0,
          carboidrati: 45.0,
          grassi: 10.0,
          completato: false,
        ),
        Meal(
          nome: 'Spuntino',
          orario: '10:30',
          calorie: 150,
          proteine: 5.0,
          carboidrati: 20.0,
          grassi: 5.0,
          completato: false,
        ),
        Meal(
          nome: 'Pranzo',
          orario: '13:00',
          calorie: 650,
          proteine: 30.0,
          carboidrati: 70.0,
          grassi: 20.0,
          completato: false,
        ),
        Meal(
          nome: 'Merenda',
          orario: '16:30',
          calorie: 200,
          proteine: 10.0,
          carboidrati: 25.0,
          grassi: 5.0,
          completato: false,
        ),
        Meal(
          nome: 'Cena',
          orario: '20:00',
          calorie: 550,
          proteine: 25.0,
          carboidrati: 60.0,
          grassi: 15.0,
          completato: false,
        ),
      ]);
    }

    // Carica l'obiettivo calorico
    final calorie = await _storageService!.caricaCalorieGiornaliere();

    // Carica gli obiettivi di macronutrienti
    final macroNutrienti = await _storageService!.caricaMacroNutrienti();

    // Carica i dati dell'acqua
    final bicchieriAcqua = await _storageService!.caricaBicchieriAcqua();
    final obiettivoBicchieri = await _storageService!.caricaObiettivoBicchieri();

    setState(() {
      _pasti = pasti;
      _calorieGiornaliere = calorie;
      _macroNutrientiTarget = macroNutrienti;
      _bicchieriAcqua = bicchieriAcqua;
      _obiettivoBicchieri = obiettivoBicchieri;
      _aggiornaCalorieConsumate();
      _isLoading = false;
    });
  }

  Future<void> _salvaDati() async {
    if (_storageService == null) return;

    await _storageService!.salvaPasti(_pasti);
    await _storageService!.salvaCalorieGiornaliere(_calorieGiornaliere);
    await _storageService!.salvaMacroNutrienti(_macroNutrientiTarget);
    await _storageService!.salvaBicchieriAcqua(_bicchieriAcqua);
    await _storageService!.salvaObiettivoBicchieri(_obiettivoBicchieri);

    // Salva lo storico delle calorie per oggi
    final oggi = DateFormat('yyyy-MM-dd').format(DateTime.now());
    await _storageService!.aggiungiAlloStorico(oggi, _calorieConsumate);
  }

  // Aggiorna il numero di bicchieri d'acqua
  void _aggiornaBicchieriAcqua(int nuovoBicchieri) {
    setState(() {
      _bicchieriAcqua = nuovoBicchieri;
      _salvaDati();
    });
  }

  void _togglePastoCompletato(int index) {
    setState(() {
      _pasti[index] = _pasti[index].copyWith(
        completato: !_pasti[index].completato,
      );
      _aggiornaCalorieConsumate();
      _salvaDati();
    });
  }

  void _aggiornaCalorieConsumate() {
    int totale = 0;
    for (var pasto in _pasti) {
      if (pasto.completato) {
        totale += pasto.calorie;
      }
    }
    _calorieConsumate = totale;
  }

  // Calcola i macronutrienti consumati
  Map<String, double> _calcolaMacronutrientiConsumati() {
    double proteine = 0.0;
    double carboidrati = 0.0;
    double grassi = 0.0;

    for (var pasto in _pasti) {
      if (pasto.completato) {
        proteine += pasto.proteine;
        carboidrati += pasto.carboidrati;
        grassi += pasto.grassi;
      }
    }

    return {
      'proteine': proteine,
      'carboidrati': carboidrati,
      'grassi': grassi,
    };
  }

  @override
  Widget build(BuildContext context) {
    final dataFormattata = DateFormat('EEEE, d MMMM').format(DateTime.now());

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Text(widget.title),
            const SizedBox(width: 8),
            Icon(
              FontAwesomeIcons.leaf,
              color: Colors.white,
              size: 16,
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(FontAwesomeIcons.wandMagicSparkles),
            tooltip: 'Generatore dieta',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const UltraAdvancedDietScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(FontAwesomeIcons.calendarDays),
            tooltip: 'Piano settimanale',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MealPlanScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(FontAwesomeIcons.chartLine),
            tooltip: 'Statistiche',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const StatsScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(FontAwesomeIcons.database),
            tooltip: 'Database Alimenti',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const FoodAdminScreen(),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(FontAwesomeIcons.gear),
            tooltip: 'Impostazioni',
            onPressed: () {
              _mostraDialogImpostazioni();
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Intestazione con saluto
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Ciao!',
                            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ).animate().fadeIn(duration: 300.ms).slideX(
                            begin: -0.2,
                            end: 0,
                            duration: 300.ms,
                          ),
                          Text(
                            dataFormattata,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Riepilogo calorie
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: CaloriesSummaryCard(
                        calorieConsumate: _calorieConsumate,
                        calorieGiornaliere: _calorieGiornaliere,
                        macronutrientiConsumati: _calcolaMacronutrientiConsumati(),
                        macronutrientiTarget: _macroNutrientiTarget,
                        data: dataFormattata,
                      ),
                    ),

                    // Monitoraggio acqua
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
                      child: WaterTrackingCard(
                        bicchieriAcqua: _bicchieriAcqua,
                        obiettivoBicchieri: _obiettivoBicchieri,
                        onBicchieriChanged: _aggiornaBicchieriAcqua,
                      ),
                    ),

                    // Suggerimento del giorno
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
                      child: MotivationalCard(
                        tip: _suggerimentoDelGiorno,
                        imageUrl: ImageConstants.motivationalImage1,
                      ),
                    ),

                    // Pulsante per il database alimentare
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
                      child: Card(
                        elevation: 2,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: InkWell(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const FoodAdminScreen(),
                              ),
                            );
                          },
                          borderRadius: BorderRadius.circular(16),
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: AppTheme.primaryColor.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Icon(
                                    FontAwesomeIcons.database,
                                    color: AppTheme.primaryColor,
                                    size: 24,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Database Alimentare',
                                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'Gestisci gli alimenti e le ricette per la tua dieta',
                                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          color: AppTheme.textSecondaryColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Icon(
                                  Icons.arrow_forward_ios,
                                  color: AppTheme.textSecondaryColor,
                                  size: 16,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),

                    // Intestazione pasti
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'I tuoi pasti',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'Oggi',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: AppTheme.primaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Lista pasti
                    _pasti.isEmpty
                        ? Center(
                            child: Padding(
                              padding: const EdgeInsets.all(32.0),
                              child: Column(
                                children: [
                                  Icon(
                                    FontAwesomeIcons.utensils,
                                    size: 48,
                                    color: Colors.grey.shade400,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'Nessun pasto disponibile',
                                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                      color: AppTheme.textSecondaryColor,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Aggiungi un pasto con il pulsante +',
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: AppTheme.textSecondaryColor,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          )
                        : ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: _pasti.length,
                            itemBuilder: (context, index) {
                              final pasto = _pasti[index];
                              return MealCard(
                                meal: pasto,
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => MealDetailScreen(
                                        pasto: pasto,
                                        onSave: (pastoAggiornato) {
                                          setState(() {
                                            _pasti[index] = pastoAggiornato;
                                            _aggiornaCalorieConsumate();
                                            _salvaDati();
                                          });
                                        },
                                      ),
                                    ),
                                  );
                                },
                                onCompletedChanged: (value) {
                                  _togglePastoCompletato(index);
                                },
                              );
                            },
                          ),

                    // Spazio extra in fondo
                    const SizedBox(height: 80),
                  ],
                ),
              ),
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AddMealScreen(
                onAdd: (nuovoPasto) {
                  setState(() {
                    _pasti.add(nuovoPasto);
                    _pasti.sort((a, b) => a.orario.compareTo(b.orario));
                    if (nuovoPasto.completato) {
                      _aggiornaCalorieConsumate();
                    }
                    _salvaDati();
                  });

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('Pasto aggiunto con successo'),
                      backgroundColor: AppTheme.successColor,
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  );
                },
              ),
            ),
          );
        },
        tooltip: 'Aggiungi pasto',
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 4,
        icon: const Icon(
          FontAwesomeIcons.plus,
          size: 18,
        ),
        label: const Text(
          'Aggiungi Pasto',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }



  void _mostraDialogImpostazioni() {
    // Controllers per i valori
    final calorieController = TextEditingController(text: _calorieGiornaliere.toString());
    final proteineController = TextEditingController(text: _macroNutrientiTarget['proteine']!.toString());
    final carboidratiController = TextEditingController(text: _macroNutrientiTarget['carboidrati']!.toString());
    final grassiController = TextEditingController(text: _macroNutrientiTarget['grassi']!.toString());
    final bicchieriController = TextEditingController(text: _obiettivoBicchieri.toString());

    // Indice del tab attivo
    int _activeTabIndex = 0;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Row(
                children: [
                  Icon(
                    FontAwesomeIcons.gear,
                    color: AppTheme.primaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  const Text('Impostazioni'),
                ],
              ),
              content: SizedBox(
                width: double.maxFinite,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Tabs
                    Row(
                      children: [
                        Expanded(
                          child: _buildSettingsTab(
                            'Nutrizione',
                            FontAwesomeIcons.utensils,
                            0,
                            _activeTabIndex,
                            (index) => setState(() => _activeTabIndex = index),
                          ),
                        ),
                        Expanded(
                          child: _buildSettingsTab(
                            'Acqua',
                            FontAwesomeIcons.droplet,
                            1,
                            _activeTabIndex,
                            (index) => setState(() => _activeTabIndex = index),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Tab content
                    if (_activeTabIndex == 0)
                      SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  FontAwesomeIcons.fire,
                                  color: AppTheme.accentColor,
                                  size: 16,
                                ),
                                const SizedBox(width: 8),
                                const Text(
                                  'Obiettivo calorico giornaliero:',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            TextField(
                              controller: calorieController,
                              keyboardType: TextInputType.number,
                              decoration: InputDecoration(
                                labelText: 'Calorie giornaliere',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: Icon(
                                  FontAwesomeIcons.bolt,
                                  color: AppTheme.accentColor,
                                  size: 16,
                                ),
                              ),
                            ),
                            const SizedBox(height: 24),
                            Row(
                              children: [
                                Icon(
                                  FontAwesomeIcons.chartPie,
                                  color: AppTheme.primaryColor,
                                  size: 16,
                                ),
                                const SizedBox(width: 8),
                                const Text(
                                  'Distribuzione macronutrienti (%):',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              'La somma deve essere 100%',
                              style: TextStyle(fontSize: 12, fontStyle: FontStyle.italic),
                            ),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(
                                  child: TextField(
                                    controller: proteineController,
                                    keyboardType: TextInputType.number,
                                    decoration: InputDecoration(
                                      labelText: 'Proteine %',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      prefixIcon: Icon(
                                        FontAwesomeIcons.drumstickBite,
                                        color: AppTheme.proteinColor,
                                        size: 16,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: TextField(
                                    controller: carboidratiController,
                                    keyboardType: TextInputType.number,
                                    decoration: InputDecoration(
                                      labelText: 'Carboidrati %',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      prefixIcon: Icon(
                                        FontAwesomeIcons.breadSlice,
                                        color: AppTheme.carbColor,
                                        size: 16,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: TextField(
                                    controller: grassiController,
                                    keyboardType: TextInputType.number,
                                    decoration: InputDecoration(
                                      labelText: 'Grassi %',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      prefixIcon: Icon(
                                        FontAwesomeIcons.cheese,
                                        color: AppTheme.fatColor,
                                        size: 16,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      )
                    else
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                FontAwesomeIcons.glassWater,
                                color: Colors.blue,
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                'Obiettivo bicchieri d\'acqua:',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Imposta il numero di bicchieri d\'acqua che vuoi bere ogni giorno',
                            style: TextStyle(fontSize: 12),
                          ),
                          const SizedBox(height: 16),
                          TextField(
                            controller: bicchieriController,
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              labelText: 'Bicchieri d\'acqua',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              prefixIcon: Icon(
                                FontAwesomeIcons.droplet,
                                color: Colors.blue,
                                size: 16,
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'Si consiglia di bere almeno 8 bicchieri d\'acqua al giorno (circa 2 litri).',
                            style: TextStyle(
                              fontSize: 12,
                              fontStyle: FontStyle.italic,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Annulla'),
                ),
                ElevatedButton(
                  onPressed: () {
                    // Validazione e salvataggio
                    try {
                      final nuoveCalorie = int.parse(calorieController.text);
                      final nuoveProteine = double.parse(proteineController.text);
                      final nuoviCarboidrati = double.parse(carboidratiController.text);
                      final nuoviGrassi = double.parse(grassiController.text);
                      final nuoviBicchieri = int.parse(bicchieriController.text);

                      // Verifica che i valori siano positivi
                      if (nuoveCalorie <= 0 || nuoveProteine < 0 || nuoviCarboidrati < 0 || nuoviGrassi < 0 || nuoviBicchieri <= 0) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: const Text('Inserisci valori positivi'),
                            backgroundColor: AppTheme.errorColor,
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        );
                        return;
                      }

                      // Verifica che la somma dei macronutrienti sia 100%
                      final somma = nuoveProteine + nuoviCarboidrati + nuoviGrassi;
                      if (somma < 99.5 || somma > 100.5) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: const Text('La somma dei macronutrienti deve essere 100%'),
                            backgroundColor: AppTheme.errorColor,
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        );
                        return;
                      }

                      this.setState(() {
                        _calorieGiornaliere = nuoveCalorie;
                        _macroNutrientiTarget = {
                          'proteine': nuoveProteine,
                          'carboidrati': nuoviCarboidrati,
                          'grassi': nuoviGrassi,
                        };
                        _obiettivoBicchieri = nuoviBicchieri;
                      });

                      _salvaDati();
                      Navigator.of(context).pop();

                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: const Text('Impostazioni aggiornate'),
                          backgroundColor: AppTheme.successColor,
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                      );
                    } catch (e) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: const Text('Inserisci valori numerici validi'),
                          backgroundColor: AppTheme.errorColor,
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                      );
                    }
                  },
                  child: const Text('Salva'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildSettingsTab(String label, IconData icon, int index, int activeIndex, Function(int) onTap) {
    final isActive = index == activeIndex;

    return InkWell(
      onTap: () => onTap(index),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: isActive ? AppTheme.primaryColor.withOpacity(0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isActive ? AppTheme.primaryColor : Colors.grey.shade300,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isActive ? AppTheme.primaryColor : Colors.grey,
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: isActive ? AppTheme.primaryColor : Colors.grey,
                fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
