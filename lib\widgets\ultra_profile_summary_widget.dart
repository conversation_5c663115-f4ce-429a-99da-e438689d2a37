import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../models/ultra_detailed_profile.dart';
import '../models/user_profile.dart';
import '../theme/dr_staffilano_theme.dart';

/// Widget per il riepilogo completo dell'UltraDetailedProfile
/// Mostra tutti i dati inseriti in sezioni ben organizzate
class UltraProfileSummaryWidget extends StatefulWidget {
  final UltraDetailedProfile profile;
  final VoidCallback? onEditProfile;

  const UltraProfileSummaryWidget({
    Key? key,
    required this.profile,
    this.onEditProfile,
  }) : super(key: key);

  @override
  State<UltraProfileSummaryWidget> createState() => _UltraProfileSummaryWidgetState();
}

class _UltraProfileSummaryWidgetState extends State<UltraProfileSummaryWidget> {
  final Set<String> _expandedSections = {};

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header sezione
          _buildSectionHeader(),
          const SizedBox(height: 20),

          // Sezioni del profilo
          _buildAnthropometricSection(),
          const SizedBox(height: 16),
          _buildBodyCompositionSection(),
          const SizedBox(height: 16),
          _buildBloodValuesSection(),
          const SizedBox(height: 16),
          _buildGoalsSection(),
          const SizedBox(height: 16),
          _buildActivitySection(),
          const SizedBox(height: 16),
          _buildDietaryPreferencesSection(),
          const SizedBox(height: 16),
          _buildMetabolicCalculationsSection(),
        ],
      ),
    ).animate().fadeIn(duration: 300.ms, delay: 200.ms).slideY(begin: 0.2, end: 0);
  }

  Widget _buildSectionHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            FontAwesomeIcons.userCheck,
            color: DrStaffilanoTheme.primaryGreen,
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        const Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Riepilogo Profilo Ultra-Dettagliato',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 4),
              Text(
                'Tutti i dati inseriti per la personalizzazione avanzata',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
        if (widget.onEditProfile != null)
          IconButton(
            onPressed: widget.onEditProfile,
            icon: Icon(
              FontAwesomeIcons.edit,
              color: DrStaffilanoTheme.primaryGreen,
              size: 18,
            ),
            tooltip: 'Modifica Profilo',
          ),
      ],
    );
  }

  Widget _buildAnthropometricSection() {
    return _buildExpandableSection(
      'anthropometric',
      'Dati Antropometrici',
      FontAwesomeIcons.user,
      DrStaffilanoTheme.primaryGreen,
      [
        _buildInfoRow('Nome', widget.profile.baseProfile.name, FontAwesomeIcons.signature),
        _buildInfoRow('Età', '${widget.profile.baseProfile.age} anni', FontAwesomeIcons.calendar),
        _buildInfoRow('Peso', '${widget.profile.baseProfile.weight} kg', FontAwesomeIcons.weight),
        _buildInfoRow('Altezza', '${widget.profile.baseProfile.height} cm', FontAwesomeIcons.ruler),
        _buildInfoRow('Sesso', widget.profile.baseProfile.gender == Gender.male ? 'Maschio' : 'Femmina', FontAwesomeIcons.venusMars),
        _buildInfoRow('BMI', '${_calculateBMI().toStringAsFixed(1)}', FontAwesomeIcons.calculator),
      ],
    );
  }

  Widget _buildBodyCompositionSection() {
    final hasBodyComposition = widget.profile.bodyFatPercentage != null ||
                              widget.profile.leanMass != null ||
                              (widget.profile.circumferences?.isNotEmpty ?? false);

    if (!hasBodyComposition) {
      return _buildEmptySection('Composizione Corporea', 'Nessun dato inserito');
    }

    final items = <Widget>[];

    if (widget.profile.bodyFatPercentage != null) {
      items.add(_buildInfoRow('Grasso Corporeo', '${widget.profile.bodyFatPercentage!.toStringAsFixed(1)}%', FontAwesomeIcons.percentage));
    }

    if (widget.profile.leanMass != null) {
      items.add(_buildInfoRow('Massa Magra', '${widget.profile.leanMass!.toStringAsFixed(1)} kg', FontAwesomeIcons.dumbbell));
    }

    if (widget.profile.bodyCompositionMethod != null) {
      items.add(_buildInfoRow('Metodo Misurazione', widget.profile.bodyCompositionMethod.toString().split('.').last, FontAwesomeIcons.stethoscope));
    }

    if (widget.profile.circumferences?.isNotEmpty ?? false) {
      widget.profile.circumferences!.forEach((key, value) {
        items.add(_buildInfoRow('$key', '${value.toStringAsFixed(1)} cm', FontAwesomeIcons.tape));
      });
    }

    return _buildExpandableSection(
      'bodyComposition',
      'Composizione Corporea',
      FontAwesomeIcons.userDoctor,
      DrStaffilanoTheme.secondaryBlue,
      items,
    );
  }

  Widget _buildBloodValuesSection() {
    final bloodValues = widget.profile.bloodValues;

    if (bloodValues == null) {
      return _buildEmptySection('Valori Ematochimici', 'Nessun esame inserito');
    }

    final items = <Widget>[];

    if (bloodValues.fastingGlucose != null) {
      items.add(_buildInfoRow('Glicemia a Digiuno', '${bloodValues.fastingGlucose} mg/dL', FontAwesomeIcons.droplet));
    }

    if (bloodValues.totalCholesterol != null) {
      items.add(_buildInfoRow('Colesterolo Totale', '${bloodValues.totalCholesterol} mg/dL', FontAwesomeIcons.heartPulse));
    }

    if (bloodValues.ldlCholesterol != null) {
      items.add(_buildInfoRow('Colesterolo LDL', '${bloodValues.ldlCholesterol} mg/dL', FontAwesomeIcons.triangleExclamation));
    }

    if (bloodValues.hdlCholesterol != null) {
      items.add(_buildInfoRow('Colesterolo HDL', '${bloodValues.hdlCholesterol} mg/dL', FontAwesomeIcons.shield));
    }

    if (bloodValues.triglycerides != null) {
      items.add(_buildInfoRow('Trigliceridi', '${bloodValues.triglycerides} mg/dL', FontAwesomeIcons.vial));
    }

    if (bloodValues.tsh != null) {
      items.add(_buildInfoRow('TSH', '${bloodValues.tsh} mU/L', FontAwesomeIcons.microscope));
    }

    if (bloodValues.iron != null) {
      items.add(_buildInfoRow('Ferro', '${bloodValues.iron} μg/dL', FontAwesomeIcons.magnet));
    }

    if (bloodValues.uricAcid != null) {
      items.add(_buildInfoRow('Acido Urico', '${bloodValues.uricAcid} mg/dL', FontAwesomeIcons.flask));
    }

    if (bloodValues.testDate != null) {
      items.add(_buildInfoRow('Data Esami', '${bloodValues.testDate!.day}/${bloodValues.testDate!.month}/${bloodValues.testDate!.year}', FontAwesomeIcons.calendarCheck));
    }

    if (items.isEmpty) {
      return _buildEmptySection('Valori Ematochimici', 'Nessun valore inserito');
    }

    return _buildExpandableSection(
      'bloodValues',
      'Valori Ematochimici',
      FontAwesomeIcons.vials,
      Colors.red.shade600,
      items,
    );
  }

  Widget _buildGoalsSection() {
    final items = <Widget>[
      _buildInfoRow('Obiettivo Primario', _getGoalDisplayName(widget.profile.primaryGoal), FontAwesomeIcons.bullseye),
    ];

    if (widget.profile.secondaryGoals.isNotEmpty) {
      for (int i = 0; i < widget.profile.secondaryGoals.length; i++) {
        items.add(_buildInfoRow('Obiettivo ${i + 2}', _getSecondaryGoalDisplayName(widget.profile.secondaryGoals[i]), FontAwesomeIcons.bullseye));
      }
    }

    // Aggiungi dettagli specifici per obiettivo
    if (widget.profile.weightLossDetails != null) {
      final details = widget.profile.weightLossDetails!;
      items.add(_buildInfoRow('Deficit Calorico', '${(details.deficitPercentage * 100).round()}%', FontAwesomeIcons.minus));
    }

    if (widget.profile.muscleGainDetails != null) {
      final details = widget.profile.muscleGainDetails!;
      items.add(_buildInfoRow('Surplus Calorico', '${(details.surplusPercentage * 100).round()}%', FontAwesomeIcons.plus));
      items.add(_buildInfoRow('Proteine', '${details.proteinMultiplier} g/kg', FontAwesomeIcons.drumstickBite));
      items.add(_buildInfoRow('Tipo Bulk', details.leanBulk ? 'Pulito' : 'Aggressivo', FontAwesomeIcons.dumbbell));
    }

    return _buildExpandableSection(
      'goals',
      'Obiettivi Gerarchizzati',
      FontAwesomeIcons.trophy,
      DrStaffilanoTheme.accentGold,
      items,
    );
  }

  Widget _buildActivitySection() {
    final items = <Widget>[
      _buildInfoRow('Attività Lavorativa', _getActivityDisplayName(widget.profile.workActivity), FontAwesomeIcons.briefcase),
    ];

    if (widget.profile.plannedExercises.isNotEmpty) {
      for (final exercise in widget.profile.plannedExercises) {
        items.add(_buildInfoRow(
          exercise.type.toString().split('.').last,
          '${exercise.frequency}x/settimana • ${exercise.durationMinutes}min',
          _getExerciseIcon(exercise.type),
        ));
      }
    }

    items.add(_buildInfoRow('Livello NEAT', _getNeatDisplayName(widget.profile.neatLevel), FontAwesomeIcons.walking));

    return _buildExpandableSection(
      'activity',
      'Livello di Attività',
      FontAwesomeIcons.personRunning,
      Colors.orange.shade600,
      items,
    );
  }

  Widget _buildDietaryPreferencesSection() {
    final items = <Widget>[];

    if (widget.profile.dietaryRegimen != null) {
      items.add(_buildInfoRow('Regime Dietetico', _getDietaryRegimenDisplayName(widget.profile.dietaryRegimen!), FontAwesomeIcons.utensils));
    }

    if (widget.profile.preferredFoods.isNotEmpty) {
      items.add(_buildInfoRow('Cibi Preferiti', widget.profile.preferredFoods.join(', '), FontAwesomeIcons.heart));
    }

    if (widget.profile.dislikedFoods.isNotEmpty) {
      items.add(_buildInfoRow('Cibi Non Graditi', widget.profile.dislikedFoods.join(', '), FontAwesomeIcons.heartCrack));
    }

    if (widget.profile.foodAllergies.isNotEmpty) {
      items.add(_buildInfoRow('Allergie', widget.profile.foodAllergies.map((a) => a.allergen).join(', '), FontAwesomeIcons.triangleExclamation));
    }

    if (widget.profile.foodIntolerances.isNotEmpty) {
      items.add(_buildInfoRow('Intolleranze', widget.profile.foodIntolerances.map((i) => i.food).join(', '), FontAwesomeIcons.ban));
    }

    if (items.isEmpty) {
      return _buildEmptySection('Preferenze Alimentari', 'Nessuna preferenza specificata');
    }

    return _buildExpandableSection(
      'dietary',
      'Preferenze Alimentari',
      FontAwesomeIcons.leaf,
      Colors.green.shade600,
      items,
    );
  }

  Widget _buildMetabolicCalculationsSection() {
    final bmr = widget.profile.calculateBMR();
    final tdee = widget.profile.calculateTDEE();
    final targetCalories = widget.profile.calculateCalorieTarget();

    final items = <Widget>[
      _buildInfoRow('BMR (Metabolismo Basale)', '${bmr.round()} kcal/giorno', FontAwesomeIcons.fire),
      _buildInfoRow('TDEE (Dispendio Totale)', '${tdee.round()} kcal/giorno', FontAwesomeIcons.chartLine),
      _buildInfoRow('Target Calorico', '${targetCalories.round()} kcal/giorno', FontAwesomeIcons.bullseye),
    ];

    // Aggiungi distribuzione macronutrienti
    final macroDistribution = widget.profile.calculateMacroDistribution();
    items.add(_buildInfoRow('Proteine', '${macroDistribution['proteins']!.round()}g (${((macroDistribution['proteins']! * 4 / targetCalories) * 100).round()}%)', FontAwesomeIcons.drumstickBite));
    items.add(_buildInfoRow('Carboidrati', '${macroDistribution['carbs']!.round()}g (${((macroDistribution['carbs']! * 4 / targetCalories) * 100).round()}%)', FontAwesomeIcons.wheatAwn));
    items.add(_buildInfoRow('Grassi', '${macroDistribution['fats']!.round()}g (${((macroDistribution['fats']! * 9 / targetCalories) * 100).round()}%)', FontAwesomeIcons.droplet));

    return _buildExpandableSection(
      'metabolic',
      'Calcoli Metabolici',
      FontAwesomeIcons.calculator,
      DrStaffilanoTheme.secondaryBlue,
      items,
    );
  }

  Widget _buildExpandableSection(String sectionId, String title, IconData icon, Color color, List<Widget> items) {
    final isExpanded = _expandedSections.contains(sectionId);

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: color.withOpacity(0.2)),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              setState(() {
                if (isExpanded) {
                  _expandedSections.remove(sectionId);
                } else {
                  _expandedSections.add(sectionId);
                }
              });
            },
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(icon, color: color, size: 16),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: color,
                      ),
                    ),
                  ),
                  Icon(
                    isExpanded ? FontAwesomeIcons.chevronUp : FontAwesomeIcons.chevronDown,
                    color: color,
                    size: 14,
                  ),
                ],
              ),
            ),
          ),
          if (isExpanded)
            Container(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Column(
                children: items.map((item) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: item,
                )).toList(),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 14, color: Colors.grey.shade600),
        const SizedBox(width: 8),
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          flex: 3,
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  Widget _buildEmptySection(String title, String message) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Icon(FontAwesomeIcons.circleInfo, color: Colors.grey.shade400, size: 16),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade600,
                  ),
                ),
                Text(
                  message,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Metodi helper
  double _calculateBMI() {
    final weight = widget.profile.baseProfile.weight;
    final height = widget.profile.baseProfile.height / 100; // Convert cm to m
    return weight / (height * height);
  }

  // Metodi helper per display names
  String _getGoalDisplayName(PrimaryGoal goal) {
    switch (goal) {
      case PrimaryGoal.weightLoss:
        return 'Perdita di Peso';
      case PrimaryGoal.weightGain:
        return 'Aumento di Peso';
      case PrimaryGoal.maintenance:
        return 'Mantenimento';
      case PrimaryGoal.bodyRecomposition:
        return 'Ricomposizione Corporea';
      case PrimaryGoal.performance:
        return 'Performance Atletica';
      default:
        return goal.toString().split('.').last;
    }
  }

  String _getSecondaryGoalDisplayName(SecondaryGoal goal) {
    return goal.toString().split('.').last;
  }

  String _getActivityDisplayName(WorkActivity activity) {
    switch (activity) {
      case WorkActivity.sedentary:
        return 'Sedentario';
      case WorkActivity.light:
        return 'Leggero';
      case WorkActivity.moderate:
        return 'Moderato';
      case WorkActivity.heavy:
        return 'Pesante';
      default:
        return activity.toString().split('.').last;
    }
  }

  String _getNeatDisplayName(NEATLevel neat) {
    switch (neat) {
      case NEATLevel.low:
        return 'Basso';
      case NEATLevel.medium:
        return 'Moderato';
      case NEATLevel.high:
        return 'Alto';
      default:
        return neat.toString().split('.').last;
    }
  }

  String _getDietaryRegimenDisplayName(DietaryRegimen regime) {
    switch (regime) {
      case DietaryRegimen.mediterranean:
        return 'Dieta Mediterranea';
      case DietaryRegimen.vegetarian:
        return 'Vegetariana';
      case DietaryRegimen.vegan:
        return 'Vegana';
      case DietaryRegimen.keto:
        return 'Chetogenica';
      case DietaryRegimen.paleo:
        return 'Paleo';
      case DietaryRegimen.lowCarb:
        return 'Low Carb';
      case DietaryRegimen.highProtein:
        return 'Alto Contenuto Proteico';
      case DietaryRegimen.balanced:
        return 'Bilanciata';
      case DietaryRegimen.intermittentFasting:
        return 'Digiuno Intermittente';
      case DietaryRegimen.glutenFree:
        return 'Senza Glutine';
      case DietaryRegimen.dairyFree:
        return 'Senza Latticini';
      case DietaryRegimen.antiInflammatory:
        return 'Anti-infiammatoria';
      default:
        return regime.toString().split('.').last;
    }
  }

  IconData _getExerciseIcon(ExerciseType type) {
    switch (type) {
      case ExerciseType.resistance:
        return FontAwesomeIcons.dumbbell;
      case ExerciseType.cardio:
        return FontAwesomeIcons.heartPulse;
      case ExerciseType.yoga:
        return FontAwesomeIcons.spa;
      case ExerciseType.pilates:
        return FontAwesomeIcons.personPraying;
      case ExerciseType.sports:
        return FontAwesomeIcons.futbol;
      case ExerciseType.flexibility:
        return FontAwesomeIcons.personWalking;
      case ExerciseType.hiit:
        return FontAwesomeIcons.fire;
      case ExerciseType.functional:
        return FontAwesomeIcons.personRunning;
      default:
        return FontAwesomeIcons.personRunning;
    }
  }
}
