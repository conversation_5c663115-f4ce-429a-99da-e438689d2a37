import '../models/food.dart';

/// Database comprensivo di 100+ alimenti italiani autentici
/// con categorizzazione appropriata e validazione culturale
class ComprehensiveItalianFoods {

  /// 25 alimenti tradizionali per colazione italiana
  static List<Food> getBreakfastFoods() {
    return [
      // DOLCI DA FORNO REGIONALI
      Food(
        id: 'breakfast_cornetto_crema',
        name: 'Cornetto alla crema',
        description: 'Cornetto ripieno di crema pasticcera',
        calories: 220,
        proteins: 6.0,
        carbs: 28.0,
        fats: 10.0,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.grain, FoodCategory.sweet],
        servingSizeGrams: 60,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.lombardia, ItalianRegion.veneto],
      ),
      Food(
        id: 'breakfast_brioche_siciliana',
        name: 'Brioche siciliana',
        description: 'Brioche tradizionale siciliana con tuppo',
        calories: 195,
        proteins: 5.5,
        carbs: 25.0,
        fats: 8.5,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.grain, FoodCategory.sweet],
        servingSizeGrams: 55,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.sicilia],
      ),
      Food(
        id: 'breakfast_maritozzo',
        name: 'Maritozzo romano',
        description: 'Dolce lievitato romano con panna',
        calories: 280,
        proteins: 7.0,
        carbs: 32.0,
        fats: 14.0,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.grain, FoodCategory.sweet, FoodCategory.dairy],
        servingSizeGrams: 80,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.lazio],
      ),
      Food(
        id: 'breakfast_bombolone',
        name: 'Bombolone toscano',
        description: 'Bombolone fritto ripieno di crema',
        calories: 250,
        proteins: 6.5,
        carbs: 30.0,
        fats: 12.0,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.grain, FoodCategory.sweet],
        servingSizeGrams: 70,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.toscana],
      ),
      Food(
        id: 'breakfast_sfogliatella',
        name: 'Sfogliatella napoletana',
        description: 'Dolce napoletano con ricotta e canditi',
        calories: 210,
        proteins: 8.0,
        carbs: 24.0,
        fats: 9.5,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.grain, FoodCategory.sweet, FoodCategory.dairy],
        servingSizeGrams: 65,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.campania],
      ),

      // CEREALI E PANE
      Food(
        id: 'breakfast_fette_biscottate',
        name: 'Fette biscottate',
        description: 'Pane biscottato tradizionale italiano',
        calories: 410,
        proteins: 11.0,
        carbs: 82.0,
        fats: 5.5,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.grain],
        servingSizeGrams: 25, // 2-3 fette
        isTraditionalItalian: true,
      ),
      Food(
        id: 'breakfast_pane_tostato',
        name: 'Pane tostato',
        description: 'Pane italiano tostato',
        calories: 270,
        proteins: 8.5,
        carbs: 50.0,
        fats: 3.2,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.grain],
        servingSizeGrams: 30,
        isTraditionalItalian: true,
      ),
      Food(
        id: 'breakfast_cereali_integrali',
        name: 'Cereali integrali italiani',
        description: 'Mix di cereali integrali italiani',
        calories: 350,
        proteins: 12.0,
        carbs: 65.0,
        fats: 6.0,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.grain],
        servingSizeGrams: 40,
        isTraditionalItalian: true,
      ),

      // MARMELLATE E CREME SPALMABILI
      Food(
        id: 'breakfast_marmellata_albicocche',
        name: 'Marmellata di albicocche',
        description: 'Marmellata tradizionale di albicocche italiane',
        calories: 240,
        proteins: 0.6,
        carbs: 60.0,
        fats: 0.1,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.sweet, FoodCategory.fruit],
        servingSizeGrams: 20,
        isTraditionalItalian: true,
      ),
      Food(
        id: 'breakfast_nutella',
        name: 'Crema alle nocciole',
        description: 'Crema spalmabile alle nocciole piemontesi',
        calories: 540,
        proteins: 6.3,
        carbs: 57.0,
        fats: 31.0,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.sweet, FoodCategory.fat],
        servingSizeGrams: 15,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.piemonte],
      ),

      // LATTICINI PER COLAZIONE
      Food(
        id: 'breakfast_latte_parzialmente_scremato',
        name: 'Latte parzialmente scremato',
        description: 'Latte vaccino parzialmente scremato',
        calories: 46,
        proteins: 3.3,
        carbs: 5.0,
        fats: 1.5,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.dairy],
        servingSizeGrams: 200, // 1 tazza
        isTraditionalItalian: true,
      ),
      Food(
        id: 'breakfast_yogurt_greco',
        name: 'Yogurt greco',
        description: 'Yogurt greco cremoso',
        calories: 97,
        proteins: 9.0,
        carbs: 4.0,
        fats: 5.0,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.dairy],
        servingSizeGrams: 125,
        isTraditionalItalian: true,
      ),
      Food(
        id: 'breakfast_ricotta_fresca',
        name: 'Ricotta fresca',
        description: 'Ricotta vaccina fresca',
        calories: 146,
        proteins: 8.8,
        carbs: 4.3,
        fats: 10.9,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.dairy],
        servingSizeGrams: 100,
        isTraditionalItalian: true,
      ),

      // CAFFÈ E BEVANDE
      Food(
        id: 'breakfast_caffe_espresso',
        name: 'Caffè espresso',
        description: 'Caffè espresso italiano',
        calories: 2,
        proteins: 0.1,
        carbs: 0.0,
        fats: 0.0,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.beverage],
        servingSizeGrams: 30, // 1 tazzina
        isTraditionalItalian: true,
      ),
      Food(
        id: 'breakfast_cappuccino',
        name: 'Cappuccino',
        description: 'Cappuccino con latte montato',
        calories: 80,
        proteins: 4.0,
        carbs: 6.0,
        fats: 4.5,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.beverage, FoodCategory.dairy],
        servingSizeGrams: 150,
        isTraditionalItalian: true,
      ),
      Food(
        id: 'breakfast_latte_macchiato',
        name: 'Latte macchiato',
        description: 'Latte caldo macchiato con caffè',
        calories: 110,
        proteins: 6.0,
        carbs: 9.0,
        fats: 6.0,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.beverage, FoodCategory.dairy],
        servingSizeGrams: 200,
        isTraditionalItalian: true,
      ),

      // FRUTTA PER COLAZIONE
      Food(
        id: 'breakfast_spremuta_arancia',
        name: 'Spremuta di arancia',
        description: 'Spremuta fresca di arance italiane',
        calories: 45,
        proteins: 0.7,
        carbs: 10.4,
        fats: 0.2,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.fruit, FoodCategory.beverage],
        servingSizeGrams: 200,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.sicilia, ItalianRegion.calabria],
      ),
      Food(
        id: 'breakfast_macedonia_frutta',
        name: 'Macedonia di frutta',
        description: 'Mix di frutta fresca italiana',
        calories: 60,
        proteins: 1.0,
        carbs: 14.0,
        fats: 0.3,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.fruit],
        servingSizeGrams: 150,
        isTraditionalItalian: true,
      ),

      // DOLCI TRADIZIONALI REGIONALI
      Food(
        id: 'breakfast_cannoli_siciliani_mini',
        name: 'Mini cannoli siciliani',
        description: 'Piccoli cannoli siciliani per colazione',
        calories: 180,
        proteins: 6.0,
        carbs: 20.0,
        fats: 9.0,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.sweet, FoodCategory.dairy],
        servingSizeGrams: 50, // versione mini
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.sicilia],
      ),
      Food(
        id: 'breakfast_baba_napoletano_mini',
        name: 'Mini babà napoletano',
        description: 'Piccolo babà napoletano al rum',
        calories: 160,
        proteins: 4.0,
        carbs: 25.0,
        fats: 5.0,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.sweet],
        servingSizeGrams: 45,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.campania],
      ),
      Food(
        id: 'breakfast_pandoro_fetta',
        name: 'Fetta di pandoro',
        description: 'Fetta di pandoro veronese',
        calories: 200,
        proteins: 5.5,
        carbs: 26.0,
        fats: 9.0,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.sweet, FoodCategory.grain],
        servingSizeGrams: 50,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.veneto],
      ),
      Food(
        id: 'breakfast_panettone_fetta',
        name: 'Fetta di panettone',
        description: 'Fetta di panettone milanese',
        calories: 190,
        proteins: 5.0,
        carbs: 28.0,
        fats: 7.0,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.sweet, FoodCategory.grain],
        servingSizeGrams: 50,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.lombardia],
      ),

      // BISCOTTI TRADIZIONALI
      Food(
        id: 'breakfast_biscotti_secchi',
        name: 'Biscotti secchi',
        description: 'Biscotti secchi tradizionali italiani',
        calories: 420,
        proteins: 8.0,
        carbs: 70.0,
        fats: 12.0,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.sweet, FoodCategory.grain],
        servingSizeGrams: 30, // 3-4 biscotti
        isTraditionalItalian: true,
      ),
      Food(
        id: 'breakfast_cantucci_toscani',
        name: 'Cantucci toscani',
        description: 'Biscotti alle mandorle toscani',
        calories: 450,
        proteins: 12.0,
        carbs: 65.0,
        fats: 16.0,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.sweet, FoodCategory.grain],
        servingSizeGrams: 25, // 2-3 cantucci
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.toscana],
      ),
      Food(
        id: 'breakfast_amaretti_saronno',
        name: 'Amaretti di Saronno',
        description: 'Amaretti tradizionali lombardi',
        calories: 480,
        proteins: 10.0,
        carbs: 60.0,
        fats: 22.0,
        suitableForMeals: [MealType.breakfast],
        categories: [FoodCategory.sweet],
        servingSizeGrams: 20, // 2-3 amaretti
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.lombardia],
      ),
    ];
  }

  /// 30 spuntini italiani appropriati e leggeri
  static List<Food> getSnackFoods() {
    return [
      // FRUTTA FRESCA ITALIANA
      Food(
        id: 'snack_mela_annurca',
        name: 'Mela annurca campana',
        description: 'Mela tipica campana, dolce e croccante',
        calories: 52,
        proteins: 0.3,
        carbs: 13.8,
        fats: 0.2,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.fruit],
        servingSizeGrams: 150,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.campania],
      ),
      Food(
        id: 'snack_pera_abate',
        name: 'Pera Abate Fetel',
        description: 'Pera italiana dolce e succosa',
        calories: 57,
        proteins: 0.4,
        carbs: 15.2,
        fats: 0.1,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.fruit],
        servingSizeGrams: 140,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.emiliaRomagna],
      ),
      Food(
        id: 'snack_arancia_tarocco',
        name: 'Arancia Tarocco',
        description: 'Arancia rossa siciliana',
        calories: 47,
        proteins: 0.9,
        carbs: 11.7,
        fats: 0.1,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.fruit],
        servingSizeGrams: 160,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.sicilia],
      ),
      Food(
        id: 'snack_mandarino_tardivo',
        name: 'Mandarino tardivo di Ciaculli',
        description: 'Mandarino siciliano dolce e profumato',
        calories: 53,
        proteins: 0.8,
        carbs: 13.3,
        fats: 0.3,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.fruit],
        servingSizeGrams: 120,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.sicilia],
      ),
      Food(
        id: 'snack_kiwi_italiano',
        name: 'Kiwi italiano',
        description: 'Kiwi coltivato in Italia',
        calories: 61,
        proteins: 1.1,
        carbs: 14.7,
        fats: 0.5,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.fruit],
        servingSizeGrams: 100,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.lazio, ItalianRegion.piemonte],
      ),

      // FRUTTA SECCA E NOCI ITALIANE
      Food(
        id: 'snack_mandorle_siciliane',
        name: 'Mandorle siciliane',
        description: 'Mandorle dolci siciliane sgusciate',
        calories: 579,
        proteins: 21.2,
        carbs: 21.6,
        fats: 49.9,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.fat],
        servingSizeGrams: 25, // Porzione piccola appropriata
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.sicilia],
      ),
      Food(
        id: 'snack_noci_sorrento',
        name: 'Noci di Sorrento',
        description: 'Noci campane di alta qualità',
        calories: 654,
        proteins: 15.2,
        carbs: 13.7,
        fats: 65.2,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.fat],
        servingSizeGrams: 20, // Porzione piccola
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.campania],
      ),
      Food(
        id: 'snack_nocciole_piemonte',
        name: 'Nocciole del Piemonte IGP',
        description: 'Nocciole piemontesi tostate',
        calories: 628,
        proteins: 14.9,
        carbs: 16.7,
        fats: 60.8,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.fat],
        servingSizeGrams: 20,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.piemonte],
      ),
      Food(
        id: 'snack_pistacchi_bronte',
        name: 'Pistacchi di Bronte DOP',
        description: 'Pistacchi siciliani di Bronte',
        calories: 562,
        proteins: 20.2,
        carbs: 27.5,
        fats: 45.3,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.fat],
        servingSizeGrams: 25,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.sicilia],
      ),

      // FORMAGGI STAGIONATI IN PICCOLE PORZIONI
      Food(
        id: 'snack_parmigiano_24_mesi',
        name: 'Parmigiano Reggiano 24 mesi',
        description: 'Parmigiano stagionato in piccole porzioni',
        calories: 392,
        proteins: 35.8,
        carbs: 0.0,
        fats: 26.0,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.dairy],
        servingSizeGrams: 25, // Porzione piccola appropriata per spuntino
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.emiliaRomagna],
      ),
      Food(
        id: 'snack_grana_padano',
        name: 'Grana Padano DOP',
        description: 'Grana Padano in piccole porzioni',
        calories: 384,
        proteins: 33.0,
        carbs: 0.0,
        fats: 28.0,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.dairy],
        servingSizeGrams: 25,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.lombardia, ItalianRegion.veneto],
      ),
      Food(
        id: 'snack_pecorino_romano',
        name: 'Pecorino Romano DOP',
        description: 'Pecorino romano in piccole porzioni',
        calories: 387,
        proteins: 32.0,
        carbs: 0.2,
        fats: 27.3,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.dairy],
        servingSizeGrams: 20, // Porzione molto piccola per il sapore intenso
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.lazio, ItalianRegion.sardegna],
      ),

      // PRODOTTI DA FORNO LEGGERI
      Food(
        id: 'snack_grissini_torinesi',
        name: 'Grissini torinesi',
        description: 'Grissini tradizionali piemontesi',
        calories: 433,
        proteins: 12.0,
        carbs: 68.0,
        fats: 13.0,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.grain],
        servingSizeGrams: 25, // 4-5 grissini
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.piemonte],
      ),
      Food(
        id: 'snack_taralli_pugliesi',
        name: 'Taralli pugliesi',
        description: 'Taralli tradizionali pugliesi',
        calories: 450,
        proteins: 11.0,
        carbs: 65.0,
        fats: 16.0,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.grain],
        servingSizeGrams: 30, // 3-4 taralli
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.puglia],
      ),
      Food(
        id: 'snack_crackers_integrali',
        name: 'Crackers integrali italiani',
        description: 'Crackers integrali prodotti in Italia',
        calories: 428,
        proteins: 10.0,
        carbs: 60.0,
        fats: 16.0,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.grain],
        servingSizeGrams: 25, // 4-5 crackers
        isTraditionalItalian: true,
      ),

      // YOGURT E LATTICINI LEGGERI
      Food(
        id: 'snack_yogurt_magro',
        name: 'Yogurt magro italiano',
        description: 'Yogurt magro bianco italiano',
        calories: 36,
        proteins: 3.5,
        carbs: 4.3,
        fats: 0.9,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.dairy],
        servingSizeGrams: 125,
        isTraditionalItalian: true,
      ),
      Food(
        id: 'snack_ricotta_light',
        name: 'Ricotta light',
        description: 'Ricotta a ridotto contenuto di grassi',
        calories: 115,
        proteins: 8.8,
        carbs: 4.3,
        fats: 7.5,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.dairy],
        servingSizeGrams: 80,
        isTraditionalItalian: true,
      ),

      // OLIVE E CONSERVE LEGGERE
      Food(
        id: 'snack_olive_taggiasche',
        name: 'Olive Taggiasche',
        description: 'Olive liguri dolci e delicate',
        calories: 142,
        proteins: 1.0,
        carbs: 1.0,
        fats: 15.0,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.fat],
        servingSizeGrams: 30, // 10-12 olive
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.liguria],
      ),
      Food(
        id: 'snack_olive_ascolane_crude',
        name: 'Olive Ascolane crude',
        description: 'Olive marchigiane grandi e carnose',
        calories: 115,
        proteins: 0.8,
        carbs: 1.0,
        fats: 12.0,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.fat],
        servingSizeGrams: 35, // 8-10 olive grandi
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.marche],
      ),

      // VERDURE CRUDE E PREPARATE
      Food(
        id: 'snack_pomodorini_pachino',
        name: 'Pomodorini di Pachino IGP',
        description: 'Pomodorini siciliani dolci',
        calories: 18,
        proteins: 0.9,
        carbs: 3.5,
        fats: 0.2,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.vegetable],
        servingSizeGrams: 100, // 8-10 pomodorini
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.sicilia],
      ),
      Food(
        id: 'snack_carote_baby',
        name: 'Carote baby',
        description: 'Carote baby crude italiane',
        calories: 41,
        proteins: 0.9,
        carbs: 9.6,
        fats: 0.2,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.vegetable],
        servingSizeGrams: 80, // 6-8 carote baby
        isTraditionalItalian: true,
      ),
      Food(
        id: 'snack_finocchi_crudi',
        name: 'Finocchi crudi',
        description: 'Finocchi italiani crudi a bastoncini',
        calories: 23,
        proteins: 1.2,
        carbs: 5.1,
        fats: 0.2,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.vegetable],
        servingSizeGrams: 100,
        isTraditionalItalian: true,
      ),

      // DOLCI LEGGERI TRADIZIONALI
      Food(
        id: 'snack_biscotti_secchi_mini',
        name: 'Mini biscotti secchi',
        description: 'Piccoli biscotti secchi tradizionali',
        calories: 420,
        proteins: 8.0,
        carbs: 70.0,
        fats: 12.0,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.sweet, FoodCategory.grain],
        servingSizeGrams: 20, // 2-3 biscotti piccoli
        isTraditionalItalian: true,
      ),
      Food(
        id: 'snack_amaretti_mini',
        name: 'Mini amaretti',
        description: 'Piccoli amaretti tradizionali',
        calories: 480,
        proteins: 10.0,
        carbs: 60.0,
        fats: 22.0,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.sweet],
        servingSizeGrams: 15, // 2 amaretti mini
        isTraditionalItalian: true,
      ),

      // BEVANDE LEGGERE
      Food(
        id: 'snack_te_verde_italiano',
        name: 'Tè verde italiano',
        description: 'Tè verde coltivato in Italia',
        calories: 1,
        proteins: 0.0,
        carbs: 0.3,
        fats: 0.0,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.beverage],
        servingSizeGrams: 200, // 1 tazza
        isTraditionalItalian: true,
      ),
      Food(
        id: 'snack_acqua_aromatizzata',
        name: 'Acqua aromatizzata italiana',
        description: 'Acqua con aromi naturali italiani',
        calories: 5,
        proteins: 0.0,
        carbs: 1.2,
        fats: 0.0,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.beverage],
        servingSizeGrams: 250,
        isTraditionalItalian: true,
      ),

      // GELATI E SORBETTI LEGGERI
      Food(
        id: 'snack_sorbetto_limone',
        name: 'Sorbetto al limone',
        description: 'Sorbetto siciliano al limone',
        calories: 120,
        proteins: 0.5,
        carbs: 30.0,
        fats: 0.1,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.sweet, FoodCategory.fruit],
        servingSizeGrams: 60, // Porzione piccola
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.sicilia],
      ),
      Food(
        id: 'snack_granita_siciliana',
        name: 'Granita siciliana',
        description: 'Granita tradizionale siciliana',
        calories: 90,
        proteins: 0.2,
        carbs: 22.0,
        fats: 0.1,
        suitableForMeals: [MealType.snack],
        categories: [FoodCategory.sweet, FoodCategory.beverage],
        servingSizeGrams: 80,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.sicilia],
      ),
    ];
  }

  /// Ottieni tutti gli alimenti per colazione
  static List<Food> getAllBreakfastFoods() {
    return getBreakfastFoods();
  }

  /// Ottieni tutti gli spuntini appropriati
  static List<Food> getAllSnackFoods() {
    return getSnackFoods();
  }
}
