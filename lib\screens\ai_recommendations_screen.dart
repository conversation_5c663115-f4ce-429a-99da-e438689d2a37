import 'package:flutter/material.dart';
import '../ai/models/ai_models.dart';
import '../ai/services/ai_service.dart';
import '../models/food.dart';
import '../models/user_profile.dart';
import '../services/user_service.dart';
import '../services/food_database_service.dart';
import '../widgets/ai_recommendation_widget.dart';
import '../widgets/food_detail_dialog.dart';
import '../widgets/food_feedback_widget.dart';

/// Schermata che mostra le raccomandazioni dell'AI
class AIRecommendationsScreen extends StatefulWidget {
  const AIRecommendationsScreen({Key? key}) : super(key: key);

  @override
  _AIRecommendationsScreenState createState() => _AIRecommendationsScreenState();
}

class _AIRecommendationsScreenState extends State<AIRecommendationsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  UserProfile? _userProfile;
  bool _isLoading = true;
  String _errorMessage = '';
  String? _selectedMealType;
  final List<String> _mealTypes = ['breakfast', 'lunch', 'dinner', 'snack'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadUserProfile();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadUserProfile() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final userService = await UserService.getInstance();
      final profile = await userService.getCurrentUserProfile();

      setState(() {
        _userProfile = profile;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Errore nel caricamento del profilo: $e';
        _isLoading = false;
      });
      print(_errorMessage);
    }
  }

  void _showFoodDetail(Food food) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              FoodDetailDialog(food: food),
              if (_userProfile != null)
                FoodFeedbackWidget(
                  food: food,
                  userProfile: _userProfile!,
                  onFeedbackSubmitted: () {
                    Navigator.of(context).pop();
                    // Ricarica le raccomandazioni dopo il feedback
                    setState(() {});
                  },
                ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Raccomandazioni AI'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_errorMessage.isNotEmpty) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Raccomandazioni AI'),
        ),
        body: Center(
          child: Text(
            _errorMessage,
            style: const TextStyle(color: Colors.red),
          ),
        ),
      );
    }

    if (_userProfile == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Raccomandazioni AI'),
        ),
        body: const Center(
          child: Text('Profilo utente non disponibile'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Raccomandazioni AI'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Per te'),
            Tab(text: 'Alimenti'),
            Tab(text: 'Analisi'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildPersonalizedTab(),
          _buildFoodRecommendationsTab(),
          _buildAnalysisTab(),
        ],
      ),
    );
  }

  Widget _buildPersonalizedTab() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'Raccomandazioni personalizzate per ${_userProfile!.name}',
            style: Theme.of(context).textTheme.titleLarge,
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 8),
        Expanded(
          child: AIRecommendationWidget(
            userProfile: _userProfile!,
            onFoodSelected: _showFoodDetail,
          ),
        ),
      ],
    );
  }

  Widget _buildFoodRecommendationsTab() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: DropdownButtonFormField<String>(
            decoration: const InputDecoration(
              labelText: 'Tipo di pasto',
              border: OutlineInputBorder(),
            ),
            value: _selectedMealType,
            items: _mealTypes.map((type) {
              return DropdownMenuItem<String>(
                value: type,
                child: Text(_getMealTypeName(type)),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedMealType = value;
              });
            },
          ),
        ),
        Expanded(
          child: AIRecommendationWidget(
            userProfile: _userProfile!,
            mealType: _selectedMealType,
            onFoodSelected: _showFoodDetail,
          ),
        ),
      ],
    );
  }

  Widget _buildAnalysisTab() {
    return FutureBuilder<Map<String, dynamic>>(
      future: AIService.getInstance().then((service) => service.getAIStats()),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text(
              'Errore nel caricamento delle statistiche: ${snapshot.error}',
              style: const TextStyle(color: Colors.red),
            ),
          );
        }

        if (!snapshot.hasData) {
          return const Center(child: Text('Nessuna statistica disponibile'));
        }

        final stats = snapshot.data!;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Statistiche del sistema AI',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const Divider(),
                      _buildStatItem('Utenti totali', '${stats['totalUsers']}'),
                      _buildStatItem('Preferenze totali', '${stats['totalPreferences']}'),
                      _buildStatItem('Media preferenze per utente', '${stats['averagePreferencesPerUser'].toStringAsFixed(1)}'),
                      _buildStatItem('Sistema inizializzato', stats['isInitialized'] ? 'Sì' : 'No'),
                      _buildStatItem('Ultimo aggiornamento', _formatDateTime(stats['lastUpdated'])),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Le tue preferenze apprese',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const Divider(),
                      _buildUserPreferences(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(value),
        ],
      ),
    );
  }

  Widget _buildUserPreferences() {
    return FutureBuilder<List<LearnedPreference>>(
      future: AIService.getInstance().then(
        (service) => service.getLearnedPreferences(_userProfile!.id)
      ),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (snapshot.hasError) {
          return Center(
            child: Text(
              'Errore nel caricamento delle preferenze: ${snapshot.error}',
              style: const TextStyle(color: Colors.red),
            ),
          );
        }

        final preferences = snapshot.data ?? [];

        if (preferences.isEmpty) {
          return const Padding(
            padding: EdgeInsets.all(16.0),
            child: Text(
              'Nessuna preferenza appresa. Fornisci feedback sugli alimenti per aiutare l\'AI a imparare le tue preferenze.',
              textAlign: TextAlign.center,
            ),
          );
        }

        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: preferences.length,
          itemBuilder: (context, index) {
            final preference = preferences[index];
            return FutureBuilder<Food?>(
              future: _getFoodById(preference.foodId),
              builder: (context, foodSnapshot) {
                if (!foodSnapshot.hasData || foodSnapshot.data == null) {
                  return const SizedBox.shrink();
                }

                final food = foodSnapshot.data!;
                final score = preference.preferenceScore;

                return ListTile(
                  title: Text(food.name),
                  subtitle: Text('Aggiornato il ${_formatDateTime(preference.updatedAt.toIso8601String())}'),
                  trailing: _buildPreferenceIndicator(score),
                  onTap: () => _showFoodDetail(food),
                );
              },
            );
          },
        );
      },
    );
  }

  Widget _buildPreferenceIndicator(double score) {
    final color = score > 0 ? Colors.green : (score < 0 ? Colors.red : Colors.grey);
    final icon = score > 0 ? Icons.thumb_up : (score < 0 ? Icons.thumb_down : Icons.thumbs_up_down);
    final label = score > 0 ? 'Ti piace' : (score < 0 ? 'Non ti piace' : 'Neutro');

    return Chip(
      backgroundColor: color.withOpacity(0.1),
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(color: color),
          ),
        ],
      ),
    );
  }

  Future<Food?> _getFoodById(String id) async {
    try {
      final foodDatabaseService = await FoodDatabaseService.getInstance();
      return await foodDatabaseService.getFoodById(id);
    } catch (e) {
      print('Errore nel caricamento dell\'alimento $id: $e');
      return null;
    }
  }

  String _getMealTypeName(String mealType) {
    switch (mealType) {
      case 'breakfast':
        return 'Colazione';
      case 'lunch':
        return 'Pranzo';
      case 'dinner':
        return 'Cena';
      case 'snack':
        return 'Spuntino';
      default:
        return mealType;
    }
  }

  String _formatDateTime(String isoString) {
    final dateTime = DateTime.parse(isoString);
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
