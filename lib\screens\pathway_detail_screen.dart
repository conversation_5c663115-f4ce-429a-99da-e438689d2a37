import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/welljourney_models.dart';
import '../models/quiz_models.dart';
import '../controllers/welljourney_controller.dart';
import '../services/quiz_service.dart';
import '../theme/dr_staffilano_theme.dart';
import 'quiz_screen.dart';

/// Schermata di dettaglio di un percorso guidato
class PathwayDetailScreen extends StatefulWidget {
  final GuidedPathway pathway;

  const PathwayDetailScreen({
    Key? key,
    required this.pathway,
  }) : super(key: key);

  @override
  State<PathwayDetailScreen> createState() => _PathwayDetailScreenState();
}

class _PathwayDetailScreenState extends State<PathwayDetailScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<WellJourneyController>(
      builder: (context, controller, child) {
        final completionPercentage = widget.pathway.getCompletionPercentage(controller.completedModules);
        final isEnrolled = controller.isEnrolledInPathway(widget.pathway.id);

        return Scaffold(
      backgroundColor: DrStaffilanoTheme.backgroundLight,
      body: Stack(
        children: [
          CustomScrollView(
            slivers: [
              _buildSliverAppBar(),
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildPathwayInfo(),
                      const SizedBox(height: 24),
                      _buildDrStaffilanoInsight(),
                      const SizedBox(height: 24),
                      _buildBenefitsSection(),
                      const SizedBox(height: 24),
                      _buildProgressSection(completionPercentage, isEnrolled),
                      const SizedBox(height: 24),
                      _buildModulesSection(controller),
                      const SizedBox(height: 100), // Bottom padding for floating button
                    ],
                  ),
                ),
              ),
            ],
          ),
          // Floating Action Button
          Positioned(
            bottom: 20,
            left: 20,
            right: 20,
            child: Center(
              child: SizedBox(
                width: 280,
                height: 56,
                child: ElevatedButton.icon(
                  onPressed: isEnrolled ? () => _continuePathway(controller) : () => _enrollInPathway(controller),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.pathway.primaryColor,
                    foregroundColor: Colors.white,
                    elevation: 8,
                    shadowColor: widget.pathway.primaryColor.withOpacity(0.3),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(28),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 16,
                    ),
                  ),
                  icon: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(
                      isEnrolled ? Icons.play_arrow : Icons.add,
                      size: 16,
                    ),
                  ),
                  label: Text(
                    isEnrolled ? 'Continua Percorso' : 'Inizia Percorso',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 0.3,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
      },
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      backgroundColor: widget.pathway.primaryColor,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          widget.pathway.title,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                widget.pathway.primaryColor,
                widget.pathway.primaryColor.withOpacity(0.8),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: Center(
            child: Icon(
              widget.pathway.category.icon,
              size: 80,
              color: Colors.white.withOpacity(0.3),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPathwayInfo() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: widget.pathway.category.icon == Icons.favorite
                    ? DrStaffilanoTheme.primaryGreen.withOpacity(0.1)
                    : widget.pathway.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  widget.pathway.category.displayName,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: widget.pathway.category.icon == Icons.favorite
                      ? DrStaffilanoTheme.primaryGreen
                      : widget.pathway.primaryColor,
                  ),
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: widget.pathway.difficulty.color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  widget.pathway.difficulty.displayName,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: widget.pathway.difficulty.color,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            widget.pathway.description,
            style: const TextStyle(
              fontSize: 16,
              height: 1.5,
              color: DrStaffilanoTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildInfoChip(
                Icons.schedule,
                '${widget.pathway.estimatedDays} giorni',
                DrStaffilanoTheme.secondaryBlue,
              ),
              const SizedBox(width: 12),
              _buildInfoChip(
                Icons.school,
                '${widget.pathway.modules.length} moduli',
                DrStaffilanoTheme.accentGold,
              ),
              if (widget.pathway.isPremium) ...[
                const SizedBox(width: 12),
                _buildInfoChip(
                  Icons.star,
                  'Premium',
                  Colors.purple,
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip(IconData icon, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrStaffilanoInsight() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.secondaryBlue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: DrStaffilanoTheme.secondaryBlue.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: DrStaffilanoTheme.secondaryBlue,
                child: const Icon(
                  Icons.person,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Parola del Dr. Staffilano',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: DrStaffilanoTheme.secondaryBlue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            widget.pathway.drStaffilanoInsight,
            style: const TextStyle(
              fontSize: 16,
              height: 1.5,
              color: DrStaffilanoTheme.textPrimary,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBenefitsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Benefici del Percorso',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: DrStaffilanoTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          ...widget.pathway.benefits.map((benefit) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: DrStaffilanoTheme.primaryGreen,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      benefit,
                      style: const TextStyle(
                        fontSize: 16,
                        color: DrStaffilanoTheme.textPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildProgressSection(double completionPercentage, bool isEnrolled) {
    if (!isEnrolled && completionPercentage == 0) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Il tuo Progresso',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: DrStaffilanoTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          LinearProgressIndicator(
            value: completionPercentage,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(widget.pathway.primaryColor),
            minHeight: 8,
          ),
          const SizedBox(height: 8),
          Text(
            '${(completionPercentage * 100).round()}% completato',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: widget.pathway.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModulesSection(WellJourneyController controller) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Moduli del Percorso',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: DrStaffilanoTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          ...widget.pathway.modules.asMap().entries.map((entry) {
            final index = entry.key;
            final module = entry.value;
            final isCompleted = controller.isModuleCompleted(module.id);
            final isLocked = _isModuleLocked(index, controller);

            return _buildModuleItem(module, index + 1, isCompleted, isLocked, controller);
          }).toList(),
        ],
      ),
    );
  }

  /// Determina se un modulo è bloccato
  bool _isModuleLocked(int moduleIndex, WellJourneyController controller) {
    // Se l'utente non è iscritto al percorso, tutti i moduli tranne il primo sono bloccati
    if (!controller.isEnrolledInPathway(widget.pathway.id)) {
      return moduleIndex > 0;
    }

    // Se l'utente è iscritto, un modulo è bloccato se il modulo precedente non è completato
    if (moduleIndex == 0) {
      return false; // Il primo modulo è sempre sbloccato se iscritti
    }

    // Controlla se tutti i moduli precedenti sono completati
    for (int i = 0; i < moduleIndex; i++) {
      final previousModule = widget.pathway.modules[i];
      if (!controller.isModuleCompleted(previousModule.id)) {
        return true; // Modulo bloccato se un modulo precedente non è completato
      }
    }

    return false; // Modulo sbloccato se tutti i precedenti sono completati
  }

  Widget _buildModuleItem(PathwayModule module, int number, bool isCompleted, bool isLocked, WellJourneyController controller) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isCompleted
          ? DrStaffilanoTheme.primaryGreen.withOpacity(0.1)
          : isLocked
            ? Colors.grey.shade100
            : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isCompleted
            ? DrStaffilanoTheme.primaryGreen.withOpacity(0.3)
            : Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Contenuto del modulo
          Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(12),
              onTap: isLocked ? () => _showLockedModuleMessage() : () => _showModuleDialog(module, controller),
              child: Padding(
                padding: const EdgeInsets.all(4),
                child: Row(
                  children: [
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: isCompleted
                          ? DrStaffilanoTheme.primaryGreen
                          : isLocked
                            ? Colors.grey.shade400
                            : widget.pathway.primaryColor,
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: isCompleted
                          ? const Icon(Icons.check, color: Colors.white, size: 18)
                          : isLocked
                            ? const Icon(Icons.lock, color: Colors.white, size: 18)
                            : Text(
                                number.toString(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            module.title,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: isLocked
                                ? Colors.grey.shade600
                                : DrStaffilanoTheme.textPrimary,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            module.description,
                            style: TextStyle(
                              fontSize: 14,
                              color: isLocked
                                ? Colors.grey.shade500
                                : DrStaffilanoTheme.textSecondary,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '${module.estimatedMinutes} min',
                            style: TextStyle(
                              fontSize: 12,
                              color: isLocked
                                ? Colors.grey.shade400
                                : widget.pathway.primaryColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Pulsante di azione
          const SizedBox(height: 12),
          _buildModuleActionButton(module, isCompleted, isLocked, controller),
        ],
      ),
    );
  }

  Widget _buildModuleActionButton(PathwayModule module, bool isCompleted, bool isLocked, WellJourneyController controller) {
    if (isLocked) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: Colors.grey.shade200,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.lock, color: Colors.grey.shade600, size: 16),
            const SizedBox(width: 8),
            Text(
              'Bloccato',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    if (isCompleted) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: DrStaffilanoTheme.primaryGreen, width: 1),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.check_circle, color: DrStaffilanoTheme.primaryGreen, size: 16),
            const SizedBox(width: 8),
            Text(
              'Completato',
              style: TextStyle(
                color: DrStaffilanoTheme.primaryGreen,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      );
    }

    // Modulo disponibile ma non completato
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () => _startModuleQuiz(module, controller),
        icon: const Icon(Icons.play_arrow, size: 18),
        label: const Text(
          'Inizia Modulo',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.3,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: widget.pathway.primaryColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 1,
        ),
      ),
    );
  }



  Future<void> _enrollInPathway(WellJourneyController controller) async {
    try {
      await controller.enrollInPathway(widget.pathway.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ti sei iscritto al percorso "${widget.pathway.title}"!'),
            backgroundColor: DrStaffilanoTheme.primaryGreen,
            action: SnackBarAction(
              label: 'Inizia',
              textColor: Colors.white,
              onPressed: () => _startFirstModule(controller),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Errore nell\'iscrizione: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _continuePathway(WellJourneyController controller) {
    // Trova il prossimo modulo non completato
    final nextModule = widget.pathway.modules.firstWhere(
      (module) => !controller.isModuleCompleted(module.id),
      orElse: () => widget.pathway.modules.first,
    );

    _showModuleDialog(nextModule, controller);
  }

  Future<void> _completeModule(PathwayModule module, WellJourneyController controller) async {
    try {
      await controller.completeModule(module.id, widget.pathway.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Modulo "${module.title}" completato! +50 punti'),
            backgroundColor: DrStaffilanoTheme.primaryGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Errore nel completamento: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _startFirstModule(WellJourneyController controller) {
    if (widget.pathway.modules.isNotEmpty) {
      _showModuleDialog(widget.pathway.modules.first, controller);
    }
  }

  /// Mostra un messaggio quando si clicca su un modulo bloccato
  void _showLockedModuleMessage() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.lock,
              color: Colors.orange,
              size: 24,
            ),
            const SizedBox(width: 8),
            const Text('Modulo Bloccato'),
          ],
        ),
        content: const Text(
          'Questo modulo non è ancora disponibile. Completa prima i moduli precedenti per sbloccarlo.',
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Ho capito'),
          ),
        ],
      ),
    );
  }

  void _showModuleDialog(PathwayModule module, WellJourneyController controller) {
    // Trova l'indice del modulo per controllare se è bloccato
    final moduleIndex = widget.pathway.modules.indexOf(module);
    final isLocked = _isModuleLocked(moduleIndex, controller);

    // Se il modulo è bloccato, mostra il messaggio di blocco invece del dialog
    if (isLocked) {
      _showLockedModuleMessage();
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(module.title),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                module.description,
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              Text(
                'Contenuto del modulo:',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                module.content,
                style: const TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: DrStaffilanoTheme.secondaryBlue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.format_quote,
                      color: DrStaffilanoTheme.secondaryBlue,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        module.drStaffilanoQuote,
                        style: TextStyle(
                          fontSize: 13,
                          fontStyle: FontStyle.italic,
                          color: DrStaffilanoTheme.secondaryBlue,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Chiudi'),
          ),
          if (!controller.isModuleCompleted(module.id))
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _startModuleQuiz(module, controller);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: DrStaffilanoTheme.primaryGreen,
              ),
              child: const Text('Inizia Quiz'),
            ),
        ],
      ),
    );
  }

  Future<void> _startModuleQuiz(PathwayModule module, WellJourneyController controller) async {
    // Controllo di sicurezza: verifica che il modulo non sia bloccato
    final moduleIndex = widget.pathway.modules.indexOf(module);
    final isLocked = _isModuleLocked(moduleIndex, controller);

    if (isLocked) {
      _showLockedModuleMessage();
      return;
    }

    try {
      final quiz = QuizService.instance.getQuizForModule(module.id);

      if (quiz == null) {
        // Se non c'è quiz, completa direttamente il modulo
        await _completeModule(module, controller);
        return;
      }

      // Controlla se l'utente ha già passato il quiz
      final hasPassed = await QuizService.instance.hasPassedQuiz('user_001', quiz.id);

      if (hasPassed) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Hai già completato questo quiz con successo!'),
              backgroundColor: DrStaffilanoTheme.primaryGreen,
            ),
          );
        }
        return;
      }

      // Naviga alla schermata del quiz
      if (mounted) {
        final result = await Navigator.push<bool>(
          context,
          MaterialPageRoute(
            builder: (context) => QuizScreen(
              quiz: quiz,
              moduleId: module.id,
            ),
          ),
        );

        // Se il quiz è stato completato con successo, il modulo è già stato marcato come completato
        if (result == true && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Modulo "${module.title}" completato con successo!'),
              backgroundColor: DrStaffilanoTheme.primaryGreen,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Errore nell\'avvio del quiz: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
