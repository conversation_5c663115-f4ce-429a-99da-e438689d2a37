import 'package:flutter/material.dart';
import '../models/user_profile.dart';
import '../utils/responsive_utils.dart';
import '../theme/dr_staffilano_theme.dart';

/// Widget che mostra informazioni sui target proteici per atleti
/// Integrato con il design system Dr. Staffilano
class AthleticProteinInfoWidget extends StatelessWidget {
  final UserProfile userProfile;
  final bool showDetailedInfo;

  const AthleticProteinInfoWidget({
    Key? key,
    required this.userProfile,
    this.showDetailedInfo = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: ResponsiveUtils.isMobile(context) ? 16 : 24,
        vertical: ResponsiveUtils.isMobile(context) ? 8 : 12,
      ),
      padding: EdgeInsets.all(ResponsiveUtils.isMobile(context) ? 16 : 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
            DrStaffilanoTheme.professionalBlue.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: DrStaffilanoTheme.primaryGreen.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context),
          if (showDetailedInfo) ...[
            const SizedBox(height: 16),
            _buildProteinTargets(context),
            const SizedBox(height: 12),
            _buildActivityLevelInfo(context),
            if (_isAthlete()) ...[
              const SizedBox(height: 12),
              _buildAthleteSpecificInfo(context),
            ],
          ],
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final isAthlete = _isAthlete();
    
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isAthlete 
                ? DrStaffilanoTheme.goldAccent.withOpacity(0.2)
                : DrStaffilanoTheme.primaryGreen.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            isAthlete ? Icons.fitness_center : Icons.restaurant,
            color: isAthlete 
                ? DrStaffilanoTheme.goldAccent
                : DrStaffilanoTheme.primaryGreen,
            size: ResponsiveUtils.isMobile(context) ? 20 : 24,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                isAthlete ? 'Piano Proteico Atletico' : 'Piano Proteico Standard',
                style: TextStyle(
                  fontSize: ResponsiveUtils.isMobile(context) ? 16 : 18,
                  fontWeight: FontWeight.bold,
                  color: DrStaffilanoTheme.textPrimary,
                ),
              ),
              Text(
                _getActivityLevelDescription(),
                style: TextStyle(
                  fontSize: ResponsiveUtils.isMobile(context) ? 12 : 14,
                  color: DrStaffilanoTheme.textSecondary,
                ),
              ),
            ],
          ),
        ),
        if (isAthlete)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: DrStaffilanoTheme.goldAccent,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              'ATLETA',
              style: TextStyle(
                fontSize: ResponsiveUtils.isMobile(context) ? 10 : 12,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildProteinTargets(BuildContext context) {
    final proteinPerKg = userProfile.calculateProteinPerKgBodyWeight();
    final totalProtein = userProfile.calculateOptimalProteinGrams();
    final macroDistribution = userProfile.calculateMacroDistribution();
    final proteinPercentage = (macroDistribution['proteins']! * 100).round();

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.7),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.track_changes,
                color: DrStaffilanoTheme.primaryGreen,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'Target Proteici Giornalieri',
                style: TextStyle(
                  fontSize: ResponsiveUtils.isMobile(context) ? 14 : 16,
                  fontWeight: FontWeight.w600,
                  color: DrStaffilanoTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildProteinMetric(
                  context,
                  'Per Kg Corporeo',
                  '${proteinPerKg.toStringAsFixed(1)}g/kg',
                  Icons.scale,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildProteinMetric(
                  context,
                  'Totale Giornaliero',
                  '${totalProtein}g',
                  Icons.restaurant_menu,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildProteinMetric(
                  context,
                  '% Calorie',
                  '$proteinPercentage%',
                  Icons.pie_chart,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProteinMetric(BuildContext context, String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.primaryGreen.withOpacity(0.05),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: DrStaffilanoTheme.primaryGreen,
            size: ResponsiveUtils.isMobile(context) ? 16 : 18,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: ResponsiveUtils.isMobile(context) ? 14 : 16,
              fontWeight: FontWeight.bold,
              color: DrStaffilanoTheme.primaryGreen,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: ResponsiveUtils.isMobile(context) ? 10 : 12,
              color: DrStaffilanoTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActivityLevelInfo(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.professionalBlue.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: DrStaffilanoTheme.professionalBlue.withOpacity(0.1),
        ),
      ),
      child: Row(
        children: [
          Icon(
            _getActivityIcon(),
            color: DrStaffilanoTheme.professionalBlue,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Livello di Attività: ${_getActivityLevelName()}',
                  style: TextStyle(
                    fontSize: ResponsiveUtils.isMobile(context) ? 14 : 16,
                    fontWeight: FontWeight.w600,
                    color: DrStaffilanoTheme.textPrimary,
                  ),
                ),
                Text(
                  _getActivityLevelDescription(),
                  style: TextStyle(
                    fontSize: ResponsiveUtils.isMobile(context) ? 12 : 14,
                    color: DrStaffilanoTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAthleteSpecificInfo(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.goldAccent.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: DrStaffilanoTheme.goldAccent.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.star,
                color: DrStaffilanoTheme.goldAccent,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'Benefici per Atleti',
                style: TextStyle(
                  fontSize: ResponsiveUtils.isMobile(context) ? 14 : 16,
                  fontWeight: FontWeight.w600,
                  color: DrStaffilanoTheme.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ..._getAthleteBenefits().map((benefit) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: DrStaffilanoTheme.primaryGreen,
                  size: 14,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    benefit,
                    style: TextStyle(
                      fontSize: ResponsiveUtils.isMobile(context) ? 12 : 14,
                      color: DrStaffilanoTheme.textSecondary,
                    ),
                  ),
                ),
              ],
            ),
          )).toList(),
        ],
      ),
    );
  }

  bool _isAthlete() {
    return userProfile.activityLevel == ActivityLevel.veryActive ||
           userProfile.activityLevel == ActivityLevel.extremelyActive;
  }

  String _getActivityLevelName() {
    switch (userProfile.activityLevel) {
      case ActivityLevel.sedentary:
        return 'Sedentario';
      case ActivityLevel.lightlyActive:
        return 'Leggermente Attivo';
      case ActivityLevel.moderatelyActive:
        return 'Moderatamente Attivo';
      case ActivityLevel.veryActive:
        return 'Molto Attivo';
      case ActivityLevel.extremelyActive:
        return 'Estremamente Attivo';
    }
  }

  String _getActivityLevelDescription() {
    switch (userProfile.activityLevel) {
      case ActivityLevel.sedentary:
        return 'Lavoro sedentario, esercizio minimo';
      case ActivityLevel.lightlyActive:
        return '1-3 allenamenti a settimana';
      case ActivityLevel.moderatelyActive:
        return '3-5 allenamenti a settimana';
      case ActivityLevel.veryActive:
        return '6-7 allenamenti a settimana';
      case ActivityLevel.extremelyActive:
        return 'Allenamento quotidiano intenso, sport agonistico';
    }
  }

  IconData _getActivityIcon() {
    switch (userProfile.activityLevel) {
      case ActivityLevel.sedentary:
        return Icons.chair;
      case ActivityLevel.lightlyActive:
        return Icons.directions_walk;
      case ActivityLevel.moderatelyActive:
        return Icons.directions_run;
      case ActivityLevel.veryActive:
        return Icons.fitness_center;
      case ActivityLevel.extremelyActive:
        return Icons.sports;
    }
  }

  List<String> _getAthleteBenefits() {
    return [
      'Recupero muscolare ottimizzato',
      'Sintesi proteica massimizzata',
      'Alimenti italiani ad alto valore biologico',
      'Distribuzione proteica per pasto (min 20g)',
      'Supporto per performance atletiche',
    ];
  }
}
