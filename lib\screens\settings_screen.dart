import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/translation_service.dart';
import '../services/specific_diet_generator_service.dart';
import '../theme/app_theme.dart';
import 'translation_manager_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  _SettingsScreenState createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _isLoading = true;
  late TranslationService _translationService;

  // Impostazioni di traduzione
  String _sourceLanguage = 'en';
  String _targetLanguage = 'it';
  bool _useOnlineApi = false;
  String _apiKey = '';
  bool _translateFoodNames = true;

  // Lingue supportate
  final List<Map<String, String>> _supportedLanguages = [
    {'code': 'it', 'name': 'Italiano'},
    {'code': 'en', 'name': 'English'},
    {'code': 'es', 'name': 'Español'},
    {'code': 'fr', 'name': 'Français'},
    {'code': 'de', 'name': 'Deutsch'},
  ];

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Inizializza il servizio di traduzione
      _translationService = await TranslationService.getInstance();

      // Carica le impostazioni
      final prefs = await SharedPreferences.getInstance();

      setState(() {
        _sourceLanguage = _translationService.sourceLanguage;
        _targetLanguage = _translationService.targetLanguage;
        _useOnlineApi = _translationService.useOnlineApi;
        _apiKey = _translationService.apiKey ?? '';

        // Carica l'impostazione per la traduzione dei nomi degli alimenti
        _translateFoodNames = prefs.getBool('translate_food_names') ?? true;
      });
    } catch (e) {
      print('Errore nel caricamento delle impostazioni: $e');
      _showErrorSnackBar('Errore nel caricamento delle impostazioni');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Salva le impostazioni di traduzione
      await _translationService.setSourceLanguage(_sourceLanguage);
      await _translationService.setTargetLanguage(_targetLanguage);
      await _translationService.setUseOnlineApi(_useOnlineApi);
      await _translationService.setApiKey(_apiKey);

      // Salva l'impostazione per la traduzione dei nomi degli alimenti
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('translate_food_names', _translateFoodNames);

      // Note: Translation functionality will be implemented in future versions
      // The consolidated SpecificDietGeneratorService doesn't currently support translation

      _showSuccessSnackBar('Impostazioni salvate con successo');
    } catch (e) {
      print('Errore nel salvataggio delle impostazioni: $e');
      _showErrorSnackBar('Errore nel salvataggio delle impostazioni');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Impostazioni'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _isLoading ? null : _saveSettings,
            tooltip: 'Salva impostazioni',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Sezione Traduzione
                  _buildSectionHeader('Traduzione', FontAwesomeIcons.language),

                  const SizedBox(height: 16),

                  // Opzione per tradurre i nomi degli alimenti
                  SwitchListTile(
                    title: const Text('Tradurre i nomi degli alimenti'),
                    subtitle: const Text('Mostra i nomi degli alimenti nella lingua selezionata'),
                    value: _translateFoodNames,
                    onChanged: (value) {
                      setState(() {
                        _translateFoodNames = value;
                      });
                    },
                    activeColor: AppTheme.primaryColor,
                  ),

                  const SizedBox(height: 16),

                  // Lingua di destinazione
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Lingua di destinazione',
                      border: OutlineInputBorder(),
                    ),
                    value: _targetLanguage,
                    items: _supportedLanguages.map((language) {
                      return DropdownMenuItem<String>(
                        value: language['code'],
                        child: Text(language['name']!),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _targetLanguage = value;
                        });
                      }
                    },
                  ),

                  const SizedBox(height: 16),

                  // Lingua di origine
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Lingua di origine',
                      border: OutlineInputBorder(),
                    ),
                    value: _sourceLanguage,
                    items: _supportedLanguages.map((language) {
                      return DropdownMenuItem<String>(
                        value: language['code'],
                        child: Text(language['name']!),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _sourceLanguage = value;
                        });
                      }
                    },
                  ),

                  const SizedBox(height: 16),

                  // Opzione per utilizzare l'API online
                  SwitchListTile(
                    title: const Text('Utilizzare API online'),
                    subtitle: const Text('Utilizza un servizio di traduzione online per tradurre i testi'),
                    value: _useOnlineApi,
                    onChanged: (value) {
                      setState(() {
                        _useOnlineApi = value;
                      });
                    },
                    activeColor: AppTheme.primaryColor,
                  ),

                  // Chiave API (visibile solo se l'API online è abilitata)
                  if (_useOnlineApi) ...[
                    const SizedBox(height: 16),

                    TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'Chiave API',
                        border: OutlineInputBorder(),
                        helperText: 'Inserisci la chiave API per il servizio di traduzione',
                      ),
                      initialValue: _apiKey,
                      onChanged: (value) {
                        setState(() {
                          _apiKey = value;
                        });
                      },
                      obscureText: true,
                    ),
                  ],

                  const SizedBox(height: 32),

                  // Sezione Gestione Traduzioni
                  _buildSectionHeader('Gestione Traduzioni', FontAwesomeIcons.database),

                  const SizedBox(height: 16),

                  // Pulsante per visualizzare le traduzioni salvate
                  ElevatedButton.icon(
                    onPressed: _viewSavedTranslations,
                    icon: const Icon(FontAwesomeIcons.list),
                    label: const Text('Visualizza traduzioni salvate'),
                    style: ElevatedButton.styleFrom(
                      minimumSize: const Size(double.infinity, 50),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Pulsante per importare traduzioni
                  ElevatedButton.icon(
                    onPressed: _importTranslations,
                    icon: const Icon(FontAwesomeIcons.fileImport),
                    label: const Text('Importa traduzioni'),
                    style: ElevatedButton.styleFrom(
                      minimumSize: const Size(double.infinity, 50),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Pulsante per esportare traduzioni
                  ElevatedButton.icon(
                    onPressed: _exportTranslations,
                    icon: const Icon(FontAwesomeIcons.fileExport),
                    label: const Text('Esporta traduzioni'),
                    style: ElevatedButton.styleFrom(
                      minimumSize: const Size(double.infinity, 50),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          color: AppTheme.primaryColor,
          size: 24,
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
        ),
      ],
    ).animate().fadeIn(duration: 300.ms, delay: 100.ms);
  }

  void _viewSavedTranslations() {
    // Apri la schermata di gestione delle traduzioni
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const TranslationManagerScreen(),
      ),
    );
  }

  void _importTranslations() {
    // Implementare l'importazione delle traduzioni
    // (ad esempio, utilizzando un file picker)
  }

  void _exportTranslations() {
    // Implementare l'esportazione delle traduzioni
    // (ad esempio, salvando un file JSON)
  }
}
