import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../controllers/advanced_diet_controller.dart';
import '../models/advanced_user_profile.dart';
import '../models/user_profile.dart';
import '../widgets/special_conditions_selector.dart';
import '../theme/app_theme.dart';

/// Schermata per le impostazioni del profilo utente
class UserProfileSettingsScreen extends StatefulWidget {
  const UserProfileSettingsScreen({Key? key}) : super(key: key);

  @override
  State<UserProfileSettingsScreen> createState() => _UserProfileSettingsScreenState();
}

class _UserProfileSettingsScreenState extends State<UserProfileSettingsScreen> {
  // Controller per i campi di testo
  final _nameController = TextEditingController();
  final _ageController = TextEditingController();
  final _heightController = TextEditingController();
  final _weightController = TextEditingController();

  // Stato del profilo utente
  late UserProfile _baseProfile;
  late AdvancedUserProfile _advancedProfile;

  // Stato della UI
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _initializeProfiles();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _ageController.dispose();
    _heightController.dispose();
    _weightController.dispose();
    super.dispose();
  }

  /// Inizializza i profili utente
  void _initializeProfiles() {
    // Crea un profilo base di esempio
    _baseProfile = UserProfile(
      id: 'user_001',
      name: 'Utente',
      age: 35,
      gender: Gender.male,
      height: 175,
      weight: 75,
      activityLevel: ActivityLevel.moderatelyActive,
      goal: Goal.maintenance,
      mealsPerDay: 4,
    );

    // Ottieni il controller
    final controller = Provider.of<AdvancedDietController>(context, listen: false);

    // Crea un profilo avanzato basato sul profilo base
    _advancedProfile = controller.createAdvancedProfile(_baseProfile);

    // Imposta il profilo nel controller
    controller.setAdvancedProfile(_advancedProfile);

    // Inizializza i controller di testo
    _nameController.text = _baseProfile.name;
    _ageController.text = _baseProfile.age.toString();
    _heightController.text = _baseProfile.height.toString();
    _weightController.text = _baseProfile.weight.toString();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Impostazioni Profilo'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveProfile,
          ),
        ],
      ),
      body: Consumer<AdvancedDietController>(
        builder: (context, controller, child) {
          return Column(
            children: [
              // Tabs
              Container(
                color: Theme.of(context).primaryColor,
                child: TabBar(
                  onTap: (index) {
                    setState(() {
                      _currentIndex = index;
                    });
                  },
                  tabs: const [
                    Tab(text: 'Profilo Base'),
                    Tab(text: 'Condizioni Speciali'),
                    Tab(text: 'Preferenze Dietetiche'),
                  ],
                ),
              ),

              // Tab content
              Expanded(
                child: IndexedStack(
                  index: _currentIndex,
                  children: [
                    _buildBaseProfileTab(),
                    _buildSpecialConditionsTab(controller),
                    _buildDietaryPreferencesTab(),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// Costruisce la scheda del profilo base
  Widget _buildBaseProfileTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Titolo
          Text(
            'Informazioni Personali',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16.0),

          // Nome
          TextField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'Nome',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16.0),

          // Età
          TextField(
            controller: _ageController,
            decoration: const InputDecoration(
              labelText: 'Età',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 16.0),

          // Genere
          DropdownButtonFormField<Gender>(
            decoration: const InputDecoration(
              labelText: 'Genere',
              border: OutlineInputBorder(),
            ),
            value: _baseProfile.gender,
            items: Gender.values.map((gender) {
              return DropdownMenuItem<Gender>(
                value: gender,
                child: Text(gender == Gender.male ? 'Maschile' : 'Femminile'),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _baseProfile = _baseProfile.copyWith(gender: value);
                });
              }
            },
          ),
          const SizedBox(height: 16.0),

          // Altezza
          TextField(
            controller: _heightController,
            decoration: const InputDecoration(
              labelText: 'Altezza (cm)',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 16.0),

          // Peso
          TextField(
            controller: _weightController,
            decoration: const InputDecoration(
              labelText: 'Peso (kg)',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 16.0),

          // Livello di attività
          DropdownButtonFormField<ActivityLevel>(
            decoration: const InputDecoration(
              labelText: 'Livello di Attività',
              border: OutlineInputBorder(),
            ),
            value: _baseProfile.activityLevel,
            items: ActivityLevel.values.map((level) {
              return DropdownMenuItem<ActivityLevel>(
                value: level,
                child: Text(_getActivityLevelName(level)),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _baseProfile = _baseProfile.copyWith(activityLevel: value);
                });
              }
            },
          ),
          const SizedBox(height: 16.0),

          // Obiettivo
          DropdownButtonFormField<Goal>(
            decoration: const InputDecoration(
              labelText: 'Obiettivo',
              border: OutlineInputBorder(),
            ),
            value: _baseProfile.goal,
            items: Goal.values.map((goal) {
              return DropdownMenuItem<Goal>(
                value: goal,
                child: Text(_getGoalName(goal)),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _baseProfile = _baseProfile.copyWith(goal: value);
                });
              }
            },
          ),
          const SizedBox(height: 16.0),

          // Pasti al giorno
          DropdownButtonFormField<int>(
            decoration: const InputDecoration(
              labelText: 'Pasti al Giorno',
              border: OutlineInputBorder(),
            ),
            value: _baseProfile.mealsPerDay,
            items: [3, 4, 5, 6].map((count) {
              return DropdownMenuItem<int>(
                value: count,
                child: Text('$count pasti'),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _baseProfile = _baseProfile.copyWith(mealsPerDay: value);
                });
              }
            },
          ),
          const SizedBox(height: 24.0),

          // Calorie e macronutrienti calcolati
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Fabbisogno Calorico Calcolato',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8.0),
                  Text('Calorie giornaliere: ${_baseProfile.calculateCalorieTarget()} kcal'),
                  const SizedBox(height: 16.0),
                  Text(
                    'Distribuzione Macronutrienti',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8.0),
                  _buildMacroDistributionTable(_baseProfile.calculateMacroDistribution()),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Costruisce la scheda delle condizioni speciali
  Widget _buildSpecialConditionsTab(AdvancedDietController controller) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Titolo
          Text(
            'Condizioni Speciali',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8.0),
          Text(
            'Personalizza il tuo profilo con condizioni mediche, intolleranze alimentari e obiettivi di fitness specifici.',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 24.0),

          // Condizioni mediche
          MedicalConditionsSelector(
            selectedConditions: _advancedProfile.medicalConditions,
            onChanged: (conditions) {
              controller.updateMedicalConditions(conditions);
              setState(() {
                _advancedProfile = controller.advancedProfile!;
              });
            },
          ),
          const SizedBox(height: 16.0),

          // Intolleranze alimentari
          FoodIntolerancesSelector(
            selectedIntolerances: _advancedProfile.foodIntolerances,
            onChanged: (stringIntolerances) {
              // Converti le stringhe in enum FoodIntolerance
              List<FoodIntolerance> foodIntolerances = _convertStringToFoodIntolerances(stringIntolerances);
              controller.updateFoodIntolerances(foodIntolerances);
              setState(() {
                _advancedProfile = controller.advancedProfile!;
              });
            },
          ),
          const SizedBox(height: 16.0),

          // Obiettivi di fitness
          FitnessGoalSelector(
            selectedGoal: _advancedProfile.fitnessGoal,
            onChanged: (goal) {
              controller.updateFitnessGoal(goal);
              setState(() {
                _advancedProfile = controller.advancedProfile!;
              });
            },
          ),
          const SizedBox(height: 16.0),

          // Attività sportiva
          Card(
            margin: const EdgeInsets.all(8.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Attività Sportiva',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16.0),

                  // Tipo di sport
                  DropdownButtonFormField<SportType>(
                    decoration: const InputDecoration(
                      labelText: 'Sport Principale',
                      border: OutlineInputBorder(),
                    ),
                    value: _advancedProfile.primarySport,
                    items: SportType.values.map((sport) {
                      return DropdownMenuItem<SportType>(
                        value: sport,
                        child: Text(_getSportTypeName(sport)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        controller.updateSportInfo(primarySport: value);
                        setState(() {
                          _advancedProfile = controller.advancedProfile!;
                        });
                      }
                    },
                  ),
                  const SizedBox(height: 16.0),

                  // Intensità dell'allenamento
                  DropdownButtonFormField<TrainingIntensity>(
                    decoration: const InputDecoration(
                      labelText: 'Intensità dell\'Allenamento',
                      border: OutlineInputBorder(),
                    ),
                    value: _advancedProfile.trainingIntensity,
                    items: TrainingIntensity.values.map((intensity) {
                      return DropdownMenuItem<TrainingIntensity>(
                        value: intensity,
                        child: Text(_getTrainingIntensityName(intensity)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        controller.updateSportInfo(trainingIntensity: value);
                        setState(() {
                          _advancedProfile = controller.advancedProfile!;
                        });
                      }
                    },
                  ),
                  const SizedBox(height: 16.0),

                  // Giorni di allenamento a settimana
                  Row(
                    children: [
                      Expanded(
                        child: Text('Giorni di allenamento a settimana: ${_advancedProfile.trainingDaysPerWeek}'),
                      ),
                      Slider(
                        value: _advancedProfile.trainingDaysPerWeek.toDouble(),
                        min: 0,
                        max: 7,
                        divisions: 7,
                        label: _advancedProfile.trainingDaysPerWeek.toString(),
                        onChanged: (value) {
                          controller.updateSportInfo(trainingDaysPerWeek: value.round());
                          setState(() {
                            _advancedProfile = controller.advancedProfile!;
                          });
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 16.0),

                  // Minuti di allenamento per sessione
                  Row(
                    children: [
                      Expanded(
                        child: Text('Minuti per sessione: ${_advancedProfile.trainingMinutesPerSession}'),
                      ),
                      Slider(
                        value: _advancedProfile.trainingMinutesPerSession.toDouble(),
                        min: 0,
                        max: 180,
                        divisions: 18,
                        label: _advancedProfile.trainingMinutesPerSession.toString(),
                        onChanged: (value) {
                          controller.updateSportInfo(trainingMinutesPerSession: value.round());
                          setState(() {
                            _advancedProfile = controller.advancedProfile!;
                          });
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16.0),

          // Timing dei pasti
          Card(
            margin: const EdgeInsets.all(8.0),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Timing dei Pasti',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8.0),
                  Text(
                    'Seleziona la tua preferenza per la distribuzione dei pasti durante la giornata.',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 16.0),

                  Wrap(
                    spacing: 8.0,
                    runSpacing: 8.0,
                    children: MealTiming.values.map((timing) {
                      final isSelected = _advancedProfile.mealTiming == timing;

                      return ChoiceChip(
                        label: Text(_getMealTimingName(timing)),
                        selected: isSelected,
                        onSelected: (selected) {
                          if (selected) {
                            controller.updateMealTiming(timing);
                            setState(() {
                              _advancedProfile = controller.advancedProfile!;
                            });
                          }
                        },
                        backgroundColor: Colors.grey[200],
                        selectedColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Costruisce la scheda delle preferenze dietetiche
  Widget _buildDietaryPreferencesTab() {
    return const Center(
      child: Text('Preferenze dietetiche - In arrivo'),
    );
  }

  /// Costruisce una tabella per la distribuzione dei macronutrienti
  Widget _buildMacroDistributionTable(Map<String, double> macroDistribution) {
    return Table(
      border: TableBorder.all(
        color: Colors.grey[300]!,
        width: 1,
      ),
      children: [
        TableRow(
          decoration: BoxDecoration(
            color: Colors.grey[200],
          ),
          children: const [
            Padding(
              padding: EdgeInsets.all(8.0),
              child: Text('Macronutriente', style: TextStyle(fontWeight: FontWeight.bold)),
            ),
            Padding(
              padding: EdgeInsets.all(8.0),
              child: Text('Percentuale', style: TextStyle(fontWeight: FontWeight.bold)),
            ),
          ],
        ),
        TableRow(
          children: [
            const Padding(
              padding: EdgeInsets.all(8.0),
              child: Text('Proteine'),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text('${(macroDistribution['proteins']! * 100).round()}%'),
            ),
          ],
        ),
        TableRow(
          children: [
            const Padding(
              padding: EdgeInsets.all(8.0),
              child: Text('Carboidrati'),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text('${(macroDistribution['carbs']! * 100).round()}%'),
            ),
          ],
        ),
        TableRow(
          children: [
            const Padding(
              padding: EdgeInsets.all(8.0),
              child: Text('Grassi'),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text('${(macroDistribution['fats']! * 100).round()}%'),
            ),
          ],
        ),
      ],
    );
  }

  /// Salva il profilo utente
  void _saveProfile() {
    // Aggiorna il profilo base con i valori dei campi di testo
    _baseProfile = _baseProfile.copyWith(
      name: _nameController.text,
      age: int.tryParse(_ageController.text) ?? _baseProfile.age,
      height: int.tryParse(_heightController.text) ?? _baseProfile.height,
      weight: int.tryParse(_weightController.text) ?? _baseProfile.weight,
    );

    // Ottieni il controller
    final controller = Provider.of<AdvancedDietController>(context, listen: false);

    // Crea un nuovo profilo avanzato con il profilo base aggiornato
    _advancedProfile = AdvancedUserProfile(
      baseProfile: _baseProfile,
      medicalConditions: _advancedProfile.medicalConditions,
      foodIntolerances: _advancedProfile.foodIntolerances,
      fitnessGoal: _advancedProfile.fitnessGoal,
      primarySport: _advancedProfile.primarySport,
      trainingIntensity: _advancedProfile.trainingIntensity,
      trainingDaysPerWeek: _advancedProfile.trainingDaysPerWeek,
      trainingMinutesPerSession: _advancedProfile.trainingMinutesPerSession,
      mealTiming: _advancedProfile.mealTiming,
      isPregnant: _advancedProfile.isPregnant,
      isBreastfeeding: _advancedProfile.isBreastfeeding,
      nutritionalNeeds: _advancedProfile.nutritionalNeeds,
      dietaryPreferences: _advancedProfile.dietaryPreferences,
      mealDistribution: _advancedProfile.mealDistribution,
    );

    // Imposta il profilo nel controller
    controller.setAdvancedProfile(_advancedProfile);

    // Mostra un messaggio di conferma
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Profilo salvato con successo'),
      ),
    );
  }

  /// Ottieni il nome del livello di attività
  String _getActivityLevelName(ActivityLevel level) {
    switch (level) {
      case ActivityLevel.sedentary:
        return 'Sedentario';
      case ActivityLevel.lightlyActive:
        return 'Leggermente attivo';
      case ActivityLevel.moderatelyActive:
        return 'Moderatamente attivo';
      case ActivityLevel.veryActive:
        return 'Molto attivo';
      case ActivityLevel.extremelyActive:
        return 'Estremamente attivo';
    }
  }

  /// Converte le stringhe delle intolleranze in enum FoodIntolerance
  List<FoodIntolerance> _convertStringToFoodIntolerances(List<String> stringIntolerances) {
    List<FoodIntolerance> foodIntolerances = [];

    for (final intolerance in stringIntolerances) {
      switch (intolerance.toLowerCase()) {
        case 'glutine':
        case 'gluten':
          foodIntolerances.add(FoodIntolerance.gluten);
          break;
        case 'lattosio':
        case 'lactose':
          foodIntolerances.add(FoodIntolerance.lactose);
          break;
        case 'uova':
        case 'eggs':
          foodIntolerances.add(FoodIntolerance.eggs);
          break;
        case 'frutta a guscio':
        case 'nuts':
          foodIntolerances.add(FoodIntolerance.nuts);
          break;
        case 'crostacei':
        case 'shellfish':
          foodIntolerances.add(FoodIntolerance.shellfish);
          break;
        case 'soia':
        case 'soy':
          foodIntolerances.add(FoodIntolerance.soy);
          break;
        case 'pesce':
        case 'fish':
          foodIntolerances.add(FoodIntolerance.fish);
          break;
        case 'fruttosio':
        case 'fructose':
          foodIntolerances.add(FoodIntolerance.fructose);
          break;
        case 'istamina':
        case 'histamine':
          foodIntolerances.add(FoodIntolerance.histamine);
          break;
        case 'fodmap':
          foodIntolerances.add(FoodIntolerance.fodmap);
          break;
      }
    }

    if (foodIntolerances.isEmpty) {
      foodIntolerances.add(FoodIntolerance.none);
    }

    return foodIntolerances;
  }

  /// Ottieni il nome dell'obiettivo
  String _getGoalName(Goal goal) {
    switch (goal) {
      case Goal.weightLoss:
        return 'Perdita di peso';
      case Goal.maintenance:
        return 'Mantenimento';
      case Goal.weightGain:
        return 'Aumento di peso';
    }
  }

  /// Ottieni il nome del tipo di sport
  String _getSportTypeName(SportType sport) {
    switch (sport) {
      case SportType.none:
        return 'Nessuno sport';
      case SportType.running:
        return 'Corsa';
      case SportType.cycling:
        return 'Ciclismo';
      case SportType.swimming:
        return 'Nuoto';
      case SportType.weightlifting:
        return 'Sollevamento pesi';
      case SportType.yoga:
        return 'Yoga';
      case SportType.pilates:
        return 'Pilates';
      case SportType.martialArts:
        return 'Arti marziali';
      case SportType.teamSports:
        return 'Sport di squadra';
      case SportType.racquetSports:
        return 'Sport con racchetta';
      case SportType.hiking:
        return 'Escursionismo';
      case SportType.climbing:
        return 'Arrampicata';
      case SportType.crossfit:
        return 'CrossFit';
      case SportType.hiit:
        return 'HIIT';
      case SportType.dance:
        return 'Danza';
      case SportType.gymnastics:
        return 'Ginnastica';
    }
  }

  /// Ottieni il nome dell'intensità dell'allenamento
  String _getTrainingIntensityName(TrainingIntensity intensity) {
    switch (intensity) {
      case TrainingIntensity.none:
        return 'Nessun allenamento';
      case TrainingIntensity.light:
        return 'Leggero';
      case TrainingIntensity.moderate:
        return 'Moderato';
      case TrainingIntensity.vigorous:
        return 'Intenso';
      case TrainingIntensity.elite:
        return 'Elite';
    }
  }

  /// Ottieni il nome del timing dei pasti
  String _getMealTimingName(MealTiming timing) {
    switch (timing) {
      case MealTiming.standard:
        return 'Standard';
      case MealTiming.earlyBird:
        return 'Mattiniero';
      case MealTiming.nightOwl:
        return 'Notturno';
      case MealTiming.intermittentFasting:
        return 'Digiuno intermittente';
      case MealTiming.frequentSmallMeals:
        return 'Pasti piccoli e frequenti';
    }
  }
}
