import 'package:flutter/material.dart';
import '../models/meal.dart';
import '../theme/new_app_theme.dart';

/// Card moderna per visualizzare un pasto nel piano alimentare
class ModernMealCard extends StatelessWidget {
  final String title;
  final String time;
  final String description;
  final VoidCallback onTap;
  final VoidCallback onFavorite;
  final bool isFavorite;
  final Meal? meal;

  const ModernMealCard({
    Key? key,
    required this.title,
    required this.time,
    required this.description,
    required this.onTap,
    required this.onFavorite,
    this.isFavorite = false,
    this.meal,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: NewAppTheme.spacing),
      decoration: NewAppTheme.cardDecoration,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(NewAppTheme.borderRadius),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            child: Padding(
              padding: const EdgeInsets.all(NewAppTheme.spacing),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        title,
                        style: NewAppTheme.subtitleMedium,
                      ),
                      IconButton(
                        icon: Icon(
                          isFavorite
                              ? Icons.favorite
                              : Icons.favorite_border,
                          color: isFavorite
                              ? NewAppTheme.primaryColor
                              : NewAppTheme.textTertiaryColor,
                        ),
                        onPressed: onFavorite,
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                  const SizedBox(height: NewAppTheme.smallSpacing / 2),
                  Row(
                    children: [
                      Icon(
                        Icons.access_time,
                        size: 14,
                        color: NewAppTheme.textTertiaryColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        time,
                        style: NewAppTheme.bodySmall,
                      ),
                    ],
                  ),
                  const SizedBox(height: NewAppTheme.spacing),
                  Text(
                    description,
                    style: NewAppTheme.bodyMedium,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: NewAppTheme.spacing),
                  _buildExampleNutritionInfo(),
                  const SizedBox(height: NewAppTheme.spacing),
                  _buildRecipeButton(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildExampleNutritionInfo() {
    // Utilizziamo valori di esempio per la visualizzazione
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildNutrientBadge(
          'Calorie',
          '320',
          'kcal',
          NewAppTheme.primaryColor,
        ),
        _buildNutrientBadge(
          'Proteine',
          '15',
          'g',
          Colors.red,
        ),
        _buildNutrientBadge(
          'Carb',
          '42',
          'g',
          Colors.blue,
        ),
        _buildNutrientBadge(
          'Grassi',
          '12',
          'g',
          Colors.green,
        ),
      ],
    );
  }

  Widget _buildNutrientBadge(
      String label, String value, String unit, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: NewAppTheme.caption,
        ),
        const SizedBox(height: 2),
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: value,
                style: NewAppTheme.bodyMedium.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextSpan(
                text: unit,
                style: NewAppTheme.bodySmall.copyWith(
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRecipeButton() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: NewAppTheme.secondaryColor,
        borderRadius: BorderRadius.circular(NewAppTheme.smallBorderRadius),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: NewAppTheme.spacing,
          vertical: NewAppTheme.smallSpacing,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Ricetta',
              style: NewAppTheme.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Icon(
              Icons.arrow_forward,
              size: 18,
              color: NewAppTheme.primaryColor,
            ),
          ],
        ),
      ),
    );
  }
}
