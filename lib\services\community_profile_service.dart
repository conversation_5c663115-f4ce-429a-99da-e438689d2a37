import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/community_user.dart';
import '../models/community_post.dart';

/// Enum per le impostazioni di privacy del profilo
enum ProfilePrivacy {
  public('public', '<PERSON><PERSON><PERSON><PERSON>', 'Visibile a tutti'),
  friends('friends', 'Solo amici', 'Visibile solo agli amici'),
  private('private', 'Privato', 'Visibile solo a te');

  const ProfilePrivacy(this.id, this.displayName, this.description);
  final String id;
  final String displayName;
  final String description;
}

/// Modello per le impostazioni del profilo
class ProfileSettings {
  final ProfilePrivacy profileVisibility;
  final ProfilePrivacy postsVisibility;
  final ProfilePrivacy friendsVisibility;
  final bool showOnlineStatus;
  final bool allowFriendRequests;
  final bool showBadges;
  final bool showStats;

  ProfileSettings({
    this.profileVisibility = ProfilePrivacy.public,
    this.postsVisibility = ProfilePrivacy.friends,
    this.friendsVisibility = ProfilePrivacy.friends,
    this.showOnlineStatus = true,
    this.allowFriendRequests = true,
    this.showBadges = true,
    this.showStats = true,
  });

  Map<String, dynamic> toJson() => {
    'profileVisibility': profileVisibility.id,
    'postsVisibility': postsVisibility.id,
    'friendsVisibility': friendsVisibility.id,
    'showOnlineStatus': showOnlineStatus,
    'allowFriendRequests': allowFriendRequests,
    'showBadges': showBadges,
    'showStats': showStats,
  };

  factory ProfileSettings.fromJson(Map<String, dynamic> json) => ProfileSettings(
    profileVisibility: ProfilePrivacy.values.firstWhere(
      (p) => p.id == json['profileVisibility'],
      orElse: () => ProfilePrivacy.public,
    ),
    postsVisibility: ProfilePrivacy.values.firstWhere(
      (p) => p.id == json['postsVisibility'],
      orElse: () => ProfilePrivacy.friends,
    ),
    friendsVisibility: ProfilePrivacy.values.firstWhere(
      (p) => p.id == json['friendsVisibility'],
      orElse: () => ProfilePrivacy.friends,
    ),
    showOnlineStatus: json['showOnlineStatus'] ?? true,
    allowFriendRequests: json['allowFriendRequests'] ?? true,
    showBadges: json['showBadges'] ?? true,
    showStats: json['showStats'] ?? true,
  );

  ProfileSettings copyWith({
    ProfilePrivacy? profileVisibility,
    ProfilePrivacy? postsVisibility,
    ProfilePrivacy? friendsVisibility,
    bool? showOnlineStatus,
    bool? allowFriendRequests,
    bool? showBadges,
    bool? showStats,
  }) => ProfileSettings(
    profileVisibility: profileVisibility ?? this.profileVisibility,
    postsVisibility: postsVisibility ?? this.postsVisibility,
    friendsVisibility: friendsVisibility ?? this.friendsVisibility,
    showOnlineStatus: showOnlineStatus ?? this.showOnlineStatus,
    allowFriendRequests: allowFriendRequests ?? this.allowFriendRequests,
    showBadges: showBadges ?? this.showBadges,
    showStats: showStats ?? this.showStats,
  );
}

/// Servizio per gestire i profili community
class CommunityProfileService extends ChangeNotifier {
  static const String _currentUserKey = 'current_community_user';
  static const String _profileSettingsKey = 'community_profile_settings';
  static const String _userPostsKey = 'community_user_posts';
  static const String _userStatsKey = 'community_user_stats';

  CommunityUser? _currentUser;
  ProfileSettings _profileSettings = ProfileSettings();
  final Map<String, List<String>> _userPosts = {}; // userId -> List<postId>
  final Map<String, Map<String, dynamic>> _userStats = {};

  /// Utente corrente
  CommunityUser? get currentUser => _currentUser;

  /// Impostazioni profilo
  ProfileSettings get profileSettings => _profileSettings;

  /// Post dell'utente per ID
  Map<String, List<String>> get userPosts => Map.unmodifiable(_userPosts);

  /// Inizializza il servizio
  Future<void> initialize() async {
    try {
      await _loadCurrentUser();
      await _loadProfileSettings();
      await _loadUserPosts();
      await _loadUserStats();

      if (kDebugMode) {
        print('✅ CommunityProfileService inizializzato');
        print('👤 Utente corrente: ${_currentUser?.username ?? 'Non impostato'}');
        print('⚙️ Privacy profilo: ${_profileSettings.profileVisibility.displayName}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore inizializzazione CommunityProfileService: $e');
      }
    }
  }

  /// Carica l'utente corrente
  Future<void> _loadCurrentUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_currentUserKey);

      if (userJson != null) {
        final userMap = jsonDecode(userJson);
        _currentUser = CommunityUser.fromJson(userMap);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore caricamento utente corrente: $e');
      }
    }
  }

  /// Salva l'utente corrente
  Future<void> _saveCurrentUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_currentUser != null) {
        final userJson = jsonEncode(_currentUser!.toJson());
        await prefs.setString(_currentUserKey, userJson);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore salvataggio utente corrente: $e');
      }
    }
  }

  /// Carica le impostazioni del profilo
  Future<void> _loadProfileSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_profileSettingsKey);

      if (settingsJson != null) {
        final settingsMap = jsonDecode(settingsJson);
        _profileSettings = ProfileSettings.fromJson(settingsMap);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore caricamento impostazioni profilo: $e');
      }
    }
  }

  /// Salva le impostazioni del profilo
  Future<void> _saveProfileSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = jsonEncode(_profileSettings.toJson());
      await prefs.setString(_profileSettingsKey, settingsJson);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore salvataggio impostazioni profilo: $e');
      }
    }
  }

  /// Carica i post degli utenti
  Future<void> _loadUserPosts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final postsJson = prefs.getString(_userPostsKey);

      if (postsJson != null) {
        final Map<String, dynamic> postsMap = jsonDecode(postsJson);
        _userPosts.clear();
        postsMap.forEach((userId, postIds) {
          _userPosts[userId] = List<String>.from(postIds);
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore caricamento post utenti: $e');
      }
    }
  }

  /// Salva i post degli utenti
  Future<void> _saveUserPosts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final postsJson = jsonEncode(_userPosts);
      await prefs.setString(_userPostsKey, postsJson);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore salvataggio post utenti: $e');
      }
    }
  }

  /// Carica le statistiche degli utenti
  Future<void> _loadUserStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsJson = prefs.getString(_userStatsKey);

      if (statsJson != null) {
        final Map<String, dynamic> statsMap = jsonDecode(statsJson);
        _userStats.clear();
        statsMap.forEach((userId, stats) {
          _userStats[userId] = Map<String, dynamic>.from(stats);
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore caricamento statistiche utenti: $e');
      }
    }
  }

  /// Salva le statistiche degli utenti
  Future<void> _saveUserStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsJson = jsonEncode(_userStats);
      await prefs.setString(_userStatsKey, statsJson);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore salvataggio statistiche utenti: $e');
      }
    }
  }

  /// Imposta l'utente corrente
  Future<void> setCurrentUser(CommunityUser user) async {
    _currentUser = user;
    await _saveCurrentUser();
    notifyListeners();

    if (kDebugMode) {
      print('👤 Utente corrente impostato: ${user.username}');
    }
  }

  /// Aggiorna il profilo dell'utente corrente
  Future<bool> updateProfile({
    String? displayName,
    String? bio,
    String? avatarUrl,
    List<String>? interests,
  }) async {
    try {
      if (_currentUser == null) return false;

      _currentUser = _currentUser!.copyWith(
        displayName: displayName,
        bio: bio,
        avatarUrl: avatarUrl,
        interests: interests,
        lastActive: DateTime.now(),
      );

      await _saveCurrentUser();
      notifyListeners();

      if (kDebugMode) {
        print('✅ Profilo aggiornato per ${_currentUser!.username}');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore aggiornamento profilo: $e');
      }
      return false;
    }
  }

  /// Aggiorna le impostazioni di privacy
  Future<bool> updatePrivacySettings(ProfileSettings newSettings) async {
    try {
      _profileSettings = newSettings;
      await _saveProfileSettings();
      notifyListeners();

      if (kDebugMode) {
        print('🔒 Impostazioni privacy aggiornate');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore aggiornamento privacy: $e');
      }
      return false;
    }
  }

  /// Aggiungi un post all'utente
  Future<void> addUserPost(String userId, String postId) async {
    try {
      if (!_userPosts.containsKey(userId)) {
        _userPosts[userId] = [];
      }

      if (!_userPosts[userId]!.contains(postId)) {
        _userPosts[userId]!.insert(0, postId); // Aggiungi in cima
        await _saveUserPosts();

        // Aggiorna le statistiche
        await _updateUserStats(userId, 'totalPosts', 1);

        notifyListeners();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore aggiunta post utente: $e');
      }
    }
  }

  /// Rimuovi un post dall'utente
  Future<void> removeUserPost(String userId, String postId) async {
    try {
      if (_userPosts.containsKey(userId)) {
        _userPosts[userId]!.remove(postId);
        await _saveUserPosts();

        // Aggiorna le statistiche
        await _updateUserStats(userId, 'totalPosts', -1);

        notifyListeners();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore rimozione post utente: $e');
      }
    }
  }

  /// Ottieni i post di un utente
  List<String> getUserPosts(String userId) {
    return _userPosts[userId] ?? [];
  }

  /// Aggiorna le statistiche dell'utente
  Future<void> _updateUserStats(String userId, String statKey, int increment) async {
    try {
      if (!_userStats.containsKey(userId)) {
        _userStats[userId] = {
          'totalPosts': 0,
          'totalLikes': 0,
          'totalComments': 0,
          'streak': 0,
          'lastPostDate': null,
        };
      }

      final currentValue = _userStats[userId]![statKey] ?? 0;
      _userStats[userId]![statKey] = (currentValue + increment).clamp(0, double.infinity).toInt();

      await _saveUserStats();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore aggiornamento statistiche: $e');
      }
    }
  }

  /// Aggiorna i like ricevuti
  Future<void> updateUserLikes(String userId, int increment) async {
    await _updateUserStats(userId, 'totalLikes', increment);
    notifyListeners();
  }

  /// Aggiorna i commenti ricevuti
  Future<void> updateUserComments(String userId, int increment) async {
    await _updateUserStats(userId, 'totalComments', increment);
    notifyListeners();
  }

  /// Aggiorna lo streak dell'utente
  Future<void> updateUserStreak(String userId) async {
    try {
      if (!_userStats.containsKey(userId)) {
        _userStats[userId] = {
          'totalPosts': 0,
          'totalLikes': 0,
          'totalComments': 0,
          'streak': 0,
          'lastPostDate': null,
        };
      }

      final now = DateTime.now();
      final lastPostDate = _userStats[userId]!['lastPostDate'];

      if (lastPostDate != null) {
        final lastDate = DateTime.parse(lastPostDate);
        final daysDifference = now.difference(lastDate).inDays;

        if (daysDifference == 1) {
          // Streak continua
          _userStats[userId]!['streak'] = (_userStats[userId]!['streak'] ?? 0) + 1;
        } else if (daysDifference > 1) {
          // Streak interrotta
          _userStats[userId]!['streak'] = 1;
        }
        // Se daysDifference == 0, è lo stesso giorno, non cambiare lo streak
      } else {
        // Primo post
        _userStats[userId]!['streak'] = 1;
      }

      _userStats[userId]!['lastPostDate'] = now.toIso8601String();
      await _saveUserStats();
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore aggiornamento streak: $e');
      }
    }
  }

  /// Ottieni le statistiche di un utente
  Map<String, dynamic> getUserStats(String userId) {
    return _userStats[userId] ?? {
      'totalPosts': 0,
      'totalLikes': 0,
      'totalComments': 0,
      'streak': 0,
      'lastPostDate': null,
    };
  }

  /// Verifica se un profilo è visibile per l'utente corrente
  bool isProfileVisible(CommunityUser targetUser, CommunityUser? currentUser, bool areFriends) {
    // Se non c'è utente corrente, solo i profili pubblici sono visibili
    if (currentUser == null) {
      return _profileSettings.profileVisibility == ProfilePrivacy.public;
    }

    // L'utente può sempre vedere il proprio profilo
    if (targetUser.id == currentUser.id) {
      return true;
    }

    // Controlla le impostazioni di privacy
    switch (_profileSettings.profileVisibility) {
      case ProfilePrivacy.public:
        return true;
      case ProfilePrivacy.friends:
        return areFriends;
      case ProfilePrivacy.private:
        return false;
    }
  }

  /// Verifica se i post sono visibili per l'utente corrente
  bool arePostsVisible(CommunityUser targetUser, CommunityUser? currentUser, bool areFriends) {
    if (currentUser == null) {
      return _profileSettings.postsVisibility == ProfilePrivacy.public;
    }

    if (targetUser.id == currentUser.id) {
      return true;
    }

    switch (_profileSettings.postsVisibility) {
      case ProfilePrivacy.public:
        return true;
      case ProfilePrivacy.friends:
        return areFriends;
      case ProfilePrivacy.private:
        return false;
    }
  }

  /// Crea un utente predefinito se non esiste
  Future<CommunityUser> createDefaultUser() async {
    final defaultUser = CommunityUser.create(
      username: 'dr_staffilano_user',
      displayName: 'Dr. Staffilano User',
      bio: 'Benvenuto in Staffilano InnerCircle™! 🏥💚',
      interests: ['Cardiologia', 'Prevenzione', 'Benessere'],
    );

    await setCurrentUser(defaultUser);
    return defaultUser;
  }

  /// Pulisci tutti i dati
  Future<void> clearAllData() async {
    try {
      _currentUser = null;
      _profileSettings = ProfileSettings();
      _userPosts.clear();
      _userStats.clear();

      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_currentUserKey);
      await prefs.remove(_profileSettingsKey);
      await prefs.remove(_userPostsKey);
      await prefs.remove(_userStatsKey);

      notifyListeners();

      if (kDebugMode) {
        print('🧹 Tutti i dati profilo puliti');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore pulizia dati profilo: $e');
      }
    }
  }

  /// Ottieni statistiche generali
  Map<String, dynamic> getGeneralStats() {
    final totalUsers = _userStats.length;
    final totalPosts = _userStats.values.fold<int>(0, (sum, stats) => sum + ((stats['totalPosts'] as int?) ?? 0));
    final totalLikes = _userStats.values.fold<int>(0, (sum, stats) => sum + ((stats['totalLikes'] as int?) ?? 0));

    return {
      'totalUsers': totalUsers,
      'totalPosts': totalPosts,
      'totalLikes': totalLikes,
      'currentUser': _currentUser?.toJson(),
      'profileSettings': _profileSettings.toJson(),
    };
  }
}
