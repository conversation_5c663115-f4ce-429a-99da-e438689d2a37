import 'package:flutter_test/flutter_test.dart';
import '../lib/models/user_profile.dart';
import '../lib/models/food.dart';
import '../lib/services/precision_food_selector.dart';
import '../lib/services/diet_generator_service.dart';
import '../lib/services/meal_appropriateness_validator.dart';
import '../lib/data/food_database.dart';

void main() {
  group('Mozzarella Snack Fix Tests', () {
    late UserProfile testProfile;
    late FoodDatabase foodDatabase;

    setUp(() async {
      // Crea un profilo utente di test
      testProfile = UserProfile(
        id: 'test_user',
        name: 'Test User',
        age: 30,
        gender: Gender.male,
        weight: 70,
        height: 175,
        activityLevel: ActivityLevel.moderatelyActive,
        goal: Goal.maintenance,
        dietType: DietType.omnivore,
        allergies: [],
        dislikedFoods: [],
        mealsPerDay: 4,
      );

      // Inizializza il database
      foodDatabase = FoodDatabase();
      await foodDatabase.resetDatabase();
    });

    test('MealAppropriatenessValidator blocks mozzarella from snacks', () async {
      print('🧪 Test: Validazione appropriatezza mozzarella per spuntini');

      // Ottieni tutti gli alimenti dal database
      final allFoods = await foodDatabase.getAllFoods();
      print('Totale alimenti nel database: ${allFoods.length}');

      // Trova tutti gli alimenti mozzarella
      final mozzarellaFoods = allFoods.where((food) =>
        food.name.toLowerCase().contains('mozzarella')).toList();

      print('Alimenti mozzarella trovati: ${mozzarellaFoods.length}');
      for (final mozzarella in mozzarellaFoods) {
        print('  - ${mozzarella.name}: ${mozzarella.suitableForMeals.map((m) => m.toString().split('.').last).join(', ')}');
      }

      // Test validazione per ogni mozzarella
      for (final mozzarella in mozzarellaFoods) {
        final isAppropriateForSnack = MealAppropriatenessValidator.isAppropriateForMeal(
          mozzarella,
          MealType.snack
        );

        print('${mozzarella.name} appropriato per spuntino: $isAppropriateForSnack');

        // VERIFICA CRITICA: Mozzarella NON deve essere appropriata per spuntini
        expect(
          isAppropriateForSnack,
          isFalse,
          reason: '${mozzarella.name} non dovrebbe essere appropriata per spuntini'
        );

        // Verifica che sia appropriata per pranzo/cena
        expect(
          MealAppropriatenessValidator.isAppropriateForMeal(mozzarella, MealType.lunch),
          isTrue,
          reason: '${mozzarella.name} dovrebbe essere appropriata per pranzo'
        );
      }

      print('✅ Test validazione appropriatezza completato');
    });

    test('PrecisionFoodSelector excludes mozzarella from snacks', () async {
      print('🧪 Test: PrecisionFoodSelector esclude mozzarella dagli spuntini');

      final foodSelector = await PrecisionFoodSelector.getInstance();

      // Genera un pasto spuntino
      final snackFoods = await foodSelector.selectFoodsForMeal(
        userProfile: testProfile,
        mealType: 'snack',
        targetCalories: 150,
        targetMacros: {'proteins': 5, 'carbs': 20, 'fats': 5},
      );

      print('Alimenti selezionati per spuntino: ${snackFoods.length}');
      for (final foodPortion in snackFoods) {
        print('  - ${foodPortion.food.name} (${foodPortion.grams}g)');
      }

      // VERIFICA CRITICA: Nessun alimento mozzarella negli spuntini
      final mozzarellaInSnack = snackFoods.where((portion) =>
        portion.food.name.toLowerCase().contains('mozzarella')).toList();

      expect(
        mozzarellaInSnack.length,
        equals(0),
        reason: 'Nessun alimento mozzarella dovrebbe apparire negli spuntini'
      );

      print('✅ Test PrecisionFoodSelector completato - zero mozzarella negli spuntini');
    });

    test('DietGeneratorService excludes mozzarella from snacks', () async {
      print('🧪 Test: DietGeneratorService esclude mozzarella dagli spuntini');

      final dietGenerator = await DietGeneratorService.getInstance();

      // Genera un piano giornaliero
      final dailyPlan = await dietGenerator.generateDailyDietPlan(
        testProfile,
        '2024-01-15',
        1800, // calorie target
        {'proteins': 90, 'carbs': 225, 'fats': 60}, // macro target
      );

      print('Piano giornaliero generato con ${dailyPlan.meals.length} pasti');

      // Trova tutti i pasti di tipo spuntino
      final snackMeals = dailyPlan.meals.where((meal) =>
        meal.type.toLowerCase() == 'snack' || meal.type.toLowerCase() == 'spuntino').toList();

      print('Pasti spuntino trovati: ${snackMeals.length}');

      // Verifica ogni spuntino
      for (final snackMeal in snackMeals) {
        print('Spuntino "${snackMeal.name}" con ${snackMeal.foods.length} alimenti:');

        for (final foodPortion in snackMeal.foods) {
          print('  - ${foodPortion.food.name} (${foodPortion.grams}g)');

          // VERIFICA CRITICA: Nessun alimento mozzarella negli spuntini
          expect(
            foodPortion.food.name.toLowerCase().contains('mozzarella'),
            isFalse,
            reason: 'Mozzarella non dovrebbe apparire negli spuntini: ${foodPortion.food.name}'
          );
        }
      }

      print('✅ Test DietGeneratorService completato - zero mozzarella negli spuntini');
    });

    test('Database mozzarella items correctly categorized', () async {
      print('🧪 Test: Verifica categorizzazione mozzarella nel database');

      final allFoods = await foodDatabase.getAllFoods();
      final mozzarellaFoods = allFoods.where((food) =>
        food.name.toLowerCase().contains('mozzarella')).toList();

      print('Verifica ${mozzarellaFoods.length} alimenti mozzarella nel database:');

      for (final mozzarella in mozzarellaFoods) {
        print('${mozzarella.name}:');
        print('  - ID: ${mozzarella.id}');
        print('  - Pasti adatti: ${mozzarella.suitableForMeals.map((m) => m.toString().split('.').last).join(', ')}');
        print('  - Calorie: ${mozzarella.calories}/100g');
        print('  - Porzione: ${mozzarella.servingSizeGrams}g');

        // VERIFICA: Mozzarella NON deve essere marcata per spuntini nel database
        expect(
          mozzarella.suitableForMeals.contains(MealType.snack),
          isFalse,
          reason: '${mozzarella.name} non dovrebbe essere marcata per spuntini nel database'
        );

        // VERIFICA: Deve essere marcata per pranzo e cena
        expect(
          mozzarella.suitableForMeals.contains(MealType.lunch),
          isTrue,
          reason: '${mozzarella.name} dovrebbe essere marcata per pranzo'
        );

        expect(
          mozzarella.suitableForMeals.contains(MealType.dinner),
          isTrue,
          reason: '${mozzarella.name} dovrebbe essere marcata per cena'
        );
      }

      print('✅ Test categorizzazione database completato');
    });

    test('Complete snack generation produces appropriate foods only', () async {
      print('🧪 Test: Generazione spuntini produce solo alimenti appropriati');

      final foodSelector = await PrecisionFoodSelector.getInstance();

      // Genera 10 spuntini diversi per testare la varietà
      for (int i = 0; i < 10; i++) {
        final snackFoods = await foodSelector.selectFoodsForMeal(
          userProfile: testProfile,
          mealType: 'snack',
          targetCalories: 120 + (i * 10), // Varia le calorie
          targetMacros: {'proteins': 4 + i, 'carbs': 15 + i, 'fats': 4 + i},
        );

        print('Spuntino ${i + 1}: ${snackFoods.length} alimenti');

        for (final foodPortion in snackFoods) {
          final food = foodPortion.food;

          // Verifica appropriatezza con il validatore
          final isAppropriate = MealAppropriatenessValidator.isAppropriateForMeal(
            food,
            MealType.snack
          );

          expect(
            isAppropriate,
            isTrue,
            reason: '${food.name} dovrebbe essere appropriato per spuntini'
          );

          // Verifica che non sia mozzarella
          expect(
            food.name.toLowerCase().contains('mozzarella'),
            isFalse,
            reason: 'Nessuna mozzarella dovrebbe apparire negli spuntini'
          );

          // Verifica limiti calorici per spuntini
          final caloriesPerServing = (food.calories * food.servingSizeGrams / 100).round();
          expect(
            caloriesPerServing,
            lessThanOrEqualTo(200),
            reason: 'Spuntini dovrebbero essere sotto le 200 calorie per porzione'
          );
        }
      }

      print('✅ Test generazione spuntini appropriati completato');
    });
  });
}
