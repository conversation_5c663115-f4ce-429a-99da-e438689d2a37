import 'package:flutter/material.dart';
import 'lib/config/supabase_config.dart';
import 'lib/services/supabase_auth_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🧪 TEST CONNESSIONE SUPABASE');
  print('=' * 50);
  
  try {
    // Test 1: Inizializzazione
    print('\n1️⃣ Test Inizializzazione...');
    await SupabaseConfig.initialize();
    print('✅ Inizializzazione completata');
    
    // Test 2: Verifica URL
    print('\n2️⃣ Test Configurazione...');
    final client = SupabaseConfig.client;
    print('📍 URL: ${client.supabaseUrl}');
    print('🔑 Auth URL: ${client.auth.url}');
    
    // Test 3: Test connessione
    print('\n3️⃣ Test Connessione...');
    final authService = SupabaseAuthService();
    
    // Prova a fare una chiamata semplice per testare la connessione
    try {
      await client.auth.getUser();
      print('✅ Connessione al server riuscita');
    } catch (e) {
      if (e.toString().contains('JWT')) {
        print('✅ Connessione al server riuscita (nessun utente loggato)');
      } else {
        print('❌ Errore connessione: $e');
      }
    }
    
    // Test 4: Test registrazione (simulata)
    print('\n4️⃣ Test Registrazione Simulata...');
    try {
      // Non facciamo la registrazione reale, solo testiamo la preparazione
      print('📧 Email test: <EMAIL>');
      print('🔒 Password test: ********');
      print('✅ Parametri registrazione preparati correttamente');
    } catch (e) {
      print('❌ Errore preparazione registrazione: $e');
    }
    
    print('\n🎉 TUTTI I TEST COMPLETATI');
    print('=' * 50);
    
  } catch (e) {
    print('❌ ERRORE CRITICO: $e');
    print('🔍 Stack trace:');
    print(e);
  }
}
