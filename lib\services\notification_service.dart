import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/community_notification.dart';
import '../models/community_post.dart';

/// Servizio per la gestione delle notifiche della community
class NotificationService extends ChangeNotifier {
  static const String _notificationsKey = 'community_notifications';
  static const String _unreadCountKey = 'unread_notifications_count';

  final List<CommunityNotification> _notifications = [];
  int _unreadCount = 0;

  /// Stream controller per le notifiche in tempo reale
  final StreamController<List<CommunityNotification>> _notificationsController =
      StreamController<List<CommunityNotification>>.broadcast();

  /// Stream controller per il contatore non lette
  final StreamController<int> _unreadCountController =
      StreamController<int>.broadcast();

  /// Lista delle notifiche (solo lettura)
  List<CommunityNotification> get notifications => List.unmodifiable(_notifications);

  /// Numero di notifiche non lette
  int get unreadCount => _unreadCount;

  /// Stream delle notifiche
  Stream<List<CommunityNotification>> get notificationsStream =>
      _notificationsController.stream;

  /// Stream del contatore non lette
  Stream<int> get unreadCountStream => _unreadCountController.stream;

  /// Inizializza il servizio caricando le notifiche salvate
  Future<void> initialize() async {
    try {
      await _loadNotifications();
      await _loadUnreadCount();

      if (kDebugMode) {
        print('✅ NotificationService inizializzato');
        print('📊 Notifiche caricate: ${_notifications.length}');
        print('🔔 Notifiche non lette: $_unreadCount');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore inizializzazione NotificationService: $e');
      }
    }
  }

  /// Carica le notifiche da SharedPreferences
  Future<void> _loadNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = prefs.getString(_notificationsKey);

      if (notificationsJson != null) {
        final List<dynamic> notificationsList = jsonDecode(notificationsJson);
        _notifications.clear();
        _notifications.addAll(
          notificationsList.map((json) => CommunityNotification.fromJson(json))
        );

        // Ordina per timestamp (più recenti prima)
        _notifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));

        _notificationsController.add(_notifications);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore caricamento notifiche: $e');
      }
    }
  }

  /// Carica il contatore non lette da SharedPreferences
  Future<void> _loadUnreadCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _unreadCount = prefs.getInt(_unreadCountKey) ?? 0;
      _unreadCountController.add(_unreadCount);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore caricamento contatore non lette: $e');
      }
    }
  }

  /// Salva le notifiche in SharedPreferences
  Future<void> _saveNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson = jsonEncode(
        _notifications.map((notification) => notification.toJson()).toList()
      );
      await prefs.setString(_notificationsKey, notificationsJson);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore salvataggio notifiche: $e');
      }
    }
  }

  /// Salva il contatore non lette in SharedPreferences
  Future<void> _saveUnreadCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_unreadCountKey, _unreadCount);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore salvataggio contatore non lette: $e');
      }
    }
  }

  /// Aggiunge una nuova notifica
  Future<void> addNotification(CommunityNotification notification) async {
    try {
      // Evita duplicati
      if (_notifications.any((n) => n.id == notification.id)) {
        return;
      }

      _notifications.insert(0, notification);

      // Mantieni solo le ultime 100 notifiche
      if (_notifications.length > 100) {
        _notifications.removeRange(100, _notifications.length);
      }

      if (!notification.isRead) {
        _unreadCount++;
        _unreadCountController.add(_unreadCount);
        await _saveUnreadCount();
      }

      await _saveNotifications();
      _notificationsController.add(_notifications);
      notifyListeners();

      if (kDebugMode) {
        print('✅ Notifica aggiunta: ${notification.message}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore aggiunta notifica: $e');
      }
    }
  }

  /// Marca una notifica come letta
  Future<void> markAsRead(String notificationId) async {
    try {
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1 && !_notifications[index].isRead) {
        _notifications[index] = _notifications[index].markAsRead();
        _unreadCount = (_unreadCount - 1).clamp(0, double.infinity).toInt();

        await _saveNotifications();
        await _saveUnreadCount();

        _notificationsController.add(_notifications);
        _unreadCountController.add(_unreadCount);
        notifyListeners();

        if (kDebugMode) {
          print('✅ Notifica marcata come letta: $notificationId');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore marcatura notifica come letta: $e');
      }
    }
  }

  /// Marca tutte le notifiche come lette
  Future<void> markAllAsRead() async {
    try {
      bool hasChanges = false;
      for (int i = 0; i < _notifications.length; i++) {
        if (!_notifications[i].isRead) {
          _notifications[i] = _notifications[i].markAsRead();
          hasChanges = true;
        }
      }

      if (hasChanges) {
        _unreadCount = 0;

        await _saveNotifications();
        await _saveUnreadCount();

        _notificationsController.add(_notifications);
        _unreadCountController.add(_unreadCount);
        notifyListeners();

        if (kDebugMode) {
          print('✅ Tutte le notifiche marcate come lette');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore marcatura tutte notifiche come lette: $e');
      }
    }
  }

  /// Rimuove una notifica
  Future<void> removeNotification(String notificationId) async {
    try {
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        final notification = _notifications.removeAt(index);

        if (!notification.isRead) {
          _unreadCount = (_unreadCount - 1).clamp(0, double.infinity).toInt();
          _unreadCountController.add(_unreadCount);
          await _saveUnreadCount();
        }

        await _saveNotifications();
        _notificationsController.add(_notifications);
        notifyListeners();

        if (kDebugMode) {
          print('✅ Notifica rimossa: $notificationId');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore rimozione notifica: $e');
      }
    }
  }

  /// Pulisce tutte le notifiche
  Future<void> clearAllNotifications() async {
    try {
      _notifications.clear();
      _unreadCount = 0;

      await _saveNotifications();
      await _saveUnreadCount();

      _notificationsController.add(_notifications);
      _unreadCountController.add(_unreadCount);
      notifyListeners();

      if (kDebugMode) {
        print('✅ Tutte le notifiche pulite');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Errore pulizia notifiche: $e');
      }
    }
  }

  /// Genera notifica per "mi piace" su un post
  Future<void> createLikeNotification({
    required String postAuthorId,
    required String postId,
    required String actorUserId,
    required String actorUserName,
    String? actorUserAvatar,
  }) async {
    // Non creare notifica se l'utente mette like al proprio post
    if (postAuthorId == actorUserId) return;

    final notification = CommunityNotification.create(
      userId: postAuthorId,
      type: NotificationType.likePost,
      postId: postId,
      actorUserId: actorUserId,
      actorUserName: actorUserName,
      actorUserAvatar: actorUserAvatar,
      message: '$actorUserName ha messo mi piace al tuo post',
    );

    await addNotification(notification);
  }

  /// Genera notifica per commento su un post
  Future<void> createCommentNotification({
    required String postAuthorId,
    required String postId,
    required String actorUserId,
    required String actorUserName,
    String? actorUserAvatar,
    String? commentPreview,
  }) async {
    // Non creare notifica se l'utente commenta il proprio post
    if (postAuthorId == actorUserId) return;

    final message = commentPreview != null && commentPreview.isNotEmpty
        ? '$actorUserName ha commentato: "${commentPreview.length > 50 ? '${commentPreview.substring(0, 50)}...' : commentPreview}"'
        : '$actorUserName ha commentato il tuo post';

    final notification = CommunityNotification.create(
      userId: postAuthorId,
      type: NotificationType.commentPost,
      postId: postId,
      actorUserId: actorUserId,
      actorUserName: actorUserName,
      actorUserAvatar: actorUserAvatar,
      message: message,
      metadata: {'commentPreview': commentPreview},
    );

    await addNotification(notification);
  }

  /// Genera notifica per condivisione di un post
  Future<void> createShareNotification({
    required String postAuthorId,
    required String postId,
    required String actorUserId,
    required String actorUserName,
    String? actorUserAvatar,
  }) async {
    // Non creare notifica se l'utente condivide il proprio post
    if (postAuthorId == actorUserId) return;

    final notification = CommunityNotification.create(
      userId: postAuthorId,
      type: NotificationType.sharePost,
      postId: postId,
      actorUserId: actorUserId,
      actorUserName: actorUserName,
      actorUserAvatar: actorUserAvatar,
      message: '$actorUserName ha condiviso il tuo post',
    );

    await addNotification(notification);
  }

  /// Genera notifica per tag utente
  Future<void> createTagNotification({
    required String taggedUserId,
    required String postId,
    required String actorUserId,
    required String actorUserName,
    String? actorUserAvatar,
  }) async {
    // Non creare notifica se l'utente tagga se stesso
    if (taggedUserId == actorUserId) return;

    final notification = CommunityNotification.create(
      userId: taggedUserId,
      type: NotificationType.tagUser,
      postId: postId,
      actorUserId: actorUserId,
      actorUserName: actorUserName,
      actorUserAvatar: actorUserAvatar,
      message: '$actorUserName ti ha taggato in un post',
    );

    await addNotification(notification);
  }

  /// Genera notifica per richiesta di amicizia
  Future<void> createFriendRequestNotification({
    required String targetUserId,
    required String actorUserId,
    required String actorUserName,
    String? actorUserAvatar,
  }) async {
    final notification = CommunityNotification.create(
      userId: targetUserId,
      type: NotificationType.friendRequest,
      actorUserId: actorUserId,
      actorUserName: actorUserName,
      actorUserAvatar: actorUserAvatar,
      message: '$actorUserName ti ha inviato una richiesta di amicizia',
    );

    await addNotification(notification);
  }

  /// Ottieni notifiche per un utente specifico
  List<CommunityNotification> getNotificationsForUser(String userId) {
    return _notifications.where((n) => n.userId == userId).toList();
  }

  /// Ottieni notifiche non lette per un utente specifico
  List<CommunityNotification> getUnreadNotificationsForUser(String userId) {
    return _notifications
        .where((n) => n.userId == userId && !n.isRead)
        .toList();
  }

  /// Ottieni il conteggio delle notifiche non lette per un utente
  int getUnreadCountForUser(String userId) {
    return _notifications
        .where((n) => n.userId == userId && !n.isRead)
        .length;
  }

  @override
  void dispose() {
    _notificationsController.close();
    _unreadCountController.close();
    super.dispose();
  }
}
