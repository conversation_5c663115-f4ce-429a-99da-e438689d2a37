import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../theme/dr_staffilano_theme.dart';

/// Widget for magical particle effects that flow toward a target
class MagicalParticles extends StatefulWidget {
  final Widget child;
  final bool isActive;
  final Color particleColor;
  final int particleCount;
  final double particleSize;

  const MagicalParticles({
    Key? key,
    required this.child,
    this.isActive = true,
    this.particleColor = DrStaffilanoTheme.accentGold,
    this.particleCount = 15,
    this.particleSize = 4.0,
  }) : super(key: key);

  @override
  State<MagicalParticles> createState() => _MagicalParticlesState();
}

class _MagicalParticlesState extends State<MagicalParticles>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late List<Particle> _particles;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _initializeParticles();

    if (widget.isActive) {
      _controller.repeat();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _initializeParticles() {
    final random = math.Random();
    _particles = List.generate(widget.particleCount, (index) {
      return Particle(
        startX: random.nextDouble(),
        startY: -0.1 + random.nextDouble() * 0.2, // Start from top area (above screen)
        targetX: 0.5 + (random.nextDouble() - 0.5) * 0.4, // Center with slight spread
        targetY: 0.2 + random.nextDouble() * 0.15, // Target the text area
        delay: random.nextDouble() * 2.0,
        speed: 0.25 + random.nextDouble() * 0.35,
        size: widget.particleSize * (0.8 + random.nextDouble() * 0.7),
        opacity: 0.7 + random.nextDouble() * 0.3,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        if (widget.isActive)
          Positioned.fill(
            child: AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return CustomPaint(
                  painter: ParticlePainter(
                    particles: _particles,
                    animation: _controller,
                    color: widget.particleColor,
                  ),
                );
              },
            ),
          ),
      ],
    );
  }
}

class Particle {
  final double startX;
  final double startY;
  final double targetX;
  final double targetY;
  final double delay;
  final double speed;
  final double size;
  final double opacity;

  Particle({
    required this.startX,
    required this.startY,
    required this.targetX,
    required this.targetY,
    required this.delay,
    required this.speed,
    required this.size,
    required this.opacity,
  });
}

class ParticlePainter extends CustomPainter {
  final List<Particle> particles;
  final Animation<double> animation;
  final Color color;

  ParticlePainter({
    required this.particles,
    required this.animation,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    for (final particle in particles) {
      final progress = ((animation.value - particle.delay) * particle.speed).clamp(0.0, 1.0);

      if (progress <= 0) continue;

      // Calculate current position with easing
      final easedProgress = _easeInOut(progress);
      final currentX = particle.startX + (particle.targetX - particle.startX) * easedProgress;
      final currentY = particle.startY + (particle.targetY - particle.startY) * easedProgress;

      // Calculate opacity with fade out near target
      final fadeProgress = 1.0 - (progress * 0.5);
      final currentOpacity = (particle.opacity * fadeProgress).clamp(0.0, 1.0);

      // Add sparkle effect
      final sparkleIntensity = math.sin(progress * math.pi * 4) * 0.3 + 0.7;
      final finalOpacity = (currentOpacity * sparkleIntensity).clamp(0.0, 1.0);

      paint.color = color.withOpacity(finalOpacity);

      // Draw star-shaped particle with glow
      _drawStarWithGlow(
        canvas,
        Offset(currentX * size.width, currentY * size.height),
        particle.size,
        paint,
        color.withOpacity(finalOpacity * 0.3),
      );
    }
  }

  double _easeInOut(double t) {
    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
  }

  void _drawStarWithGlow(Canvas canvas, Offset center, double size, Paint paint, Color glowColor) {
    // Draw glow effect first
    final glowPaint = Paint()
      ..color = glowColor
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0);

    _drawStar(canvas, center, size * 1.5, glowPaint);

    // Draw main star
    _drawStar(canvas, center, size, paint);
  }

  void _drawStar(Canvas canvas, Offset center, double size, Paint paint) {
    final path = Path();
    const numPoints = 5;
    const outerRadius = 1.0;
    const innerRadius = 0.4;

    for (int i = 0; i < numPoints * 2; i++) {
      final angle = (i * math.pi) / numPoints - math.pi / 2; // Start from top
      final radius = (i % 2 == 0) ? outerRadius : innerRadius;
      final x = center.dx + math.cos(angle) * radius * size;
      final y = center.dy + math.sin(angle) * radius * size;

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
