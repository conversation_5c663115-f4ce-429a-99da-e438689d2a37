import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/diet_plan.dart';
import '../models/ultra_detailed_profile.dart';
import '../widgets/planned_meal_card.dart';
import '../theme/dr_staffilano_theme.dart';

/// SCHERMATA VISUALIZZAZIONE PIANO DIETETICO COMPLETO
/// Mostra il piano generato dall'ultra-advanced diet generator
class DietPlanViewScreen extends StatefulWidget {
  final DailyDietPlan dailyPlan;
  final UltraDetailedProfile profile;

  const DietPlanViewScreen({
    Key? key,
    required this.dailyPlan,
    required this.profile,
  }) : super(key: key);

  @override
  State<DietPlanViewScreen> createState() => _DietPlanViewScreenState();
}

class _DietPlanViewScreenState extends State<DietPlanViewScreen> {
  bool _showNutritionalDetails = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DrStaffilanoTheme.backgroundLight,
      appBar: AppBar(
        title: const Text('Piano Dietetico'),
        backgroundColor: DrStaffilanoTheme.primaryGreen,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(_showNutritionalDetails ? Icons.visibility_off : Icons.analytics),
            tooltip: _showNutritionalDetails ? 'Nascondi Dettagli' : 'Mostra Dettagli Nutrizionali',
            onPressed: () {
              setState(() {
                _showNutritionalDetails = !_showNutritionalDetails;
              });
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPlanHeader(),
            const SizedBox(height: 24),
            _buildNutritionalSummary(),
            const SizedBox(height: 24),
            _buildMealsList(),
          ],
        ),
      ),
    );
  }

  Widget _buildPlanHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: DrStaffilanoTheme.primaryGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  FontAwesomeIcons.utensils,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Piano Ultra-Personalizzato',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Generato per ${widget.profile.baseProfile.name}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildHeaderStat('Data', widget.dailyPlan.date),
              const SizedBox(width: 24),
              _buildHeaderStat('Pasti', '${widget.dailyPlan.meals.length}'),
              const SizedBox(width: 24),
              _buildHeaderStat('Target', '${widget.dailyPlan.calorieTarget} kcal'),
            ],
          ),
        ],
      ),
    ).animate().fadeIn(duration: 300.ms).slideY(begin: -0.2, end: 0);
  }

  Widget _buildHeaderStat(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildNutritionalSummary() {
    final totalCalories = widget.dailyPlan.meals.fold(0, (sum, meal) =>
      sum + meal.foods.fold(0, (mealSum, portion) => mealSum + portion.calories));

    final totalProteins = widget.dailyPlan.meals.fold(0.0, (sum, meal) =>
      sum + meal.foods.fold(0.0, (mealSum, portion) => mealSum + portion.proteins));

    final totalCarbs = widget.dailyPlan.meals.fold(0.0, (sum, meal) =>
      sum + meal.foods.fold(0.0, (mealSum, portion) => mealSum + portion.carbs));

    final totalFats = widget.dailyPlan.meals.fold(0.0, (sum, meal) =>
      sum + meal.foods.fold(0.0, (mealSum, portion) => mealSum + portion.fats));

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                FontAwesomeIcons.chartPie,
                color: DrStaffilanoTheme.primaryGreen,
                size: 20,
              ),
              const SizedBox(width: 12),
              const Text(
                'Riepilogo Nutrizionale',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildNutritionalStat('Calorie', '$totalCalories', 'kcal', DrStaffilanoTheme.primaryGreen),
              _buildNutritionalStat('Proteine', '${totalProteins.round()}', 'g', DrStaffilanoTheme.secondaryBlue),
              _buildNutritionalStat('Carboidrati', '${totalCarbs.round()}', 'g', DrStaffilanoTheme.accentGold),
              _buildNutritionalStat('Grassi', '${totalFats.round()}', 'g', Colors.orange),
            ],
          ),

          if (_showNutritionalDetails) ...[
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            _buildDetailedNutrition(),
          ],
        ],
      ),
    ).animate().fadeIn(duration: 300.ms, delay: 100.ms).slideY(begin: 0.2, end: 0);
  }

  Widget _buildNutritionalStat(String label, String value, String unit, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            color: color,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          unit,
          style: TextStyle(
            color: color,
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  Widget _buildDetailedNutrition() {
    final bmr = widget.profile.calculateBMR();
    final tdee = widget.profile.calculateTDEE();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Dettagli Metabolici',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildMetabolicStat('BMR', '${bmr.round()}', 'kcal'),
            _buildMetabolicStat('TDEE', '${tdee.round()}', 'kcal'),
            _buildMetabolicStat('Deficit/Surplus', '${(widget.dailyPlan.calorieTarget - tdee).round()}', 'kcal'),
          ],
        ),
      ],
    );
  }

  Widget _buildMetabolicStat(String label, String value, String unit) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          unit,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  Widget _buildMealsList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              FontAwesomeIcons.utensils,
              color: DrStaffilanoTheme.primaryGreen,
              size: 20,
            ),
            const SizedBox(width: 12),
            const Text(
              'Pasti del Giorno',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        ...widget.dailyPlan.meals.asMap().entries.map((entry) {
          final index = entry.key;
          final meal = entry.value;

          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: PlannedMealCard(
              meal: meal,
              onCompletedChanged: (completed) {
                // Handle meal completion if needed
              },
              onFoodReplaced: (mealId, oldFood, newFood) {
                // Handle food replacement if needed
              },
            ).animate().fadeIn(duration: 300.ms, delay: (200 + index * 100).ms).slideX(begin: 0.2, end: 0),
          );
        }).toList(),
      ],
    );
  }
}
