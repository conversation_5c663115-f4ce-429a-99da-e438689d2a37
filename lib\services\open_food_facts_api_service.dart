import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:uuid/uuid.dart';
import '../models/food.dart';

/// Service per interagire con l'API Open Food Facts
/// Documentazione: https://openfoodfacts.github.io/api-documentation/
class OpenFoodFactsApiService {
  static const String _baseUrl = 'https://world.openfoodfacts.org/api/v2';

  // Endpoint API
  static const String _searchEndpoint = '/search';
  static const String _productEndpoint = '/product';

  // Client HTTP
  final http.Client _client;

  OpenFoodFactsApiService({http.Client? client}) : _client = client ?? http.Client();

  /// Cerca alimenti nel database Open Food Facts
  /// [query] - Termine di ricerca
  /// [page] - Numero di pagina (default: 1)
  /// [pageSize] - Numero di risultati per pagina (default: 24, max: 1000)
  /// [locale] - Lingua dei risultati (default: it)
  Future<List<Map<String, dynamic>>> searchFoods({
    required String query,
    int page = 1,
    int pageSize = 24,
    String locale = 'it',
  }) async {
    try {
      final queryParams = {
        'search_terms': query,
        'page': page.toString(),
        'page_size': pageSize.toString(),
        'lc': locale,
        'fields': 'code,product_name,brands,nutriments,ingredients_text,categories,labels,image_url,nutriscore_grade,ecoscore_grade,nova_group',
      };

      final uri = Uri.parse('$_baseUrl$_searchEndpoint').replace(
        queryParameters: queryParams,
      );

      final response = await _client.get(uri);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return List<Map<String, dynamic>>.from(data['products']);
      } else {
        throw Exception('Errore nella ricerca Open Food Facts: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Errore nella ricerca Open Food Facts: $e');
    }
  }

  /// Ottieni dettagli di un prodotto specifico dal database Open Food Facts
  /// [barcode] - Codice a barre del prodotto
  Future<Map<String, dynamic>> getProductDetails(String barcode) async {
    try {
      final uri = Uri.parse('$_baseUrl$_productEndpoint/$barcode');

      final response = await _client.get(uri);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 1) {
          return data['product'];
        } else {
          throw Exception('Prodotto non trovato: $barcode');
        }
      } else {
        throw Exception('Errore nel recupero dettagli Open Food Facts: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Errore nel recupero dettagli Open Food Facts: $e');
    }
  }

  /// Converte un prodotto Open Food Facts in un oggetto Food
  /// [offProduct] - Dati del prodotto dal database Open Food Facts
  Future<Food> convertOffProductToFood(Map<String, dynamic> offProduct) async {
    try {
      // Estrai l'ID del prodotto (codice a barre)
      final String barcode = offProduct['code'] ?? '';

      // Estrai i nutrienti
      final nutrients = _extractNutrients(offProduct);

      // Determina le categorie di alimenti
      final categories = _determineFoodCategories(offProduct);

      // Determina i tipi di pasto adatti
      final mealTypes = _determineSuitableMealTypes(offProduct, categories);

      // Estrai informazioni sugli allergeni
      final allergens = _extractAllergens(offProduct);

      // Determina se è adatto a diete specifiche
      final dietaryInfo = _determineDietarySuitability(offProduct, allergens);

      // Estrai le porzioni comuni
      final servingSizes = _extractServingSizes(offProduct);

      // Crea l'oggetto Food
      return Food(
        id: const Uuid().v4(), // Genera un nuovo UUID
        name: offProduct['product_name'] ?? 'Prodotto sconosciuto',
        description: _generateDescription(offProduct),
        imageUrl: offProduct['image_url'] ?? '',
        calories: nutrients['calories'] ?? 0,
        proteins: nutrients['proteins'] ?? 0.0,
        carbs: nutrients['carbs'] ?? 0.0,
        fats: nutrients['fats'] ?? 0.0,
        fiber: nutrients['fiber'] ?? 0.0,
        sugar: nutrients['sugar'] ?? 0.0,
        suitableForMeals: mealTypes,
        categories: categories,
        isVegetarian: dietaryInfo['isVegetarian'] ?? false,
        isVegan: dietaryInfo['isVegan'] ?? false,
        isGlutenFree: dietaryInfo['isGlutenFree'] ?? false,
        isDairyFree: dietaryInfo['isDairyFree'] ?? false,
        allergens: allergens,
        dataSource: DataSource.open_food_facts,
        sourceId: barcode,
        sourceDescription: 'Open Food Facts',
        brandName: offProduct['brands'] ?? '',
        validationStatus: ValidationStatus.pending, // I dati OFF richiedono verifica
        lastValidatedAt: DateTime.now(),

        tags: _generateTags(offProduct, categories, dietaryInfo),
        micronutrients: _extractMicronutrients(offProduct),
        commonServingSize1Description: servingSizes['description1'] ?? '',
        commonServingSize1Grams: servingSizes['grams1'] ?? 0,
        commonServingSize2Description: servingSizes['description2'] ?? '',
        commonServingSize2Grams: servingSizes['grams2'] ?? 0,
      );
    } catch (e) {
      throw Exception('Errore nella conversione del prodotto Open Food Facts: $e');
    }
  }

  /// Estrae i valori nutrizionali principali dai dati Open Food Facts
  Map<String, dynamic> _extractNutrients(Map<String, dynamic> offProduct) {
    final result = {
      'calories': 0,
      'proteins': 0.0,
      'carbs': 0.0,
      'fats': 0.0,
      'fiber': 0.0,
      'sugar': 0.0,
    };

    if (offProduct.containsKey('nutriments')) {
      final nutriments = offProduct['nutriments'];

      // Estrai i valori nutrizionali per 100g
      if (nutriments.containsKey('energy-kcal_100g')) {
        result['calories'] = (nutriments['energy-kcal_100g'] as num?)?.round() ?? 0;
      } else if (nutriments.containsKey('energy_100g')) {
        // Converti kJ in kcal se necessario (approssimazione)
        final energy = (nutriments['energy_100g'] as num?) ?? 0;
        result['calories'] = (energy / 4.184).round();
      }

      result['proteins'] = (nutriments['proteins_100g'] as num?)?.toDouble() ?? 0.0;
      result['carbs'] = (nutriments['carbohydrates_100g'] as num?)?.toDouble() ?? 0.0;
      result['fats'] = (nutriments['fat_100g'] as num?)?.toDouble() ?? 0.0;
      result['fiber'] = (nutriments['fiber_100g'] as num?)?.toDouble() ?? 0.0;
      result['sugar'] = (nutriments['sugars_100g'] as num?)?.toDouble() ?? 0.0;
    }

    return result;
  }

  /// Estrae i micronutrienti dai dati Open Food Facts
  Map<String, double> _extractMicronutrients(Map<String, dynamic> offProduct) {
    final result = <String, double>{};

    if (offProduct.containsKey('nutriments')) {
      final nutriments = offProduct['nutriments'];

      // Mappa dei micronutrienti da estrarre
      final micronutrientMap = {
        'calcium_100g': 'calcium',
        'iron_100g': 'iron',
        'vitamin-a_100g': 'vitamin_a',
        'vitamin-c_100g': 'vitamin_c',
        'vitamin-d_100g': 'vitamin_d',
        'vitamin-e_100g': 'vitamin_e',
        'vitamin-k_100g': 'vitamin_k',
        'vitamin-b1_100g': 'vitamin_b1',
        'vitamin-b2_100g': 'vitamin_b2',
        'vitamin-b6_100g': 'vitamin_b6',
        'vitamin-b9_100g': 'folate',
        'vitamin-b12_100g': 'vitamin_b12',
        'potassium_100g': 'potassium',
        'magnesium_100g': 'magnesium',
        'zinc_100g': 'zinc',
        'sodium_100g': 'sodium',
      };

      for (final entry in micronutrientMap.entries) {
        if (nutriments.containsKey(entry.key)) {
          final value = (nutriments[entry.key] as num?)?.toDouble() ?? 0.0;
          if (value > 0) {
            result[entry.value] = value;
          }
        }
      }
    }

    return result;
  }

  /// Determina le categorie di alimenti in base ai dati Open Food Facts
  List<FoodCategory> _determineFoodCategories(Map<String, dynamic> offProduct) {
    final categories = <FoodCategory>{};

    // Estrai le categorie dal prodotto
    final productCategories = (offProduct['categories'] ?? '').toLowerCase();

    // Verifica le categorie in base a parole chiave
    if (_containsAny(productCategories, ['fruit', 'frutta', 'apple', 'banana', 'orange', 'berry', 'mela', 'banana', 'arancia'])) {
      categories.add(FoodCategory.fruit);
    }

    if (_containsAny(productCategories, ['vegetable', 'verdura', 'carrot', 'broccoli', 'spinach', 'carota', 'broccoli', 'spinaci'])) {
      categories.add(FoodCategory.vegetable);
    }

    if (_containsAny(productCategories, ['grain', 'cereal', 'bread', 'pasta', 'rice', 'cereale', 'pane', 'pasta', 'riso'])) {
      categories.add(FoodCategory.grain);
    }

    if (_containsAny(productCategories, ['meat', 'chicken', 'beef', 'pork', 'fish', 'carne', 'pollo', 'manzo', 'maiale', 'pesce'])) {
      categories.add(FoodCategory.protein);
    }

    if (_containsAny(productCategories, ['milk', 'cheese', 'yogurt', 'dairy', 'latte', 'formaggio', 'yogurt', 'latticini'])) {
      categories.add(FoodCategory.dairy);
    }

    if (_containsAny(productCategories, ['oil', 'butter', 'margarine', 'olio', 'burro', 'margarina'])) {
      categories.add(FoodCategory.fat);
    }

    if (_containsAny(productCategories, ['sweet', 'candy', 'chocolate', 'dessert', 'dolce', 'caramella', 'cioccolato'])) {
      categories.add(FoodCategory.sweet);
    }

    if (_containsAny(productCategories, ['beverage', 'drink', 'juice', 'water', 'bevanda', 'succo', 'acqua'])) {
      categories.add(FoodCategory.beverage);
    }

    // Se non è stata trovata alcuna categoria, aggiungi "mixed"
    if (categories.isEmpty) {
      categories.add(FoodCategory.mixed);
    }

    return categories.toList();
  }

  /// Verifica se una stringa contiene una delle parole chiave
  bool _containsAny(String text, List<String> keywords) {
    for (final keyword in keywords) {
      if (text.contains(keyword)) {
        return true;
      }
    }
    return false;
  }

  /// Determina i tipi di pasto adatti in base alle categorie e ai dati Open Food Facts
  List<MealType> _determineSuitableMealTypes(Map<String, dynamic> offProduct, List<FoodCategory> categories) {
    final mealTypes = <MealType>{};

    // Logica semplificata per determinare i tipi di pasto adatti
    // In un'implementazione reale, questa logica sarebbe più sofisticata

    // Colazione: frutta, cereali, latticini
    if (categories.contains(FoodCategory.fruit) ||
        categories.contains(FoodCategory.grain) ||
        categories.contains(FoodCategory.dairy)) {
      mealTypes.add(MealType.breakfast);
    }

    // Pranzo e cena: proteine, cereali, verdure
    if (categories.contains(FoodCategory.protein) ||
        categories.contains(FoodCategory.grain) ||
        categories.contains(FoodCategory.vegetable)) {
      mealTypes.add(MealType.lunch);
      mealTypes.add(MealType.dinner);
    }

    // Spuntino: frutta, latticini, dolci
    if (categories.contains(FoodCategory.fruit) ||
        categories.contains(FoodCategory.dairy) ||
        categories.contains(FoodCategory.sweet)) {
      mealTypes.add(MealType.snack);
    }

    // Se non è stato trovato alcun tipo di pasto, aggiungi tutti
    if (mealTypes.isEmpty) {
      mealTypes.addAll([MealType.breakfast, MealType.lunch, MealType.dinner, MealType.snack]);
    }

    return mealTypes.toList();
  }

  /// Estrae informazioni sugli allergeni dai dati Open Food Facts
  List<String> _extractAllergens(Map<String, dynamic> offProduct) {
    final allergens = <String>{};

    // Estrai gli allergeni dal prodotto
    final allergensText = (offProduct['allergens'] ?? '').toLowerCase();

    // Mappa degli allergeni in Open Food Facts
    final allergenMap = {
      'milk': 'latticini',
      'gluten': 'glutine',
      'nuts': 'frutta a guscio',
      'peanuts': 'arachidi',
      'eggs': 'uova',
      'fish': 'pesce',
      'crustaceans': 'crostacei',
      'soybeans': 'soia',
      'latte': 'latticini',
      'glutine': 'glutine',
      'frutta a guscio': 'frutta a guscio',
      'arachidi': 'arachidi',
      'uova': 'uova',
      'pesce': 'pesce',
      'crostacei': 'crostacei',
      'soia': 'soia',
    };

    // Verifica la presenza di allergeni
    for (final entry in allergenMap.entries) {
      if (allergensText.contains(entry.key)) {
        allergens.add(entry.value);
      }
    }

    return allergens.toList();
  }

  /// Determina l'idoneità a diete specifiche
  Map<String, bool> _determineDietarySuitability(Map<String, dynamic> offProduct, List<String> allergens) {
    final result = {
      'isVegetarian': true,
      'isVegan': true,
      'isGlutenFree': true,
      'isDairyFree': true,
    };

    // Estrai le etichette dal prodotto
    final labels = (offProduct['labels'] ?? '').toLowerCase();
    final ingredients = (offProduct['ingredients_text'] ?? '').toLowerCase();

    // Verifica se è vegetariano
    if (labels.contains('non-vegetarian') ||
        _containsAny(ingredients, ['meat', 'chicken', 'beef', 'pork', 'fish', 'carne', 'pollo', 'manzo', 'maiale', 'pesce'])) {
      result['isVegetarian'] = false;
      result['isVegan'] = false;
    }

    // Verifica se è vegano
    if (result['isVegetarian'] == true) {
      if (labels.contains('non-vegan') ||
          _containsAny(ingredients, ['milk', 'cheese', 'yogurt', 'egg', 'honey', 'latte', 'formaggio', 'yogurt', 'uovo', 'miele'])) {
        result['isVegan'] = false;
      }
    }

    // Verifica se è senza glutine
    if (!labels.contains('gluten-free') &&
        (_containsAny(ingredients, ['wheat', 'gluten', 'frumento', 'glutine']) ||
         allergens.contains('glutine'))) {
      result['isGlutenFree'] = false;
    }

    // Verifica se è senza latticini
    if (!labels.contains('dairy-free') &&
        (_containsAny(ingredients, ['milk', 'cheese', 'yogurt', 'dairy', 'latte', 'formaggio', 'yogurt']) ||
         allergens.contains('latticini'))) {
      result['isDairyFree'] = false;
    }

    return result;
  }

  /// Estrae le informazioni sulle porzioni comuni
  Map<String, dynamic> _extractServingSizes(Map<String, dynamic> offProduct) {
    final result = {
      'description1': '',
      'grams1': 0,
      'description2': '',
      'grams2': 0,
    };

    // Estrai le informazioni sulla porzione
    if (offProduct.containsKey('serving_size')) {
      final servingSize = offProduct['serving_size'] as String? ?? '';

      // Cerca di estrarre il peso dalla descrizione della porzione
      final RegExp gramRegex = RegExp(r'(\d+)\s*g');
      final match = gramRegex.firstMatch(servingSize);

      if (match != null) {
        final grams = int.tryParse(match.group(1) ?? '0') ?? 0;
        result['description1'] = servingSize;
        result['grams1'] = grams;
      }
    }

    return result;
  }

  /// Genera una descrizione per l'alimento
  String _generateDescription(Map<String, dynamic> offProduct) {
    final productName = offProduct['product_name'] ?? '';
    final brands = offProduct['brands'] ?? '';
    final additionalDescriptions = <String>[];

    if (brands.isNotEmpty) {
      additionalDescriptions.add('Marca: $brands');
    }

    if (offProduct.containsKey('nutriscore_grade')) {
      additionalDescriptions.add('Nutriscore: ${offProduct['nutriscore_grade']}');
    }

    if (offProduct.containsKey('ecoscore_grade')) {
      additionalDescriptions.add('Ecoscore: ${offProduct['ecoscore_grade']}');
    }

    if (additionalDescriptions.isEmpty) {
      return productName;
    } else {
      return '$productName (${additionalDescriptions.join(', ')})';
    }
  }

  /// Genera tag per l'alimento
  List<String> _generateTags(Map<String, dynamic> offProduct, List<FoodCategory> categories, Map<String, bool> dietaryInfo) {
    final tags = <String>{};

    // Aggiungi tag per le categorie
    for (final category in categories) {
      tags.add(category.toString().split('.').last);
    }

    // Aggiungi tag per le diete
    if (dietaryInfo['isVegetarian'] == true) {
      tags.add('vegetariano');
    }

    if (dietaryInfo['isVegan'] == true) {
      tags.add('vegano');
    }

    if (dietaryInfo['isGlutenFree'] == true) {
      tags.add('senza glutine');
    }

    if (dietaryInfo['isDairyFree'] == true) {
      tags.add('senza latticini');
    }

    // Aggiungi tag per il Nutriscore
    if (offProduct.containsKey('nutriscore_grade')) {
      tags.add('nutriscore-${offProduct['nutriscore_grade']}');
    }

    // Aggiungi tag per il gruppo NOVA (livello di lavorazione)
    if (offProduct.containsKey('nova_group')) {
      final novaGroup = offProduct['nova_group'];
      if (novaGroup == 1) {
        tags.add('non processato');
      } else if (novaGroup == 4) {
        tags.add('ultra processato');
      }
    }

    // Aggiungi tag dalle etichette del prodotto
    final labels = (offProduct['labels'] ?? '').toLowerCase();
    if (labels.contains('organic') || labels.contains('bio') || labels.contains('biologico')) {
      tags.add('biologico');
    }

    return tags.toList();
  }
}
