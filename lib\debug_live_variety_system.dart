import 'dart:io';
import 'services/specific_diet_generator_service.dart';
import 'services/precision_food_selector.dart';
import 'services/food_variety_manager.dart';
import 'services/food_database_analyzer.dart';
import 'data/specific_diet_foods.dart';
import 'models/user_profile.dart';
import 'models/food.dart';

/// DEBUG COMPLETO DEL SISTEMA DI VARIETÀ LIVE
/// Analizza il problema di varietà limitata con 157 alimenti disponibili
Future<void> main() async {
  print('🔍 DEBUG SISTEMA VARIETÀ LIVE - ANALISI COMPLETA');
  print('=' * 70);
  print('Problema: Solo pochi alimenti utilizzati nonostante 157 disponibili');
  print('Obiettivo: Identificare e risolvere la causa della varietà limitata\n');

  try {
    // FASE 1: VERIFICA DATABASE E SERVIZI
    print('1️⃣ VERIFICA DATABASE E INIZIALIZZAZIONE SERVIZI');
    print('-' * 55);
    
    // Verifica database alimenti specifici
    final allSpecificFoods = SpecificDietFoods.getAllFoods();
    print('📊 Database SpecificDietFoods: ${allSpecificFoods.length} alimenti');
    
    if (allSpecificFoods.length != 157) {
      print('⚠️ ATTENZIONE: Numero alimenti diverso da 157!');
    }
    
    // Analizza categorie disponibili
    final categoryCounts = <FoodCategory, int>{};
    for (final food in allSpecificFoods) {
      for (final category in food.categories) {
        categoryCounts[category] = (categoryCounts[category] ?? 0) + 1;
      }
    }
    
    print('\n📈 Distribuzione per categoria:');
    for (final entry in categoryCounts.entries) {
      print('   ${entry.key.toString().split('.').last}: ${entry.value} alimenti');
    }
    
    // Inizializza servizi
    final varietyManager = await FoodVarietyManager.getInstance();
    await varietyManager.resetUsageHistory();
    print('\n✅ FoodVarietyManager inizializzato e resettato');
    
    final precisionSelector = await PrecisionFoodSelector.getInstance();
    print('✅ PrecisionFoodSelector inizializzato');
    
    final specificGenerator = await SpecificDietGeneratorService.getInstance();
    print('✅ SpecificDietGeneratorService inizializzato');
    
    // FASE 2: VERIFICA INTEGRAZIONE
    print('\n2️⃣ VERIFICA INTEGRAZIONE PRECISION SELECTOR');
    print('-' * 50);
    
    // Crea profilo utente di test
    final testProfile = UserProfile(
      id: 'debug_user_${DateTime.now().millisecondsSinceEpoch}',
      name: 'Debug User',
      age: 30,
      gender: Gender.male,
      height: 175,
      weight: 70,
      activityLevel: ActivityLevel.moderate,
      goal: Goal.maintain,
      dietType: DietType.omnivore,
      allergies: [],
      dislikedFoods: [],
      mealsPerDay: 3,
    );
    
    print('👤 Profilo test creato: ${testProfile.calculateCalorieTarget()} kcal/giorno');
    
    // Test diretto del PrecisionFoodSelector
    print('\n🧪 Test diretto PrecisionFoodSelector:');
    try {
      final directSelection = await precisionSelector.selectFoodsForMeal(
        userProfile: testProfile,
        mealType: 'lunch',
        targetCalories: 600,
        targetMacros: {'proteins': 30, 'carbs': 60, 'fats': 20},
        forDate: DateTime.now(),
      );
      
      print('   ✅ Selezione diretta riuscita: ${directSelection.length} alimenti');
      for (final fp in directSelection) {
        print('      - ${fp.food.name} (${fp.grams}g)');
      }
    } catch (e) {
      print('   ❌ Errore selezione diretta: $e');
    }
    
    // FASE 3: TEST GENERAZIONE MULTIPLA
    print('\n3️⃣ TEST GENERAZIONE PIANI MULTIPLI');
    print('-' * 40);
    
    final allGeneratedFoods = <String>[];
    final plansByGeneration = <int, List<String>>{};
    
    for (int generation = 1; generation <= 5; generation++) {
      print('\n📋 Generazione $generation:');
      
      try {
        final weeklyPlan = await specificGenerator.generateWeeklyDietPlan(
          testProfile,
          weeks: 1,
        );
        
        if (weeklyPlan.dailyPlans.isNotEmpty) {
          final dailyPlan = weeklyPlan.dailyPlans.first;
          final generationFoods = <String>[];
          
          for (final meal in dailyPlan.meals) {
            final mealFoods = meal.foods.map((fp) => fp.food.name).toList();
            generationFoods.addAll(mealFoods);
            print('   ${_getMealName(meal.type)}: ${mealFoods.join(', ')}');
          }
          
          plansByGeneration[generation] = generationFoods;
          allGeneratedFoods.addAll(generationFoods);
        } else {
          print('   ❌ Nessun piano generato');
        }
      } catch (e) {
        print('   ❌ Errore generazione $generation: $e');
      }
      
      // Pausa tra generazioni per permettere al sistema di varietà di funzionare
      await Future.delayed(Duration(milliseconds: 100));
    }
    
    // FASE 4: ANALISI VARIETÀ
    print('\n4️⃣ ANALISI VARIETÀ OTTENUTA');
    print('-' * 35);
    
    final uniqueGeneratedFoods = allGeneratedFoods.toSet();
    final varietyRatio = uniqueGeneratedFoods.length / allGeneratedFoods.length;
    final databaseUtilization = uniqueGeneratedFoods.length / allSpecificFoods.length;
    
    print('📊 Statistiche varietà:');
    print('   - Alimenti totali selezionati: ${allGeneratedFoods.length}');
    print('   - Alimenti unici utilizzati: ${uniqueGeneratedFoods.length}');
    print('   - Rapporto varietà: ${(varietyRatio * 100).toStringAsFixed(1)}%');
    print('   - Utilizzo database: ${(databaseUtilization * 100).toStringAsFixed(1)}% (${uniqueGeneratedFoods.length}/157)');
    
    // Analisi sovrapposizioni tra generazioni
    print('\n🔄 Sovrapposizioni tra generazioni:');
    for (int i = 1; i <= 4; i++) {
      final foods1 = plansByGeneration[i]?.toSet() ?? {};
      final foods2 = plansByGeneration[i + 1]?.toSet() ?? {};
      final overlap = foods1.intersection(foods2);
      final overlapPercentage = foods1.isNotEmpty ? (overlap.length / foods1.length * 100) : 0;
      
      print('   Gen $i vs Gen ${i + 1}: ${overlap.length} alimenti comuni (${overlapPercentage.toStringAsFixed(1)}%)');
    }
    
    // FASE 5: VERIFICA TRACKING VARIETÀ
    print('\n5️⃣ VERIFICA TRACKING VARIETÀ');
    print('-' * 35);
    
    final stats = varietyManager.getUsageStatistics();
    print('📈 Statistiche FoodVarietyManager:');
    print('   - Alimenti tracciati: ${stats['totalTrackedFoods']}');
    print('   - Utilizzi totali: ${stats['totalUsages']}');
    print('   - Utilizzi recenti: ${stats['recentUsages']}');
    
    final recentFoods = varietyManager.getRecentlyUsedFoods();
    print('   - Alimenti utilizzati di recente: ${recentFoods.length}');
    
    if (recentFoods.isNotEmpty) {
      print('   - Primi 10 alimenti recenti:');
      for (int i = 0; i < math.min(10, recentFoods.length); i++) {
        print('     ${i + 1}. ${recentFoods[i]}');
      }
    }
    
    // FASE 6: ANALISI FILTRI
    print('\n6️⃣ ANALISI FILTRI E RESTRIZIONI');
    print('-' * 40);
    
    // Test filtri per tipo di pasto
    for (final mealType in ['breakfast', 'lunch', 'dinner']) {
      final mealTypeEnum = MealType.values.firstWhere(
        (e) => e.toString().split('.').last == mealType,
        orElse: () => MealType.lunch,
      );
      
      final suitableForMeal = allSpecificFoods.where((food) =>
        food.suitableForMeals.contains(mealTypeEnum)
      ).toList();
      
      print('🍽️ ${_getMealName(mealType)}:');
      print('   - Alimenti adatti: ${suitableForMeal.length}/${allSpecificFoods.length}');
      print('   - Percentuale disponibile: ${(suitableForMeal.length / allSpecificFoods.length * 100).toStringAsFixed(1)}%');
    }
    
    // Test filtri per dieta
    final dietFiltered = allSpecificFoods.where((food) {
      switch (testProfile.dietType) {
        case DietType.omnivore:
          return true;
        case DietType.vegetarian:
          return food.isVegetarian;
        case DietType.vegan:
          return food.isVegan;
        default:
          return true;
      }
    }).toList();
    
    print('\n🥗 Filtro dieta (${testProfile.dietType}):');
    print('   - Alimenti compatibili: ${dietFiltered.length}/${allSpecificFoods.length}');
    print('   - Percentuale disponibile: ${(dietFiltered.length / allSpecificFoods.length * 100).toStringAsFixed(1)}%');
    
    // FASE 7: DIAGNOSI PROBLEMI
    print('\n7️⃣ DIAGNOSI PROBLEMI');
    print('-' * 25);
    
    final issues = <String>[];
    
    if (databaseUtilization < 0.3) {
      issues.add('Utilizzo database molto basso (${(databaseUtilization * 100).toStringAsFixed(1)}%)');
    }
    
    if (varietyRatio < 0.5) {
      issues.add('Varietà insufficiente (${(varietyRatio * 100).toStringAsFixed(1)}%)');
    }
    
    if (stats['totalTrackedFoods'] as int == 0) {
      issues.add('Sistema di tracking non funzionante');
    }
    
    // Verifica sovrapposizioni eccessive
    var excessiveOverlap = false;
    for (int i = 1; i <= 4; i++) {
      final foods1 = plansByGeneration[i]?.toSet() ?? {};
      final foods2 = plansByGeneration[i + 1]?.toSet() ?? {};
      final overlap = foods1.intersection(foods2);
      final overlapPercentage = foods1.isNotEmpty ? (overlap.length / foods1.length * 100) : 0;
      
      if (overlapPercentage > 70) {
        excessiveOverlap = true;
        break;
      }
    }
    
    if (excessiveOverlap) {
      issues.add('Sovrapposizione eccessiva tra generazioni consecutive');
    }
    
    // FASE 8: RACCOMANDAZIONI
    print('\n8️⃣ RACCOMANDAZIONI E SOLUZIONI');
    print('-' * 40);
    
    if (issues.isEmpty) {
      print('✅ SISTEMA FUNZIONANTE CORRETTAMENTE!');
      print('   - Varietà adeguata ottenuta');
      print('   - Tracking attivo');
      print('   - Buon utilizzo del database');
    } else {
      print('⚠️ PROBLEMI IDENTIFICATI:');
      for (int i = 0; i < issues.length; i++) {
        print('   ${i + 1}. ${issues[i]}');
      }
      
      print('\n💡 SOLUZIONI PROPOSTE:');
      
      if (databaseUtilization < 0.3) {
        print('   🔧 Ridurre filtri restrittivi');
        print('   🔧 Migliorare algoritmo di selezione');
        print('   🔧 Aumentare numero alimenti per pasto');
      }
      
      if (stats['totalTrackedFoods'] as int == 0) {
        print('   🔧 Verificare integrazione FoodVarietyManager');
        print('   🔧 Controllare chiamate recordFoodUsage()');
      }
      
      if (varietyRatio < 0.5) {
        print('   🔧 Implementare selezione pesata casuale');
        print('   🔧 Aumentare peso punteggi varietà');
        print('   🔧 Ridurre periodo cooldown');
      }
    }
    
    print('\n' + '=' * 70);
    print('🎯 DEBUG COMPLETATO');
    
    if (issues.isEmpty) {
      print('✅ Sistema di varietà funzionante correttamente');
    } else {
      print('⚠️ Problemi identificati - implementazione correzioni necessaria');
    }
    
  } catch (e, stackTrace) {
    print('\n❌ ERRORE CRITICO: $e');
    print('Stack trace: $stackTrace');
  }
}

String _getMealName(String mealType) {
  switch (mealType) {
    case 'breakfast': return 'Colazione';
    case 'lunch': return 'Pranzo';
    case 'dinner': return 'Cena';
    case 'snack': return 'Spuntino';
    default: return mealType;
  }
}
