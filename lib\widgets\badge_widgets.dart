import 'package:flutter/material.dart' hide Badge;
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/badge_models.dart';
import '../services/badge_service.dart';
import '../theme/dr_staffilano_theme.dart';

/// Widget per visualizzare una card badge
class BadgeCard extends StatefulWidget {
  final AppBadge badge;
  final BadgeProgress? progress;
  final bool isUnlocked;
  final VoidCallback? onTap;
  final bool showProgress;
  final bool animate;

  const BadgeCard({
    Key? key,
    required this.badge,
    this.progress,
    this.isUnlocked = false,
    this.onTap,
    this.showProgress = true,
    this.animate = true,
  }) : super(key: key);

  @override
  State<BadgeCard> createState() => _BadgeCardState();
}

class _BadgeCardState extends State<BadgeCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (widget.animate) {
      _animationController.forward();
    } else {
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTap: widget.onTap,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  if (widget.isUnlocked) ...[
                    BoxShadow(
                      color: widget.badge.rarity.glowColor,
                      blurRadius: 20 * _glowAnimation.value,
                      spreadRadius: 2 * _glowAnimation.value,
                    ),
                  ],
                  BoxShadow(
                    color: Colors.black.withOpacity(isDarkMode ? 0.3 : 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Card(
                elevation: 0,
                color: isDarkMode
                    ? DrStaffilanoTheme.backgroundDark
                    : Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                  side: BorderSide(
                    color: widget.isUnlocked
                        ? widget.badge.rarity.color.withOpacity(0.5)
                        : Colors.grey.withOpacity(0.3),
                    width: 2,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(8),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Icona badge
                      _buildBadgeIcon(),
                      const SizedBox(height: 6),

                      // Nome badge
                      Text(
                        widget.badge.name,
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: widget.isUnlocked
                              ? (isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary)
                              : Colors.grey,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 4),

                      // Rarità
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: widget.badge.rarity.color.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          widget.badge.rarity.displayName,
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                            color: widget.badge.rarity.color,
                          ),
                        ),
                      ),

                      // Progresso (se richiesto e non sbloccato)
                      if (widget.showProgress && !widget.isUnlocked && widget.progress != null) ...[
                        const SizedBox(height: 12),
                        _buildProgressIndicator(),
                      ],

                      // Data sblocco (se sbloccato)
                      if (widget.isUnlocked && widget.progress?.unlockedAt != null) ...[
                        const SizedBox(height: 8),
                        Text(
                          'Sbloccato il ${_formatDate(widget.progress!.unlockedAt!)}',
                          style: TextStyle(
                            fontSize: 10,
                            color: widget.badge.primaryColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Widget per l'icona del badge
  Widget _buildBadgeIcon() {
    final iconSize = 26.0;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      width: iconSize + 8,
      height: iconSize + 8,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: widget.isUnlocked
            ? LinearGradient(
                colors: [
                  widget.badge.primaryColor,
                  widget.badge.secondaryColor,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : LinearGradient(
                colors: [
                  Colors.grey.shade400,
                  Colors.grey.shade600,
                ],
              ),
        boxShadow: widget.isUnlocked
            ? [
                BoxShadow(
                  color: widget.badge.primaryColor.withOpacity(0.3),
                  blurRadius: 8,
                  spreadRadius: 2,
                ),
              ]
            : null,
      ),
      child: Icon(
        widget.badge.icon,
        size: iconSize,
        color: widget.isUnlocked
            ? Colors.white
            : Colors.grey.shade300,
      ),
    );
  }

  /// Widget per l'indicatore di progresso
  Widget _buildProgressIndicator() {
    final progress = widget.progress!.progress;

    return Column(
      children: [
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey.shade200,
          valueColor: AlwaysStoppedAnimation<Color>(widget.badge.primaryColor),
          minHeight: 4,
        ),
        const SizedBox(height: 4),
        Text(
          '${widget.progress!.currentValue}/${widget.progress!.requiredValue} ${widget.badge.requiredUnit}',
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey.shade600,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// Formatta la data
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Widget per la griglia di badge
class BadgeGrid extends StatelessWidget {
  final List<AppBadge> badges;
  final List<BadgeProgress> progressList;
  final List<String> unlockedBadges;
  final int crossAxisCount;
  final double childAspectRatio;
  final bool showProgress;
  final Function(AppBadge)? onBadgeTap;

  const BadgeGrid({
    Key? key,
    required this.badges,
    required this.progressList,
    required this.unlockedBadges,
    this.crossAxisCount = 3,
    this.childAspectRatio = 0.8,
    this.showProgress = true,
    this.onBadgeTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const ClampingScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: childAspectRatio,
        crossAxisSpacing: 6,
        mainAxisSpacing: 6,
      ),
      itemCount: badges.length,
      itemBuilder: (context, index) {
        final badge = badges[index];
        final progress = progressList.firstWhere(
          (p) => p.badgeId == badge.id,
          orElse: () => BadgeProgress(
            badgeId: badge.id,
            currentValue: 0,
            requiredValue: badge.requiredValue,
            lastUpdated: DateTime.now(),
          ),
        );
        final isUnlocked = unlockedBadges.contains(badge.id);

        return BadgeCard(
          badge: badge,
          progress: progress,
          isUnlocked: isUnlocked,
          showProgress: showProgress,
          animate: true,
          onTap: onBadgeTap != null ? () => onBadgeTap!(badge) : null,
        );
      },
    );
  }
}

/// Widget per la sezione badge nella home screen
class HomeBadgeSection extends StatelessWidget {
  final int maxBadgesToShow;
  final VoidCallback? onViewAllTap;

  const HomeBadgeSection({
    Key? key,
    this.maxBadgesToShow = 6,
    this.onViewAllTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return FutureBuilder(
      future: _loadBadgeData(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return _buildLoadingState();
        }

        final data = snapshot.data as Map<String, dynamic>;
        final recentBadges = data['recentBadges'] as List<AppBadge>;
        final progressList = data['progressList'] as List<BadgeProgress>;
        final unlockedBadges = data['unlockedBadges'] as List<String>;
        final totalUnlocked = unlockedBadges.length;
        final totalBadges = data['totalBadges'] as int;

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                DrStaffilanoTheme.accentGold.withOpacity(0.1),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: DrStaffilanoTheme.accentGold,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.emoji_events,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'I Tuoi Badge',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                            ),
                          ),
                          Text(
                            '$totalUnlocked di $totalBadges sbloccati',
                            style: TextStyle(
                              fontSize: 14,
                              color: DrStaffilanoTheme.primaryGreen,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (onViewAllTap != null)
                      TextButton(
                        onPressed: onViewAllTap,
                        child: Text(
                          'Vedi Tutti',
                          style: TextStyle(
                            color: DrStaffilanoTheme.professionalBlue,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                  ],
                ),

                const SizedBox(height: 16),

                // Barra di progresso generale
                _buildOverallProgress(totalUnlocked, totalBadges),

                const SizedBox(height: 20),

                // Griglia badge recenti
                if (recentBadges.isNotEmpty) ...[
                  Text(
                    'Badge Recenti',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 12),
                  BadgeGrid(
                    badges: recentBadges,
                    progressList: progressList,
                    unlockedBadges: unlockedBadges,
                    crossAxisCount: 3,
                    childAspectRatio: 0.9,
                    showProgress: false,
                    onBadgeTap: (badge) => _showBadgeDetail(context, badge),
                  ),
                ] else ...[
                  _buildNoBadgesState(),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  /// Carica i dati dei badge
  Future<Map<String, dynamic>> _loadBadgeData() async {
    final badgeService = BadgeService.instance;

    if (!badgeService.isInitialized) {
      await badgeService.initialize();
    }

    final allBadges = badgeService.allBadges;
    final unlockedBadges = badgeService.unlockedBadges;
    final userCollection = badgeService.userCollection;

    // Prendi i badge più recenti (sbloccati + in progresso)
    final recentBadges = <AppBadge>[];

    // Prima aggiungi i badge sbloccati più recenti
    final sortedUnlocked = unlockedBadges.take(3).toList();
    recentBadges.addAll(sortedUnlocked);

    // Poi aggiungi badge in progresso (non sbloccati)
    final inProgress = allBadges.where((badge) =>
        !badgeService.isBadgeUnlocked(badge.id) &&
        (badgeService.getBadgeProgress(badge.id)?.currentValue ?? 0) > 0
    ).take(maxBadgesToShow - recentBadges.length).toList();

    recentBadges.addAll(inProgress);

    // Se non ci sono abbastanza badge, aggiungi i primi disponibili
    if (recentBadges.length < maxBadgesToShow) {
      final remaining = allBadges.where((badge) =>
          !recentBadges.contains(badge)
      ).take(maxBadgesToShow - recentBadges.length).toList();
      recentBadges.addAll(remaining);
    }

    return {
      'recentBadges': recentBadges.take(maxBadgesToShow).toList(),
      'progressList': userCollection?.badgeProgress ?? [],
      'unlockedBadges': userCollection?.unlockedBadges ?? [],
      'totalBadges': allBadges.length,
    };
  }

  /// Widget per il progresso generale
  Widget _buildOverallProgress(int unlocked, int total) {
    final progress = total > 0 ? unlocked / total : 0.0;

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progresso Generale',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: DrStaffilanoTheme.textSecondary,
              ),
            ),
            Text(
              '${(progress * 100).round()}%',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: DrStaffilanoTheme.primaryGreen,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey.shade200,
          valueColor: AlwaysStoppedAnimation<Color>(DrStaffilanoTheme.primaryGreen),
          minHeight: 8,
        ),
      ],
    );
  }

  /// Widget per lo stato di caricamento
  Widget _buildLoadingState() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(20),
      ),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  /// Widget per lo stato senza badge
  Widget _buildNoBadgesState() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        children: [
          Icon(
            Icons.emoji_events_outlined,
            size: 48,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 12),
          Text(
            'Inizia il tuo viaggio!',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Completa quiz e sfide per guadagnare i tuoi primi badge',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Mostra il dettaglio di un badge
  void _showBadgeDetail(BuildContext context, AppBadge badge) {
    showDialog(
      context: context,
      builder: (context) => BadgeDetailDialog(badge: badge),
    );
  }
}

/// Dialog per i dettagli di un badge
class BadgeDetailDialog extends StatelessWidget {
  final AppBadge badge;

  const BadgeDetailDialog({
    Key? key,
    required this.badge,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final badgeService = BadgeService.instance;
    final isUnlocked = badgeService.isBadgeUnlocked(badge.id);
    final progress = badgeService.getBadgeProgress(badge.id);

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400),
        decoration: BoxDecoration(
          color: isDarkMode ? DrStaffilanoTheme.backgroundDark : Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 20,
              spreadRadius: 5,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header con icona badge
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    badge.primaryColor,
                    badge.secondaryColor,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
              ),
              child: Column(
                children: [
                  // Icona grande del badge
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white.withOpacity(0.2),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 10,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: Icon(
                      badge.icon,
                      size: 40,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Nome badge
                  Text(
                    badge.name,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 8),

                  // Rarità
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      badge.rarity.displayName,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Contenuto
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Descrizione
                  Text(
                    'Descrizione',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    badge.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
                      height: 1.5,
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Progresso o stato
                  if (isUnlocked) ...[
                    _buildUnlockedState(progress),
                  ] else ...[
                    _buildProgressState(progress),
                  ],

                  const SizedBox(height: 20),

                  // Citazione Dr. Staffilano
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              FontAwesomeIcons.userDoctor,
                              color: DrStaffilanoTheme.primaryGreen,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Dr. Staffilano dice:',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: DrStaffilanoTheme.primaryGreen,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          badge.drStaffilanoQuote,
                          style: TextStyle(
                            fontSize: 14,
                            color: isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
                            fontStyle: FontStyle.italic,
                            height: 1.4,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Pulsante chiudi
            Padding(
              padding: const EdgeInsets.only(bottom: 24, left: 24, right: 24),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: badge.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Chiudi',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Widget per lo stato sbloccato
  Widget _buildUnlockedState(BadgeProgress? progress) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: Colors.green,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Badge Sbloccato!',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
                if (progress?.unlockedAt != null)
                  Text(
                    'Ottenuto il ${_formatDate(progress!.unlockedAt!)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.green.shade700,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Widget per lo stato in progresso
  Widget _buildProgressState(BadgeProgress? progress) {
    if (progress == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Progresso',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
              ),
              Text(
                '${progress.currentValue}/${progress.requiredValue}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          LinearProgressIndicator(
            value: progress.progress,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
            minHeight: 8,
          ),
          const SizedBox(height: 8),
          Text(
            '${(progress.progress * 100).round()}% completato',
            style: TextStyle(
              fontSize: 12,
              color: Colors.orange.shade700,
            ),
          ),
        ],
      ),
    );
  }

  /// Formatta la data
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
