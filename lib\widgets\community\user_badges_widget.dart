import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/community_user.dart';
import '../../services/community_profile_service.dart';
import '../../theme/dr_staffilano_theme.dart';

/// Modello per i badge
class UserBadge {
  final String id;
  final String name;
  final String description;
  final String icon;
  final Color color;
  final DateTime? earnedDate;
  final bool isEarned;
  final int requiredValue;
  final int currentValue;

  UserBadge({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    this.earnedDate,
    this.isEarned = false,
    this.requiredValue = 0,
    this.currentValue = 0,
  });

  double get progress => requiredValue > 0 ? (currentValue / requiredValue).clamp(0.0, 1.0) : 0.0;
}

/// Widget per visualizzare i badge dell'utente
class UserBadgesWidget extends StatelessWidget {
  final CommunityUser user;

  const UserBadgesWidget({
    super.key,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<CommunityProfileService>(
      builder: (context, profileService, child) {
        final stats = profileService.getUserStats(user.id);
        final badges = _generateUserBadges(stats);
        
        return SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Badge ottenuti
              _buildEarnedBadges(badges),
              
              const SizedBox(height: 24),
              
              // Badge in progresso
              _buildProgressBadges(badges),
              
              const SizedBox(height: 24),
              
              // Badge bloccati
              _buildLockedBadges(badges),
            ],
          ),
        );
      },
    );
  }

  /// Genera i badge dell'utente basati sulle statistiche
  List<UserBadge> _generateUserBadges(Map<String, dynamic> stats) {
    final totalPosts = stats['totalPosts'] ?? 0;
    final totalLikes = stats['totalLikes'] ?? 0;
    final streak = stats['streak'] ?? 0;
    final totalComments = stats['totalComments'] ?? 0;

    return [
      // Badge per i post
      UserBadge(
        id: 'first_post',
        name: 'Primo Post',
        description: 'Hai pubblicato il tuo primo post!',
        icon: '🎉',
        color: DrStaffilanoTheme.primaryGreen,
        isEarned: totalPosts >= 1,
        earnedDate: totalPosts >= 1 ? DateTime.now().subtract(const Duration(days: 5)) : null,
        requiredValue: 1,
        currentValue: totalPosts,
      ),
      UserBadge(
        id: 'prolific_writer',
        name: 'Scrittore Prolifico',
        description: 'Hai pubblicato 10 post',
        icon: '✍️',
        color: DrStaffilanoTheme.secondaryBlue,
        isEarned: totalPosts >= 10,
        earnedDate: totalPosts >= 10 ? DateTime.now().subtract(const Duration(days: 2)) : null,
        requiredValue: 10,
        currentValue: totalPosts,
      ),
      UserBadge(
        id: 'content_master',
        name: 'Maestro dei Contenuti',
        description: 'Hai pubblicato 50 post',
        icon: '👑',
        color: Colors.amber,
        isEarned: totalPosts >= 50,
        earnedDate: totalPosts >= 50 ? DateTime.now() : null,
        requiredValue: 50,
        currentValue: totalPosts,
      ),

      // Badge per i like
      UserBadge(
        id: 'liked',
        name: 'Apprezzato',
        description: 'Hai ricevuto 10 like',
        icon: '❤️',
        color: Colors.red,
        isEarned: totalLikes >= 10,
        earnedDate: totalLikes >= 10 ? DateTime.now().subtract(const Duration(days: 3)) : null,
        requiredValue: 10,
        currentValue: totalLikes,
      ),
      UserBadge(
        id: 'popular',
        name: 'Popolare',
        description: 'Hai ricevuto 100 like',
        icon: '🌟',
        color: Colors.orange,
        isEarned: totalLikes >= 100,
        earnedDate: totalLikes >= 100 ? DateTime.now() : null,
        requiredValue: 100,
        currentValue: totalLikes,
      ),

      // Badge per lo streak
      UserBadge(
        id: 'consistent',
        name: 'Costante',
        description: 'Streak di 7 giorni',
        icon: '🔥',
        color: Colors.orange,
        isEarned: streak >= 7,
        earnedDate: streak >= 7 ? DateTime.now().subtract(const Duration(days: 1)) : null,
        requiredValue: 7,
        currentValue: streak,
      ),
      UserBadge(
        id: 'dedicated',
        name: 'Dedicato',
        description: 'Streak di 30 giorni',
        icon: '💪',
        color: Colors.purple,
        isEarned: streak >= 30,
        earnedDate: streak >= 30 ? DateTime.now() : null,
        requiredValue: 30,
        currentValue: streak,
      ),

      // Badge speciali Dr. Staffilano
      UserBadge(
        id: 'heart_health',
        name: 'Cuore Sano',
        description: 'Esperto di salute cardiaca',
        icon: '💚',
        color: DrStaffilanoTheme.primaryGreen,
        isEarned: user.interests.contains('Cardiologia'),
        earnedDate: user.interests.contains('Cardiologia') ? user.joinDate : null,
        requiredValue: 1,
        currentValue: user.interests.contains('Cardiologia') ? 1 : 0,
      ),
      UserBadge(
        id: 'prevention_advocate',
        name: 'Sostenitore della Prevenzione',
        description: 'Promuovi la prevenzione medica',
        icon: '🛡️',
        color: DrStaffilanoTheme.professionalBlue,
        isEarned: user.interests.contains('Prevenzione'),
        earnedDate: user.interests.contains('Prevenzione') ? user.joinDate : null,
        requiredValue: 1,
        currentValue: user.interests.contains('Prevenzione') ? 1 : 0,
      ),
      UserBadge(
        id: 'wellness_champion',
        name: 'Campione del Benessere',
        description: 'Esperto di benessere generale',
        icon: '🌿',
        color: Colors.green,
        isEarned: user.interests.contains('Benessere'),
        earnedDate: user.interests.contains('Benessere') ? user.joinDate : null,
        requiredValue: 1,
        currentValue: user.interests.contains('Benessere') ? 1 : 0,
      ),
    ];
  }

  /// Costruisce la sezione dei badge ottenuti
  Widget _buildEarnedBadges(List<UserBadge> badges) {
    final earnedBadges = badges.where((badge) => badge.isEarned).toList();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Badge Ottenuti',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: DrStaffilanoTheme.primaryGreen,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                earnedBadges.length.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (earnedBadges.isEmpty)
          _buildEmptyState('Nessun badge ottenuto ancora', 'Continua a essere attivo per guadagnare i tuoi primi badge!')
        else
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.2,
            ),
            itemCount: earnedBadges.length,
            itemBuilder: (context, index) => _buildBadgeCard(earnedBadges[index], true),
          ),
      ],
    );
  }

  /// Costruisce la sezione dei badge in progresso
  Widget _buildProgressBadges(List<UserBadge> badges) {
    final progressBadges = badges.where((badge) => !badge.isEarned && badge.currentValue > 0).toList();
    
    if (progressBadges.isEmpty) return const SizedBox.shrink();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'In Progresso',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.2,
          ),
          itemCount: progressBadges.length,
          itemBuilder: (context, index) => _buildBadgeCard(progressBadges[index], false),
        ),
      ],
    );
  }

  /// Costruisce la sezione dei badge bloccati
  Widget _buildLockedBadges(List<UserBadge> badges) {
    final lockedBadges = badges.where((badge) => !badge.isEarned && badge.currentValue == 0).toList();
    
    if (lockedBadges.isEmpty) return const SizedBox.shrink();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Da Sbloccare',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.2,
          ),
          itemCount: lockedBadges.length,
          itemBuilder: (context, index) => _buildBadgeCard(lockedBadges[index], false),
        ),
      ],
    );
  }

  /// Costruisce una card del badge
  Widget _buildBadgeCard(UserBadge badge, bool isEarned) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isEarned ? badge.color : Colors.grey.withOpacity(0.3),
          width: isEarned ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icona del badge
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: isEarned ? badge.color.withOpacity(0.1) : Colors.grey.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                badge.icon,
                style: TextStyle(
                  fontSize: 24,
                  color: isEarned ? null : Colors.grey,
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Nome del badge
          Text(
            badge.name,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: isEarned ? Colors.black87 : Colors.grey,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          
          const SizedBox(height: 4),
          
          // Descrizione
          Text(
            badge.description,
            style: TextStyle(
              fontSize: 10,
              color: isEarned ? Colors.grey[600] : Colors.grey,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          
          // Progresso per badge non ottenuti
          if (!isEarned && badge.currentValue > 0) ...[
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: badge.progress,
              backgroundColor: Colors.grey.withOpacity(0.2),
              valueColor: AlwaysStoppedAnimation<Color>(badge.color),
              minHeight: 4,
            ),
            const SizedBox(height: 4),
            Text(
              '${badge.currentValue}/${badge.requiredValue}',
              style: TextStyle(
                fontSize: 10,
                color: badge.color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
          
          // Data di ottenimento per badge ottenuti
          if (isEarned && badge.earnedDate != null) ...[
            const SizedBox(height: 4),
            Text(
              _formatEarnedDate(badge.earnedDate!),
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey[500],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Costruisce lo stato vuoto
  Widget _buildEmptyState(String title, String description) {
    return Container(
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(
            Icons.emoji_events_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Formatta la data di ottenimento del badge
  String _formatEarnedDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Oggi';
    } else if (difference.inDays == 1) {
      return 'Ieri';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} giorni fa';
    } else {
      return '${(difference.inDays / 7).floor()} settimane fa';
    }
  }
}
