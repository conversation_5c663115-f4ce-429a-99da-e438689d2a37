import '../models/food.dart';

/// Database di proteine italiane sicure e cotte con metodi tradizionali
class SafeItalianProteins {
  static List<Food> getSafeProteins() {
    return [
      // POLLO - METODI TRADIZIONALI ITALIANI
      Food(
        id: 'safe_protein_1',
        name: 'Petto di pollo alla griglia',
        description: 'Petto di pollo grigliato, senza pelle, cotto',
        calories: 165,
        proteins: 31.0,
        carbs: 0.0,
        fats: 3.6,
        fiber: 0.0,
        sugar: 0.0,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        calcium: 15.0,
        potassium: 256.0,
        magnesium: 28.0,
        phosphorus: 228.0,
        sodium: 74.0,
        micronutrients: {
          'vitamina_b3': 10.9,
          'vitamina_b6': 0.5,
          'vitamina_b12': 0.3,
          'selenio': 27.6,
        },
        tags: ['pollo', 'griglia', 'magro', 'proteico', 'sicuro'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
      ),

      Food(
        id: 'safe_protein_2',
        name: 'Pollo arrosto',
        description: 'Pollo arrosto al forno con rosmarino',
        calories: 190,
        proteins: 29.0,
        carbs: 0.0,
        fats: 7.4,
        fiber: 0.0,
        sugar: 0.0,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        calcium: 12.0,
        potassium: 229.0,
        magnesium: 25.0,
        phosphorus: 200.0,
        sodium: 82.0,
        micronutrients: {
          'vitamina_b3': 8.5,
          'vitamina_b6': 0.4,
          'ferro': 1.3,
          'zinco': 2.1,
        },
        tags: ['pollo', 'arrosto', 'forno', 'rosmarino', 'sicuro'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
      ),

      // MANZO - METODI TRADIZIONALI ITALIANI
      Food(
        id: 'safe_protein_3',
        name: 'Manzo ai ferri',
        description: 'Bistecca di manzo ai ferri, magra',
        calories: 158,
        proteins: 26.0,
        carbs: 0.0,
        fats: 5.4,
        fiber: 0.0,
        sugar: 0.0,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        calcium: 18.0,
        potassium: 318.0,
        magnesium: 21.0,
        phosphorus: 198.0,
        sodium: 52.0,
        micronutrients: {
          'ferro': 2.9,
          'zinco': 4.5,
          'vitamina_b12': 2.6,
          'vitamina_b6': 0.4,
        },
        tags: ['manzo', 'ferri', 'bistecca', 'magro', 'sicuro'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
      ),

      Food(
        id: 'safe_protein_4',
        name: 'Brasato al Barolo',
        description: 'Manzo brasato al vino Barolo, tradizionale piemontese',
        calories: 185,
        proteins: 24.0,
        carbs: 2.0,
        fats: 8.0,
        fiber: 0.0,
        sugar: 1.5,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        calcium: 15.0,
        potassium: 295.0,
        magnesium: 19.0,
        phosphorus: 180.0,
        sodium: 65.0,
        micronutrients: {
          'ferro': 3.2,
          'zinco': 4.8,
          'vitamina_b12': 2.8,
          'resveratrolo': 0.5,
        },
        tags: ['manzo', 'brasato', 'barolo', 'piemonte', 'sicuro'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
        italianRegions: [ItalianRegion.piemonte],
      ),

      // PESCE - METODI TRADIZIONALI ITALIANI
      Food(
        id: 'safe_protein_5',
        name: 'Branzino al forno',
        description: 'Branzino al forno con olive e pomodorini',
        calories: 97,
        proteins: 18.5,
        carbs: 1.0,
        fats: 2.0,
        fiber: 0.0,
        sugar: 0.5,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: ['pesce'],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        calcium: 14.0,
        potassium: 280.0,
        magnesium: 50.0,
        phosphorus: 220.0,
        sodium: 140.0,
        micronutrients: {
          'omega_3': 650.0,
          'vitamina_b12': 1.4,
          'selenio': 14.0,
          'iodio': 18.0,
        },
        tags: ['branzino', 'forno', 'pesce', 'mediterraneo', 'sicuro'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
      ),

      Food(
        id: 'safe_protein_6',
        name: 'Salmone al vapore',
        description: 'Salmone cotto al vapore con erbe aromatiche',
        calories: 142,
        proteins: 25.4,
        carbs: 0.0,
        fats: 4.4,
        fiber: 0.0,
        sugar: 0.0,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: ['pesce'],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        calcium: 9.0,
        potassium: 384.0,
        magnesium: 30.0,
        phosphorus: 252.0,
        sodium: 44.0,
        micronutrients: {
          'omega_3': 1800.0,
          'vitamina_d': 11.0,
          'vitamina_b12': 3.2,
          'selenio': 22.0,
        },
        tags: ['salmone', 'vapore', 'pesce', 'omega3', 'sicuro'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
      ),

      // VITELLO - METODI TRADIZIONALI ITALIANI
      Food(
        id: 'safe_protein_7',
        name: 'Scaloppine al limone',
        description: 'Scaloppine di vitello al limone, tradizionali',
        calories: 172,
        proteins: 24.0,
        carbs: 2.0,
        fats: 7.0,
        fiber: 0.0,
        sugar: 1.0,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.lunch, MealType.dinner],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: [],
        servingSize: '100g cotto',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        calcium: 20.0,
        potassium: 325.0,
        magnesium: 24.0,
        phosphorus: 210.0,
        sodium: 58.0,
        micronutrients: {
          'ferro': 2.2,
          'zinco': 3.8,
          'vitamina_b12': 1.8,
          'vitamina_c': 8.0,
        },
        tags: ['vitello', 'scaloppine', 'limone', 'tradizionale', 'sicuro'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
      ),

      // UOVA - METODI SICURI
      Food(
        id: 'safe_protein_8',
        name: 'Uova sode',
        description: 'Uova di gallina sode, completamente cotte',
        calories: 155,
        proteins: 12.6,
        carbs: 1.1,
        fats: 10.6,
        fiber: 0.0,
        sugar: 1.1,
        categories: [FoodCategory.protein],
        suitableForMeals: [MealType.breakfast, MealType.lunch, MealType.snack],
        isVegetarian: true,
        isVegan: false,
        isGlutenFree: true,
        isDairyFree: true,
        allergens: ['uova'],
        servingSize: '100g (2 uova medie)',
        servingSizeGrams: 100,
        foodState: FoodState.cooked,
        calcium: 50.0,
        potassium: 126.0,
        magnesium: 10.0,
        phosphorus: 172.0,
        sodium: 124.0,
        micronutrients: {
          'vitamina_a': 140.0,
          'vitamina_d': 2.0,
          'vitamina_b12': 1.1,
          'colina': 251.0,
        },
        tags: ['uova', 'sode', 'proteico', 'colazione', 'sicuro'],
        dataSource: DataSource.crea,
        isTraditionalItalian: true,
      ),
    ];
  }
}
