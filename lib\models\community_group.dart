import 'package:uuid/uuid.dart';

/// Tipi di gruppi nella community
enum GroupType {
  public('public', 'Pubblico', '🌍'),
  private('private', 'Privato', '🔒'),
  premium('premium', 'Premium', '⭐'),
  official('official', 'Ufficiale', '✅');

  const GroupType(this.id, this.displayName, this.emoji);

  final String id;
  final String displayName;
  final String emoji;
}

/// Categorie tematiche dei gruppi
enum GroupCategory {
  dietaMediterranea('dieta_mediterranea', 'Dieta Mediterranea', '🫒', '#10B981'),
  sportNutrition('sport_nutrition', 'Sport Nutrition', '💪', '#3B82F6'),
  heartHealth('heart_health', 'Heart Health', '❤️', '#EF4444'),
  weightManagement('weight_management', 'Gestione Peso', '⚖️', '#F59E0B'),
  preventionCare('prevention_care', 'Prevenzione & Cura', '🏥', '#8B5CF6'),
  recipes('recipes', 'Ricette Salutari', '👨‍🍳', '#84CC16'),
  beginners('beginners', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '🌱', '#06B6D4'),
  experts('experts', 'Esperti', '🎓', '#6366F1'),
  challenges('challenges', 'Sfide', '🏆', '#F97316'),
  support('support', 'Supporto', '🤝', '#EC4899');

  const GroupCategory(this.id, this.displayName, this.emoji, this.colorHex);

  final String id;
  final String displayName;
  final String emoji;
  final String colorHex;
}

/// Modello per i gruppi della community
class CommunityGroup {
  final String id;
  final String name;
  final String description;
  final String? imageUrl;
  final GroupType type;
  final GroupCategory category;
  final String creatorId;
  final List<String> moderatorIds;
  final int membersCount;
  final int postsCount;
  final DateTime createdAt;
  final DateTime lastActivity;
  final Map<String, dynamic> rules;
  final List<String> tags;
  final bool isActive;
  final bool isFeatured;
  final Map<String, dynamic> settings;

  CommunityGroup({
    required this.id,
    required this.name,
    required this.description,
    this.imageUrl,
    required this.type,
    required this.category,
    required this.creatorId,
    this.moderatorIds = const [],
    this.membersCount = 0,
    this.postsCount = 0,
    required this.createdAt,
    required this.lastActivity,
    this.rules = const {},
    this.tags = const [],
    this.isActive = true,
    this.isFeatured = false,
    this.settings = const {},
  });

  /// Crea un nuovo gruppo
  factory CommunityGroup.create({
    required String name,
    required String description,
    String? imageUrl,
    required GroupType type,
    required GroupCategory category,
    required String creatorId,
    List<String> moderatorIds = const [],
    Map<String, dynamic> rules = const {},
    List<String> tags = const [],
    Map<String, dynamic> settings = const {},
  }) {
    return CommunityGroup(
      id: const Uuid().v4(),
      name: name,
      description: description,
      imageUrl: imageUrl,
      type: type,
      category: category,
      creatorId: creatorId,
      moderatorIds: moderatorIds,
      createdAt: DateTime.now(),
      lastActivity: DateTime.now(),
      rules: rules,
      tags: tags,
      settings: settings,
    );
  }

  /// Copia con modifiche
  CommunityGroup copyWith({
    String? name,
    String? description,
    String? imageUrl,
    GroupType? type,
    GroupCategory? category,
    List<String>? moderatorIds,
    int? membersCount,
    int? postsCount,
    DateTime? lastActivity,
    Map<String, dynamic>? rules,
    List<String>? tags,
    bool? isActive,
    bool? isFeatured,
    Map<String, dynamic>? settings,
  }) {
    return CommunityGroup(
      id: id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      type: type ?? this.type,
      category: category ?? this.category,
      creatorId: creatorId,
      moderatorIds: moderatorIds ?? this.moderatorIds,
      membersCount: membersCount ?? this.membersCount,
      postsCount: postsCount ?? this.postsCount,
      createdAt: createdAt,
      lastActivity: lastActivity ?? this.lastActivity,
      rules: rules ?? this.rules,
      tags: tags ?? this.tags,
      isActive: isActive ?? this.isActive,
      isFeatured: isFeatured ?? this.isFeatured,
      settings: settings ?? this.settings,
    );
  }

  /// Converti in Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'type': type.id,
      'category': category.id,
      'creatorId': creatorId,
      'moderatorIds': moderatorIds,
      'membersCount': membersCount,
      'postsCount': postsCount,
      'createdAt': createdAt.toIso8601String(),
      'lastActivity': lastActivity.toIso8601String(),
      'rules': rules,
      'tags': tags,
      'isActive': isActive,
      'isFeatured': isFeatured,
      'settings': settings,
    };
  }

  /// Crea da Map
  factory CommunityGroup.fromMap(Map<String, dynamic> map) {
    return CommunityGroup(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      imageUrl: map['imageUrl'],
      type: GroupType.values.firstWhere(
        (type) => type.id == map['type'],
        orElse: () => GroupType.public,
      ),
      category: GroupCategory.values.firstWhere(
        (category) => category.id == map['category'],
        orElse: () => GroupCategory.support,
      ),
      creatorId: map['creatorId'] ?? '',
      moderatorIds: List<String>.from(map['moderatorIds'] ?? []),
      membersCount: map['membersCount']?.toInt() ?? 0,
      postsCount: map['postsCount']?.toInt() ?? 0,
      createdAt: DateTime.parse(map['createdAt'] ?? DateTime.now().toIso8601String()),
      lastActivity: DateTime.parse(map['lastActivity'] ?? DateTime.now().toIso8601String()),
      rules: Map<String, dynamic>.from(map['rules'] ?? {}),
      tags: List<String>.from(map['tags'] ?? []),
      isActive: map['isActive'] ?? true,
      isFeatured: map['isFeatured'] ?? false,
      settings: Map<String, dynamic>.from(map['settings'] ?? {}),
    );
  }

  /// Verifica se il gruppo è attivo (attività negli ultimi 7 giorni)
  bool get isRecentlyActive {
    return DateTime.now().difference(lastActivity).inDays <= 7;
  }

  /// Verifica se il gruppo è popolare (molti membri e post)
  bool get isPopular {
    return membersCount >= 50 && postsCount >= 20;
  }

  /// Ottieni il tasso di attività (post per membro)
  double get activityRate {
    if (membersCount == 0) return 0.0;
    return postsCount / membersCount;
  }

  /// Verifica se l'utente è moderatore
  bool isModerator(String userId) {
    return moderatorIds.contains(userId) || creatorId == userId;
  }

  /// Verifica se l'utente è il creatore
  bool isCreator(String userId) {
    return creatorId == userId;
  }

  /// Ottieni il colore del gruppo basato sulla categoria
  String get colorHex => category.colorHex;

  /// Ottieni l'emoji del gruppo
  String get emoji => category.emoji;

  /// Ottieni il display name completo
  String get fullDisplayName => '${category.emoji} $name';

  @override
  String toString() {
    return 'CommunityGroup(id: $id, name: $name, category: ${category.displayName}, members: $membersCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CommunityGroup && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Modello per l'appartenenza ai gruppi
class GroupMembership {
  final String id;
  final String groupId;
  final String userId;
  final DateTime joinedAt;
  final bool isActive;
  final bool isMuted;
  final Map<String, dynamic> preferences;

  GroupMembership({
    required this.id,
    required this.groupId,
    required this.userId,
    required this.joinedAt,
    this.isActive = true,
    this.isMuted = false,
    this.preferences = const {},
  });

  /// Crea una nuova membership
  factory GroupMembership.create({
    required String groupId,
    required String userId,
    Map<String, dynamic> preferences = const {},
  }) {
    return GroupMembership(
      id: const Uuid().v4(),
      groupId: groupId,
      userId: userId,
      joinedAt: DateTime.now(),
      preferences: preferences,
    );
  }

  /// Converti in Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'groupId': groupId,
      'userId': userId,
      'joinedAt': joinedAt.toIso8601String(),
      'isActive': isActive,
      'isMuted': isMuted,
      'preferences': preferences,
    };
  }

  /// Crea da Map
  factory GroupMembership.fromMap(Map<String, dynamic> map) {
    return GroupMembership(
      id: map['id'] ?? '',
      groupId: map['groupId'] ?? '',
      userId: map['userId'] ?? '',
      joinedAt: DateTime.parse(map['joinedAt'] ?? DateTime.now().toIso8601String()),
      isActive: map['isActive'] ?? true,
      isMuted: map['isMuted'] ?? false,
      preferences: Map<String, dynamic>.from(map['preferences'] ?? {}),
    );
  }

  /// Verifica se è un membro di lunga data (più di 30 giorni)
  bool get isLongTimeMember {
    return DateTime.now().difference(joinedAt).inDays >= 30;
  }
}
