import '../models/food.dart';

class AdditionalFoods {
  static List<Food> getAdditionalFoods() {
    return [
      // COLAZIONE
      Food(
        id: 'breakfast_4',
        name: 'Frittata di verdure',
        description: 'Frittata con spinaci, pomodorini, cipolla e formaggio',
        imageUrl: 'https://images.unsplash.com/photo-1510693206972-df098062cb71?q=80&w=500',
        calories: 250,
        proteins: 18.0,
        carbs: 5.0,
        fats: 16.0,
        fiber: 2.0,
        sugar: 3.0,
        suitableForMeals: const [MealType.breakfast, MealType.lunch],
        categories: const [FoodCategory.protein, FoodCategory.vegetable],
        isVegetarian: true,
        isGlutenFree: true,
        allergens: const ['uova', 'latticini'],
        servingSize: '1 porzione (200g)',
        servingSizeGrams: 200,
      ),
      Food(
        id: 'breakfast_5',
        name: 'Pancake integrali con frutti di bosco',
        description: 'Pancake preparati con farina integrale, serviti con frutti di bosco freschi e sciroppo d\'acero',
        imageUrl: 'https://images.unsplash.com/photo-1528207776546-365bb710ee93?q=80&w=500',
        calories: 350,
        proteins: 8.0,
        carbs: 60.0,
        fats: 9.0,
        fiber: 6.0,
        sugar: 25.0,
        suitableForMeals: const [MealType.breakfast],
        categories: const [FoodCategory.grain, FoodCategory.fruit],
        isVegetarian: true,
        allergens: const ['glutine', 'uova', 'latticini'],
        servingSize: '1 porzione (250g)',
        servingSizeGrams: 250,
      ),
      
      // PRANZO
      Food(
        id: 'lunch_4',
        name: 'Zuppa di lenticchie',
        description: 'Zuppa di lenticchie con carote, sedano, cipolla e spezie',
        imageUrl: 'https://images.unsplash.com/photo-1547592166-23ac45744acd?q=80&w=500',
        calories: 280,
        proteins: 18.0,
        carbs: 40.0,
        fats: 4.0,
        fiber: 15.0,
        sugar: 6.0,
        suitableForMeals: const [MealType.lunch, MealType.dinner],
        categories: const [FoodCategory.protein, FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        servingSize: '1 porzione (300g)',
        servingSizeGrams: 300,
      ),
      Food(
        id: 'lunch_5',
        name: 'Insalata di farro con tonno e verdure',
        description: 'Insalata di farro con tonno, pomodorini, olive, mais e condimento leggero',
        imageUrl: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?q=80&w=500',
        calories: 390,
        proteins: 25.0,
        carbs: 45.0,
        fats: 12.0,
        fiber: 8.0,
        sugar: 5.0,
        suitableForMeals: const [MealType.lunch],
        categories: const [FoodCategory.grain, FoodCategory.protein, FoodCategory.vegetable],
        allergens: const ['glutine', 'pesce'],
        servingSize: '1 porzione (350g)',
        servingSizeGrams: 350,
      ),
      
      // CENA
      Food(
        id: 'dinner_4',
        name: 'Zuppa di verdure miste',
        description: 'Zuppa con carote, sedano, zucchine, patate, pomodori e erbe aromatiche',
        imageUrl: 'https://images.unsplash.com/photo-1476718406336-bb5a9690ee2a?q=80&w=500',
        calories: 220,
        proteins: 8.0,
        carbs: 35.0,
        fats: 6.0,
        fiber: 10.0,
        sugar: 8.0,
        suitableForMeals: const [MealType.dinner],
        categories: const [FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        servingSize: '1 porzione (350g)',
        servingSizeGrams: 350,
      ),
      Food(
        id: 'dinner_5',
        name: 'Bistecca di manzo con patate al forno',
        description: 'Bistecca di manzo alla griglia con patate al forno e rosmarino',
        imageUrl: 'https://images.unsplash.com/photo-1504973960431-1c467e159aa4?q=80&w=500',
        calories: 520,
        proteins: 40.0,
        carbs: 30.0,
        fats: 25.0,
        fiber: 3.0,
        sugar: 2.0,
        suitableForMeals: const [MealType.dinner],
        categories: const [FoodCategory.protein, FoodCategory.vegetable],
        isGlutenFree: true,
        isDairyFree: true,
        servingSize: '1 porzione (400g)',
        servingSizeGrams: 400,
      ),
      
      // SPUNTINI
      Food(
        id: 'snack_4',
        name: 'Hummus con carote',
        description: 'Hummus di ceci con bastoncini di carota',
        imageUrl: 'https://images.unsplash.com/photo-1541592106381-b31e9677c0e5?q=80&w=500',
        calories: 170,
        proteins: 7.0,
        carbs: 20.0,
        fats: 8.0,
        fiber: 6.0,
        sugar: 4.0,
        suitableForMeals: const [MealType.snack],
        categories: const [FoodCategory.protein, FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        allergens: const ['sesamo'],
        servingSize: '1 porzione (150g)',
        servingSizeGrams: 150,
      ),
      Food(
        id: 'snack_5',
        name: 'Smoothie verde',
        description: 'Smoothie con spinaci, banana, mela e zenzero',
        imageUrl: 'https://images.unsplash.com/photo-1556679343-c1c4b8e4fdce?q=80&w=500',
        calories: 160,
        proteins: 3.0,
        carbs: 35.0,
        fats: 1.0,
        fiber: 5.0,
        sugar: 25.0,
        suitableForMeals: const [MealType.snack],
        categories: const [FoodCategory.fruit, FoodCategory.vegetable],
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        servingSize: '1 porzione (250ml)',
        servingSizeGrams: 250,
      ),
    ];
  }
}
