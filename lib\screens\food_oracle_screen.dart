import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:camera/camera.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../ai/services/food_oracle_service.dart';
import '../ai/models/food_oracle_models.dart';
import '../services/intelligent_food_recognition_service.dart';
import '../services/lightweight_food_recognition_service.dart';
import '../services/accurate_food_recognition_service.dart';
import '../models/food_recognition_result.dart';
import '../models/user_profile.dart';
import '../providers/user_provider.dart';
import '../widgets/food_oracle/analysis_result_widget.dart';
import '../widgets/food_oracle/camera_preview_widget.dart';
import '../widgets/food_oracle/food_oracle_suggestions_widget.dart';
import '../widgets/food_recognition_result_card.dart';
import '../widgets/loading_indicator.dart';
import '../theme/dr_staffilano_theme.dart';
import '../constants/app_constants.dart';

/// Schermata principale del Food Oracle
class FoodOracleScreen extends StatefulWidget {
  const FoodOracleScreen({Key? key}) : super(key: key);

  @override
  State<FoodOracleScreen> createState() => _FoodOracleScreenState();
}

class _FoodOracleScreenState extends State<FoodOracleScreen> with WidgetsBindingObserver {
  /// Servizio Food Oracle
  late Future<FoodOracleService> _foodOracleServiceFuture;

  /// Servizio intelligente di riconoscimento alimentare
  late IntelligentFoodRecognitionService _intelligentRecognitionService;

  /// Servizio leggero di riconoscimento alimentare (fallback)
  late LightweightFoodRecognitionService _lightweightRecognitionService;

  /// Servizio accurato di riconoscimento alimentare (principale)
  late AccurateFoodRecognitionService _accurateRecognitionService;

  /// Controller della fotocamera
  CameraController? _cameraController;

  /// Lista delle fotocamere disponibili
  List<CameraDescription>? _cameras;

  /// Indice della fotocamera attualmente in uso
  int _selectedCameraIndex = 0;

  /// Flag che indica se la fotocamera è inizializzata
  bool _isCameraInitialized = false;

  /// Flag che indica se è in corso un'analisi
  bool _isAnalyzing = false;

  /// Risultato dell'analisi
  FoodOracleAnalysisResult? _analysisResult;

  /// Risultato del riconoscimento alimentare
  FoodRecognitionResult? _recognitionResult;

  /// Tipo di pasto selezionato
  String _selectedMealType = 'lunch';

  /// Picker per le immagini
  final ImagePicker _imagePicker = ImagePicker();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _foodOracleServiceFuture = FoodOracleService.getInstance();
    _intelligentRecognitionService = IntelligentFoodRecognitionService.getInstance();
    _lightweightRecognitionService = LightweightFoodRecognitionService.getInstance();
    _accurateRecognitionService = AccurateFoodRecognitionService.getInstance();
    _initializeCamera();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _cameraController?.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // Gestisce il ciclo di vita dell'app per la fotocamera
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return;
    }

    if (state == AppLifecycleState.inactive) {
      _cameraController?.dispose();
    } else if (state == AppLifecycleState.resumed) {
      _initializeCamera();
    }
  }

  /// Inizializza la fotocamera
  Future<void> _initializeCamera() async {
    try {
      // Ottieni la lista delle fotocamere disponibili
      _cameras = await availableCameras();

      if (_cameras == null || _cameras!.isEmpty) {
        return;
      }

      // Seleziona la fotocamera posteriore se disponibile
      _selectedCameraIndex = _cameras!.indexWhere(
        (camera) => camera.lensDirection == CameraLensDirection.back,
      );

      if (_selectedCameraIndex == -1) {
        _selectedCameraIndex = 0;
      }

      // Inizializza il controller della fotocamera
      await _initializeCameraController(_cameras![_selectedCameraIndex]);
    } catch (e) {
      print('Errore nell\'inizializzazione della fotocamera: $e');
    }
  }

  /// Inizializza il controller della fotocamera
  Future<void> _initializeCameraController(CameraDescription cameraDescription) async {
    try {
      // Dispose del controller precedente se esistente
      if (_cameraController != null) {
        await _cameraController!.dispose();
        _cameraController = null;
      }

      // Reset dello stato
      if (mounted) {
        setState(() {
          _isCameraInitialized = false;
        });
      }

      print('🔧 Inizializzazione controller fotocamera: ${cameraDescription.name}');

      // Crea un nuovo controller
      _cameraController = CameraController(
        cameraDescription,
        ResolutionPreset.high,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      // Inizializza il controller con timeout
      await _cameraController!.initialize().timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw Exception('Timeout nell\'inizializzazione della fotocamera');
        },
      );

      // Verifica che il controller sia ancora valido
      if (_cameraController != null && _cameraController!.value.isInitialized) {
        if (mounted) {
          setState(() {
            _isCameraInitialized = true;
          });
        }
        print('✅ Fotocamera inizializzata con successo');
      } else {
        throw Exception('Controller fotocamera non valido dopo l\'inizializzazione');
      }
    } catch (e, stackTrace) {
      print('❌ Errore nell\'inizializzazione del controller della fotocamera: $e');
      print('Stack trace: $stackTrace');

      // Cleanup in caso di errore
      if (_cameraController != null) {
        try {
          await _cameraController!.dispose();
        } catch (disposeError) {
          print('Errore nel dispose del controller: $disposeError');
        }
        _cameraController = null;
      }

      if (mounted) {
        setState(() {
          _isCameraInitialized = false;
        });
      }
    }
  }

  /// Cambia la fotocamera (anteriore/posteriore)
  Future<void> _switchCamera() async {
    if (_cameras == null || _cameras!.isEmpty || _cameras!.length < 2) {
      return;
    }

    // Calcola l'indice della prossima fotocamera
    final newIndex = (_selectedCameraIndex + 1) % _cameras!.length;

    // Inizializza il controller con la nuova fotocamera
    await _initializeCameraController(_cameras![newIndex]);

    if (mounted) {
      setState(() {
        _selectedCameraIndex = newIndex;
      });
    }
  }

  /// Scatta una foto e analizza il risultato
  Future<void> _takePictureAndAnalyze() async {
    print('📸 Tentativo di scatto foto...');

    // Controlli preliminari più dettagliati
    if (_isAnalyzing) {
      print('⚠️ Analisi già in corso, ignoro il comando');
      return;
    }

    if (_cameraController == null) {
      print('❌ Controller fotocamera è null');
      _showCameraError('Fotocamera non inizializzata. Riavvia l\'app.');
      return;
    }

    if (!_cameraController!.value.isInitialized) {
      print('❌ Controller fotocamera non inizializzato');
      _showCameraError('Fotocamera non pronta. Attendi l\'inizializzazione.');
      return;
    }

    if (_cameraController!.value.isTakingPicture) {
      print('⚠️ Scatto già in corso');
      return;
    }

    try {
      print('🔄 Avvio scatto foto...');

      setState(() {
        _isAnalyzing = true;
        _analysisResult = null;
        _recognitionResult = null;
      });

      // Scatta la foto con timeout
      final XFile photo = await _cameraController!.takePicture().timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw Exception('Timeout durante lo scatto della foto');
        },
      );

      print('📷 Foto scattata: ${photo.path}');

      // Verifica che il file esista e abbia una dimensione valida
      final file = File(photo.path);
      if (!await file.exists()) {
        throw Exception('File foto non trovato: ${photo.path}');
      }

      final fileSize = await file.length();
      if (fileSize == 0) {
        throw Exception('File foto vuoto');
      }

      print('✅ File foto valido: ${fileSize} bytes');

      // Analizza l'immagine
      await _analyzeImage(file);
    } catch (e, stackTrace) {
      print('❌ Errore nello scatto della foto: $e');
      print('Stack trace: $stackTrace');

      if (mounted) {
        String userMessage = 'Errore durante lo scatto della foto.';

        if (e.toString().contains('Timeout')) {
          userMessage = 'Timeout fotocamera. Riprova tra qualche secondo.';
        } else if (e.toString().contains('Camera')) {
          userMessage = 'Problema con la fotocamera. Verifica i permessi dell\'app.';
        } else if (e.toString().contains('Permission')) {
          userMessage = 'Permessi fotocamera negati. Abilita i permessi nelle impostazioni.';
        } else if (e.toString().contains('File')) {
          userMessage = 'Errore nel salvataggio della foto. Riprova.';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(userMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Riprova',
              textColor: Colors.white,
              onPressed: () => _takePictureAndAnalyze(),
            ),
          ),
        );

        setState(() {
          _isAnalyzing = false;
        });
      }
    }
  }

  /// Mostra un errore della fotocamera
  void _showCameraError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  /// Seleziona un'immagine dalla galleria e la analizza
  Future<void> _pickImageAndAnalyze() async {
    try {
      // Seleziona un'immagine dalla galleria
      final XFile? pickedImage = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (pickedImage == null) {
        // L'utente ha annullato la selezione
        return;
      }

      // Verifica che il file esista
      if (!await File(pickedImage.path).exists()) {
        throw Exception('File immagine selezionato non valido');
      }

      setState(() {
        _isAnalyzing = true;
        _analysisResult = null;
        _recognitionResult = null;
      });

      // Analizza l'immagine
      await _analyzeImage(File(pickedImage.path));
    } catch (e, stackTrace) {
      print('❌ Errore nella selezione dell\'immagine: $e');
      print('Stack trace: $stackTrace');

      if (mounted) {
        String userMessage = 'Errore nella selezione dell\'immagine.';

        if (e.toString().contains('Permission')) {
          userMessage = 'Permessi galleria negati. Abilita i permessi nelle impostazioni.';
        } else if (e.toString().contains('File')) {
          userMessage = 'File immagine non valido. Seleziona un\'altra immagine.';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(userMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );

        setState(() {
          _isAnalyzing = false;
        });
      }
    }
  }

  /// Analizza un'immagine
  Future<void> _analyzeImage(File imageFile) async {
    try {
      print('🔍 Avvio analisi Food Oracle per ${imageFile.path}');

      // Verifica che il file esista
      if (!await imageFile.exists()) {
        throw Exception('File immagine non trovato: ${imageFile.path}');
      }

      // Verifica che il servizio sia disponibile
      if (_intelligentRecognitionService == null) {
        throw Exception('Servizio di riconoscimento non disponibile');
      }

      // Prova prima il servizio accurato, poi i fallback
      FoodRecognitionResult result;

      try {
        print('🎯 Tentativo con servizio accurato...');
        result = await _accurateRecognitionService.analyzeFood(
          imageFile: imageFile,
          mealType: _selectedMealType,
          userId: 'user_001', // TODO: Ottieni l'ID utente reale
        );
        print('✅ Servizio accurato completato con successo');
      } catch (accurateError) {
        print('⚠️ Servizio accurato fallito: $accurateError');
        print('🚀 Fallback al servizio leggero...');

        try {
          result = await _lightweightRecognitionService.analyzeFood(
            imageFile: imageFile,
            mealType: _selectedMealType,
            userId: 'user_001',
          );
          print('✅ Servizio leggero completato con successo');
        } catch (lightweightError) {
          print('❌ Anche il servizio leggero è fallito: $lightweightError');
          print('🧠 Ultimo tentativo con servizio intelligente...');

          try {
            result = await _intelligentRecognitionService.analyzeFood(
              imageFile: imageFile,
              mealType: _selectedMealType,
              userId: 'user_001',
            );
            print('✅ Servizio intelligente completato come ultimo fallback');
          } catch (intelligentError) {
            print('❌ Tutti i servizi di riconoscimento sono falliti');
            throw Exception('Impossibile analizzare l\'immagine con nessun servizio disponibile');
          }
        }
      }

      if (mounted) {
        setState(() {
          _recognitionResult = result;
          _isAnalyzing = false;
        });

        print('✅ Analisi completata: ${result.recognizedFoods.length} alimenti riconosciuti');
      }
    } catch (e, stackTrace) {
      print('❌ Errore nell\'analisi dell\'immagine: $e');
      print('Stack trace: $stackTrace');

      if (mounted) {
        // Mostra un messaggio di errore user-friendly
        String userMessage = 'Si è verificato un errore durante l\'analisi dell\'immagine.';

        if (e.toString().contains('Database')) {
          userMessage = 'Errore nel database degli alimenti. Riprova tra qualche secondo.';
        } else if (e.toString().contains('File')) {
          userMessage = 'Errore nel caricamento dell\'immagine. Riprova con un\'altra foto.';
        } else if (e.toString().contains('Null check')) {
          userMessage = 'Errore interno dell\'app. Riavvia l\'applicazione.';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(userMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Riprova',
              textColor: Colors.white,
              onPressed: () => _analyzeImage(imageFile),
            ),
          ),
        );

        setState(() {
          _isAnalyzing = false;
        });
      }
    }
  }

  /// Salva i risultati dell'analisi nel diario alimentare
  Future<void> _saveAnalysisToFoodDiary() async {
    if (_analysisResult == null) {
      return;
    }

    try {
      // Ottieni il profilo utente
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final UserProfile userProfile = userProvider.userProfile!;

      // Ottieni il servizio Food Oracle
      final foodOracleService = await _foodOracleServiceFuture;

      // Salva l'analisi nel diario alimentare
      final success = await foodOracleService.saveAnalysisToFoodDiary(
        _analysisResult!,
        userProfile.id,
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Analisi salvata nel diario alimentare')),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Errore nel salvataggio dell\'analisi')),
          );
        }
      }
    } catch (e) {
      print('Errore nel salvataggio dell\'analisi: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Errore nel salvataggio dell\'analisi: $e')),
        );
      }
    }
  }

  /// Resetta l'analisi e torna alla fotocamera
  void _resetAnalysis() {
    setState(() {
      _analysisResult = null;
      _recognitionResult = null;
    });
  }

  /// Mostra il dialog informativo
  void _showInfoDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: DrStaffilanoTheme.accentGold.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                FontAwesomeIcons.camera,
                size: 20,
                color: DrStaffilanoTheme.accentGold,
              ),
            ),
            const SizedBox(width: 12),
            const Text('Food Oracle'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Il Food Oracle utilizza l\'intelligenza artificiale per analizzare le tue foto di cibo e fornire:',
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 16),
            Row(
              children: [
                Icon(Icons.restaurant, color: DrStaffilanoTheme.primaryGreen, size: 20),
                SizedBox(width: 8),
                Expanded(child: Text('Riconoscimento automatico degli alimenti')),
              ],
            ),
            SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.scale, color: DrStaffilanoTheme.primaryGreen, size: 20),
                SizedBox(width: 8),
                Expanded(child: Text('Stima intelligente delle porzioni')),
              ],
            ),
            SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.analytics, color: DrStaffilanoTheme.primaryGreen, size: 20),
                SizedBox(width: 8),
                Expanded(child: Text('Analisi nutrizionale completa')),
              ],
            ),
            SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.lightbulb, color: DrStaffilanoTheme.primaryGreen, size: 20),
                SizedBox(width: 8),
                Expanded(child: Text('Suggerimenti personalizzati')),
              ],
            ),
            SizedBox(height: 16),
            Text(
              'Scatta una foto del tuo pasto o seleziona un\'immagine dalla galleria per iniziare l\'analisi.',
              style: TextStyle(
                fontSize: 14,
                color: DrStaffilanoTheme.textSecondary,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            child: const Text('Ho capito'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DrStaffilanoTheme.backgroundLight,
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: DrStaffilanoTheme.accentGold.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                FontAwesomeIcons.camera,
                size: 20,
                color: DrStaffilanoTheme.accentGold,
              ),
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppConstants.foodOracleName,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Text(
                  'Analisi nutrizionale AI',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.normal,
                    color: DrStaffilanoTheme.textOnPrimary,
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          if (_analysisResult == null && _isCameraInitialized)
            IconButton(
              icon: const Icon(Icons.flip_camera_ios),
              onPressed: _switchCamera,
              tooltip: 'Cambia fotocamera',
            ),
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: _showInfoDialog,
            tooltip: 'Informazioni',
          ),
        ],
        elevation: 0,
        backgroundColor: DrStaffilanoTheme.primaryGreen,
      ),
      body: _buildBody(),
    );
  }

  /// Costruisce il corpo della schermata
  Widget _buildBody() {
    // Se è in corso un'analisi, mostra l'indicatore di caricamento
    if (_isAnalyzing) {
      return const Center(
        child: LoadingIndicator(
          message: 'Analisi dell\'immagine in corso...',
        ),
      );
    }

    // Se c'è un risultato di analisi, mostra il risultato
    if (_analysisResult != null || _recognitionResult != null) {
      return _buildAnalysisResult();
    }

    // Altrimenti, mostra la fotocamera o un messaggio di errore
    return _isCameraInitialized
        ? _buildCameraPreview()
        : const Center(
            child: Text('Impossibile inizializzare la fotocamera'),
          );
  }

  /// Costruisce l'anteprima della fotocamera
  Widget _buildCameraPreview() {
    return Column(
      children: [
        // Selettore del tipo di pasto
        _buildMealTypeSelector(),

        // Anteprima della fotocamera
        Expanded(
          child: Container(
            margin: const EdgeInsets.all(16),
            child: CameraPreviewWidget(
              controller: _cameraController,
              onTap: () {
                // Opzionale: focus tap
                if (_cameraController != null && _cameraController!.value.isInitialized) {
                  print('👆 Tap su anteprima fotocamera');
                }
              },
            ),
          ),
        ),

        // Pulsanti di azione
        _buildActionButtons(),
      ],
    );
  }

  /// Costruisce il selettore del tipo di pasto
  Widget _buildMealTypeSelector() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.backgroundWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Tipo di pasto',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: DrStaffilanoTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildMealTypeButton('breakfast', 'Colazione', FontAwesomeIcons.mugSaucer),
              _buildMealTypeButton('lunch', 'Pranzo', FontAwesomeIcons.utensils),
              _buildMealTypeButton('dinner', 'Cena', FontAwesomeIcons.bowlFood),
              _buildMealTypeButton('snack', 'Spuntino', FontAwesomeIcons.apple),
            ],
          ),
        ],
      ),
    );
  }

  /// Costruisce un pulsante per il tipo di pasto
  Widget _buildMealTypeButton(String type, String label, IconData icon) {
    final isSelected = _selectedMealType == type;

    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedMealType = type;
          });
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          margin: const EdgeInsets.symmetric(horizontal: 4),
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected ? DrStaffilanoTheme.primaryGreen : DrStaffilanoTheme.surfaceLight,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? DrStaffilanoTheme.primaryGreen : DrStaffilanoTheme.surfaceLight,
              width: 2,
            ),
            boxShadow: isSelected ? [
              BoxShadow(
                color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ] : null,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 20,
                color: isSelected ? DrStaffilanoTheme.textOnPrimary : DrStaffilanoTheme.primaryGreen,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: isSelected ? DrStaffilanoTheme.textOnPrimary : DrStaffilanoTheme.primaryGreen,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Costruisce i pulsanti di azione
  Widget _buildActionButtons() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.backgroundWhite,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.shadowMedium,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            'Inizia l\'analisi',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: DrStaffilanoTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Scatta una foto o seleziona un\'immagine dalla galleria',
            style: TextStyle(
              fontSize: 14,
              color: DrStaffilanoTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              // Pulsante per selezionare dalla galleria
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _pickImageAndAnalyze,
                  icon: const Icon(Icons.photo_library),
                  label: const Text('Galleria'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: DrStaffilanoTheme.secondaryBlue,
                    foregroundColor: DrStaffilanoTheme.textOnPrimary,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // Pulsante per scattare una foto
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _takePictureAndAnalyze,
                  icon: const Icon(Icons.camera_alt),
                  label: const Text('Fotocamera'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: DrStaffilanoTheme.primaryGreen,
                    foregroundColor: DrStaffilanoTheme.textOnPrimary,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Suggerimenti per una migliore analisi
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: DrStaffilanoTheme.accentGold.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: DrStaffilanoTheme.accentGold.withOpacity(0.3),
              ),
            ),
            child: const Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  color: DrStaffilanoTheme.accentGold,
                  size: 20,
                ),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Per risultati migliori: illuminazione buona, cibo ben visibile, angolazione dall\'alto',
                    style: TextStyle(
                      fontSize: 12,
                      color: DrStaffilanoTheme.textSecondary,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Costruisce il risultato dell'analisi
  Widget _buildAnalysisResult() {
    // Se abbiamo un risultato del nuovo servizio di riconoscimento, usalo
    if (_recognitionResult != null) {
      return FoodRecognitionResultCard(
        result: _recognitionResult!,
        onSave: _saveRecognitionResult,
        onRetake: _resetAnalysis,
        onResultCorrected: (correctedResult) {
          setState(() {
            _recognitionResult = correctedResult;
          });
        },
      ).animate().fadeIn(duration: 300.ms);
    }

    // Altrimenti usa il vecchio sistema
    if (_analysisResult != null) {
      return Column(
        children: [
          // Risultato dell'analisi
          Expanded(
            child: AnalysisResultWidget(
              analysisResult: _analysisResult!,
            ).animate().fadeIn(duration: 300.ms),
          ),

          // Suggerimenti
          if (_analysisResult!.suggestions != null && _analysisResult!.suggestions!.isNotEmpty)
            SizedBox(
              height: 150,
              child: FoodOracleSuggestionsWidget(
                suggestions: _analysisResult!.suggestions!,
              ).animate().slideY(begin: 0.2, end: 0, duration: 300.ms, delay: 200.ms),
            ),

          // Pulsanti di azione
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Pulsante per tornare alla fotocamera
                ElevatedButton.icon(
                  icon: const Icon(Icons.camera_alt),
                  label: const Text('Nuova foto'),
                  onPressed: _resetAnalysis,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[800],
                  ),
                ),

                // Pulsante per salvare l'analisi
                ElevatedButton.icon(
                  icon: const Icon(Icons.save),
                  label: const Text('Salva nel diario'),
                  onPressed: _saveAnalysisToFoodDiary,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: DrStaffilanoTheme.primaryGreen,
                  ),
                ),
              ],
            ).animate().fadeIn(duration: 300.ms, delay: 400.ms),
          ),
        ],
      );
    }

    return const SizedBox.shrink();
  }

  /// Salva il risultato del riconoscimento nel diario alimentare
  Future<void> _saveRecognitionResult() async {
    if (_recognitionResult == null) return;

    try {
      // TODO: Implementa il salvataggio nel diario alimentare
      // Per ora mostra solo un messaggio di successo

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Text('Analisi salvata: ${_recognitionResult!.recognizedFoods.length} alimenti'),
              ],
            ),
            backgroundColor: DrStaffilanoTheme.primaryGreen,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );

        // Torna alla fotocamera dopo il salvataggio
        _resetAnalysis();
      }
    } catch (e) {
      print('❌ Errore nel salvataggio: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Errore nel salvataggio: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
