import '../../models/food.dart';

/// Risultato dell'analisi di un'immagine di cibo
class FoodOracleAnalysisResult {
  /// ID univoco dell'analisi
  final String id;
  
  /// Timestamp dell'analisi
  final DateTime timestamp;
  
  /// Percorso dell'immagine analizzata
  final String imagePath;
  
  /// Tipo di pasto (colazione, pranzo, cena, spuntino)
  final String? mealType;
  
  /// Alimenti rilevati nell'immagine
  final List<DetectedFood> detectedFoods;
  
  /// Valori nutrizionali totali del pasto
  final NutritionalValues totalNutritionalValues;
  
  /// Punteggio di confidenza dell'analisi (0-1)
  final double confidenceScore;
  
  /// Messaggi di avviso o errore
  final List<String> warnings;
  
  /// Suggerimenti per migliorare il pasto
  final List<FoodOracleSuggestion>? suggestions;

  FoodOracleAnalysisResult({
    required this.id,
    required this.timestamp,
    required this.imagePath,
    this.mealType,
    required this.detectedFoods,
    required this.totalNutritionalValues,
    required this.confidenceScore,
    this.warnings = const [],
    this.suggestions,
  });

  /// Crea una copia dell'oggetto con alcuni campi modificati
  FoodOracleAnalysisResult copyWith({
    String? id,
    DateTime? timestamp,
    String? imagePath,
    String? mealType,
    List<DetectedFood>? detectedFoods,
    NutritionalValues? totalNutritionalValues,
    double? confidenceScore,
    List<String>? warnings,
    List<FoodOracleSuggestion>? suggestions,
  }) {
    return FoodOracleAnalysisResult(
      id: id ?? this.id,
      timestamp: timestamp ?? this.timestamp,
      imagePath: imagePath ?? this.imagePath,
      mealType: mealType ?? this.mealType,
      detectedFoods: detectedFoods ?? this.detectedFoods,
      totalNutritionalValues: totalNutritionalValues ?? this.totalNutritionalValues,
      confidenceScore: confidenceScore ?? this.confidenceScore,
      warnings: warnings ?? this.warnings,
      suggestions: suggestions ?? this.suggestions,
    );
  }

  /// Converte l'oggetto in una mappa
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'timestamp': timestamp.toIso8601String(),
      'imagePath': imagePath,
      'mealType': mealType,
      'detectedFoods': detectedFoods.map((food) => food.toMap()).toList(),
      'totalNutritionalValues': totalNutritionalValues.toMap(),
      'confidenceScore': confidenceScore,
      'warnings': warnings,
      'suggestions': suggestions?.map((suggestion) => suggestion.toMap()).toList(),
    };
  }

  /// Crea un oggetto da una mappa
  factory FoodOracleAnalysisResult.fromMap(Map<String, dynamic> map) {
    return FoodOracleAnalysisResult(
      id: map['id'],
      timestamp: DateTime.parse(map['timestamp']),
      imagePath: map['imagePath'],
      mealType: map['mealType'],
      detectedFoods: (map['detectedFoods'] as List)
          .map((food) => DetectedFood.fromMap(food))
          .toList(),
      totalNutritionalValues: NutritionalValues.fromMap(map['totalNutritionalValues']),
      confidenceScore: map['confidenceScore'],
      warnings: List<String>.from(map['warnings'] ?? []),
      suggestions: map['suggestions'] != null
          ? (map['suggestions'] as List)
              .map((suggestion) => FoodOracleSuggestion.fromMap(suggestion))
              .toList()
          : null,
    );
  }
}

/// Alimento rilevato nell'immagine
class DetectedFood {
  /// Alimento rilevato
  final Food food;
  
  /// Quantità stimata in grammi
  final int estimatedGrams;
  
  /// Punteggio di confidenza del rilevamento (0-1)
  final double confidenceScore;
  
  /// Posizione nell'immagine (coordinate normalizzate)
  final BoundingBox? boundingBox;
  
  /// Valori nutrizionali calcolati in base alla quantità
  final NutritionalValues nutritionalValues;

  DetectedFood({
    required this.food,
    required this.estimatedGrams,
    required this.confidenceScore,
    this.boundingBox,
    required this.nutritionalValues,
  });

  /// Crea una copia dell'oggetto con alcuni campi modificati
  DetectedFood copyWith({
    Food? food,
    int? estimatedGrams,
    double? confidenceScore,
    BoundingBox? boundingBox,
    NutritionalValues? nutritionalValues,
  }) {
    return DetectedFood(
      food: food ?? this.food,
      estimatedGrams: estimatedGrams ?? this.estimatedGrams,
      confidenceScore: confidenceScore ?? this.confidenceScore,
      boundingBox: boundingBox ?? this.boundingBox,
      nutritionalValues: nutritionalValues ?? this.nutritionalValues,
    );
  }

  /// Converte l'oggetto in una mappa
  Map<String, dynamic> toMap() {
    return {
      'food': food.toJson(),
      'estimatedGrams': estimatedGrams,
      'confidenceScore': confidenceScore,
      'boundingBox': boundingBox?.toMap(),
      'nutritionalValues': nutritionalValues.toMap(),
    };
  }

  /// Crea un oggetto da una mappa
  factory DetectedFood.fromMap(Map<String, dynamic> map) {
    return DetectedFood(
      food: Food.fromJson(map['food']),
      estimatedGrams: map['estimatedGrams'],
      confidenceScore: map['confidenceScore'],
      boundingBox: map['boundingBox'] != null
          ? BoundingBox.fromMap(map['boundingBox'])
          : null,
      nutritionalValues: NutritionalValues.fromMap(map['nutritionalValues']),
    );
  }
}

/// Valori nutrizionali di un alimento o pasto
class NutritionalValues {
  final int calories;
  final double proteins;
  final double carbs;
  final double fats;
  final double fiber;
  final double sugar;
  final Map<String, double> micronutrients;

  NutritionalValues({
    required this.calories,
    required this.proteins,
    required this.carbs,
    required this.fats,
    required this.fiber,
    required this.sugar,
    this.micronutrients = const {},
  });

  /// Converte l'oggetto in una mappa
  Map<String, dynamic> toMap() {
    return {
      'calories': calories,
      'proteins': proteins,
      'carbs': carbs,
      'fats': fats,
      'fiber': fiber,
      'sugar': sugar,
      'micronutrients': micronutrients,
    };
  }

  /// Crea un oggetto da una mappa
  factory NutritionalValues.fromMap(Map<String, dynamic> map) {
    return NutritionalValues(
      calories: map['calories'],
      proteins: map['proteins'],
      carbs: map['carbs'],
      fats: map['fats'],
      fiber: map['fiber'],
      sugar: map['sugar'],
      micronutrients: Map<String, double>.from(map['micronutrients'] ?? {}),
    );
  }

  /// Somma due oggetti NutritionalValues
  NutritionalValues operator +(NutritionalValues other) {
    final mergedMicronutrients = Map<String, double>.from(micronutrients);
    other.micronutrients.forEach((key, value) {
      mergedMicronutrients[key] = (mergedMicronutrients[key] ?? 0) + value;
    });

    return NutritionalValues(
      calories: calories + other.calories,
      proteins: proteins + other.proteins,
      carbs: carbs + other.carbs,
      fats: fats + other.fats,
      fiber: fiber + other.fiber,
      sugar: sugar + other.sugar,
      micronutrients: mergedMicronutrients,
    );
  }
}

/// Posizione di un alimento nell'immagine
class BoundingBox {
  final double x;
  final double y;
  final double width;
  final double height;

  BoundingBox({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
  });

  /// Converte l'oggetto in una mappa
  Map<String, dynamic> toMap() {
    return {
      'x': x,
      'y': y,
      'width': width,
      'height': height,
    };
  }

  /// Crea un oggetto da una mappa
  factory BoundingBox.fromMap(Map<String, dynamic> map) {
    return BoundingBox(
      x: map['x'],
      y: map['y'],
      width: map['width'],
      height: map['height'],
    );
  }
}

/// Correzioni fornite dall'utente per i risultati dell'analisi
class FoodOracleUserCorrections {
  /// Alimenti da rimuovere (indici nell'array originale)
  final List<int> foodsToRemove;
  
  /// Alimenti da aggiungere
  final List<DetectedFood> foodsToAdd;
  
  /// Alimenti da modificare (indice nell'array originale e nuovo valore)
  final Map<int, DetectedFood> foodsToModify;

  FoodOracleUserCorrections({
    this.foodsToRemove = const [],
    this.foodsToAdd = const [],
    this.foodsToModify = const {},
  });

  /// Converte l'oggetto in una mappa
  Map<String, dynamic> toMap() {
    return {
      'foodsToRemove': foodsToRemove,
      'foodsToAdd': foodsToAdd.map((food) => food.toMap()).toList(),
      'foodsToModify': foodsToModify.map(
        (key, value) => MapEntry(key.toString(), value.toMap()),
      ),
    };
  }

  /// Crea un oggetto da una mappa
  factory FoodOracleUserCorrections.fromMap(Map<String, dynamic> map) {
    return FoodOracleUserCorrections(
      foodsToRemove: List<int>.from(map['foodsToRemove'] ?? []),
      foodsToAdd: (map['foodsToAdd'] as List? ?? [])
          .map((food) => DetectedFood.fromMap(food))
          .toList(),
      foodsToModify: (map['foodsToModify'] as Map<String, dynamic>? ?? {}).map(
        (key, value) => MapEntry(int.parse(key), DetectedFood.fromMap(value)),
      ),
    );
  }
}

/// Suggerimento per migliorare il pasto analizzato
class FoodOracleSuggestion {
  /// Tipo di suggerimento
  final SuggestionType type;
  
  /// Messaggio del suggerimento
  final String message;
  
  /// Alimento suggerito (opzionale)
  final Food? suggestedFood;
  
  /// Quantità suggerita in grammi (opzionale)
  final int? suggestedGrams;
  
  /// Punteggio di rilevanza del suggerimento (0-1)
  final double relevanceScore;

  FoodOracleSuggestion({
    required this.type,
    required this.message,
    this.suggestedFood,
    this.suggestedGrams,
    required this.relevanceScore,
  });

  /// Converte l'oggetto in una mappa
  Map<String, dynamic> toMap() {
    return {
      'type': type.toString().split('.').last,
      'message': message,
      'suggestedFood': suggestedFood?.toJson(),
      'suggestedGrams': suggestedGrams,
      'relevanceScore': relevanceScore,
    };
  }

  /// Crea un oggetto da una mappa
  factory FoodOracleSuggestion.fromMap(Map<String, dynamic> map) {
    return FoodOracleSuggestion(
      type: SuggestionType.values.firstWhere(
        (e) => e.toString().split('.').last == map['type'],
        orElse: () => SuggestionType.general,
      ),
      message: map['message'],
      suggestedFood: map['suggestedFood'] != null
          ? Food.fromJson(map['suggestedFood'])
          : null,
      suggestedGrams: map['suggestedGrams'],
      relevanceScore: map['relevanceScore'],
    );
  }
}

/// Tipi di suggerimenti
enum SuggestionType {
  /// Suggerimento generale
  general,
  
  /// Suggerimento per aggiungere un alimento
  addFood,
  
  /// Suggerimento per rimuovere un alimento
  removeFood,
  
  /// Suggerimento per sostituire un alimento
  replaceFood,
  
  /// Suggerimento per modificare la quantità
  adjustPortion,
  
  /// Suggerimento per bilanciare i macronutrienti
  balanceMacros,
  
  /// Suggerimento per migliorare i micronutrienti
  improveMicronutrients,
}
