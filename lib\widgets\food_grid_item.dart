import 'package:flutter/material.dart';
import '../models/food.dart';
import '../screens/comprehensive_food_nutrition_screen.dart';

class FoodGridItem extends StatelessWidget {
  final Food food;
  final bool showNutrients;
  final bool showCategories;
  final VoidCallback? onSelected;

  const FoodGridItem({
    Key? key,
    required this.food,
    this.showNutrients = true,
    this.showCategories = true,
    this.onSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      clipBehavior: Clip.antiAlias,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          if (onSelected != null) {
            onSelected!();
          } else {
            // Se non è stato fornito un callback, apri la schermata nutrizionale completa
            ComprehensiveFoodNutritionScreen.showAsModal(context, food);
          }
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Immagine dell'alimento
            Stack(
              alignment: Alignment.bottomLeft,
              children: [
                if (food.imageUrl.isNotEmpty)
                  Image.network(
                    food.imageUrl,
                    height: 120,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      height: 120,
                      width: double.infinity,
                      color: Colors.grey[300],
                      child: const Icon(Icons.restaurant, size: 48, color: Colors.grey),
                    ),
                  )
                else
                  Container(
                    height: 120,
                    width: double.infinity,
                    color: Colors.grey[300],
                    child: const Icon(Icons.restaurant, size: 48, color: Colors.grey),
                  ),

                // Badge per alimenti stagionali o tradizionali
                if (food.isSeasonal || food.isTraditionalItalian)
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Wrap(
                      spacing: 4,
                      children: [
                        if (food.isSeasonal)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.green,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Text(
                              'Stagionale',
                              style: TextStyle(color: Colors.white, fontSize: 10),
                            ),
                          ),
                        if (food.isTraditionalItalian)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Text(
                              'Tradizionale',
                              style: TextStyle(color: Colors.white, fontSize: 10),
                            ),
                          ),
                      ],
                    ),
                  ),
              ],
            ),

            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Nome dell'alimento
                  Text(
                    food.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),

                  // Descrizione breve
                  if (food.description.isNotEmpty)
                    Text(
                      food.description,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  const SizedBox(height: 8),

                  // Informazioni nutrizionali
                  if (showNutrients)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _buildNutrientInfo('${food.calories} kcal', Icons.local_fire_department, Colors.red),
                        _buildNutrientInfo('${food.proteins}g', Icons.fitness_center, Colors.blue),
                        _buildNutrientInfo('${food.carbs}g', Icons.grain, Colors.orange),
                        _buildNutrientInfo('${food.fats}g', Icons.opacity, Colors.yellow[700]!),
                      ],
                    ),

                  // Categorie
                  if (showCategories && food.categories.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Wrap(
                        spacing: 4,
                        runSpacing: 4,
                        children: food.categories.map((category) {
                          return Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.blue[100],
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              category.toString().split('.').last,
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.blue[800],
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNutrientInfo(String text, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, size: 14, color: color),
        const SizedBox(width: 2),
        Text(
          text,
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }
}

class FoodListItem extends StatelessWidget {
  final Food food;
  final bool showNutrients;
  final VoidCallback? onSelected;

  const FoodListItem({
    Key? key,
    required this.food,
    this.showNutrients = true,
    this.onSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: InkWell(
        onTap: () {
          if (onSelected != null) {
            onSelected!();
          } else {
            // Se non è stato fornito un callback, apri la schermata nutrizionale completa
            ComprehensiveFoodNutritionScreen.showAsModal(context, food);
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              // Immagine dell'alimento
              if (food.imageUrl.isNotEmpty)
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    food.imageUrl,
                    height: 60,
                    width: 60,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      height: 60,
                      width: 60,
                      color: Colors.grey[300],
                      child: const Icon(Icons.restaurant, size: 30, color: Colors.grey),
                    ),
                  ),
                )
              else
                Container(
                  height: 60,
                  width: 60,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.restaurant, size: 30, color: Colors.grey),
                ),
              const SizedBox(width: 12),

              // Informazioni sull'alimento
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Nome dell'alimento
                    Text(
                      food.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),

                    // Descrizione breve
                    if (food.description.isNotEmpty)
                      Text(
                        food.description,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                    // Informazioni nutrizionali
                    if (showNutrients)
                      Padding(
                        padding: const EdgeInsets.only(top: 4.0),
                        child: Row(
                          children: [
                            _buildNutrientInfo('${food.calories} kcal', Icons.local_fire_department, Colors.red),
                            const SizedBox(width: 8),
                            _buildNutrientInfo('P: ${food.proteins}g', Icons.fitness_center, Colors.blue),
                            const SizedBox(width: 8),
                            _buildNutrientInfo('C: ${food.carbs}g', Icons.grain, Colors.orange),
                            const SizedBox(width: 8),
                            _buildNutrientInfo('G: ${food.fats}g', Icons.opacity, Colors.yellow[700]!),
                          ],
                        ),
                      ),
                  ],
                ),
              ),

              // Badge per alimenti stagionali o tradizionali
              if (food.isSeasonal || food.isTraditionalItalian)
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (food.isSeasonal)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        margin: const EdgeInsets.only(bottom: 4),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'Stagionale',
                          style: TextStyle(color: Colors.white, fontSize: 10),
                        ),
                      ),
                    if (food.isTraditionalItalian)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'Tradizionale',
                          style: TextStyle(color: Colors.white, fontSize: 10),
                        ),
                      ),
                  ],
                ),

              // Icona per indicare che ci sono più dettagli
              const Icon(Icons.chevron_right),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNutrientInfo(String text, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, size: 14, color: color),
        const SizedBox(width: 2),
        Text(
          text,
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }
}
