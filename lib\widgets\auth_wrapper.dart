import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';
import '../screens/auth_screen.dart';
import '../screens/modern_home_screen.dart';
import '../theme/dr_staffilano_theme.dart';

/// Wrapper che gestisce automaticamente l'autenticazione
/// Mostra la schermata di login se l'utente non è autenticato,
/// altrimenti mostra la home screen
class AuthWrapper extends StatefulWidget {
  const AuthWrapper({Key? key}) : super(key: key);

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  User? _user;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeAuth();
  }

  Future<void> _initializeAuth() async {
    try {
      // Ottieni l'utente corrente
      _user = SupabaseConfig.client.auth.currentUser;
      print('👤 Utente corrente: ${_user?.email ?? 'Nessuno'}');

      // Ascolta i cambiamenti di stato dell'autenticazione
      SupabaseConfig.client.auth.onAuthStateChange.listen((data) {
        print('🔄 Cambio stato auth: ${data.event}');
        if (data.session?.user != null) {
          print('✅ Utente autenticato: ${data.session!.user.email}');
        } else {
          print('❌ Utente non autenticato');
        }

        if (mounted) {
          setState(() {
            _user = data.session?.user;
          });
        }
      });
    } catch (e) {
      print('❌ Errore inizializzazione auth: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingScreen();
    }

    // Se l'utente è autenticato, mostra la home moderna con bottom navigation
    if (_user != null) {
      return const ModernHomeScreen();
    }

    // Altrimenti mostra la schermata di login
    return const AuthScreen();
  }

  Widget _buildLoadingScreen() {
    return Scaffold(
      backgroundColor: DrStaffilanoTheme.primaryGreen,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Logo
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(60),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Icon(
                Icons.favorite,
                color: DrStaffilanoTheme.primaryGreen,
                size: 60,
              ),
            ),
            const SizedBox(height: 32),
            
            // Titolo
            const Text(
              'Dr. Staffilano',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            
            // Sottotitolo
            Text(
              'Il tuo nutrizionista digitale',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white.withOpacity(0.8),
              ),
            ),
            const SizedBox(height: 48),
            
            // Loading indicator
            const CircularProgressIndicator(
              color: Colors.white,
              strokeWidth: 3,
            ),
            const SizedBox(height: 16),
            
            // Testo loading
            Text(
              'Caricamento...',
              style: TextStyle(
                fontSize: 14,
                color: Colors.white.withOpacity(0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
