import 'package:flutter/material.dart';
import '../../theme/dr_staffilano_theme.dart';

/// Widget per header fisso che rimane sempre visibile durante lo scroll
class StickyHeader extends StatelessWidget {
  final String title;
  final String? subtitle;
  final String? backgroundImage;
  final List<Widget>? actions;
  final Widget child;
  final Widget? bottomSection;
  final double headerHeight;
  final Widget? floatingActionButton;

  const StickyHeader({
    super.key,
    required this.title,
    this.subtitle,
    this.backgroundImage,
    this.actions,
    required this.child,
    this.bottomSection,
    this.headerHeight = 160,
    this.floatingActionButton,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Header fisso
          _buildFixedHeader(context),

          // Sezione bottom opzionale (es. statistiche)
          if (bottomSection != null) bottomSection!,

          // Contenuto scrollabile
          Expanded(child: child),
        ],
      ),
      floatingActionButton: floatingActionButton,
    );
  }

  /// Costruisce l'header fisso
  Widget _buildFixedHeader(BuildContext context) {
    return Container(
      height: headerHeight,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            DrStaffilanoTheme.primaryGreen,
            DrStaffilanoTheme.primaryGreen.withOpacity(0.8),
          ],
        ),
        image: backgroundImage != null
            ? DecorationImage(
                image: NetworkImage(backgroundImage!),
                fit: BoxFit.cover,
                colorFilter: ColorFilter.mode(
                  DrStaffilanoTheme.primaryGreen.withOpacity(0.7),
                  BlendMode.overlay,
                ),
              )
            : null,
      ),
      child: Stack(
        children: [
          // Overlay gradient
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withOpacity(0.3),
                ],
              ),
            ),
          ),

          // SafeArea per il contenuto
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                children: [
                  // AppBar row con azioni
                  Row(
                    children: [
                      // Back button se necessario
                      if (Navigator.canPop(context))
                        IconButton(
                          icon: const Icon(Icons.arrow_back, color: Colors.white),
                          onPressed: () => Navigator.pop(context),
                        ),

                      const Spacer(),

                      // Actions
                      if (actions != null) ...actions!,
                    ],
                  ),

                  const Spacer(),

                  // Titolo e sottotitolo
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 24,
                          ),
                        ),
                        if (subtitle != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            subtitle!,
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Widget per header fisso semplificato senza immagine di sfondo
class SimpleStickyHeader extends StatelessWidget {
  final String title;
  final String? subtitle;
  final List<Widget>? actions;
  final Widget child;
  final Widget? bottomSection;
  final Color? backgroundColor;
  final double headerHeight;

  const SimpleStickyHeader({
    super.key,
    required this.title,
    this.subtitle,
    this.actions,
    required this.child,
    this.bottomSection,
    this.backgroundColor,
    this.headerHeight = 120,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Header fisso
          Container(
            height: headerHeight,
            color: backgroundColor ?? DrStaffilanoTheme.primaryGreen,
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    // AppBar row
                    Row(
                      children: [
                        if (Navigator.canPop(context))
                          IconButton(
                            icon: const Icon(Icons.arrow_back, color: Colors.white),
                            onPressed: () => Navigator.pop(context),
                          ),
                        const Spacer(),
                        if (actions != null) ...actions!,
                      ],
                    ),

                    const Spacer(),

                    // Titolo
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 20,
                            ),
                          ),
                          if (subtitle != null) ...[
                            const SizedBox(height: 4),
                            Text(
                              subtitle!,
                              style: const TextStyle(
                                color: Colors.white70,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),

                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
          ),

          // Sezione bottom
          if (bottomSection != null) bottomSection!,

          // Contenuto scrollabile
          Expanded(child: child),
        ],
      ),
    );
  }
}
