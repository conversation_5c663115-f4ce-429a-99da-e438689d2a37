import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/welljourney_models.dart';
import '../controllers/advanced_challenges_controller.dart';
import '../theme/dr_staffilano_theme.dart';

/// Barra filtri avanzata per le sfide Dr. Staffilano
class ChallengeFilterBar extends StatefulWidget {
  final ChallengeCategory? selectedCategory;
  final ChallengeDifficulty? filterDifficulty;
  final ChallengeSortOrder sortOrder;
  final Function(ChallengeCategory?) onCategoryChanged;
  final Function(ChallengeDifficulty?) onDifficultyChanged;
  final Function(ChallengeSortOrder) onSortOrderChanged;
  final bool isDarkMode;

  const ChallengeFilterBar({
    Key? key,
    this.selectedCategory,
    this.filterDifficulty,
    required this.sortOrder,
    required this.onCategoryChanged,
    required this.onDifficultyChanged,
    required this.onSortOrderChanged,
    this.isDarkMode = false,
  }) : super(key: key);

  @override
  State<ChallengeFilterBar> createState() => _ChallengeFilterBarState();
}

class _ChallengeFilterBarState extends State<ChallengeFilterBar>
    with TickerProviderStateMixin {
  
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: widget.isDarkMode ? DrStaffilanoTheme.backgroundDark : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildMainFilterRow(),
          AnimatedBuilder(
            animation: _slideAnimation,
            builder: (context, child) {
              return ClipRect(
                child: Align(
                  alignment: Alignment.topCenter,
                  heightFactor: _slideAnimation.value,
                  child: _buildExpandedFilters(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  /// Riga principale dei filtri
  Widget _buildMainFilterRow() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Icon(
            FontAwesomeIcons.filter,
            size: 16,
            color: widget.isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
          ),
          const SizedBox(width: 8),
          Text(
            'Filtri',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: widget.isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildCategoryChips(),
                  const SizedBox(width: 8),
                  _buildActiveFiltersCount(),
                ],
              ),
            ),
          ),
          const SizedBox(width: 8),
          _buildExpandButton(),
        ],
      ),
    );
  }

  /// Chips delle categorie
  Widget _buildCategoryChips() {
    return Row(
      children: [
        _buildCategoryChip(null, 'Tutte'),
        const SizedBox(width: 8),
        ...ChallengeCategory.values.map((category) {
          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: _buildCategoryChip(category, category.displayName),
          );
        }).toList(),
      ],
    );
  }

  /// Singolo chip categoria
  Widget _buildCategoryChip(ChallengeCategory? category, String label) {
    final isSelected = widget.selectedCategory == category;
    final color = category?.primaryColor ?? DrStaffilanoTheme.primaryGreen;
    
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        widget.onCategoryChanged(category);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected 
              ? color.withOpacity(0.2)
              : (widget.isDarkMode ? Colors.white.withOpacity(0.1) : Colors.grey.shade100),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? color : Colors.transparent,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (category != null) ...[
              Icon(
                category.icon,
                size: 12,
                color: isSelected ? color : (widget.isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary),
              ),
              const SizedBox(width: 4),
            ],
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                color: isSelected ? color : (widget.isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary),
              ),
            ),
          ],
        ),
      ),
    ).animate(target: isSelected ? 1 : 0)
        .scale(begin: const Offset(1.0, 1.0), end: const Offset(1.05, 1.05));
  }

  /// Contatore filtri attivi
  Widget _buildActiveFiltersCount() {
    int activeFilters = 0;
    if (widget.selectedCategory != null) activeFilters++;
    if (widget.filterDifficulty != null) activeFilters++;
    
    if (activeFilters == 0) return const SizedBox.shrink();
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.accentGold.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        '$activeFilters filtri',
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.bold,
          color: DrStaffilanoTheme.accentGold,
        ),
      ),
    ).animate().scale(delay: 100.ms);
  }

  /// Pulsante espansione
  Widget _buildExpandButton() {
    return GestureDetector(
      onTap: () {
        setState(() {
          _isExpanded = !_isExpanded;
        });
        
        if (_isExpanded) {
          _animationController.forward();
        } else {
          _animationController.reverse();
        }
        
        HapticFeedback.lightImpact();
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: _isExpanded 
              ? DrStaffilanoTheme.primaryGreen.withOpacity(0.1)
              : (widget.isDarkMode ? Colors.white.withOpacity(0.1) : Colors.grey.shade100),
          borderRadius: BorderRadius.circular(8),
        ),
        child: AnimatedRotation(
          duration: const Duration(milliseconds: 200),
          turns: _isExpanded ? 0.5 : 0,
          child: Icon(
            Icons.expand_more,
            size: 16,
            color: _isExpanded 
                ? DrStaffilanoTheme.primaryGreen
                : (widget.isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary),
          ),
        ),
      ),
    );
  }

  /// Filtri espansi
  Widget _buildExpandedFilters() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Divider(
            color: widget.isDarkMode ? Colors.white.withOpacity(0.1) : Colors.grey.shade200,
            height: 1,
          ),
          const SizedBox(height: 16),
          _buildDifficultyFilter(),
          const SizedBox(height: 16),
          _buildSortOrderFilter(),
          const SizedBox(height: 16),
          _buildActionButtons(),
        ],
      ),
    );
  }

  /// Filtro difficoltà
  Widget _buildDifficultyFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Difficoltà',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: widget.isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: [
            _buildDifficultyChip(null, 'Tutte'),
            ...ChallengeDifficulty.values.map((difficulty) {
              return _buildDifficultyChip(difficulty, difficulty.displayName);
            }).toList(),
          ],
        ),
      ],
    );
  }

  /// Chip difficoltà
  Widget _buildDifficultyChip(ChallengeDifficulty? difficulty, String label) {
    final isSelected = widget.filterDifficulty == difficulty;
    final color = difficulty?.color ?? DrStaffilanoTheme.primaryGreen;
    
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        widget.onDifficultyChanged(difficulty);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected 
              ? color.withOpacity(0.2)
              : (widget.isDarkMode ? Colors.white.withOpacity(0.1) : Colors.grey.shade100),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? color : Colors.transparent,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (difficulty != null) ...[
              Icon(
                difficulty.icon,
                size: 12,
                color: isSelected ? color : (widget.isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary),
              ),
              const SizedBox(width: 4),
            ],
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
                color: isSelected ? color : (widget.isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Filtro ordinamento
  Widget _buildSortOrderFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Ordinamento',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: widget.isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: widget.isDarkMode ? Colors.white.withOpacity(0.1) : Colors.grey.shade100,
            borderRadius: BorderRadius.circular(12),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<ChallengeSortOrder>(
              value: widget.sortOrder,
              isExpanded: true,
              dropdownColor: widget.isDarkMode ? DrStaffilanoTheme.backgroundDark : Colors.white,
              style: TextStyle(
                fontSize: 14,
                color: widget.isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
              ),
              onChanged: (value) {
                if (value != null) {
                  HapticFeedback.lightImpact();
                  widget.onSortOrderChanged(value);
                }
              },
              items: ChallengeSortOrder.values.map((order) {
                return DropdownMenuItem(
                  value: order,
                  child: Text(order.displayName),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  /// Pulsanti azione
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () {
              HapticFeedback.lightImpact();
              widget.onCategoryChanged(null);
              widget.onDifficultyChanged(null);
              widget.onSortOrderChanged(ChallengeSortOrder.newest);
            },
            icon: const Icon(Icons.clear_all, size: 16),
            label: const Text('Cancella Filtri'),
            style: OutlinedButton.styleFrom(
              foregroundColor: widget.isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
              side: BorderSide(
                color: widget.isDarkMode ? Colors.white.withOpacity(0.3) : Colors.grey.shade300,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () {
              setState(() {
                _isExpanded = false;
              });
              _animationController.reverse();
              HapticFeedback.mediumImpact();
            },
            icon: const Icon(Icons.check, size: 16),
            label: const Text('Applica'),
            style: ElevatedButton.styleFrom(
              backgroundColor: DrStaffilanoTheme.primaryGreen,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
