import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/diet_plan.dart';
import '../models/ultra_detailed_profile.dart';

/// Servizio per la persistenza dei piani dietetici ultra-personalizzati
class UltraDietPlanStorage {
  static const String _keyCurrentPlan = 'ultra_current_diet_plan';
  static const String _keyPlanHistory = 'ultra_diet_plan_history';
  static const String _keyPlanProfile = 'ultra_plan_profile';
  static const String _keyPlanGenerationDate = 'ultra_plan_generation_date';
  static const String _keyPlanApplied = 'ultra_plan_applied';

  /// Salva il piano dietetico corrente
  static Future<bool> saveCurrentPlan({
    required DailyDietPlan plan,
    required UltraDetailedProfile profile,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Salva il piano
      final planJson = jsonEncode(plan.toMap());
      await prefs.setString(_keyCurrentPlan, planJson);
      
      // Salva il profilo associato
      final profileJson = jsonEncode(profile.toMap());
      await prefs.setString(_keyPlanProfile, profileJson);
      
      // Salva la data di generazione
      await prefs.setString(_keyPlanGenerationDate, DateTime.now().toIso8601String());
      
      // Inizializza stato applicazione
      await prefs.setBool(_keyPlanApplied, false);
      
      print('✅ Piano ultra-personalizzato salvato con successo');
      return true;
    } catch (e) {
      print('❌ Errore salvataggio piano: $e');
      return false;
    }
  }

  /// Carica il piano dietetico corrente
  static Future<DailyDietPlan?> loadCurrentPlan() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final planJson = prefs.getString(_keyCurrentPlan);
      
      if (planJson == null) return null;
      
      final planMap = jsonDecode(planJson) as Map<String, dynamic>;
      return DailyDietPlan.fromMap(planMap);
    } catch (e) {
      print('❌ Errore caricamento piano: $e');
      return null;
    }
  }

  /// Carica il profilo associato al piano corrente
  static Future<UltraDetailedProfile?> loadCurrentPlanProfile() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profileJson = prefs.getString(_keyPlanProfile);
      
      if (profileJson == null) return null;
      
      final profileMap = jsonDecode(profileJson) as Map<String, dynamic>;
      return UltraDetailedProfile.fromMap(profileMap);
    } catch (e) {
      print('❌ Errore caricamento profilo piano: $e');
      return null;
    }
  }

  /// Ottieni la data di generazione del piano corrente
  static Future<DateTime?> getCurrentPlanGenerationDate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dateString = prefs.getString(_keyPlanGenerationDate);
      
      if (dateString == null) return null;
      
      return DateTime.parse(dateString);
    } catch (e) {
      print('❌ Errore caricamento data generazione: $e');
      return null;
    }
  }

  /// Verifica se esiste un piano corrente
  static Future<bool> hasCurrentPlan() async {
    final plan = await loadCurrentPlan();
    return plan != null;
  }

  /// Marca il piano come applicato
  static Future<bool> markPlanAsApplied() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_keyPlanApplied, true);
      print('✅ Piano marcato come applicato');
      return true;
    } catch (e) {
      print('❌ Errore marcatura piano applicato: $e');
      return false;
    }
  }

  /// Verifica se il piano è stato applicato
  static Future<bool> isPlanApplied() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_keyPlanApplied) ?? false;
    } catch (e) {
      print('❌ Errore verifica piano applicato: $e');
      return false;
    }
  }

  /// Salva il piano nella cronologia
  static Future<bool> savePlanToHistory({
    required DailyDietPlan plan,
    required UltraDetailedProfile profile,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Carica cronologia esistente
      final historyJson = prefs.getString(_keyPlanHistory);
      List<Map<String, dynamic>> history = [];
      
      if (historyJson != null) {
        final historyList = jsonDecode(historyJson) as List;
        history = historyList.cast<Map<String, dynamic>>();
      }
      
      // Aggiungi nuovo piano alla cronologia
      final planEntry = {
        'plan': plan.toMap(),
        'profile': profile.toMap(),
        'generatedAt': DateTime.now().toIso8601String(),
        'appliedAt': null,
      };
      
      history.insert(0, planEntry); // Aggiungi in cima
      
      // Mantieni solo gli ultimi 10 piani
      if (history.length > 10) {
        history = history.take(10).toList();
      }
      
      // Salva cronologia aggiornata
      await prefs.setString(_keyPlanHistory, jsonEncode(history));
      
      print('✅ Piano aggiunto alla cronologia');
      return true;
    } catch (e) {
      print('❌ Errore salvataggio cronologia: $e');
      return false;
    }
  }

  /// Carica la cronologia dei piani
  static Future<List<Map<String, dynamic>>> loadPlanHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString(_keyPlanHistory);
      
      if (historyJson == null) return [];
      
      final historyList = jsonDecode(historyJson) as List;
      return historyList.cast<Map<String, dynamic>>();
    } catch (e) {
      print('❌ Errore caricamento cronologia: $e');
      return [];
    }
  }

  /// Elimina il piano corrente
  static Future<bool> clearCurrentPlan() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.remove(_keyCurrentPlan);
      await prefs.remove(_keyPlanProfile);
      await prefs.remove(_keyPlanGenerationDate);
      await prefs.remove(_keyPlanApplied);
      
      print('✅ Piano corrente eliminato');
      return true;
    } catch (e) {
      print('❌ Errore eliminazione piano: $e');
      return false;
    }
  }

  /// Ottieni statistiche sui piani generati
  static Future<Map<String, dynamic>> getPlanStatistics() async {
    try {
      final history = await loadPlanHistory();
      final currentPlan = await loadCurrentPlan();
      final isApplied = await isPlanApplied();
      final generationDate = await getCurrentPlanGenerationDate();
      
      return {
        'totalPlansGenerated': history.length + (currentPlan != null ? 1 : 0),
        'hasCurrentPlan': currentPlan != null,
        'isCurrentPlanApplied': isApplied,
        'lastGenerationDate': generationDate,
        'planHistory': history.length,
      };
    } catch (e) {
      print('❌ Errore calcolo statistiche: $e');
      return {};
    }
  }

  /// Esporta tutti i dati per backup
  static Future<Map<String, dynamic>?> exportAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      return {
        'currentPlan': prefs.getString(_keyCurrentPlan),
        'planProfile': prefs.getString(_keyPlanProfile),
        'generationDate': prefs.getString(_keyPlanGenerationDate),
        'planApplied': prefs.getBool(_keyPlanApplied),
        'planHistory': prefs.getString(_keyPlanHistory),
        'exportDate': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('❌ Errore esportazione dati: $e');
      return null;
    }
  }

  /// Importa dati da backup
  static Future<bool> importAllData(Map<String, dynamic> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      if (data['currentPlan'] != null) {
        await prefs.setString(_keyCurrentPlan, data['currentPlan']);
      }
      if (data['planProfile'] != null) {
        await prefs.setString(_keyPlanProfile, data['planProfile']);
      }
      if (data['generationDate'] != null) {
        await prefs.setString(_keyPlanGenerationDate, data['generationDate']);
      }
      if (data['planApplied'] != null) {
        await prefs.setBool(_keyPlanApplied, data['planApplied']);
      }
      if (data['planHistory'] != null) {
        await prefs.setString(_keyPlanHistory, data['planHistory']);
      }
      
      print('✅ Dati importati con successo');
      return true;
    } catch (e) {
      print('❌ Errore importazione dati: $e');
      return false;
    }
  }
}
