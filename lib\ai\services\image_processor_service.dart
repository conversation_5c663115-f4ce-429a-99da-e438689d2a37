import 'dart:io';
import 'dart:typed_data';
import 'package:image/image.dart' as img;
import 'package:flutter/foundation.dart';

/// Servizio per l'elaborazione delle immagini
class ImageProcessorService {
  static final ImageProcessorService _instance = ImageProcessorService._internal();

  factory ImageProcessorService() {
    return _instance;
  }

  ImageProcessorService._internal();

  /// Elabora un'immagine per prepararla all'analisi
  /// [imagePath] - Il percorso dell'immagine da elaborare
  /// [targetSize] - La dimensione target dell'immagine (larghezza, altezza)
  Future<Uint8List> processImage(String imagePath, {List<int>? targetSize}) async {
    // Carica l'immagine
    final File imageFile = File(imagePath);
    final Uint8List imageBytes = await imageFile.readAsBytes();

    // Decodifica l'immagine
    final img.Image? image = img.decodeImage(imageBytes);
    if (image == null) {
      throw Exception('Impossibile decodificare l\'immagine');
    }

    // Ridimensiona l'immagine se necessario
    img.Image processedImage = image;
    if (targetSize != null && targetSize.length == 2) {
      processedImage = img.copyResize(
        image,
        width: targetSize[0],
        height: targetSize[1],
        interpolation: img.Interpolation.linear,
      );
    }

    // Normalizza la luminosità e il contrasto
    processedImage = _normalizeImage(processedImage);

    // Codifica l'immagine elaborata
    final Uint8List processedImageBytes = Uint8List.fromList(img.encodeJpg(processedImage, quality: 90));

    return processedImageBytes;
  }

  /// Elabora un'immagine in modo asincrono
  Future<Uint8List> processImageAsync(String imagePath, {List<int>? targetSize}) async {
    return compute(_processImageIsolate, {
      'imagePath': imagePath,
      'targetSize': targetSize,
    });
  }

  /// Normalizza la luminosità e il contrasto di un'immagine
  img.Image _normalizeImage(img.Image image) {
    // Calcola la luminosità media
    int totalBrightness = 0;
    for (int y = 0; y < image.height; y++) {
      for (int x = 0; x < image.width; x++) {
        final pixel = image.getPixel(x, y);
        final r = pixel.r;
        final g = pixel.g;
        final b = pixel.b;
        totalBrightness += ((r + g + b) / 3).round();
      }
    }
    final avgBrightness = totalBrightness / (image.width * image.height);

    // Normalizza la luminosità se necessario
    if (avgBrightness < 80 || avgBrightness > 200) {
      return img.adjustColor(
        image,
        brightness: avgBrightness < 80 ? 0.2 : -0.2,
        contrast: avgBrightness < 80 ? 0.2 : 0.1,
      );
    }

    return image;
  }

  /// Segmenta un'immagine in regioni di interesse
  /// [image] - L'immagine da segmentare
  /// Restituisce una lista di regioni di interesse (x, y, larghezza, altezza)
  List<List<double>> segmentImage(Uint8List imageBytes) {
    // Implementazione base della segmentazione
    // In una versione reale, utilizzeremmo un modello di segmentazione semantica

    // Per ora, restituiamo una singola regione che copre l'intera immagine
    final img.Image? image = img.decodeImage(imageBytes);
    if (image == null) {
      return [];
    }

    return [
      [0.0, 0.0, 1.0, 1.0] // x, y, larghezza, altezza (normalizzati)
    ];
  }

  /// Salva un'immagine elaborata
  /// [imageBytes] - I byte dell'immagine da salvare
  /// [outputPath] - Il percorso in cui salvare l'immagine
  Future<String> saveProcessedImage(Uint8List imageBytes, String outputPath) async {
    final File outputFile = File(outputPath);
    await outputFile.writeAsBytes(imageBytes);
    return outputPath;
  }
}

/// Funzione per elaborare un'immagine in un isolate separato
Future<Uint8List> _processImageIsolate(Map<String, dynamic> params) async {
  final String imagePath = params['imagePath'];
  final List<int>? targetSize = params['targetSize'];

  // Carica l'immagine
  final File imageFile = File(imagePath);
  final Uint8List imageBytes = await imageFile.readAsBytes();

  // Decodifica l'immagine
  final img.Image? image = img.decodeImage(imageBytes);
  if (image == null) {
    throw Exception('Impossibile decodificare l\'immagine');
  }

  // Ridimensiona l'immagine se necessario
  img.Image processedImage = image;
  if (targetSize != null && targetSize.length == 2) {
    processedImage = img.copyResize(
      image,
      width: targetSize[0],
      height: targetSize[1],
      interpolation: img.Interpolation.linear,
    );
  }

  // Normalizza la luminosità e il contrasto
  // Implementazione semplificata per l'isolate
  int totalBrightness = 0;
  for (int y = 0; y < processedImage.height; y += 10) { // Campiona ogni 10 pixel per velocità
    for (int x = 0; x < processedImage.width; x += 10) {
      final pixel = processedImage.getPixel(x, y);
      final r = pixel.r;
      final g = pixel.g;
      final b = pixel.b;
      totalBrightness += ((r + g + b) / 3).round();
    }
  }
  final sampledPixels = (processedImage.width * processedImage.height) / 100;
  final avgBrightness = totalBrightness / sampledPixels;

  if (avgBrightness < 80 || avgBrightness > 200) {
    processedImage = img.adjustColor(
      processedImage,
      brightness: avgBrightness < 80 ? 0.2 : -0.2,
      contrast: avgBrightness < 80 ? 0.2 : 0.1,
    );
  }

  // Codifica l'immagine elaborata
  final Uint8List processedImageBytes = Uint8List.fromList(img.encodeJpg(processedImage, quality: 90));

  return processedImageBytes;
}
