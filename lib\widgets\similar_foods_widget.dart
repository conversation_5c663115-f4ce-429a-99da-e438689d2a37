import 'package:flutter/material.dart';
import '../ai/models/ai_models.dart';
import '../ai/services/ai_service.dart';
import '../models/food.dart';
import '../utils/ui_utils.dart';

/// Widget che mostra alimenti simili a un alimento selezionato
class SimilarFoodsWidget extends StatefulWidget {
  final Food food;
  final Function(Food) onFoodSelected;
  final String title;
  final IconData icon;
  final Color color;
  final Future<List<FoodRecommendation>> Function(Food, int) fetchFunction;

  const SimilarFoodsWidget({
    Key? key,
    required this.food,
    required this.onFoodSelected,
    this.title = 'Alimenti simili',
    this.icon = Icons.restaurant,
    this.color = Colors.blue,
    required this.fetchFunction,
  }) : super(key: key);

  @override
  _SimilarFoodsWidgetState createState() => _SimilarFoodsWidgetState();
}

class _SimilarFoodsWidgetState extends State<SimilarFoodsWidget> {
  bool _isLoading = true;
  String _errorMessage = '';
  List<FoodRecommendation> _recommendations = [];

  @override
  void initState() {
    super.initState();
    _loadRecommendations();
  }

  @override
  void didUpdateWidget(SimilarFoodsWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.food.id != widget.food.id) {
      _loadRecommendations();
    }
  }

  Future<void> _loadRecommendations() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final recommendations = await widget.fetchFunction(widget.food, 5);
      
      setState(() {
        _recommendations = recommendations;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Errore nel caricamento delle raccomandazioni: $e';
        _isLoading = false;
      });
      print(_errorMessage);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Row(
            children: [
              Icon(widget.icon, color: widget.color, size: 20),
              const SizedBox(width: 8),
              Text(
                widget.title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: widget.color,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        if (_isLoading)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: CircularProgressIndicator(),
            ),
          )
        else if (_errorMessage.isNotEmpty)
          Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                _errorMessage,
                style: const TextStyle(color: Colors.red),
              ),
            ),
          )
        else if (_recommendations.isEmpty)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Text('Nessun alimento simile trovato'),
            ),
          )
        else
          SizedBox(
            height: 180,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _recommendations.length,
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              itemBuilder: (context, index) {
                final recommendation = _recommendations[index];
                return _buildFoodCard(recommendation);
              },
            ),
          ),
      ],
    );
  }

  Widget _buildFoodCard(FoodRecommendation recommendation) {
    final food = recommendation.food;
    final score = recommendation.score;
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      elevation: 1,
      child: InkWell(
        onTap: () => widget.onFoodSelected(food),
        child: SizedBox(
          width: 160,
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: widget.color.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        _getCategoryIcon(food.categories.isNotEmpty ? food.categories.first : null),
                        size: 16,
                        color: widget.color,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        food.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  '${food.calories} kcal / 100g',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildNutrientText('P', '${food.proteins}g', Colors.red),
                    _buildNutrientText('C', '${food.carbs}g', Colors.blue),
                    _buildNutrientText('G', '${food.fats}g', Colors.green),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: score,
                  backgroundColor: Colors.grey[200],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _getScoreColor(score),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Compatibilità: ${(score * 100).round()}%',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNutrientText(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[800],
          ),
        ),
      ],
    );
  }

  IconData _getCategoryIcon(FoodCategory? category) {
    if (category == null) return Icons.restaurant;
    return UIUtils.getCategoryIcon(category);
  }

  Color _getScoreColor(double score) {
    if (score >= 0.8) {
      return Colors.green;
    } else if (score >= 0.6) {
      return Colors.lightGreen;
    } else if (score >= 0.4) {
      return Colors.amber;
    } else if (score >= 0.2) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
