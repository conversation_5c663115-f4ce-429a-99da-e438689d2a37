import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../services/storage_service.dart';
import '../services/diet_statistics_service.dart';
import '../models/diet_statistics.dart';
import '../theme/dr_staffilano_theme.dart';

class StatsScreen extends StatefulWidget {
  const StatsScreen({super.key});

  @override
  State<StatsScreen> createState() => _StatsScreenState();
}

class _StatsScreenState extends State<StatsScreen> with TickerProviderStateMixin {
  final DietStatisticsService _statsService = DietStatisticsService();
  StorageService? _storageService;

  DailyDietStats? _todayStats;
  WeeklyDietStats? _weeklyStats;
  List<DailyDietStats> _monthlyStats = [];
  List<Achievement> _achievements = [];
  bool _isLoading = true;
  int _selectedTabIndex = 0;

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    try {
      _storageService = await StorageService.getInstance();
      await _loadAllData();
    } catch (e) {
      print('Errore nell\'inizializzazione dei servizi: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadAllData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final today = DateFormat('yyyy-MM-dd').format(DateTime.now());

      // Calcola e carica le statistiche di oggi
      _todayStats = await _statsService.calculateAndSaveDailyStats(today);

      // Carica le statistiche settimanali
      final weekStart = DateTime.now().subtract(Duration(days: DateTime.now().weekday - 1));
      _weeklyStats = await _statsService.getWeeklyStats(weekStart);

      // Carica le statistiche mensili
      _monthlyStats = await _statsService.getMonthlyStats();

      // Carica gli achievement
      _achievements = await _statsService.getAchievements();

    } catch (e) {
      print('Errore nel caricamento delle statistiche: $e');
      // In caso di errore, mostra dati vuoti
      _todayStats = null;
      _weeklyStats = null;
      _monthlyStats = [];
      _achievements = [];
    }

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: DrStaffilanoTheme.backgroundLight,
      appBar: AppBar(
        title: const Text('Dashboard Progresso'),
        backgroundColor: DrStaffilanoTheme.primaryGreen,
        foregroundColor: DrStaffilanoTheme.textOnPrimary,
        elevation: 0,

        bottom: TabBar(
          controller: _tabController,
          indicatorColor: DrStaffilanoTheme.textOnPrimary,
          labelColor: DrStaffilanoTheme.textOnPrimary,
          unselectedLabelColor: DrStaffilanoTheme.textOnPrimary.withOpacity(0.7),
          tabs: const [
            Tab(icon: Icon(FontAwesomeIcons.calendar), text: 'Oggi'),
            Tab(icon: Icon(FontAwesomeIcons.chartLine), text: 'Settimana'),
            Tab(icon: Icon(FontAwesomeIcons.chartColumn), text: 'Mese'),
            Tab(icon: Icon(FontAwesomeIcons.trophy), text: 'Obiettivi'),
          ],
        ),
      ),
      body: _isLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: DrStaffilanoTheme.primaryGreen,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Caricamento statistiche...',
                    style: TextStyle(
                      color: DrStaffilanoTheme.textSecondary,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            )
          : TabBarView(
              controller: _tabController,
              children: [
                _buildTodayTab(),
                _buildWeeklyTab(),
                _buildMonthlyTab(),
                _buildAchievementsTab(),
              ],
            ),
    );
  }

  /// Tab per le statistiche di oggi
  Widget _buildTodayTab() {
    if (_todayStats == null) {
      return _buildEmptyState(
        'Nessun dato per oggi',
        'Completa alcuni pasti per vedere le statistiche giornaliere.',
        FontAwesomeIcons.calendar,
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTodayOverviewCard(_todayStats!),
          const SizedBox(height: 16),
          _buildCaloriesProgressCard(_todayStats!),
          const SizedBox(height: 16),
          _buildMacrosProgressCard(_todayStats!),
          const SizedBox(height: 16),
          _buildMealsProgressCard(_todayStats!),
        ],
      ),
    );
  }

  /// Tab per le statistiche settimanali
  Widget _buildWeeklyTab() {
    if (_weeklyStats == null || _weeklyStats!.dailyStats.isEmpty) {
      return _buildEmptyState(
        'Nessun dato settimanale',
        'Completa alcuni pasti durante la settimana per vedere le statistiche.',
        FontAwesomeIcons.chartLine,
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWeeklyOverviewCard(_weeklyStats!),
          const SizedBox(height: 16),
          _buildWeeklyCaloriesChart(_weeklyStats!),
          const SizedBox(height: 16),
          _buildWeeklyMacrosChart(_weeklyStats!),
        ],
      ),
    );
  }

  /// Tab per le statistiche mensili
  Widget _buildMonthlyTab() {
    if (_monthlyStats.isEmpty) {
      return _buildEmptyState(
        'Nessun dato mensile',
        'Completa alcuni pasti durante il mese per vedere le statistiche.',
        FontAwesomeIcons.chartColumn,
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMonthlyOverviewCard(),
          const SizedBox(height: 16),
          _buildMonthlyTrendChart(),
          const SizedBox(height: 16),
          _buildMonthlyCalendarView(),
        ],
      ),
    );
  }

  /// Tab per gli achievement
  Widget _buildAchievementsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAchievementsOverviewCard(),
          const SizedBox(height: 16),
          _buildAchievementsList(),
        ],
      ),
    );
  }

  /// Stato vuoto generico
  Widget _buildEmptyState(String title, String subtitle, IconData icon) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 48,
                color: DrStaffilanoTheme.primaryGreen,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              title,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: DrStaffilanoTheme.textPrimary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 16,
                color: DrStaffilanoTheme.textSecondary,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Card panoramica di oggi
  Widget _buildTodayOverviewCard(DailyDietStats stats) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: DrStaffilanoTheme.primaryGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Progresso di Oggi',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: stats.isSuccessfulDay
                      ? Colors.white.withOpacity(0.2)
                      : Colors.orange.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      stats.isSuccessfulDay
                          ? FontAwesomeIcons.circleCheck
                          : FontAwesomeIcons.clock,
                      color: Colors.white,
                      size: 16,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      stats.isSuccessfulDay ? 'Obiettivo' : 'In corso',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildOverviewStat(
                  'Calorie',
                  '${stats.caloriesConsumed}',
                  '${stats.caloriesTarget}',
                  stats.caloriesPercentage,
                  FontAwesomeIcons.fire,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildOverviewStat(
                  'Pasti',
                  '${stats.mealsCompleted}',
                  '${stats.mealsPlanned}',
                  stats.mealsCompletionPercentage,
                  FontAwesomeIcons.utensils,
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: -0.2, end: 0);
  }

  Widget _buildOverviewStat(String label, String current, String target, double percentage, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: Colors.white, size: 16),
            const SizedBox(width: 8),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          '$current / $target',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '${percentage.toStringAsFixed(0)}%',
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  /// Card progresso calorie
  Widget _buildCaloriesProgressCard(DailyDietStats stats) {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: DrStaffilanoTheme.accentGold.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    FontAwesomeIcons.fire,
                    color: DrStaffilanoTheme.accentGold,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Calorie',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${stats.caloriesConsumed} / ${stats.caloriesTarget} kcal',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${stats.caloriesPercentage.toStringAsFixed(0)}%',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: stats.isCaloriesOnTrack
                        ? DrStaffilanoTheme.primaryGreen
                        : DrStaffilanoTheme.accentGold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: (stats.caloriesPercentage / 100).clamp(0.0, 1.0),
              backgroundColor: Colors.grey.shade200,
              valueColor: AlwaysStoppedAnimation<Color>(
                stats.isCaloriesOnTrack
                    ? DrStaffilanoTheme.primaryGreen
                    : DrStaffilanoTheme.accentGold,
              ),
              minHeight: 8,
              borderRadius: BorderRadius.circular(4),
            ),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 600.ms, delay: 200.ms).slideX(begin: -0.2, end: 0);
  }

  /// Card progresso macronutrienti
  Widget _buildMacrosProgressCard(DailyDietStats stats) {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: DrStaffilanoTheme.secondaryBlue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    FontAwesomeIcons.chartPie,
                    color: DrStaffilanoTheme.secondaryBlue,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Macronutrienti',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildMacroProgressRow(
              'Proteine',
              stats.proteinsConsumed,
              stats.proteinsTarget,
              stats.proteinsPercentage,
              DrStaffilanoTheme.primaryGreen,
            ),
            const SizedBox(height: 12),
            _buildMacroProgressRow(
              'Carboidrati',
              stats.carbsConsumed,
              stats.carbsTarget,
              stats.carbsPercentage,
              DrStaffilanoTheme.secondaryBlue,
            ),
            const SizedBox(height: 12),
            _buildMacroProgressRow(
              'Grassi',
              stats.fatsConsumed,
              stats.fatsTarget,
              stats.fatsPercentage,
              DrStaffilanoTheme.accentGold,
            ),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 600.ms, delay: 400.ms).slideX(begin: 0.2, end: 0);
  }

  Widget _buildMacroProgressRow(String label, double consumed, double target, double percentage, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              '${consumed.toStringAsFixed(1)}g / ${target.toStringAsFixed(1)}g',
              style: TextStyle(
                fontSize: 14,
                color: DrStaffilanoTheme.textSecondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        LinearProgressIndicator(
          value: (percentage / 100).clamp(0.0, 1.0),
          backgroundColor: color.withOpacity(0.2),
          valueColor: AlwaysStoppedAnimation<Color>(color),
          minHeight: 6,
          borderRadius: BorderRadius.circular(3),
        ),
      ],
    );
  }

  /// Card progresso pasti
  Widget _buildMealsProgressCard(DailyDietStats stats) {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    FontAwesomeIcons.utensils,
                    color: DrStaffilanoTheme.primaryGreen,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Pasti Completati',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${stats.mealsCompleted} di ${stats.mealsPlanned} pasti',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${stats.mealsCompletionPercentage.toStringAsFixed(0)}%',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: stats.mealsCompletionPercentage >= 80
                        ? DrStaffilanoTheme.primaryGreen
                        : DrStaffilanoTheme.accentGold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: (stats.mealsCompletionPercentage / 100).clamp(0.0, 1.0),
              backgroundColor: Colors.grey.shade200,
              valueColor: AlwaysStoppedAnimation<Color>(
                stats.mealsCompletionPercentage >= 80
                    ? DrStaffilanoTheme.primaryGreen
                    : DrStaffilanoTheme.accentGold,
              ),
              minHeight: 8,
              borderRadius: BorderRadius.circular(4),
            ),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 600.ms, delay: 600.ms).slideY(begin: 0.2, end: 0);
  }

  // Placeholder per i metodi delle altre tab
  Widget _buildWeeklyOverviewCard(WeeklyDietStats stats) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text('Statistiche settimanali - In sviluppo'),
      ),
    );
  }

  Widget _buildWeeklyCaloriesChart(WeeklyDietStats stats) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text('Grafico calorie settimanali - In sviluppo'),
      ),
    );
  }

  Widget _buildWeeklyMacrosChart(WeeklyDietStats stats) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text('Grafico macro settimanali - In sviluppo'),
      ),
    );
  }

  Widget _buildMonthlyOverviewCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text('Panoramica mensile - In sviluppo'),
      ),
    );
  }

  Widget _buildMonthlyTrendChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text('Trend mensile - In sviluppo'),
      ),
    );
  }

  Widget _buildMonthlyCalendarView() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text('Vista calendario - In sviluppo'),
      ),
    );
  }

  Widget _buildAchievementsOverviewCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text('Panoramica obiettivi - In sviluppo'),
      ),
    );
  }

  Widget _buildAchievementsList() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text('Lista achievement - In sviluppo'),
      ),
    );
  }
}
