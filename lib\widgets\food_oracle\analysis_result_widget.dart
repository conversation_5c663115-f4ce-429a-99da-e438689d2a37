import 'dart:io';
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../ai/models/food_oracle_models.dart';
import '../../theme/app_theme.dart';
import 'detected_food_item_widget.dart';

/// Widget per visualizzare i risultati dell'analisi
class AnalysisResultWidget extends StatelessWidget {
  /// Risultato dell'analisi
  final FoodOracleAnalysisResult analysisResult;

  const AnalysisResultWidget({
    Key? key,
    required this.analysisResult,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Immagine analizzata
          _buildAnalyzedImage(),

          const SizedBox(height: 16.0),

          // Avvisi
          if (analysisResult.warnings.isNotEmpty)
            _buildWarnings(),

          const SizedBox(height: 16.0),

          // Titolo della sezione
          Text(
            'Alimenti rilevati',
            style: Theme.of(context).textTheme.titleLarge,
          ),

          const SizedBox(height: 8.0),

          // Lista degli alimenti rilevati
          if (analysisResult.detectedFoods.isEmpty)
            const Text('Nessun alimento rilevato'),

          if (analysisResult.detectedFoods.isNotEmpty)
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: analysisResult.detectedFoods.length,
              itemBuilder: (context, index) {
                final detectedFood = analysisResult.detectedFoods[index];
                return DetectedFoodItemWidget(
                  detectedFood: detectedFood,
                  index: index + 1,
                );
              },
            ),

          const SizedBox(height: 24.0),

          // Valori nutrizionali totali
          _buildTotalNutritionalValues(),

          const SizedBox(height: 24.0),

          // Distribuzione dei macronutrienti
          _buildMacroDistribution(),
        ],
      ),
    );
  }

  /// Costruisce l'immagine analizzata
  Widget _buildAnalyzedImage() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8.0),
      child: Image.file(
        File(analysisResult.imagePath),
        height: 200,
        width: double.infinity,
        fit: BoxFit.cover,
      ),
    );
  }

  /// Costruisce gli avvisi
  Widget _buildWarnings() {
    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Colors.amber.withOpacity(0.2),
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: Colors.amber),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.warning_amber_rounded, color: Colors.amber),
              SizedBox(width: 8.0),
              Text(
                'Avvisi',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.amber,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8.0),
          ...analysisResult.warnings.map((warning) => Padding(
            padding: const EdgeInsets.only(bottom: 4.0),
            child: Text('• $warning'),
          )),
        ],
      ),
    );
  }

  /// Costruisce i valori nutrizionali totali
  Widget _buildTotalNutritionalValues() {
    final nutritionalValues = analysisResult.totalNutritionalValues;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Valori nutrizionali totali',
          style: TextStyle(
            fontSize: 18.0,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
        const SizedBox(height: 8.0),
        Card(
          elevation: 2.0,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // Calorie
                _buildNutrientRow(
                  icon: FontAwesomeIcons.fire,
                  label: 'Calorie',
                  value: '${nutritionalValues.calories} kcal',
                  color: Colors.red,
                ),
                const Divider(),

                // Proteine
                _buildNutrientRow(
                  icon: FontAwesomeIcons.dumbbell,
                  label: 'Proteine',
                  value: '${nutritionalValues.proteins.toStringAsFixed(1)} g',
                  color: Colors.blue,
                ),
                const Divider(),

                // Carboidrati
                _buildNutrientRow(
                  icon: FontAwesomeIcons.breadSlice,
                  label: 'Carboidrati',
                  value: '${nutritionalValues.carbs.toStringAsFixed(1)} g',
                  color: Colors.orange,
                ),

                // Zuccheri (indentati)
                if (nutritionalValues.sugar > 0)
                  Padding(
                    padding: const EdgeInsets.only(left: 32.0),
                    child: _buildNutrientRow(
                      icon: FontAwesomeIcons.cubesStacked,
                      label: 'di cui zuccheri',
                      value: '${nutritionalValues.sugar.toStringAsFixed(1)} g',
                      color: Colors.pink,
                      fontSize: 14.0,
                    ),
                  ),

                const Divider(),

                // Grassi
                _buildNutrientRow(
                  icon: FontAwesomeIcons.droplet,
                  label: 'Grassi',
                  value: '${nutritionalValues.fats.toStringAsFixed(1)} g',
                  color: Colors.yellow.shade800,
                ),

                const Divider(),

                // Fibre
                _buildNutrientRow(
                  icon: FontAwesomeIcons.seedling,
                  label: 'Fibre',
                  value: '${nutritionalValues.fiber.toStringAsFixed(1)} g',
                  color: Colors.green,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Costruisce una riga per un nutriente
  Widget _buildNutrientRow({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
    double fontSize = 16.0,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Icon(icon, color: color, size: fontSize),
          const SizedBox(width: 8.0),
          Text(
            label,
            style: TextStyle(fontSize: fontSize),
          ),
          const Spacer(),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: fontSize,
            ),
          ),
        ],
      ),
    );
  }

  /// Costruisce la distribuzione dei macronutrienti
  Widget _buildMacroDistribution() {
    final nutritionalValues = analysisResult.totalNutritionalValues;

    // Calcola le calorie da ogni macronutriente
    final proteinCalories = nutritionalValues.proteins * 4;
    final carbCalories = nutritionalValues.carbs * 4;
    final fatCalories = nutritionalValues.fats * 9;

    // Calcola il totale delle calorie dai macronutrienti
    final totalMacroCalories = proteinCalories + carbCalories + fatCalories;

    // Calcola le percentuali
    final proteinPercentage = totalMacroCalories > 0 ? (proteinCalories / totalMacroCalories) * 100 : 0;
    final carbPercentage = totalMacroCalories > 0 ? (carbCalories / totalMacroCalories) * 100 : 0;
    final fatPercentage = totalMacroCalories > 0 ? (fatCalories / totalMacroCalories) * 100 : 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Distribuzione dei macronutrienti',
          style: TextStyle(
            fontSize: 18.0,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
        const SizedBox(height: 16.0),
        Row(
          children: [
            // Grafico a torta
            SizedBox(
              height: 150,
              width: 150,
              child: PieChart(
                PieChartData(
                  sections: [
                    PieChartSectionData(
                      value: proteinPercentage.toDouble(),
                      title: '${proteinPercentage.round()}%',
                      color: Colors.blue,
                      radius: 50,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    PieChartSectionData(
                      value: carbPercentage.toDouble(),
                      title: '${carbPercentage.round()}%',
                      color: Colors.orange,
                      radius: 50,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    PieChartSectionData(
                      value: fatPercentage.toDouble(),
                      title: '${fatPercentage.round()}%',
                      color: Colors.yellow.shade800,
                      radius: 50,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                  sectionsSpace: 2,
                  centerSpaceRadius: 30,
                ),
              ),
            ),
            const SizedBox(width: 16.0),

            // Legenda
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildLegendItem(
                    color: Colors.blue,
                    label: 'Proteine',
                    percentage: proteinPercentage.toDouble(),
                  ),
                  const SizedBox(height: 8.0),
                  _buildLegendItem(
                    color: Colors.orange,
                    label: 'Carboidrati',
                    percentage: carbPercentage.toDouble(),
                  ),
                  const SizedBox(height: 8.0),
                  _buildLegendItem(
                    color: Colors.yellow.shade800,
                    label: 'Grassi',
                    percentage: fatPercentage.toDouble(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Costruisce un elemento della legenda
  Widget _buildLegendItem({
    required Color color,
    required String label,
    required double percentage,
  }) {
    return Row(
      children: [
        Container(
          width: 16.0,
          height: 16.0,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8.0),
        Text(
          '$label: ${percentage.toStringAsFixed(1)}%',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
