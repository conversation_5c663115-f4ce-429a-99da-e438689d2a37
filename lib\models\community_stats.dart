/// Modello per le statistiche della community
class CommunityStats {
  final int totalMembers;
  final int totalPosts;
  final int activeChallenges;
  final int totalGroups;
  final int onlineMembers;
  final int todayPosts;
  final int weeklyActiveUsers;
  final double engagementRate;

  const CommunityStats({
    required this.totalMembers,
    required this.totalPosts,
    required this.activeChallenges,
    required this.totalGroups,
    this.onlineMembers = 0,
    this.todayPosts = 0,
    this.weeklyActiveUsers = 0,
    this.engagementRate = 0.0,
  });

  /// Crea un'istanza da JSON
  factory CommunityStats.fromJson(Map<String, dynamic> json) {
    return CommunityStats(
      totalMembers: json['totalMembers'] ?? 0,
      totalPosts: json['totalPosts'] ?? 0,
      activeChallenges: json['activeChallenges'] ?? 0,
      totalGroups: json['totalGroups'] ?? 0,
      onlineMembers: json['onlineMembers'] ?? 0,
      todayPosts: json['todayPosts'] ?? 0,
      weeklyActiveUsers: json['weeklyActiveUsers'] ?? 0,
      engagementRate: (json['engagementRate'] ?? 0.0).toDouble(),
    );
  }

  /// Converte in JSON
  Map<String, dynamic> toJson() {
    return {
      'totalMembers': totalMembers,
      'totalPosts': totalPosts,
      'activeChallenges': activeChallenges,
      'totalGroups': totalGroups,
      'onlineMembers': onlineMembers,
      'todayPosts': todayPosts,
      'weeklyActiveUsers': weeklyActiveUsers,
      'engagementRate': engagementRate,
    };
  }

  /// Crea una copia con valori modificati
  CommunityStats copyWith({
    int? totalMembers,
    int? totalPosts,
    int? activeChallenges,
    int? totalGroups,
    int? onlineMembers,
    int? todayPosts,
    int? weeklyActiveUsers,
    double? engagementRate,
  }) {
    return CommunityStats(
      totalMembers: totalMembers ?? this.totalMembers,
      totalPosts: totalPosts ?? this.totalPosts,
      activeChallenges: activeChallenges ?? this.activeChallenges,
      totalGroups: totalGroups ?? this.totalGroups,
      onlineMembers: onlineMembers ?? this.onlineMembers,
      todayPosts: todayPosts ?? this.todayPosts,
      weeklyActiveUsers: weeklyActiveUsers ?? this.weeklyActiveUsers,
      engagementRate: engagementRate ?? this.engagementRate,
    );
  }

  /// Formatta i numeri per la visualizzazione
  String get formattedMembers => _formatNumber(totalMembers);
  String get formattedPosts => _formatNumber(totalPosts);
  String get formattedGroups => _formatNumber(totalGroups);
  String get formattedOnlineMembers => _formatNumber(onlineMembers);

  /// Formatta un numero per la visualizzazione (es. 1.2K, 15.3M)
  String _formatNumber(int number) {
    if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    } else {
      return number.toString();
    }
  }

  @override
  String toString() {
    return 'CommunityStats(totalMembers: $totalMembers, totalPosts: $totalPosts, '
        'activeChallenges: $activeChallenges, totalGroups: $totalGroups)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CommunityStats &&
        other.totalMembers == totalMembers &&
        other.totalPosts == totalPosts &&
        other.activeChallenges == activeChallenges &&
        other.totalGroups == totalGroups &&
        other.onlineMembers == onlineMembers &&
        other.todayPosts == todayPosts &&
        other.weeklyActiveUsers == weeklyActiveUsers &&
        other.engagementRate == engagementRate;
  }

  @override
  int get hashCode {
    return Object.hash(
      totalMembers,
      totalPosts,
      activeChallenges,
      totalGroups,
      onlineMembers,
      todayPosts,
      weeklyActiveUsers,
      engagementRate,
    );
  }
}
