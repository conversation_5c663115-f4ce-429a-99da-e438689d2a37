import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'dart:math' as math;
import '../theme/dr_staffilano_theme.dart';
import '../services/nutriscore_service.dart';

/// Widget per grafici interattivi avanzati del NutriScore
class AdvancedNutriScoreChart extends StatefulWidget {
  final List<Map<String, dynamic>> data;
  final String title;
  final ChartType type;
  final Duration animationDuration;
  final bool isDarkMode;

  const AdvancedNutriScoreChart({
    Key? key,
    required this.data,
    required this.title,
    required this.type,
    this.animationDuration = const Duration(milliseconds: 1500),
    this.isDarkMode = false,
  }) : super(key: key);

  @override
  State<AdvancedNutriScoreChart> createState() => _AdvancedNutriScoreChartState();
}

class _AdvancedNutriScoreChartState extends State<AdvancedNutriScoreChart>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  int _selectedIndex = -1;
  bool _showTooltip = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic,
    );

    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: widget.isDarkMode ? [
            Colors.grey[800]!,
            Colors.grey[850]!,
          ] : [
            Colors.white,
            DrStaffilanoTheme.primaryGreen.withOpacity(0.02),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 2,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 24),
          SizedBox(
            height: 280,
            child: AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return _buildChart();
              },
            ),
          ),
          if (_showTooltip) _buildTooltip(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                DrStaffilanoTheme.primaryGreen,
                DrStaffilanoTheme.primaryGreen.withOpacity(0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Icon(
            _getChartIcon(),
            color: Colors.white,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.title,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: widget.isDarkMode ? Colors.white : DrStaffilanoTheme.textPrimary,
                  letterSpacing: -0.5,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                _getSubtitle(),
                style: TextStyle(
                  fontSize: 14,
                  color: widget.isDarkMode ? Colors.white70 : DrStaffilanoTheme.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        _buildPeriodSelector(),
      ],
    );
  }

  Widget _buildPeriodSelector() {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.backgroundLight,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: DrStaffilanoTheme.primaryGreen.withOpacity(0.2),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildPeriodButton('7G', true),
          _buildPeriodButton('30G', false),
          _buildPeriodButton('90G', false),
        ],
      ),
    );
  }

  Widget _buildPeriodButton(String text, bool isSelected) {
    return GestureDetector(
      onTap: () {
        // TODO: Implementare cambio periodo
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? DrStaffilanoTheme.primaryGreen : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          text,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: isSelected ? Colors.white : DrStaffilanoTheme.textSecondary,
          ),
        ),
      ),
    );
  }

  Widget _buildChart() {
    switch (widget.type) {
      case ChartType.line:
        return _buildLineChart();
      case ChartType.bar:
        return _buildBarChart();
      case ChartType.pie:
        return _buildPieChart();
      case ChartType.area:
        return _buildAreaChart();
    }
  }

  Widget _buildLineChart() {
    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          horizontalInterval: 20,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              interval: 20,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: TextStyle(
                    fontSize: 12,
                    color: DrStaffilanoTheme.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                );
              },
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              getTitlesWidget: (value, meta) {
                final days = ['L', 'M', 'M', 'G', 'V', 'S', 'D'];
                if (value.toInt() >= 0 && value.toInt() < days.length) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      days[value.toInt()],
                      style: TextStyle(
                        fontSize: 12,
                        color: DrStaffilanoTheme.textSecondary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ),
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false),
        minX: 0,
        maxX: 6,
        minY: 0,
        maxY: 100,
        lineBarsData: [
          LineChartBarData(
            spots: _generateSpots(),
            isCurved: true,
            curveSmoothness: 0.3,
            gradient: LinearGradient(
              colors: [
                DrStaffilanoTheme.primaryGreen,
                DrStaffilanoTheme.accentGold,
              ],
            ),
            barWidth: 4,
            isStrokeCapRound: true,
            dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                return FlDotCirclePainter(
                  radius: 6,
                  color: Colors.white,
                  strokeWidth: 3,
                  strokeColor: DrStaffilanoTheme.primaryGreen,
                );
              },
            ),
            belowBarData: BarAreaData(
              show: true,
              gradient: LinearGradient(
                colors: [
                  DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
                  DrStaffilanoTheme.primaryGreen.withOpacity(0.05),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
        ],
        lineTouchData: LineTouchData(
          enabled: true,
          touchCallback: (FlTouchEvent event, LineTouchResponse? touchResponse) {
            setState(() {
              if (touchResponse != null && touchResponse.lineBarSpots != null) {
                _selectedIndex = touchResponse.lineBarSpots!.first.spotIndex;
                _showTooltip = true;
              } else {
                _showTooltip = false;
              }
            });
          },
          touchTooltipData: LineTouchTooltipData(
            tooltipBorder: BorderSide.none,
            getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
              return [];
            },
          ),
        ),
      ),
    );
  }

  Widget _buildBarChart() {
    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: 100,
        barTouchData: BarTouchData(
          enabled: true,
          touchCallback: (FlTouchEvent event, BarTouchResponse? response) {
            setState(() {
              if (response != null && response.spot != null) {
                _selectedIndex = response.spot!.touchedBarGroupIndex;
                _showTooltip = true;
              } else {
                _showTooltip = false;
              }
            });
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                final categories = ['Edu', 'Con', 'App', 'Exp'];
                if (value.toInt() >= 0 && value.toInt() < categories.length) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      categories[value.toInt()],
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: DrStaffilanoTheme.textSecondary,
                      ),
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: TextStyle(
                    fontSize: 12,
                    color: DrStaffilanoTheme.textSecondary,
                  ),
                );
              },
            ),
          ),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false),
        barGroups: _generateBarGroups(),
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          horizontalInterval: 25,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
              strokeWidth: 1,
            );
          },
        ),
      ),
    );
  }

  Widget _buildPieChart() {
    return PieChart(
      PieChartData(
        sectionsSpace: 4,
        centerSpaceRadius: 60,
        sections: _generatePieSections(),
        pieTouchData: PieTouchData(
          touchCallback: (FlTouchEvent event, PieTouchResponse? response) {
            setState(() {
              if (response != null && response.touchedSection != null) {
                _selectedIndex = response.touchedSection!.touchedSectionIndex;
                _showTooltip = true;
              } else {
                _showTooltip = false;
              }
            });
          },
        ),
      ),
    );
  }

  Widget _buildAreaChart() {
    return LineChart(
      LineChartData(
        gridData: FlGridData(show: false),
        titlesData: FlTitlesData(show: false),
        borderData: FlBorderData(show: false),
        lineBarsData: [
          LineChartBarData(
            spots: _generateSpots(),
            isCurved: true,
            color: Colors.transparent,
            belowBarData: BarAreaData(
              show: true,
              gradient: LinearGradient(
                colors: [
                  DrStaffilanoTheme.primaryGreen.withOpacity(0.6),
                  DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            dotData: FlDotData(show: false),
            barWidth: 0,
          ),
        ],
      ),
    );
  }

  Widget _buildTooltip() {
    if (_selectedIndex == -1) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.primaryGreen,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: DrStaffilanoTheme.primaryGreen.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Text(
        'Valore: ${_getTooltipValue()}',
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
      ),
    );
  }

  // Helper methods
  IconData _getChartIcon() {
    switch (widget.type) {
      case ChartType.line:
        return FontAwesomeIcons.chartLine;
      case ChartType.bar:
        return FontAwesomeIcons.chartColumn;
      case ChartType.pie:
        return FontAwesomeIcons.chartPie;
      case ChartType.area:
        return FontAwesomeIcons.chartArea;
    }
  }

  String _getSubtitle() {
    switch (widget.type) {
      case ChartType.line:
        return 'Andamento temporale';
      case ChartType.bar:
        return 'Confronto categorie';
      case ChartType.pie:
        return 'Distribuzione percentuale';
      case ChartType.area:
        return 'Trend cumulativo';
    }
  }

  List<FlSpot> _generateSpots() {
    return List.generate(7, (index) {
      final value = 30 + (math.Random().nextDouble() * 40) + (index * 5);
      return FlSpot(index.toDouble(), value * _animation.value);
    });
  }

  List<BarChartGroupData> _generateBarGroups() {
    final colors = [
      DrStaffilanoTheme.primaryGreen,
      DrStaffilanoTheme.secondaryBlue,
      DrStaffilanoTheme.accentGold,
      Colors.purple,
    ];

    return List.generate(4, (index) {
      final value = 20 + (math.Random().nextDouble() * 60);
      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: value * _animation.value,
            color: colors[index],
            width: 20,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(6),
              topRight: Radius.circular(6),
            ),
            gradient: LinearGradient(
              colors: [
                colors[index],
                colors[index].withOpacity(0.7),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ],
      );
    });
  }

  List<PieChartSectionData> _generatePieSections() {
    final colors = [
      DrStaffilanoTheme.primaryGreen,
      DrStaffilanoTheme.secondaryBlue,
      DrStaffilanoTheme.accentGold,
      Colors.purple,
    ];

    final values = [35.0, 25.0, 25.0, 15.0];

    return List.generate(4, (index) {
      final isSelected = index == _selectedIndex;
      return PieChartSectionData(
        color: colors[index],
        value: values[index] * _animation.value,
        title: '${values[index].toInt()}%',
        radius: isSelected ? 70 : 60,
        titleStyle: TextStyle(
          fontSize: isSelected ? 16 : 14,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        gradient: LinearGradient(
          colors: [
            colors[index],
            colors[index].withOpacity(0.8),
          ],
        ),
      );
    });
  }

  String _getTooltipValue() {
    // Implementazione placeholder
    return '${65 + _selectedIndex * 5}%';
  }
}

enum ChartType { line, bar, pie, area }
