import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../models/community_post.dart';
import '../../models/community_user.dart';
import '../../theme/dr_staffilano_theme.dart';
import '../../services/media_service.dart';
import 'media_selection_modal.dart';
import 'media_preview_widget.dart';
import 'friend_tagging_modal.dart';
import 'emoji_picker_modal.dart';
import 'location_picker_modal.dart';
import 'more_options_modal.dart';

/// Modal Facebook-style per la creazione di post nella community
class FacebookStylePostModal extends StatefulWidget {
  final CommunityUser currentUser;
  final Function(CommunityPost)? onPostCreated;

  const FacebookStylePostModal({
    Key? key,
    required this.currentUser,
    this.onPostCreated,
  }) : super(key: key);

  @override
  State<FacebookStylePostModal> createState() => _FacebookStylePostModalState();
}

class _FacebookStylePostModalState extends State<FacebookStylePostModal>
    with TickerProviderStateMixin {
  late AnimationController _modalAnimationController;
  late AnimationController _scaleAnimationController;
  late Animation<double> _modalAnimation;
  late Animation<double> _scaleAnimation;

  final TextEditingController _textController = TextEditingController();
  final FocusNode _textFocusNode = FocusNode();

  PostType _selectedPostType = PostType.text;
  List<String> _selectedImages = [];
  List<MediaFile> _selectedMediaFiles = [];

  // New functionality variables
  List<String> _taggedUserIds = [];
  LocationData? _selectedLocation;
  PostOptions _postOptions = PostOptions();
  List<CommunityUser> _availableUsers = [];

  bool _isPostEnabled = false;

  static const int maxCharacters = 500;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _textController.addListener(_updatePostButtonState);
    _loadAvailableUsers();

    // Auto-focus sul campo di testo dopo l'animazione
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _textFocusNode.requestFocus();
      }
    });
  }

  void _setupAnimations() {
    _modalAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _modalAnimation = CurvedAnimation(
      parent: _modalAnimationController,
      curve: Curves.easeOutCubic,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleAnimationController,
      curve: Curves.elasticOut,
    ));

    _modalAnimationController.forward();
    _scaleAnimationController.forward();
  }

  @override
  void dispose() {
    debugPrint('🔴 FacebookStylePostModal.dispose() chiamato');
    debugPrint('🔴 _selectedMediaFiles al momento del dispose: ${_selectedMediaFiles.length}');
    _modalAnimationController.dispose();
    _scaleAnimationController.dispose();
    _textController.dispose();
    _textFocusNode.dispose();
    super.dispose();
  }

  void _loadAvailableUsers() {
    // TODO: Load from CommunityService
    // For now, create some mock users
    _availableUsers = [
      CommunityUser.create(
        username: 'mario_rossi',
        displayName: 'Mario Rossi',
        bio: 'Appassionato di nutrizione',
        avatarUrl: null,
      ),
      CommunityUser.create(
        username: 'giulia_bianchi',
        displayName: 'Giulia Bianchi',
        bio: 'Dietista professionista',
        avatarUrl: null,
      ),
      CommunityUser.create(
        username: 'luca_verdi',
        displayName: 'Luca Verdi',
        bio: 'Fitness enthusiast',
        avatarUrl: null,
      ),
    ];
  }

  void _updatePostButtonState() {
    final hasContent = _textController.text.trim().isNotEmpty ||
                     _selectedImages.isNotEmpty ||
                     _selectedMediaFiles.isNotEmpty;
    if (_isPostEnabled != hasContent) {
      setState(() {
        _isPostEnabled = hasContent;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: AnimatedBuilder(
        animation: _modalAnimation,
        builder: (context, child) {
          return Container(
            color: Colors.black.withOpacity(0.5 * _modalAnimation.value),
            child: Center(
              child: AnimatedBuilder(
                animation: _scaleAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _scaleAnimation.value,
                    child: _buildModalContent(),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildModalContent() {
    final screenSize = MediaQuery.of(context).size;
    final modalWidth = screenSize.width > 600 ? 500.0 : screenSize.width * 0.9;
    final modalHeight = screenSize.height * 0.7;

    return Container(
      width: modalWidth,
      height: modalHeight,
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(),
          _buildContent(),
          _buildFooter(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Profile picture
          CircleAvatar(
            radius: 20,
            backgroundImage: widget.currentUser.avatarUrl != null
                ? NetworkImage(widget.currentUser.avatarUrl!)
                : null,
            backgroundColor: DrStaffilanoTheme.primaryGreen,
            child: widget.currentUser.avatarUrl == null
                ? Text(
                    widget.currentUser.displayName.isNotEmpty
                        ? widget.currentUser.displayName[0].toUpperCase()
                        : 'U',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                : null,
          ),
          const SizedBox(width: 12),

          // User name and privacy
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.currentUser.displayName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                GestureDetector(
                  onTap: _showPrivacySelector,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getPrivacyColor(_postOptions.privacy).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color: _getPrivacyColor(_postOptions.privacy).withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _postOptions.privacy.icon,
                          style: const TextStyle(fontSize: 12),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _postOptions.privacy.displayName,
                          style: TextStyle(
                            fontSize: 12,
                            color: _getPrivacyColor(_postOptions.privacy),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Icon(
                          Icons.arrow_drop_down,
                          size: 16,
                          color: _getPrivacyColor(_postOptions.privacy),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Close button
          IconButton(
            onPressed: _closeModal,
            icon: const Icon(Icons.close),
            iconSize: 24,
            color: Colors.grey[600],
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Expanded(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            // Compact text input area (Facebook style)
            _buildCompactTextInput(),

            // Tagged friends display
            if (_taggedUserIds.isNotEmpty) _buildTaggedFriends(),

            // Selected location display
            if (_selectedLocation != null) _buildSelectedLocation(),

            const SizedBox(height: 12),

            // Main photo/video upload area (prominent)
            _buildPhotoVideoUploadArea(),

            const SizedBox(height: 12),

            // Media attachments preview
            if (_selectedMediaFiles.isNotEmpty)
              MediaPreviewWidget(
                mediaFiles: _selectedMediaFiles,
                onRemoveMedia: _removeMediaFile,
                onMediaTap: _viewMediaFile,
              ),

            // Legacy image preview (manteniamo per compatibilità)
            if (_selectedImages.isNotEmpty) _buildImagePreview(),

            // Action buttons at bottom
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactTextInput() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(25),
      ),
      child: TextField(
        controller: _textController,
        focusNode: _textFocusNode,
        maxLines: _textFocusNode.hasFocus ? 3 : 1,
        minLines: 1,
        style: const TextStyle(fontSize: 16),
        decoration: InputDecoration(
          hintText: 'A cosa stai pensando?',
          hintStyle: TextStyle(
            fontSize: 16,
            color: Colors.grey[500],
          ),
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          contentPadding: EdgeInsets.zero,
          isDense: true,
        ),
        onChanged: (value) {
          _updatePostButtonState();
        },
        onTap: () {
          setState(() {}); // Trigger rebuild to expand text field
        },
      ),
    );
  }

  Widget _buildTaggedFriends() {
    if (_taggedUserIds.isEmpty) return const SizedBox.shrink();

    final taggedUsers = _availableUsers
        .where((user) => _taggedUserIds.contains(user.id))
        .toList();

    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: DrStaffilanoTheme.professionalBlue.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: DrStaffilanoTheme.professionalBlue.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon
          Row(
            children: [
              Icon(
                FontAwesomeIcons.userTag,
                size: 14,
                color: DrStaffilanoTheme.professionalBlue,
              ),
              const SizedBox(width: 6),
              Text(
                'Con:',
                style: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: DrStaffilanoTheme.professionalBlue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),

          // Tagged users chips
          Wrap(
            spacing: 6,
            runSpacing: 4,
            children: taggedUsers.map((user) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: DrStaffilanoTheme.professionalBlue.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // User avatar
                    CircleAvatar(
                      radius: 8,
                      backgroundImage: user.avatarUrl != null
                          ? NetworkImage(user.avatarUrl!)
                          : null,
                      backgroundColor: DrStaffilanoTheme.professionalBlue,
                      child: user.avatarUrl == null
                          ? Text(
                              user.displayName.isNotEmpty
                                  ? user.displayName[0].toUpperCase()
                                  : 'U',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            )
                          : null,
                    ),
                    const SizedBox(width: 6),

                    // User name
                    Text(
                      user.displayName,
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w600,
                        color: DrStaffilanoTheme.professionalBlue,
                      ),
                    ),
                    const SizedBox(width: 4),

                    // Remove button
                    GestureDetector(
                      onTap: () => _removeTaggedUser(user.id),
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.close,
                          size: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedLocation() {
    if (_selectedLocation == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFFF5533D).withOpacity(0.05), // Rosso per posizione
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFF5533D).withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Location icon
          Icon(
            FontAwesomeIcons.locationDot,
            size: 14,
            color: const Color(0xFFF5533D),
          ),
          const SizedBox(width: 8),

          // Location info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _selectedLocation!.name,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFFF5533D),
                  ),
                ),
                if (_selectedLocation!.address?.isNotEmpty == true)
                  Text(
                    _selectedLocation!.address!,
                    style: TextStyle(
                      fontSize: 11,
                      color: Colors.grey[600],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),

          // Remove button
          GestureDetector(
            onTap: _removeSelectedLocation,
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.close,
                size: 14,
                color: Colors.grey[600],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoVideoUploadArea() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(
          color: Colors.grey.withOpacity(0.3),
          width: 2,
          style: BorderStyle.solid,
        ),
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey[50],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Upload icon
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: DrStaffilanoTheme.primaryGreen.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              FontAwesomeIcons.image,
              size: 24,
              color: DrStaffilanoTheme.primaryGreen,
            ),
          ),

          const SizedBox(height: 12),

          // Main text
          Text(
            'Aggiungi foto/video',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),

          const SizedBox(height: 6),

          // Subtitle
          Text(
            'o trascina e rilascia',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),

          const SizedBox(height: 16),

          // Upload button
          ElevatedButton.icon(
            onPressed: _selectImages,
            icon: const Icon(Icons.add_photo_alternate, size: 18),
            label: const Text('Aggiungi'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey[200],
              foregroundColor: Colors.grey[700],
              elevation: 0,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImagePreview() {
    return Container(
      height: 100,
      margin: const EdgeInsets.only(bottom: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _selectedImages.length,
        itemBuilder: (context, index) {
          return Container(
            width: 100,
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.grey[200],
            ),
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    _selectedImages[index],
                    width: 100,
                    height: 100,
                    fit: BoxFit.cover,
                  ),
                ),
                Positioned(
                  top: 4,
                  right: 4,
                  child: GestureDetector(
                    onTap: () => _removeImage(index),
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: const BoxDecoration(
                        color: Colors.black54,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Text(
            'Aggiungi al tuo post',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
          ),
          const Spacer(),
          _buildActionButton(
            icon: FontAwesomeIcons.image,
            color: const Color(0xFF45BD62),
            onTap: _selectImages,
          ),
          const SizedBox(width: 8),
          _buildActionButton(
            icon: FontAwesomeIcons.userTag,
            color: const Color(0xFF1877F2),
            onTap: _tagFriends,
          ),
          const SizedBox(width: 8),
          _buildActionButton(
            icon: FontAwesomeIcons.faceSmile,
            color: const Color(0xFFF7B928),
            onTap: _addEmoji,
          ),
          const SizedBox(width: 8),
          _buildActionButton(
            icon: FontAwesomeIcons.locationDot,
            color: const Color(0xFFF5533D),
            onTap: _selectLocation,
          ),
          const SizedBox(width: 8),
          _buildActionButton(
            icon: Icons.more_horiz,
            color: Colors.grey[600]!,
            onTap: _showMoreOptions,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 36,
        height: 36,
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(18),
        ),
        child: Icon(
          icon,
          color: color,
          size: 18,
        ),
      ),
    );
  }

  Widget _buildFooter() {
    final characterCount = _textController.text.length;
    final isOverLimit = characterCount > maxCharacters;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Colors.grey.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Character counter
          if (characterCount > 0)
            Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    isOverLimit
                        ? 'Troppi caratteri!'
                        : '${maxCharacters - characterCount} caratteri rimanenti',
                    style: TextStyle(
                      fontSize: 12,
                      color: isOverLimit ? Colors.red : Colors.grey[600],
                      fontWeight: isOverLimit ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                  // Circular progress indicator
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      value: characterCount / maxCharacters,
                      strokeWidth: 2,
                      backgroundColor: Colors.grey[300],
                      valueColor: AlwaysStoppedAnimation<Color>(
                        isOverLimit
                            ? Colors.red
                            : characterCount > maxCharacters * 0.8
                                ? Colors.orange
                                : DrStaffilanoTheme.primaryGreen,
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // Post button
          SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton(
              onPressed: (_isPostEnabled && !isOverLimit) ? _createPost : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: (_isPostEnabled && !isOverLimit)
                    ? DrStaffilanoTheme.primaryGreen
                    : Colors.grey[300],
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Pubblica',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: (_isPostEnabled && !isOverLimit) ? Colors.white : Colors.grey[600],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Action methods
  void _selectImages() {
    HapticFeedback.lightImpact();
    MediaSelectionModal.show(
      context,
      onMediaSelected: _onMediaSelected,
    );
  }

  void _selectLocation() {
    HapticFeedback.lightImpact();
    LocationPickerModal.show(
      context,
      currentLocation: _selectedLocation,
      onLocationSelected: (location) {
        setState(() {
          _selectedLocation = location;
        });
        if (location != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('📍 Posizione aggiunta: ${location.name}'),
              backgroundColor: DrStaffilanoTheme.primaryGreen,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      },
    );
  }

  void _tagFriends() {
    HapticFeedback.lightImpact();
    FriendTaggingModal.show(
      context,
      availableUsers: _availableUsers,
      selectedUserIds: _taggedUserIds,
      onUsersSelected: (userIds) {
        setState(() {
          _taggedUserIds = userIds;
        });
        if (userIds.isNotEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('👥 ${userIds.length} amici taggati'),
              backgroundColor: DrStaffilanoTheme.professionalBlue,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      },
    );
  }

  void _addEmoji() {
    HapticFeedback.lightImpact();
    EmojiPickerModal.show(
      context,
      onEmojiSelected: (emoji) {
        // Insert emoji at cursor position
        final text = _textController.text;
        final selection = _textController.selection;
        final newText = text.replaceRange(
          selection.start,
          selection.end,
          emoji,
        );
        _textController.value = TextEditingValue(
          text: newText,
          selection: TextSelection.collapsed(
            offset: selection.start + emoji.length,
          ),
        );
        _updatePostButtonState();
      },
    );
  }

  void _showMoreOptions() {
    HapticFeedback.lightImpact();
    MoreOptionsModal.show(
      context,
      currentOptions: _postOptions,
      onOptionsChanged: (options) {
        setState(() {
          _postOptions = options;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('⚙️ Opzioni post aggiornate'),
            backgroundColor: DrStaffilanoTheme.accentGold,
            behavior: SnackBarBehavior.floating,
          ),
        );
      },
    );
  }



  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
    _updatePostButtonState();
  }

  void _removeTaggedUser(String userId) {
    HapticFeedback.lightImpact();
    setState(() {
      _taggedUserIds.remove(userId);
    });

    // Show feedback
    final user = _availableUsers.firstWhere((u) => u.id == userId);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('❌ ${user.displayName} rimosso dai tag'),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _removeSelectedLocation() {
    HapticFeedback.lightImpact();
    final locationName = _selectedLocation?.name ?? 'Posizione';
    setState(() {
      _selectedLocation = null;
    });

    // Show feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('📍 $locationName rimossa'),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Ottieni il colore per l'indicatore di privacy
  Color _getPrivacyColor(PostPrivacy privacy) {
    switch (privacy) {
      case PostPrivacy.public:
        return DrStaffilanoTheme.primaryGreen;
      case PostPrivacy.friends:
        return DrStaffilanoTheme.professionalBlue;
      case PostPrivacy.private:
        return const Color(0xFFF5533D); // Rosso per privato
    }
  }

  /// Mostra il selettore di privacy rapido
  void _showPrivacySelector() {
    HapticFeedback.lightImpact();

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: 400,
          constraints: const BoxConstraints(maxWidth: 400),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    Text(
                      'Privacy del post',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[800],
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                      iconSize: 20,
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              ),

              // Divider
              Divider(
                height: 1,
                color: Colors.grey[300],
              ),

              // Privacy options
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Column(
                  children: PostPrivacy.values.map((privacy) {
                    final isSelected = _postOptions.privacy == privacy;
                    return ListTile(
                      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
                      leading: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: _getPrivacyColor(privacy).withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: Text(
                            privacy.icon,
                            style: const TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                      title: Text(
                        privacy.displayName,
                        style: TextStyle(
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          color: isSelected ? _getPrivacyColor(privacy) : Colors.grey[800],
                        ),
                      ),
                      subtitle: Text(
                        privacy.description,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      trailing: isSelected
                          ? Icon(
                              Icons.check_circle,
                              color: _getPrivacyColor(privacy),
                              size: 20,
                            )
                          : null,
                      onTap: () {
                        HapticFeedback.lightImpact();
                        setState(() {
                          _postOptions = _postOptions.copyWith(privacy: privacy);
                        });
                        Navigator.pop(context);

                        // Show feedback
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('${privacy.icon} Privacy cambiata in "${privacy.displayName}"'),
                            backgroundColor: _getPrivacyColor(privacy),
                            behavior: SnackBarBehavior.floating,
                            duration: const Duration(seconds: 2),
                          ),
                        );
                      },
                    );
                  }).toList(),
                ),
              ),

              const SizedBox(height: 12),
            ],
          ),
        ),
      ),
    );
  }

  // Media management methods
  void _onMediaSelected(List<MediaFile> mediaFiles) {
    debugPrint('🔥 _onMediaSelected chiamato con ${mediaFiles.length} file');
    debugPrint('🔥 _selectedMediaFiles prima dell\'aggiunta: ${_selectedMediaFiles.length}');
    debugPrint('🔥 FONTE: ${mediaFiles.length == 1 ? 'SINGLE PHOTO' : 'MULTIPLE PHOTOS'}');

    for (int i = 0; i < mediaFiles.length; i++) {
      final file = mediaFiles[i];
      debugPrint('🔥 File $i: ${file.name}');
      debugPrint('   - Tipo: ${file.type}');
      debugPrint('   - Dimensione: ${file.sizeFormatted}');
      debugPrint('   - Path: ${file.path.substring(0, 50)}...');
      debugPrint('   - ID: ${file.id}');
      debugPrint('   - Ha thumbnail: ${file.thumbnailData != null}');
      if (file.thumbnailData != null) {
        debugPrint('   - Thumbnail size: ${file.thumbnailData!.length} bytes');
      }
    }

    debugPrint('🔥 PRIMA di setState - widget mounted: $mounted');
    setState(() {
      debugPrint('🔥 DENTRO setState - aggiungendo ${mediaFiles.length} file...');
      _selectedMediaFiles.addAll(mediaFiles);
      debugPrint('🔥 DENTRO setState - _selectedMediaFiles.length ora: ${_selectedMediaFiles.length}');
    });

    debugPrint('🔥 DOPO setState - _selectedMediaFiles.length: ${_selectedMediaFiles.length}');
    _updatePostButtonState();

    debugPrint('🔥 setState completato. Verifica finale:');

    // Verifica che i file siano effettivamente nella lista
    for (int i = 0; i < _selectedMediaFiles.length; i++) {
      debugPrint('🔥 File finale in lista $i: ${_selectedMediaFiles[i].name}');
      debugPrint('🔥   - Path: ${_selectedMediaFiles[i].path.substring(0, 50)}...');
      debugPrint('🔥   - Ha thumbnail: ${_selectedMediaFiles[i].thumbnailData != null}');
    }

    debugPrint('🔥 Triggering widget rebuild...');
    // Force rebuild per debug
    if (mounted) {
      debugPrint('🔥 Widget is mounted, setState should trigger rebuild');
    } else {
      debugPrint('🔥 ❌ Widget NOT mounted!');
    }

    // Mostra feedback per selezioni multiple
    if (mediaFiles.length > 1) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            '📸 ${mediaFiles.length} foto aggiunte! Scorri per vedere tutte le anteprime.',
          ),
          backgroundColor: DrStaffilanoTheme.primaryGreen,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 3),
          action: SnackBarAction(
            label: 'OK',
            textColor: Colors.white,
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    }
  }

  void _removeMediaFile(MediaFile mediaFile) {
    setState(() {
      _selectedMediaFiles.removeWhere((file) => file.id == mediaFile.id);
    });
    _updatePostButtonState();

    // Feedback per rimozione
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '🗑️ ${mediaFile.isImage ? 'Foto' : 'Video'} rimosso dal post',
        ),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
        action: SnackBarAction(
          label: 'ANNULLA',
          textColor: Colors.white,
          onPressed: () {
            // Ripristina il file rimosso
            setState(() {
              _selectedMediaFiles.add(mediaFile);
            });
            _updatePostButtonState();
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  void _viewMediaFile(MediaFile mediaFile) {
    // TODO: Implement full-screen media viewer
    HapticFeedback.lightImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Visualizzazione ${mediaFile.name}'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _createPost() {
    if (!_isPostEnabled) return;

    HapticFeedback.mediumImpact();

    // Combina le immagini legacy con i nuovi media files
    final allImageUrls = <String>[
      ..._selectedImages,
      ..._selectedMediaFiles.where((file) => file.isImage).map((file) => file.path),
    ];

    // Ottieni i dati completi degli utenti taggati
    final taggedUsers = _availableUsers
        .where((user) => _taggedUserIds.contains(user.id))
        .toList();

    final post = CommunityPost.create(
      authorId: widget.currentUser.id,
      type: _selectedPostType,
      content: _textController.text.trim(),
      imageUrls: allImageUrls,
      tags: [], // TODO: Extract hashtags from content
      taggedUserIds: _taggedUserIds,
      taggedUsers: taggedUsers,
      location: _selectedLocation?.toMap(),
      privacy: _postOptions.privacy,
      metadata: {
        'scheduledTime': _postOptions.scheduledTime?.toIso8601String(),
        'hasPoll': _postOptions.hasPoll,
        'backgroundColor': _postOptions.backgroundColor,
        'advancedSettings': _postOptions.advancedSettings,
      },
    );

    widget.onPostCreated?.call(post);
    _closeModal();
  }

  void _closeModal() {
    debugPrint('🔴 _closeModal chiamato');
    debugPrint('🔴 _selectedMediaFiles prima della chiusura: ${_selectedMediaFiles.length}');
    _modalAnimationController.reverse().then((_) {
      if (mounted) {
        debugPrint('🔴 Navigator.pop() chiamato');
        Navigator.of(context).pop();
      }
    });
  }
}
