import 'package:flutter/material.dart';
import '../theme/dr_staffilano_theme.dart';

/// Widget per il feedback visivo dei punti guadagnati nei quiz
class QuizPointsFeedbackWidget extends StatefulWidget {
  final int pointsEarned;
  final int bonusPoints;
  final int percentage;
  final String performanceLevel;
  final VoidCallback? onAnimationComplete;

  const QuizPointsFeedbackWidget({
    Key? key,
    required this.pointsEarned,
    required this.bonusPoints,
    required this.percentage,
    required this.performanceLevel,
    this.onAnimationComplete,
  }) : super(key: key);

  @override
  State<QuizPointsFeedbackWidget> createState() => _QuizPointsFeedbackWidgetState();
}

class _QuizPointsFeedbackWidgetState extends State<QuizPointsFeedbackWidget>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _slideController;
  late AnimationController _pointsController;

  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _pointsAnimation;

  @override
  void initState() {
    super.initState();

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _pointsController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));

    _pointsAnimation = Tween<double>(
      begin: 0.0,
      end: widget.pointsEarned.toDouble(),
    ).animate(CurvedAnimation(
      parent: _pointsController,
      curve: Curves.easeOut,
    ));

    _startAnimations();
  }

  void _startAnimations() async {
    await Future.delayed(const Duration(milliseconds: 300));
    _slideController.forward();

    await Future.delayed(const Duration(milliseconds: 200));
    _scaleController.forward();

    await Future.delayed(const Duration(milliseconds: 400));
    _pointsController.forward();

    await Future.delayed(const Duration(milliseconds: 1500));
    widget.onAnimationComplete?.call();
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _slideController.dispose();
    _pointsController.dispose();
    super.dispose();
  }

  Color _getPerformanceColor() {
    switch (widget.performanceLevel.toLowerCase()) {
      case 'eccellente':
        return DrStaffilanoTheme.accentGold;
      case 'molto buono':
        return DrStaffilanoTheme.primaryGreen;
      case 'buono':
        return DrStaffilanoTheme.secondaryBlue;
      case 'sufficiente':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getPerformanceIcon() {
    switch (widget.performanceLevel.toLowerCase()) {
      case 'eccellente':
        return Icons.star;
      case 'molto buono':
        return Icons.favorite;
      case 'buono':
        return Icons.thumb_up;
      case 'sufficiente':
        return Icons.check_circle;
      default:
        return Icons.info;
    }
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          margin: const EdgeInsets.all(20),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                _getPerformanceColor().withOpacity(0.1),
                _getPerformanceColor().withOpacity(0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: _getPerformanceColor().withOpacity(0.3),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: _getPerformanceColor().withOpacity(0.2),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Icona performance
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _getPerformanceColor(),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: _getPerformanceColor().withOpacity(0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Icon(
                  _getPerformanceIcon(),
                  color: Colors.white,
                  size: 32,
                ),
              ),

              const SizedBox(height: 16),

              // Livello performance
              Text(
                widget.performanceLevel,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: _getPerformanceColor(),
                ),
              ),

              const SizedBox(height: 8),

              // Percentuale
              Text(
                '${widget.percentage}%',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: DrStaffilanoTheme.textPrimary,
                ),
              ),

              const SizedBox(height: 20),

              // Punti guadagnati
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.stars,
                    color: DrStaffilanoTheme.accentGold,
                    size: 28,
                  ),
                  const SizedBox(width: 8),
                  AnimatedBuilder(
                    animation: _pointsAnimation,
                    builder: (context, child) {
                      return Text(
                        '+${_pointsAnimation.value.round()}',
                        style: const TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: DrStaffilanoTheme.accentGold,
                        ),
                      );
                    },
                  ),
                  const SizedBox(width: 4),
                  const Text(
                    'punti',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: DrStaffilanoTheme.textSecondary,
                    ),
                  ),
                ],
              ),

              // Bonus points se presenti
              if (widget.bonusPoints > 0) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: DrStaffilanoTheme.accentGold.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: DrStaffilanoTheme.accentGold.withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.celebration,
                        color: DrStaffilanoTheme.accentGold,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Bonus: +${widget.bonusPoints}',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: DrStaffilanoTheme.accentGold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 16),

              // Messaggio motivazionale
              Text(
                _getMotivationalMessage(),
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                  color: DrStaffilanoTheme.textSecondary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getMotivationalMessage() {
    if (widget.percentage >= 95) {
      return 'Perfetto! La tua conoscenza è eccezionale!';
    } else if (widget.percentage >= 85) {
      return 'Eccellente! Continua così!';
    } else if (widget.percentage >= 75) {
      return 'Molto bene! Stai facendo progressi fantastici!';
    } else if (widget.percentage >= 65) {
      return 'Buon lavoro! Ogni passo conta!';
    } else {
      return 'Continua a studiare, migliorerai!';
    }
  }
}
